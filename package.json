{"name": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "version": "1.0.0", "type": "module", "description": "Modern manga reading website built with React, TypeScript, and Vite", "keywords": ["manga", "comic", "reader", "react", "typescript", "vite"], "author": "BlogTruyen Team", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "clean": "rm -rf dist node_modules/.vite"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "lucide-react": "^0.513.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.2", "react-router-dom": "^7.6.2", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.30", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jiti": "^2.4.2", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}