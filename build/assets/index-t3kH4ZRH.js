import{Q as Ro,j as i,a as Po}from"./query-DYyOl6VU.js";import{r as x,c as Se,a as Mo,L as te,O as Tt,u as Oo,N as wt,d as Ao,e as To,f as Eo}from"./router-CxATPNq6.js";import{a as Lo}from"./vendor-BtP0CW_r.js";import{S as Io,P as fe,c as at,u as ve,a as Xt,b as Jt,d as Et,e as Qt,f as B,g as Nr,h as jr,i as Lt,j as Do,k as _o,R as $o,F as Fo,D as zo,l as Bo,m as Vo}from"./ui-B4eytjC_.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();var ht={},En;function Ko(){if(En)return ht;En=1;var t=Lo();return ht.createRoot=t.createRoot,ht.hydrateRoot=t.hydrateRoot,ht}var Ho=Ko();const Uo=new Ro({defaultOptions:{queries:{staleTime:5*60*1e3,gcTime:10*60*1e3,retry:(t,e)=>(e==null?void 0:e.status)>=400&&(e==null?void 0:e.status)<500?!1:t<3,refetchOnWindowFocus:!1},mutations:{retry:!1}}});function Sr(t){var e,n,r="";if(typeof t=="string"||typeof t=="number")r+=t;else if(typeof t=="object")if(Array.isArray(t)){var s=t.length;for(e=0;e<s;e++)t[e]&&(n=Sr(t[e]))&&(r&&(r+=" "),r+=n)}else for(n in t)t[n]&&(r&&(r+=" "),r+=n);return r}function kr(){for(var t,e,n=0,r="",s=arguments.length;n<s;n++)(t=arguments[n])&&(e=Sr(t))&&(r&&(r+=" "),r+=e);return r}const Ln=t=>typeof t=="boolean"?`${t}`:t===0?"0":t,In=kr,Cr=(t,e)=>n=>{var r;if((e==null?void 0:e.variants)==null)return In(t,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:s,defaultVariants:o}=e,a=Object.keys(s).map(l=>{const f=n==null?void 0:n[l],u=o==null?void 0:o[l];if(f===null)return null;const h=Ln(f)||Ln(u);return s[l][h]}),c=n&&Object.entries(n).reduce((l,f)=>{let[u,h]=f;return h===void 0||(l[u]=h),l},{}),d=e==null||(r=e.compoundVariants)===null||r===void 0?void 0:r.reduce((l,f)=>{let{class:u,className:h,...m}=f;return Object.entries(m).every(p=>{let[g,y]=p;return Array.isArray(y)?y.includes({...o,...c}[g]):{...o,...c}[g]===y})?[...l,u,h]:l},[]);return In(t,a,d,n==null?void 0:n.class,n==null?void 0:n.className)},pn="-",Go=t=>{const e=qo(t),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=t;return{getClassGroupId:a=>{const c=a.split(pn);return c[0]===""&&c.length!==1&&c.shift(),Rr(c,e)||Wo(a)},getConflictingClassGroupIds:(a,c)=>{const d=n[a]||[];return c&&r[a]?[...d,...r[a]]:d}}},Rr=(t,e)=>{var a;if(t.length===0)return e.classGroupId;const n=t[0],r=e.nextPart.get(n),s=r?Rr(t.slice(1),r):void 0;if(s)return s;if(e.validators.length===0)return;const o=t.join(pn);return(a=e.validators.find(({validator:c})=>c(o)))==null?void 0:a.classGroupId},Dn=/^\[(.+)\]$/,Wo=t=>{if(Dn.test(t)){const e=Dn.exec(t)[1],n=e==null?void 0:e.substring(0,e.indexOf(":"));if(n)return"arbitrary.."+n}},qo=t=>{const{theme:e,classGroups:n}=t,r={nextPart:new Map,validators:[]};for(const s in n)Zt(n[s],r,s,e);return r},Zt=(t,e,n,r)=>{t.forEach(s=>{if(typeof s=="string"){const o=s===""?e:_n(e,s);o.classGroupId=n;return}if(typeof s=="function"){if(Yo(s)){Zt(s(r),e,n,r);return}e.validators.push({validator:s,classGroupId:n});return}Object.entries(s).forEach(([o,a])=>{Zt(a,_n(e,o),n,r)})})},_n=(t,e)=>{let n=t;return e.split(pn).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Yo=t=>t.isThemeGetter,Xo=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,n=new Map,r=new Map;const s=(o,a)=>{n.set(o,a),e++,e>t&&(e=0,r=n,n=new Map)};return{get(o){let a=n.get(o);if(a!==void 0)return a;if((a=r.get(o))!==void 0)return s(o,a),a},set(o,a){n.has(o)?n.set(o,a):s(o,a)}}},en="!",tn=":",Jo=tn.length,Qo=t=>{const{prefix:e,experimentalParseClassName:n}=t;let r=s=>{const o=[];let a=0,c=0,d=0,l;for(let p=0;p<s.length;p++){let g=s[p];if(a===0&&c===0){if(g===tn){o.push(s.slice(d,p)),d=p+Jo;continue}if(g==="/"){l=p;continue}}g==="["?a++:g==="]"?a--:g==="("?c++:g===")"&&c--}const f=o.length===0?s:s.substring(d),u=Zo(f),h=u!==f,m=l&&l>d?l-d:void 0;return{modifiers:o,hasImportantModifier:h,baseClassName:u,maybePostfixModifierPosition:m}};if(e){const s=e+tn,o=r;r=a=>a.startsWith(s)?o(a.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:a,maybePostfixModifierPosition:void 0}}if(n){const s=r;r=o=>n({className:o,parseClassName:s})}return r},Zo=t=>t.endsWith(en)?t.substring(0,t.length-1):t.startsWith(en)?t.substring(1):t,ei=t=>{const e=Object.fromEntries(t.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const s=[];let o=[];return r.forEach(a=>{a[0]==="["||e[a]?(s.push(...o.sort(),a),o=[]):o.push(a)}),s.push(...o.sort()),s}},ti=t=>({cache:Xo(t.cacheSize),parseClassName:Qo(t),sortModifiers:ei(t),...Go(t)}),ni=/\s+/,ri=(t,e)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:s,sortModifiers:o}=e,a=[],c=t.trim().split(ni);let d="";for(let l=c.length-1;l>=0;l-=1){const f=c[l],{isExternal:u,modifiers:h,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:g}=n(f);if(u){d=f+(d.length>0?" "+d:d);continue}let y=!!g,v=r(y?p.substring(0,g):p);if(!v){if(!y){d=f+(d.length>0?" "+d:d);continue}if(v=r(p),!v){d=f+(d.length>0?" "+d:d);continue}y=!1}const b=o(h).join(":"),j=m?b+en:b,S=j+v;if(a.includes(S))continue;a.push(S);const k=s(v,y);for(let A=0;A<k.length;++A){const T=k[A];a.push(j+T)}d=f+(d.length>0?" "+d:d)}return d};function si(){let t=0,e,n,r="";for(;t<arguments.length;)(e=arguments[t++])&&(n=Pr(e))&&(r&&(r+=" "),r+=n);return r}const Pr=t=>{if(typeof t=="string")return t;let e,n="";for(let r=0;r<t.length;r++)t[r]&&(e=Pr(t[r]))&&(n&&(n+=" "),n+=e);return n};function oi(t,...e){let n,r,s,o=a;function a(d){const l=e.reduce((f,u)=>u(f),t());return n=ti(l),r=n.cache.get,s=n.cache.set,o=c,c(d)}function c(d){const l=r(d);if(l)return l;const f=ri(d,n);return s(d,f),f}return function(){return o(si.apply(null,arguments))}}const X=t=>{const e=n=>n[t]||[];return e.isThemeGetter=!0,e},Mr=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Or=/^\((?:(\w[\w-]*):)?(.+)\)$/i,ii=/^\d+\/\d+$/,ai=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ci=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,li=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,di=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ui=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Fe=t=>ii.test(t),_=t=>!!t&&!Number.isNaN(Number(t)),Pe=t=>!!t&&Number.isInteger(Number(t)),Kt=t=>t.endsWith("%")&&_(t.slice(0,-1)),je=t=>ai.test(t),fi=()=>!0,hi=t=>ci.test(t)&&!li.test(t),Ar=()=>!1,mi=t=>di.test(t),pi=t=>ui.test(t),gi=t=>!R(t)&&!P(t),xi=t=>Ue(t,Lr,Ar),R=t=>Mr.test(t),Ee=t=>Ue(t,Ir,hi),Ht=t=>Ue(t,Ni,_),$n=t=>Ue(t,Tr,Ar),yi=t=>Ue(t,Er,pi),mt=t=>Ue(t,Dr,mi),P=t=>Or.test(t),Xe=t=>Ge(t,Ir),bi=t=>Ge(t,ji),Fn=t=>Ge(t,Tr),vi=t=>Ge(t,Lr),wi=t=>Ge(t,Er),pt=t=>Ge(t,Dr,!0),Ue=(t,e,n)=>{const r=Mr.exec(t);return r?r[1]?e(r[1]):n(r[2]):!1},Ge=(t,e,n=!1)=>{const r=Or.exec(t);return r?r[1]?e(r[1]):n:!1},Tr=t=>t==="position"||t==="percentage",Er=t=>t==="image"||t==="url",Lr=t=>t==="length"||t==="size"||t==="bg-size",Ir=t=>t==="length",Ni=t=>t==="number",ji=t=>t==="family-name",Dr=t=>t==="shadow",Si=()=>{const t=X("color"),e=X("font"),n=X("text"),r=X("font-weight"),s=X("tracking"),o=X("leading"),a=X("breakpoint"),c=X("container"),d=X("spacing"),l=X("radius"),f=X("shadow"),u=X("inset-shadow"),h=X("text-shadow"),m=X("drop-shadow"),p=X("blur"),g=X("perspective"),y=X("aspect"),v=X("ease"),b=X("animate"),j=()=>["auto","avoid","all","avoid-page","page","left","right","column"],S=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...S(),P,R],A=()=>["auto","hidden","clip","visible","scroll"],T=()=>["auto","contain","none"],w=()=>[P,R,d],M=()=>[Fe,"full","auto",...w()],O=()=>[Pe,"none","subgrid",P,R],F=()=>["auto",{span:["full",Pe,P,R]},Pe,P,R],K=()=>[Pe,"auto",P,R],$=()=>["auto","min","max","fr",P,R],I=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],U=()=>["start","end","center","stretch","center-safe","end-safe"],E=()=>["auto",...w()],D=()=>[Fe,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...w()],N=()=>[t,P,R],C=()=>[...S(),Fn,$n,{position:[P,R]}],V=()=>["no-repeat",{repeat:["","x","y","space","round"]}],z=()=>["auto","cover","contain",vi,xi,{size:[P,R]}],q=()=>[Kt,Xe,Ee],G=()=>["","none","full",l,P,R],Y=()=>["",_,Xe,Ee],ce=()=>["solid","dashed","dotted","double"],pe=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],H=()=>[_,Kt,Fn,$n],Re=()=>["","none",p,P,R],Q=()=>["none",_,P,R],ge=()=>["none",_,P,R],$e=()=>[_,P,R],Te=()=>[Fe,"full",...w()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[je],breakpoint:[je],color:[fi],container:[je],"drop-shadow":[je],ease:["in","out","in-out"],font:[gi],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[je],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[je],shadow:[je],spacing:["px",_],text:[je],"text-shadow":[je],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Fe,R,P,y]}],container:["container"],columns:[{columns:[_,R,P,c]}],"break-after":[{"break-after":j()}],"break-before":[{"break-before":j()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:M()}],"inset-x":[{"inset-x":M()}],"inset-y":[{"inset-y":M()}],start:[{start:M()}],end:[{end:M()}],top:[{top:M()}],right:[{right:M()}],bottom:[{bottom:M()}],left:[{left:M()}],visibility:["visible","invisible","collapse"],z:[{z:[Pe,"auto",P,R]}],basis:[{basis:[Fe,"full","auto",c,...w()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[_,Fe,"auto","initial","none",R]}],grow:[{grow:["",_,P,R]}],shrink:[{shrink:["",_,P,R]}],order:[{order:[Pe,"first","last","none",P,R]}],"grid-cols":[{"grid-cols":O()}],"col-start-end":[{col:F()}],"col-start":[{"col-start":K()}],"col-end":[{"col-end":K()}],"grid-rows":[{"grid-rows":O()}],"row-start-end":[{row:F()}],"row-start":[{"row-start":K()}],"row-end":[{"row-end":K()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":$()}],"auto-rows":[{"auto-rows":$()}],gap:[{gap:w()}],"gap-x":[{"gap-x":w()}],"gap-y":[{"gap-y":w()}],"justify-content":[{justify:[...I(),"normal"]}],"justify-items":[{"justify-items":[...U(),"normal"]}],"justify-self":[{"justify-self":["auto",...U()]}],"align-content":[{content:["normal",...I()]}],"align-items":[{items:[...U(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...U(),{baseline:["","last"]}]}],"place-content":[{"place-content":I()}],"place-items":[{"place-items":[...U(),"baseline"]}],"place-self":[{"place-self":["auto",...U()]}],p:[{p:w()}],px:[{px:w()}],py:[{py:w()}],ps:[{ps:w()}],pe:[{pe:w()}],pt:[{pt:w()}],pr:[{pr:w()}],pb:[{pb:w()}],pl:[{pl:w()}],m:[{m:E()}],mx:[{mx:E()}],my:[{my:E()}],ms:[{ms:E()}],me:[{me:E()}],mt:[{mt:E()}],mr:[{mr:E()}],mb:[{mb:E()}],ml:[{ml:E()}],"space-x":[{"space-x":w()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":w()}],"space-y-reverse":["space-y-reverse"],size:[{size:D()}],w:[{w:[c,"screen",...D()]}],"min-w":[{"min-w":[c,"screen","none",...D()]}],"max-w":[{"max-w":[c,"screen","none","prose",{screen:[a]},...D()]}],h:[{h:["screen","lh",...D()]}],"min-h":[{"min-h":["screen","lh","none",...D()]}],"max-h":[{"max-h":["screen","lh",...D()]}],"font-size":[{text:["base",n,Xe,Ee]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,P,Ht]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Kt,R]}],"font-family":[{font:[bi,R,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,P,R]}],"line-clamp":[{"line-clamp":[_,"none",P,Ht]}],leading:[{leading:[o,...w()]}],"list-image":[{"list-image":["none",P,R]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",P,R]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:N()}],"text-color":[{text:N()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ce(),"wavy"]}],"text-decoration-thickness":[{decoration:[_,"from-font","auto",P,Ee]}],"text-decoration-color":[{decoration:N()}],"underline-offset":[{"underline-offset":[_,"auto",P,R]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:w()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",P,R]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",P,R]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:C()}],"bg-repeat":[{bg:V()}],"bg-size":[{bg:z()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Pe,P,R],radial:["",P,R],conic:[Pe,P,R]},wi,yi]}],"bg-color":[{bg:N()}],"gradient-from-pos":[{from:q()}],"gradient-via-pos":[{via:q()}],"gradient-to-pos":[{to:q()}],"gradient-from":[{from:N()}],"gradient-via":[{via:N()}],"gradient-to":[{to:N()}],rounded:[{rounded:G()}],"rounded-s":[{"rounded-s":G()}],"rounded-e":[{"rounded-e":G()}],"rounded-t":[{"rounded-t":G()}],"rounded-r":[{"rounded-r":G()}],"rounded-b":[{"rounded-b":G()}],"rounded-l":[{"rounded-l":G()}],"rounded-ss":[{"rounded-ss":G()}],"rounded-se":[{"rounded-se":G()}],"rounded-ee":[{"rounded-ee":G()}],"rounded-es":[{"rounded-es":G()}],"rounded-tl":[{"rounded-tl":G()}],"rounded-tr":[{"rounded-tr":G()}],"rounded-br":[{"rounded-br":G()}],"rounded-bl":[{"rounded-bl":G()}],"border-w":[{border:Y()}],"border-w-x":[{"border-x":Y()}],"border-w-y":[{"border-y":Y()}],"border-w-s":[{"border-s":Y()}],"border-w-e":[{"border-e":Y()}],"border-w-t":[{"border-t":Y()}],"border-w-r":[{"border-r":Y()}],"border-w-b":[{"border-b":Y()}],"border-w-l":[{"border-l":Y()}],"divide-x":[{"divide-x":Y()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":Y()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ce(),"hidden","none"]}],"divide-style":[{divide:[...ce(),"hidden","none"]}],"border-color":[{border:N()}],"border-color-x":[{"border-x":N()}],"border-color-y":[{"border-y":N()}],"border-color-s":[{"border-s":N()}],"border-color-e":[{"border-e":N()}],"border-color-t":[{"border-t":N()}],"border-color-r":[{"border-r":N()}],"border-color-b":[{"border-b":N()}],"border-color-l":[{"border-l":N()}],"divide-color":[{divide:N()}],"outline-style":[{outline:[...ce(),"none","hidden"]}],"outline-offset":[{"outline-offset":[_,P,R]}],"outline-w":[{outline:["",_,Xe,Ee]}],"outline-color":[{outline:N()}],shadow:[{shadow:["","none",f,pt,mt]}],"shadow-color":[{shadow:N()}],"inset-shadow":[{"inset-shadow":["none",u,pt,mt]}],"inset-shadow-color":[{"inset-shadow":N()}],"ring-w":[{ring:Y()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:N()}],"ring-offset-w":[{"ring-offset":[_,Ee]}],"ring-offset-color":[{"ring-offset":N()}],"inset-ring-w":[{"inset-ring":Y()}],"inset-ring-color":[{"inset-ring":N()}],"text-shadow":[{"text-shadow":["none",h,pt,mt]}],"text-shadow-color":[{"text-shadow":N()}],opacity:[{opacity:[_,P,R]}],"mix-blend":[{"mix-blend":[...pe(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":pe()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[_]}],"mask-image-linear-from-pos":[{"mask-linear-from":H()}],"mask-image-linear-to-pos":[{"mask-linear-to":H()}],"mask-image-linear-from-color":[{"mask-linear-from":N()}],"mask-image-linear-to-color":[{"mask-linear-to":N()}],"mask-image-t-from-pos":[{"mask-t-from":H()}],"mask-image-t-to-pos":[{"mask-t-to":H()}],"mask-image-t-from-color":[{"mask-t-from":N()}],"mask-image-t-to-color":[{"mask-t-to":N()}],"mask-image-r-from-pos":[{"mask-r-from":H()}],"mask-image-r-to-pos":[{"mask-r-to":H()}],"mask-image-r-from-color":[{"mask-r-from":N()}],"mask-image-r-to-color":[{"mask-r-to":N()}],"mask-image-b-from-pos":[{"mask-b-from":H()}],"mask-image-b-to-pos":[{"mask-b-to":H()}],"mask-image-b-from-color":[{"mask-b-from":N()}],"mask-image-b-to-color":[{"mask-b-to":N()}],"mask-image-l-from-pos":[{"mask-l-from":H()}],"mask-image-l-to-pos":[{"mask-l-to":H()}],"mask-image-l-from-color":[{"mask-l-from":N()}],"mask-image-l-to-color":[{"mask-l-to":N()}],"mask-image-x-from-pos":[{"mask-x-from":H()}],"mask-image-x-to-pos":[{"mask-x-to":H()}],"mask-image-x-from-color":[{"mask-x-from":N()}],"mask-image-x-to-color":[{"mask-x-to":N()}],"mask-image-y-from-pos":[{"mask-y-from":H()}],"mask-image-y-to-pos":[{"mask-y-to":H()}],"mask-image-y-from-color":[{"mask-y-from":N()}],"mask-image-y-to-color":[{"mask-y-to":N()}],"mask-image-radial":[{"mask-radial":[P,R]}],"mask-image-radial-from-pos":[{"mask-radial-from":H()}],"mask-image-radial-to-pos":[{"mask-radial-to":H()}],"mask-image-radial-from-color":[{"mask-radial-from":N()}],"mask-image-radial-to-color":[{"mask-radial-to":N()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":S()}],"mask-image-conic-pos":[{"mask-conic":[_]}],"mask-image-conic-from-pos":[{"mask-conic-from":H()}],"mask-image-conic-to-pos":[{"mask-conic-to":H()}],"mask-image-conic-from-color":[{"mask-conic-from":N()}],"mask-image-conic-to-color":[{"mask-conic-to":N()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:C()}],"mask-repeat":[{mask:V()}],"mask-size":[{mask:z()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",P,R]}],filter:[{filter:["","none",P,R]}],blur:[{blur:Re()}],brightness:[{brightness:[_,P,R]}],contrast:[{contrast:[_,P,R]}],"drop-shadow":[{"drop-shadow":["","none",m,pt,mt]}],"drop-shadow-color":[{"drop-shadow":N()}],grayscale:[{grayscale:["",_,P,R]}],"hue-rotate":[{"hue-rotate":[_,P,R]}],invert:[{invert:["",_,P,R]}],saturate:[{saturate:[_,P,R]}],sepia:[{sepia:["",_,P,R]}],"backdrop-filter":[{"backdrop-filter":["","none",P,R]}],"backdrop-blur":[{"backdrop-blur":Re()}],"backdrop-brightness":[{"backdrop-brightness":[_,P,R]}],"backdrop-contrast":[{"backdrop-contrast":[_,P,R]}],"backdrop-grayscale":[{"backdrop-grayscale":["",_,P,R]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[_,P,R]}],"backdrop-invert":[{"backdrop-invert":["",_,P,R]}],"backdrop-opacity":[{"backdrop-opacity":[_,P,R]}],"backdrop-saturate":[{"backdrop-saturate":[_,P,R]}],"backdrop-sepia":[{"backdrop-sepia":["",_,P,R]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":w()}],"border-spacing-x":[{"border-spacing-x":w()}],"border-spacing-y":[{"border-spacing-y":w()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",P,R]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[_,"initial",P,R]}],ease:[{ease:["linear","initial",v,P,R]}],delay:[{delay:[_,P,R]}],animate:[{animate:["none",b,P,R]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,P,R]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:Q()}],"rotate-x":[{"rotate-x":Q()}],"rotate-y":[{"rotate-y":Q()}],"rotate-z":[{"rotate-z":Q()}],scale:[{scale:ge()}],"scale-x":[{"scale-x":ge()}],"scale-y":[{"scale-y":ge()}],"scale-z":[{"scale-z":ge()}],"scale-3d":["scale-3d"],skew:[{skew:$e()}],"skew-x":[{"skew-x":$e()}],"skew-y":[{"skew-y":$e()}],transform:[{transform:[P,R,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Te()}],"translate-x":[{"translate-x":Te()}],"translate-y":[{"translate-y":Te()}],"translate-z":[{"translate-z":Te()}],"translate-none":["translate-none"],accent:[{accent:N()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:N()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",P,R]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":w()}],"scroll-mx":[{"scroll-mx":w()}],"scroll-my":[{"scroll-my":w()}],"scroll-ms":[{"scroll-ms":w()}],"scroll-me":[{"scroll-me":w()}],"scroll-mt":[{"scroll-mt":w()}],"scroll-mr":[{"scroll-mr":w()}],"scroll-mb":[{"scroll-mb":w()}],"scroll-ml":[{"scroll-ml":w()}],"scroll-p":[{"scroll-p":w()}],"scroll-px":[{"scroll-px":w()}],"scroll-py":[{"scroll-py":w()}],"scroll-ps":[{"scroll-ps":w()}],"scroll-pe":[{"scroll-pe":w()}],"scroll-pt":[{"scroll-pt":w()}],"scroll-pr":[{"scroll-pr":w()}],"scroll-pb":[{"scroll-pb":w()}],"scroll-pl":[{"scroll-pl":w()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",P,R]}],fill:[{fill:["none",...N()]}],"stroke-w":[{stroke:[_,Xe,Ee,Ht]}],stroke:[{stroke:["none",...N()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},ki=oi(Si);function Z(...t){return ki(kr(t))}const Ci=Cr("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),W=x.forwardRef(({className:t,variant:e,size:n,asChild:r=!1,...s},o)=>{const a=r?Io:"button";return i.jsx(a,{className:Z(Ci({variant:e,size:n,className:t})),ref:o,...s})});W.displayName="Button";const ne=x.forwardRef(({className:t,...e},n)=>i.jsx("div",{ref:n,className:Z("rounded-lg border bg-card text-card-foreground shadow-sm",t),...e}));ne.displayName="Card";const le=x.forwardRef(({className:t,...e},n)=>i.jsx("div",{ref:n,className:Z("flex flex-col space-y-1.5 p-6",t),...e}));le.displayName="CardHeader";const ie=x.forwardRef(({className:t,...e},n)=>i.jsx("h3",{ref:n,className:Z("text-2xl font-semibold leading-none tracking-tight",t),...e}));ie.displayName="CardTitle";const de=x.forwardRef(({className:t,...e},n)=>i.jsx("p",{ref:n,className:Z("text-sm text-muted-foreground",t),...e}));de.displayName="CardDescription";const re=x.forwardRef(({className:t,...e},n)=>i.jsx("div",{ref:n,className:Z("p-6 pt-0",t),...e}));re.displayName="CardContent";const Ri=x.forwardRef(({className:t,...e},n)=>i.jsx("div",{ref:n,className:Z("flex items-center p-6 pt-0",t),...e}));Ri.displayName="CardFooter";const Nt=x.forwardRef(({className:t,type:e,...n},r)=>i.jsx("input",{type:e,className:Z("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...n}));Nt.displayName="Input";var Pi="Label",_r=x.forwardRef((t,e)=>i.jsx(fe.label,{...t,ref:e,onMouseDown:n=>{var s;n.target.closest("button, input, select, textarea")||((s=t.onMouseDown)==null||s.call(t,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));_r.displayName=Pi;var $r=_r;const Mi=Cr("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),nn=x.forwardRef(({className:t,...e},n)=>i.jsx($r,{ref:n,className:Z(Mi(),t),...e}));nn.displayName=$r.displayName;function Fr(t){const e=t+"CollectionProvider",[n,r]=at(e),[s,o]=n(e,{collectionRef:{current:null},itemMap:new Map}),a=g=>{const{scope:y,children:v}=g,b=Se.useRef(null),j=Se.useRef(new Map).current;return i.jsx(s,{scope:y,itemMap:j,collectionRef:b,children:v})};a.displayName=e;const c=t+"CollectionSlot",d=Xt(c),l=Se.forwardRef((g,y)=>{const{scope:v,children:b}=g,j=o(c,v),S=ve(y,j.collectionRef);return i.jsx(d,{ref:S,children:b})});l.displayName=c;const f=t+"CollectionItemSlot",u="data-radix-collection-item",h=Xt(f),m=Se.forwardRef((g,y)=>{const{scope:v,children:b,...j}=g,S=Se.useRef(null),k=ve(y,S),A=o(f,v);return Se.useEffect(()=>(A.itemMap.set(S,{ref:S,...j}),()=>void A.itemMap.delete(S))),i.jsx(h,{[u]:"",ref:k,children:b})});m.displayName=f;function p(g){const y=o(t+"CollectionConsumer",g);return Se.useCallback(()=>{const b=y.collectionRef.current;if(!b)return[];const j=Array.from(b.querySelectorAll(`[${u}]`));return Array.from(y.itemMap.values()).sort((A,T)=>j.indexOf(A.ref.current)-j.indexOf(T.ref.current))},[y.collectionRef,y.itemMap])}return[{Provider:a,Slot:l,ItemSlot:m},p,r]}var Oi=x.createContext(void 0);function zr(t){const e=x.useContext(Oi);return t||e||"ltr"}const Ai=["top","right","bottom","left"],Me=Math.min,oe=Math.max,jt=Math.round,gt=Math.floor,be=t=>({x:t,y:t}),Ti={left:"right",right:"left",bottom:"top",top:"bottom"},Ei={start:"end",end:"start"};function rn(t,e,n){return oe(t,Me(e,n))}function ke(t,e){return typeof t=="function"?t(e):t}function Ce(t){return t.split("-")[0]}function We(t){return t.split("-")[1]}function gn(t){return t==="x"?"y":"x"}function xn(t){return t==="y"?"height":"width"}function xe(t){return["top","bottom"].includes(Ce(t))?"y":"x"}function yn(t){return gn(xe(t))}function Li(t,e,n){n===void 0&&(n=!1);const r=We(t),s=yn(t),o=xn(s);let a=s==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return e.reference[o]>e.floating[o]&&(a=St(a)),[a,St(a)]}function Ii(t){const e=St(t);return[sn(t),e,sn(e)]}function sn(t){return t.replace(/start|end/g,e=>Ei[e])}function Di(t,e,n){const r=["left","right"],s=["right","left"],o=["top","bottom"],a=["bottom","top"];switch(t){case"top":case"bottom":return n?e?s:r:e?r:s;case"left":case"right":return e?o:a;default:return[]}}function _i(t,e,n,r){const s=We(t);let o=Di(Ce(t),n==="start",r);return s&&(o=o.map(a=>a+"-"+s),e&&(o=o.concat(o.map(sn)))),o}function St(t){return t.replace(/left|right|bottom|top/g,e=>Ti[e])}function $i(t){return{top:0,right:0,bottom:0,left:0,...t}}function Br(t){return typeof t!="number"?$i(t):{top:t,right:t,bottom:t,left:t}}function kt(t){const{x:e,y:n,width:r,height:s}=t;return{width:r,height:s,top:n,left:e,right:e+r,bottom:n+s,x:e,y:n}}function zn(t,e,n){let{reference:r,floating:s}=t;const o=xe(e),a=yn(e),c=xn(a),d=Ce(e),l=o==="y",f=r.x+r.width/2-s.width/2,u=r.y+r.height/2-s.height/2,h=r[c]/2-s[c]/2;let m;switch(d){case"top":m={x:f,y:r.y-s.height};break;case"bottom":m={x:f,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:u};break;case"left":m={x:r.x-s.width,y:u};break;default:m={x:r.x,y:r.y}}switch(We(e)){case"start":m[a]-=h*(n&&l?-1:1);break;case"end":m[a]+=h*(n&&l?-1:1);break}return m}const Fi=async(t,e,n)=>{const{placement:r="bottom",strategy:s="absolute",middleware:o=[],platform:a}=n,c=o.filter(Boolean),d=await(a.isRTL==null?void 0:a.isRTL(e));let l=await a.getElementRects({reference:t,floating:e,strategy:s}),{x:f,y:u}=zn(l,r,d),h=r,m={},p=0;for(let g=0;g<c.length;g++){const{name:y,fn:v}=c[g],{x:b,y:j,data:S,reset:k}=await v({x:f,y:u,initialPlacement:r,placement:h,strategy:s,middlewareData:m,rects:l,platform:a,elements:{reference:t,floating:e}});f=b??f,u=j??u,m={...m,[y]:{...m[y],...S}},k&&p<=50&&(p++,typeof k=="object"&&(k.placement&&(h=k.placement),k.rects&&(l=k.rects===!0?await a.getElementRects({reference:t,floating:e,strategy:s}):k.rects),{x:f,y:u}=zn(l,h,d)),g=-1)}return{x:f,y:u,placement:h,strategy:s,middlewareData:m}};async function et(t,e){var n;e===void 0&&(e={});const{x:r,y:s,platform:o,rects:a,elements:c,strategy:d}=t,{boundary:l="clippingAncestors",rootBoundary:f="viewport",elementContext:u="floating",altBoundary:h=!1,padding:m=0}=ke(e,t),p=Br(m),y=c[h?u==="floating"?"reference":"floating":u],v=kt(await o.getClippingRect({element:(n=await(o.isElement==null?void 0:o.isElement(y)))==null||n?y:y.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(c.floating)),boundary:l,rootBoundary:f,strategy:d})),b=u==="floating"?{x:r,y:s,width:a.floating.width,height:a.floating.height}:a.reference,j=await(o.getOffsetParent==null?void 0:o.getOffsetParent(c.floating)),S=await(o.isElement==null?void 0:o.isElement(j))?await(o.getScale==null?void 0:o.getScale(j))||{x:1,y:1}:{x:1,y:1},k=kt(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:b,offsetParent:j,strategy:d}):b);return{top:(v.top-k.top+p.top)/S.y,bottom:(k.bottom-v.bottom+p.bottom)/S.y,left:(v.left-k.left+p.left)/S.x,right:(k.right-v.right+p.right)/S.x}}const zi=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:r,placement:s,rects:o,platform:a,elements:c,middlewareData:d}=e,{element:l,padding:f=0}=ke(t,e)||{};if(l==null)return{};const u=Br(f),h={x:n,y:r},m=yn(s),p=xn(m),g=await a.getDimensions(l),y=m==="y",v=y?"top":"left",b=y?"bottom":"right",j=y?"clientHeight":"clientWidth",S=o.reference[p]+o.reference[m]-h[m]-o.floating[p],k=h[m]-o.reference[m],A=await(a.getOffsetParent==null?void 0:a.getOffsetParent(l));let T=A?A[j]:0;(!T||!await(a.isElement==null?void 0:a.isElement(A)))&&(T=c.floating[j]||o.floating[p]);const w=S/2-k/2,M=T/2-g[p]/2-1,O=Me(u[v],M),F=Me(u[b],M),K=O,$=T-g[p]-F,I=T/2-g[p]/2+w,U=rn(K,I,$),E=!d.arrow&&We(s)!=null&&I!==U&&o.reference[p]/2-(I<K?O:F)-g[p]/2<0,D=E?I<K?I-K:I-$:0;return{[m]:h[m]+D,data:{[m]:U,centerOffset:I-U-D,...E&&{alignmentOffset:D}},reset:E}}}),Bi=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var n,r;const{placement:s,middlewareData:o,rects:a,initialPlacement:c,platform:d,elements:l}=e,{mainAxis:f=!0,crossAxis:u=!0,fallbackPlacements:h,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:p="none",flipAlignment:g=!0,...y}=ke(t,e);if((n=o.arrow)!=null&&n.alignmentOffset)return{};const v=Ce(s),b=xe(c),j=Ce(c)===c,S=await(d.isRTL==null?void 0:d.isRTL(l.floating)),k=h||(j||!g?[St(c)]:Ii(c)),A=p!=="none";!h&&A&&k.push(..._i(c,g,p,S));const T=[c,...k],w=await et(e,y),M=[];let O=((r=o.flip)==null?void 0:r.overflows)||[];if(f&&M.push(w[v]),u){const I=Li(s,a,S);M.push(w[I[0]],w[I[1]])}if(O=[...O,{placement:s,overflows:M}],!M.every(I=>I<=0)){var F,K;const I=(((F=o.flip)==null?void 0:F.index)||0)+1,U=T[I];if(U&&(!(u==="alignment"?b!==xe(U):!1)||O.every(N=>N.overflows[0]>0&&xe(N.placement)===b)))return{data:{index:I,overflows:O},reset:{placement:U}};let E=(K=O.filter(D=>D.overflows[0]<=0).sort((D,N)=>D.overflows[1]-N.overflows[1])[0])==null?void 0:K.placement;if(!E)switch(m){case"bestFit":{var $;const D=($=O.filter(N=>{if(A){const C=xe(N.placement);return C===b||C==="y"}return!0}).map(N=>[N.placement,N.overflows.filter(C=>C>0).reduce((C,V)=>C+V,0)]).sort((N,C)=>N[1]-C[1])[0])==null?void 0:$[0];D&&(E=D);break}case"initialPlacement":E=c;break}if(s!==E)return{reset:{placement:E}}}return{}}}};function Bn(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function Vn(t){return Ai.some(e=>t[e]>=0)}const Vi=function(t){return t===void 0&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:r="referenceHidden",...s}=ke(t,e);switch(r){case"referenceHidden":{const o=await et(e,{...s,elementContext:"reference"}),a=Bn(o,n.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:Vn(a)}}}case"escaped":{const o=await et(e,{...s,altBoundary:!0}),a=Bn(o,n.floating);return{data:{escapedOffsets:a,escaped:Vn(a)}}}default:return{}}}}};async function Ki(t,e){const{placement:n,platform:r,elements:s}=t,o=await(r.isRTL==null?void 0:r.isRTL(s.floating)),a=Ce(n),c=We(n),d=xe(n)==="y",l=["left","top"].includes(a)?-1:1,f=o&&d?-1:1,u=ke(e,t);let{mainAxis:h,crossAxis:m,alignmentAxis:p}=typeof u=="number"?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return c&&typeof p=="number"&&(m=c==="end"?p*-1:p),d?{x:m*f,y:h*l}:{x:h*l,y:m*f}}const Hi=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var n,r;const{x:s,y:o,placement:a,middlewareData:c}=e,d=await Ki(e,t);return a===((n=c.offset)==null?void 0:n.placement)&&(r=c.arrow)!=null&&r.alignmentOffset?{}:{x:s+d.x,y:o+d.y,data:{...d,placement:a}}}}},Ui=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:r,placement:s}=e,{mainAxis:o=!0,crossAxis:a=!1,limiter:c={fn:y=>{let{x:v,y:b}=y;return{x:v,y:b}}},...d}=ke(t,e),l={x:n,y:r},f=await et(e,d),u=xe(Ce(s)),h=gn(u);let m=l[h],p=l[u];if(o){const y=h==="y"?"top":"left",v=h==="y"?"bottom":"right",b=m+f[y],j=m-f[v];m=rn(b,m,j)}if(a){const y=u==="y"?"top":"left",v=u==="y"?"bottom":"right",b=p+f[y],j=p-f[v];p=rn(b,p,j)}const g=c.fn({...e,[h]:m,[u]:p});return{...g,data:{x:g.x-n,y:g.y-r,enabled:{[h]:o,[u]:a}}}}}},Gi=function(t){return t===void 0&&(t={}),{options:t,fn(e){const{x:n,y:r,placement:s,rects:o,middlewareData:a}=e,{offset:c=0,mainAxis:d=!0,crossAxis:l=!0}=ke(t,e),f={x:n,y:r},u=xe(s),h=gn(u);let m=f[h],p=f[u];const g=ke(c,e),y=typeof g=="number"?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(d){const j=h==="y"?"height":"width",S=o.reference[h]-o.floating[j]+y.mainAxis,k=o.reference[h]+o.reference[j]-y.mainAxis;m<S?m=S:m>k&&(m=k)}if(l){var v,b;const j=h==="y"?"width":"height",S=["top","left"].includes(Ce(s)),k=o.reference[u]-o.floating[j]+(S&&((v=a.offset)==null?void 0:v[u])||0)+(S?0:y.crossAxis),A=o.reference[u]+o.reference[j]+(S?0:((b=a.offset)==null?void 0:b[u])||0)-(S?y.crossAxis:0);p<k?p=k:p>A&&(p=A)}return{[h]:m,[u]:p}}}},Wi=function(t){return t===void 0&&(t={}),{name:"size",options:t,async fn(e){var n,r;const{placement:s,rects:o,platform:a,elements:c}=e,{apply:d=()=>{},...l}=ke(t,e),f=await et(e,l),u=Ce(s),h=We(s),m=xe(s)==="y",{width:p,height:g}=o.floating;let y,v;u==="top"||u==="bottom"?(y=u,v=h===(await(a.isRTL==null?void 0:a.isRTL(c.floating))?"start":"end")?"left":"right"):(v=u,y=h==="end"?"top":"bottom");const b=g-f.top-f.bottom,j=p-f.left-f.right,S=Me(g-f[y],b),k=Me(p-f[v],j),A=!e.middlewareData.shift;let T=S,w=k;if((n=e.middlewareData.shift)!=null&&n.enabled.x&&(w=j),(r=e.middlewareData.shift)!=null&&r.enabled.y&&(T=b),A&&!h){const O=oe(f.left,0),F=oe(f.right,0),K=oe(f.top,0),$=oe(f.bottom,0);m?w=p-2*(O!==0||F!==0?O+F:oe(f.left,f.right)):T=g-2*(K!==0||$!==0?K+$:oe(f.top,f.bottom))}await d({...e,availableWidth:w,availableHeight:T});const M=await a.getDimensions(c.floating);return p!==M.width||g!==M.height?{reset:{rects:!0}}:{}}}};function It(){return typeof window<"u"}function qe(t){return Vr(t)?(t.nodeName||"").toLowerCase():"#document"}function ae(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function Ne(t){var e;return(e=(Vr(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function Vr(t){return It()?t instanceof Node||t instanceof ae(t).Node:!1}function he(t){return It()?t instanceof Element||t instanceof ae(t).Element:!1}function we(t){return It()?t instanceof HTMLElement||t instanceof ae(t).HTMLElement:!1}function Kn(t){return!It()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof ae(t).ShadowRoot}function ct(t){const{overflow:e,overflowX:n,overflowY:r,display:s}=me(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!["inline","contents"].includes(s)}function qi(t){return["table","td","th"].includes(qe(t))}function Dt(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch{return!1}})}function bn(t){const e=vn(),n=he(t)?me(t):t;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!e&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!e&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Yi(t){let e=Oe(t);for(;we(e)&&!He(e);){if(bn(e))return e;if(Dt(e))return null;e=Oe(e)}return null}function vn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function He(t){return["html","body","#document"].includes(qe(t))}function me(t){return ae(t).getComputedStyle(t)}function _t(t){return he(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function Oe(t){if(qe(t)==="html")return t;const e=t.assignedSlot||t.parentNode||Kn(t)&&t.host||Ne(t);return Kn(e)?e.host:e}function Kr(t){const e=Oe(t);return He(e)?t.ownerDocument?t.ownerDocument.body:t.body:we(e)&&ct(e)?e:Kr(e)}function tt(t,e,n){var r;e===void 0&&(e=[]),n===void 0&&(n=!0);const s=Kr(t),o=s===((r=t.ownerDocument)==null?void 0:r.body),a=ae(s);if(o){const c=on(a);return e.concat(a,a.visualViewport||[],ct(s)?s:[],c&&n?tt(c):[])}return e.concat(s,tt(s,[],n))}function on(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Hr(t){const e=me(t);let n=parseFloat(e.width)||0,r=parseFloat(e.height)||0;const s=we(t),o=s?t.offsetWidth:n,a=s?t.offsetHeight:r,c=jt(n)!==o||jt(r)!==a;return c&&(n=o,r=a),{width:n,height:r,$:c}}function wn(t){return he(t)?t:t.contextElement}function Ke(t){const e=wn(t);if(!we(e))return be(1);const n=e.getBoundingClientRect(),{width:r,height:s,$:o}=Hr(e);let a=(o?jt(n.width):n.width)/r,c=(o?jt(n.height):n.height)/s;return(!a||!Number.isFinite(a))&&(a=1),(!c||!Number.isFinite(c))&&(c=1),{x:a,y:c}}const Xi=be(0);function Ur(t){const e=ae(t);return!vn()||!e.visualViewport?Xi:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function Ji(t,e,n){return e===void 0&&(e=!1),!n||e&&n!==ae(t)?!1:e}function Ie(t,e,n,r){e===void 0&&(e=!1),n===void 0&&(n=!1);const s=t.getBoundingClientRect(),o=wn(t);let a=be(1);e&&(r?he(r)&&(a=Ke(r)):a=Ke(t));const c=Ji(o,n,r)?Ur(o):be(0);let d=(s.left+c.x)/a.x,l=(s.top+c.y)/a.y,f=s.width/a.x,u=s.height/a.y;if(o){const h=ae(o),m=r&&he(r)?ae(r):r;let p=h,g=on(p);for(;g&&r&&m!==p;){const y=Ke(g),v=g.getBoundingClientRect(),b=me(g),j=v.left+(g.clientLeft+parseFloat(b.paddingLeft))*y.x,S=v.top+(g.clientTop+parseFloat(b.paddingTop))*y.y;d*=y.x,l*=y.y,f*=y.x,u*=y.y,d+=j,l+=S,p=ae(g),g=on(p)}}return kt({width:f,height:u,x:d,y:l})}function Nn(t,e){const n=_t(t).scrollLeft;return e?e.left+n:Ie(Ne(t)).left+n}function Gr(t,e,n){n===void 0&&(n=!1);const r=t.getBoundingClientRect(),s=r.left+e.scrollLeft-(n?0:Nn(t,r)),o=r.top+e.scrollTop;return{x:s,y:o}}function Qi(t){let{elements:e,rect:n,offsetParent:r,strategy:s}=t;const o=s==="fixed",a=Ne(r),c=e?Dt(e.floating):!1;if(r===a||c&&o)return n;let d={scrollLeft:0,scrollTop:0},l=be(1);const f=be(0),u=we(r);if((u||!u&&!o)&&((qe(r)!=="body"||ct(a))&&(d=_t(r)),we(r))){const m=Ie(r);l=Ke(r),f.x=m.x+r.clientLeft,f.y=m.y+r.clientTop}const h=a&&!u&&!o?Gr(a,d,!0):be(0);return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-d.scrollLeft*l.x+f.x+h.x,y:n.y*l.y-d.scrollTop*l.y+f.y+h.y}}function Zi(t){return Array.from(t.getClientRects())}function ea(t){const e=Ne(t),n=_t(t),r=t.ownerDocument.body,s=oe(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),o=oe(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+Nn(t);const c=-n.scrollTop;return me(r).direction==="rtl"&&(a+=oe(e.clientWidth,r.clientWidth)-s),{width:s,height:o,x:a,y:c}}function ta(t,e){const n=ae(t),r=Ne(t),s=n.visualViewport;let o=r.clientWidth,a=r.clientHeight,c=0,d=0;if(s){o=s.width,a=s.height;const l=vn();(!l||l&&e==="fixed")&&(c=s.offsetLeft,d=s.offsetTop)}return{width:o,height:a,x:c,y:d}}function na(t,e){const n=Ie(t,!0,e==="fixed"),r=n.top+t.clientTop,s=n.left+t.clientLeft,o=we(t)?Ke(t):be(1),a=t.clientWidth*o.x,c=t.clientHeight*o.y,d=s*o.x,l=r*o.y;return{width:a,height:c,x:d,y:l}}function Hn(t,e,n){let r;if(e==="viewport")r=ta(t,n);else if(e==="document")r=ea(Ne(t));else if(he(e))r=na(e,n);else{const s=Ur(t);r={x:e.x-s.x,y:e.y-s.y,width:e.width,height:e.height}}return kt(r)}function Wr(t,e){const n=Oe(t);return n===e||!he(n)||He(n)?!1:me(n).position==="fixed"||Wr(n,e)}function ra(t,e){const n=e.get(t);if(n)return n;let r=tt(t,[],!1).filter(c=>he(c)&&qe(c)!=="body"),s=null;const o=me(t).position==="fixed";let a=o?Oe(t):t;for(;he(a)&&!He(a);){const c=me(a),d=bn(a);!d&&c.position==="fixed"&&(s=null),(o?!d&&!s:!d&&c.position==="static"&&!!s&&["absolute","fixed"].includes(s.position)||ct(a)&&!d&&Wr(t,a))?r=r.filter(f=>f!==a):s=c,a=Oe(a)}return e.set(t,r),r}function sa(t){let{element:e,boundary:n,rootBoundary:r,strategy:s}=t;const a=[...n==="clippingAncestors"?Dt(e)?[]:ra(e,this._c):[].concat(n),r],c=a[0],d=a.reduce((l,f)=>{const u=Hn(e,f,s);return l.top=oe(u.top,l.top),l.right=Me(u.right,l.right),l.bottom=Me(u.bottom,l.bottom),l.left=oe(u.left,l.left),l},Hn(e,c,s));return{width:d.right-d.left,height:d.bottom-d.top,x:d.left,y:d.top}}function oa(t){const{width:e,height:n}=Hr(t);return{width:e,height:n}}function ia(t,e,n){const r=we(e),s=Ne(e),o=n==="fixed",a=Ie(t,!0,o,e);let c={scrollLeft:0,scrollTop:0};const d=be(0);function l(){d.x=Nn(s)}if(r||!r&&!o)if((qe(e)!=="body"||ct(s))&&(c=_t(e)),r){const m=Ie(e,!0,o,e);d.x=m.x+e.clientLeft,d.y=m.y+e.clientTop}else s&&l();o&&!r&&s&&l();const f=s&&!r&&!o?Gr(s,c):be(0),u=a.left+c.scrollLeft-d.x-f.x,h=a.top+c.scrollTop-d.y-f.y;return{x:u,y:h,width:a.width,height:a.height}}function Ut(t){return me(t).position==="static"}function Un(t,e){if(!we(t)||me(t).position==="fixed")return null;if(e)return e(t);let n=t.offsetParent;return Ne(t)===n&&(n=n.ownerDocument.body),n}function qr(t,e){const n=ae(t);if(Dt(t))return n;if(!we(t)){let s=Oe(t);for(;s&&!He(s);){if(he(s)&&!Ut(s))return s;s=Oe(s)}return n}let r=Un(t,e);for(;r&&qi(r)&&Ut(r);)r=Un(r,e);return r&&He(r)&&Ut(r)&&!bn(r)?n:r||Yi(t)||n}const aa=async function(t){const e=this.getOffsetParent||qr,n=this.getDimensions,r=await n(t.floating);return{reference:ia(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function ca(t){return me(t).direction==="rtl"}const la={convertOffsetParentRelativeRectToViewportRelativeRect:Qi,getDocumentElement:Ne,getClippingRect:sa,getOffsetParent:qr,getElementRects:aa,getClientRects:Zi,getDimensions:oa,getScale:Ke,isElement:he,isRTL:ca};function Yr(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function da(t,e){let n=null,r;const s=Ne(t);function o(){var c;clearTimeout(r),(c=n)==null||c.disconnect(),n=null}function a(c,d){c===void 0&&(c=!1),d===void 0&&(d=1),o();const l=t.getBoundingClientRect(),{left:f,top:u,width:h,height:m}=l;if(c||e(),!h||!m)return;const p=gt(u),g=gt(s.clientWidth-(f+h)),y=gt(s.clientHeight-(u+m)),v=gt(f),j={rootMargin:-p+"px "+-g+"px "+-y+"px "+-v+"px",threshold:oe(0,Me(1,d))||1};let S=!0;function k(A){const T=A[0].intersectionRatio;if(T!==d){if(!S)return a();T?a(!1,T):r=setTimeout(()=>{a(!1,1e-7)},1e3)}T===1&&!Yr(l,t.getBoundingClientRect())&&a(),S=!1}try{n=new IntersectionObserver(k,{...j,root:s.ownerDocument})}catch{n=new IntersectionObserver(k,j)}n.observe(t)}return a(!0),o}function ua(t,e,n,r){r===void 0&&(r={});const{ancestorScroll:s=!0,ancestorResize:o=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:d=!1}=r,l=wn(t),f=s||o?[...l?tt(l):[],...tt(e)]:[];f.forEach(v=>{s&&v.addEventListener("scroll",n,{passive:!0}),o&&v.addEventListener("resize",n)});const u=l&&c?da(l,n):null;let h=-1,m=null;a&&(m=new ResizeObserver(v=>{let[b]=v;b&&b.target===l&&m&&(m.unobserve(e),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var j;(j=m)==null||j.observe(e)})),n()}),l&&!d&&m.observe(l),m.observe(e));let p,g=d?Ie(t):null;d&&y();function y(){const v=Ie(t);g&&!Yr(g,v)&&n(),g=v,p=requestAnimationFrame(y)}return n(),()=>{var v;f.forEach(b=>{s&&b.removeEventListener("scroll",n),o&&b.removeEventListener("resize",n)}),u==null||u(),(v=m)==null||v.disconnect(),m=null,d&&cancelAnimationFrame(p)}}const fa=Hi,ha=Ui,ma=Bi,pa=Wi,ga=Vi,Gn=zi,xa=Gi,ya=(t,e,n)=>{const r=new Map,s={platform:la,...n},o={...s.platform,_c:r};return Fi(t,e,{...s,platform:o})};var ba=typeof document<"u",va=function(){},yt=ba?x.useLayoutEffect:va;function Ct(t,e){if(t===e)return!0;if(typeof t!=typeof e)return!1;if(typeof t=="function"&&t.toString()===e.toString())return!0;let n,r,s;if(t&&e&&typeof t=="object"){if(Array.isArray(t)){if(n=t.length,n!==e.length)return!1;for(r=n;r--!==0;)if(!Ct(t[r],e[r]))return!1;return!0}if(s=Object.keys(t),n=s.length,n!==Object.keys(e).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(e,s[r]))return!1;for(r=n;r--!==0;){const o=s[r];if(!(o==="_owner"&&t.$$typeof)&&!Ct(t[o],e[o]))return!1}return!0}return t!==t&&e!==e}function Xr(t){return typeof window>"u"?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function Wn(t,e){const n=Xr(t);return Math.round(e*n)/n}function Gt(t){const e=x.useRef(t);return yt(()=>{e.current=t}),e}function wa(t){t===void 0&&(t={});const{placement:e="bottom",strategy:n="absolute",middleware:r=[],platform:s,elements:{reference:o,floating:a}={},transform:c=!0,whileElementsMounted:d,open:l}=t,[f,u]=x.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[h,m]=x.useState(r);Ct(h,r)||m(r);const[p,g]=x.useState(null),[y,v]=x.useState(null),b=x.useCallback(N=>{N!==A.current&&(A.current=N,g(N))},[]),j=x.useCallback(N=>{N!==T.current&&(T.current=N,v(N))},[]),S=o||p,k=a||y,A=x.useRef(null),T=x.useRef(null),w=x.useRef(f),M=d!=null,O=Gt(d),F=Gt(s),K=Gt(l),$=x.useCallback(()=>{if(!A.current||!T.current)return;const N={placement:e,strategy:n,middleware:h};F.current&&(N.platform=F.current),ya(A.current,T.current,N).then(C=>{const V={...C,isPositioned:K.current!==!1};I.current&&!Ct(w.current,V)&&(w.current=V,Mo.flushSync(()=>{u(V)}))})},[h,e,n,F,K]);yt(()=>{l===!1&&w.current.isPositioned&&(w.current.isPositioned=!1,u(N=>({...N,isPositioned:!1})))},[l]);const I=x.useRef(!1);yt(()=>(I.current=!0,()=>{I.current=!1}),[]),yt(()=>{if(S&&(A.current=S),k&&(T.current=k),S&&k){if(O.current)return O.current(S,k,$);$()}},[S,k,$,O,M]);const U=x.useMemo(()=>({reference:A,floating:T,setReference:b,setFloating:j}),[b,j]),E=x.useMemo(()=>({reference:S,floating:k}),[S,k]),D=x.useMemo(()=>{const N={position:n,left:0,top:0};if(!E.floating)return N;const C=Wn(E.floating,f.x),V=Wn(E.floating,f.y);return c?{...N,transform:"translate("+C+"px, "+V+"px)",...Xr(E.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:C,top:V}},[n,c,E.floating,f.x,f.y]);return x.useMemo(()=>({...f,update:$,refs:U,elements:E,floatingStyles:D}),[f,$,U,E,D])}const Na=t=>{function e(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:t,fn(n){const{element:r,padding:s}=typeof t=="function"?t(n):t;return r&&e(r)?r.current!=null?Gn({element:r.current,padding:s}).fn(n):{}:r?Gn({element:r,padding:s}).fn(n):{}}}},ja=(t,e)=>({...fa(t),options:[t,e]}),Sa=(t,e)=>({...ha(t),options:[t,e]}),ka=(t,e)=>({...xa(t),options:[t,e]}),Ca=(t,e)=>({...ma(t),options:[t,e]}),Ra=(t,e)=>({...pa(t),options:[t,e]}),Pa=(t,e)=>({...ga(t),options:[t,e]}),Ma=(t,e)=>({...Na(t),options:[t,e]});var Oa="Arrow",Jr=x.forwardRef((t,e)=>{const{children:n,width:r=10,height:s=5,...o}=t;return i.jsx(fe.svg,{...o,ref:e,width:r,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?n:i.jsx("polygon",{points:"0,0 30,0 15,10"})})});Jr.displayName=Oa;var Aa=Jr;function Ta(t){const[e,n]=x.useState(void 0);return Jt(()=>{if(t){n({width:t.offsetWidth,height:t.offsetHeight});const r=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const o=s[0];let a,c;if("borderBoxSize"in o){const d=o.borderBoxSize,l=Array.isArray(d)?d[0]:d;a=l.inlineSize,c=l.blockSize}else a=t.offsetWidth,c=t.offsetHeight;n({width:a,height:c})});return r.observe(t,{box:"border-box"}),()=>r.unobserve(t)}else n(void 0)},[t]),e}var jn="Popper",[Qr,Zr]=at(jn),[Ea,es]=Qr(jn),ts=t=>{const{__scopePopper:e,children:n}=t,[r,s]=x.useState(null);return i.jsx(Ea,{scope:e,anchor:r,onAnchorChange:s,children:n})};ts.displayName=jn;var ns="PopperAnchor",rs=x.forwardRef((t,e)=>{const{__scopePopper:n,virtualRef:r,...s}=t,o=es(ns,n),a=x.useRef(null),c=ve(e,a);return x.useEffect(()=>{o.onAnchorChange((r==null?void 0:r.current)||a.current)}),r?null:i.jsx(fe.div,{...s,ref:c})});rs.displayName=ns;var Sn="PopperContent",[La,Ia]=Qr(Sn),ss=x.forwardRef((t,e)=>{var H,Re,Q,ge,$e,Te;const{__scopePopper:n,side:r="bottom",sideOffset:s=0,align:o="center",alignOffset:a=0,arrowPadding:c=0,avoidCollisions:d=!0,collisionBoundary:l=[],collisionPadding:f=0,sticky:u="partial",hideWhenDetached:h=!1,updatePositionStrategy:m="optimized",onPlaced:p,...g}=t,y=es(Sn,n),[v,b]=x.useState(null),j=ve(e,Ye=>b(Ye)),[S,k]=x.useState(null),A=Ta(S),T=(A==null?void 0:A.width)??0,w=(A==null?void 0:A.height)??0,M=r+(o!=="center"?"-"+o:""),O=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},F=Array.isArray(l)?l:[l],K=F.length>0,$={padding:O,boundary:F.filter(_a),altBoundary:K},{refs:I,floatingStyles:U,placement:E,isPositioned:D,middlewareData:N}=wa({strategy:"fixed",placement:M,whileElementsMounted:(...Ye)=>ua(...Ye,{animationFrame:m==="always"}),elements:{reference:y.anchor},middleware:[ja({mainAxis:s+w,alignmentAxis:a}),d&&Sa({mainAxis:!0,crossAxis:!1,limiter:u==="partial"?ka():void 0,...$}),d&&Ca({...$}),Ra({...$,apply:({elements:Ye,rects:Tn,availableWidth:jo,availableHeight:So})=>{const{width:ko,height:Co}=Tn.reference,ft=Ye.floating.style;ft.setProperty("--radix-popper-available-width",`${jo}px`),ft.setProperty("--radix-popper-available-height",`${So}px`),ft.setProperty("--radix-popper-anchor-width",`${ko}px`),ft.setProperty("--radix-popper-anchor-height",`${Co}px`)}}),S&&Ma({element:S,padding:c}),$a({arrowWidth:T,arrowHeight:w}),h&&Pa({strategy:"referenceHidden",...$})]}),[C,V]=as(E),z=Et(p);Jt(()=>{D&&(z==null||z())},[D,z]);const q=(H=N.arrow)==null?void 0:H.x,G=(Re=N.arrow)==null?void 0:Re.y,Y=((Q=N.arrow)==null?void 0:Q.centerOffset)!==0,[ce,pe]=x.useState();return Jt(()=>{v&&pe(window.getComputedStyle(v).zIndex)},[v]),i.jsx("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:D?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ce,"--radix-popper-transform-origin":[(ge=N.transformOrigin)==null?void 0:ge.x,($e=N.transformOrigin)==null?void 0:$e.y].join(" "),...((Te=N.hide)==null?void 0:Te.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:i.jsx(La,{scope:n,placedSide:C,onArrowChange:k,arrowX:q,arrowY:G,shouldHideArrow:Y,children:i.jsx(fe.div,{"data-side":C,"data-align":V,...g,ref:j,style:{...g.style,animation:D?void 0:"none"}})})})});ss.displayName=Sn;var os="PopperArrow",Da={top:"bottom",right:"left",bottom:"top",left:"right"},is=x.forwardRef(function(e,n){const{__scopePopper:r,...s}=e,o=Ia(os,r),a=Da[o.placedSide];return i.jsx("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:i.jsx(Aa,{...s,ref:n,style:{...s.style,display:"block"}})})});is.displayName=os;function _a(t){return t!==null}var $a=t=>({name:"transformOrigin",options:t,fn(e){var y,v,b;const{placement:n,rects:r,middlewareData:s}=e,a=((y=s.arrow)==null?void 0:y.centerOffset)!==0,c=a?0:t.arrowWidth,d=a?0:t.arrowHeight,[l,f]=as(n),u={start:"0%",center:"50%",end:"100%"}[f],h=(((v=s.arrow)==null?void 0:v.x)??0)+c/2,m=(((b=s.arrow)==null?void 0:b.y)??0)+d/2;let p="",g="";return l==="bottom"?(p=a?u:`${h}px`,g=`${-d}px`):l==="top"?(p=a?u:`${h}px`,g=`${r.floating.height+d}px`):l==="right"?(p=`${-d}px`,g=a?u:`${m}px`):l==="left"&&(p=`${r.floating.width+d}px`,g=a?u:`${m}px`),{data:{x:p,y:g}}}});function as(t){const[e,n="center"]=t.split("-");return[e,n]}var Fa=ts,za=rs,Ba=ss,Va=is,Wt="rovingFocusGroup.onEntryFocus",Ka={bubbles:!1,cancelable:!0},lt="RovingFocusGroup",[an,cs,Ha]=Fr(lt),[Ua,ls]=at(lt,[Ha]),[Ga,Wa]=Ua(lt),ds=x.forwardRef((t,e)=>i.jsx(an.Provider,{scope:t.__scopeRovingFocusGroup,children:i.jsx(an.Slot,{scope:t.__scopeRovingFocusGroup,children:i.jsx(qa,{...t,ref:e})})}));ds.displayName=lt;var qa=x.forwardRef((t,e)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:s=!1,dir:o,currentTabStopId:a,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:d,onEntryFocus:l,preventScrollOnEntryFocus:f=!1,...u}=t,h=x.useRef(null),m=ve(e,h),p=zr(o),[g,y]=Nr({prop:a,defaultProp:c??null,onChange:d,caller:lt}),[v,b]=x.useState(!1),j=Et(l),S=cs(n),k=x.useRef(!1),[A,T]=x.useState(0);return x.useEffect(()=>{const w=h.current;if(w)return w.addEventListener(Wt,j),()=>w.removeEventListener(Wt,j)},[j]),i.jsx(Ga,{scope:n,orientation:r,dir:p,loop:s,currentTabStopId:g,onItemFocus:x.useCallback(w=>y(w),[y]),onItemShiftTab:x.useCallback(()=>b(!0),[]),onFocusableItemAdd:x.useCallback(()=>T(w=>w+1),[]),onFocusableItemRemove:x.useCallback(()=>T(w=>w-1),[]),children:i.jsx(fe.div,{tabIndex:v||A===0?-1:0,"data-orientation":r,...u,ref:m,style:{outline:"none",...t.style},onMouseDown:B(t.onMouseDown,()=>{k.current=!0}),onFocus:B(t.onFocus,w=>{const M=!k.current;if(w.target===w.currentTarget&&M&&!v){const O=new CustomEvent(Wt,Ka);if(w.currentTarget.dispatchEvent(O),!O.defaultPrevented){const F=S().filter(E=>E.focusable),K=F.find(E=>E.active),$=F.find(E=>E.id===g),U=[K,$,...F].filter(Boolean).map(E=>E.ref.current);hs(U,f)}}k.current=!1}),onBlur:B(t.onBlur,()=>b(!1))})})}),us="RovingFocusGroupItem",fs=x.forwardRef((t,e)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:s=!1,tabStopId:o,children:a,...c}=t,d=Qt(),l=o||d,f=Wa(us,n),u=f.currentTabStopId===l,h=cs(n),{onFocusableItemAdd:m,onFocusableItemRemove:p,currentTabStopId:g}=f;return x.useEffect(()=>{if(r)return m(),()=>p()},[r,m,p]),i.jsx(an.ItemSlot,{scope:n,id:l,focusable:r,active:s,children:i.jsx(fe.span,{tabIndex:u?0:-1,"data-orientation":f.orientation,...c,ref:e,onMouseDown:B(t.onMouseDown,y=>{r?f.onItemFocus(l):y.preventDefault()}),onFocus:B(t.onFocus,()=>f.onItemFocus(l)),onKeyDown:B(t.onKeyDown,y=>{if(y.key==="Tab"&&y.shiftKey){f.onItemShiftTab();return}if(y.target!==y.currentTarget)return;const v=Ja(y,f.orientation,f.dir);if(v!==void 0){if(y.metaKey||y.ctrlKey||y.altKey||y.shiftKey)return;y.preventDefault();let j=h().filter(S=>S.focusable).map(S=>S.ref.current);if(v==="last")j.reverse();else if(v==="prev"||v==="next"){v==="prev"&&j.reverse();const S=j.indexOf(y.currentTarget);j=f.loop?Qa(j,S+1):j.slice(S+1)}setTimeout(()=>hs(j))}}),children:typeof a=="function"?a({isCurrentTabStop:u,hasTabStop:g!=null}):a})})});fs.displayName=us;var Ya={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Xa(t,e){return e!=="rtl"?t:t==="ArrowLeft"?"ArrowRight":t==="ArrowRight"?"ArrowLeft":t}function Ja(t,e,n){const r=Xa(t.key,n);if(!(e==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(e==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return Ya[r]}function hs(t,e=!1){const n=document.activeElement;for(const r of t)if(r===n||(r.focus({preventScroll:e}),document.activeElement!==n))return}function Qa(t,e){return t.map((n,r)=>t[(e+r)%t.length])}var Za=ds,ec=fs,cn=["Enter"," "],tc=["ArrowDown","PageUp","Home"],ms=["ArrowUp","PageDown","End"],nc=[...tc,...ms],rc={ltr:[...cn,"ArrowRight"],rtl:[...cn,"ArrowLeft"]},sc={ltr:["ArrowLeft"],rtl:["ArrowRight"]},dt="Menu",[nt,oc,ic]=Fr(dt),[De,ps]=at(dt,[ic,Zr,ls]),$t=Zr(),gs=ls(),[ac,_e]=De(dt),[cc,ut]=De(dt),xs=t=>{const{__scopeMenu:e,open:n=!1,children:r,dir:s,onOpenChange:o,modal:a=!0}=t,c=$t(e),[d,l]=x.useState(null),f=x.useRef(!1),u=Et(o),h=zr(s);return x.useEffect(()=>{const m=()=>{f.current=!0,document.addEventListener("pointerdown",p,{capture:!0,once:!0}),document.addEventListener("pointermove",p,{capture:!0,once:!0})},p=()=>f.current=!1;return document.addEventListener("keydown",m,{capture:!0}),()=>{document.removeEventListener("keydown",m,{capture:!0}),document.removeEventListener("pointerdown",p,{capture:!0}),document.removeEventListener("pointermove",p,{capture:!0})}},[]),i.jsx(Fa,{...c,children:i.jsx(ac,{scope:e,open:n,onOpenChange:u,content:d,onContentChange:l,children:i.jsx(cc,{scope:e,onClose:x.useCallback(()=>u(!1),[u]),isUsingKeyboardRef:f,dir:h,modal:a,children:r})})})};xs.displayName=dt;var lc="MenuAnchor",kn=x.forwardRef((t,e)=>{const{__scopeMenu:n,...r}=t,s=$t(n);return i.jsx(za,{...s,...r,ref:e})});kn.displayName=lc;var Cn="MenuPortal",[dc,ys]=De(Cn,{forceMount:void 0}),bs=t=>{const{__scopeMenu:e,forceMount:n,children:r,container:s}=t,o=_e(Cn,e);return i.jsx(dc,{scope:e,forceMount:n,children:i.jsx(Lt,{present:n||o.open,children:i.jsx(Do,{asChild:!0,container:s,children:r})})})};bs.displayName=Cn;var ue="MenuContent",[uc,Rn]=De(ue),vs=x.forwardRef((t,e)=>{const n=ys(ue,t.__scopeMenu),{forceMount:r=n.forceMount,...s}=t,o=_e(ue,t.__scopeMenu),a=ut(ue,t.__scopeMenu);return i.jsx(nt.Provider,{scope:t.__scopeMenu,children:i.jsx(Lt,{present:r||o.open,children:i.jsx(nt.Slot,{scope:t.__scopeMenu,children:a.modal?i.jsx(fc,{...s,ref:e}):i.jsx(hc,{...s,ref:e})})})})}),fc=x.forwardRef((t,e)=>{const n=_e(ue,t.__scopeMenu),r=x.useRef(null),s=ve(e,r);return x.useEffect(()=>{const o=r.current;if(o)return Bo(o)},[]),i.jsx(Pn,{...t,ref:s,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:B(t.onFocusOutside,o=>o.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),hc=x.forwardRef((t,e)=>{const n=_e(ue,t.__scopeMenu);return i.jsx(Pn,{...t,ref:e,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),mc=Xt("MenuContent.ScrollLock"),Pn=x.forwardRef((t,e)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:s,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:c,onEntryFocus:d,onEscapeKeyDown:l,onPointerDownOutside:f,onFocusOutside:u,onInteractOutside:h,onDismiss:m,disableOutsideScroll:p,...g}=t,y=_e(ue,n),v=ut(ue,n),b=$t(n),j=gs(n),S=oc(n),[k,A]=x.useState(null),T=x.useRef(null),w=ve(e,T,y.onContentChange),M=x.useRef(0),O=x.useRef(""),F=x.useRef(0),K=x.useRef(null),$=x.useRef("right"),I=x.useRef(0),U=p?$o:x.Fragment,E=p?{as:mc,allowPinchZoom:!0}:void 0,D=C=>{var H,Re;const V=O.current+C,z=S().filter(Q=>!Q.disabled),q=document.activeElement,G=(H=z.find(Q=>Q.ref.current===q))==null?void 0:H.textValue,Y=z.map(Q=>Q.textValue),ce=Cc(Y,V,G),pe=(Re=z.find(Q=>Q.textValue===ce))==null?void 0:Re.ref.current;(function Q(ge){O.current=ge,window.clearTimeout(M.current),ge!==""&&(M.current=window.setTimeout(()=>Q(""),1e3))})(V),pe&&setTimeout(()=>pe.focus())};x.useEffect(()=>()=>window.clearTimeout(M.current),[]),_o();const N=x.useCallback(C=>{var z,q;return $.current===((z=K.current)==null?void 0:z.side)&&Pc(C,(q=K.current)==null?void 0:q.area)},[]);return i.jsx(uc,{scope:n,searchRef:O,onItemEnter:x.useCallback(C=>{N(C)&&C.preventDefault()},[N]),onItemLeave:x.useCallback(C=>{var V;N(C)||((V=T.current)==null||V.focus(),A(null))},[N]),onTriggerLeave:x.useCallback(C=>{N(C)&&C.preventDefault()},[N]),pointerGraceTimerRef:F,onPointerGraceIntentChange:x.useCallback(C=>{K.current=C},[]),children:i.jsx(U,{...E,children:i.jsx(Fo,{asChild:!0,trapped:s,onMountAutoFocus:B(o,C=>{var V;C.preventDefault(),(V=T.current)==null||V.focus({preventScroll:!0})}),onUnmountAutoFocus:a,children:i.jsx(zo,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:l,onPointerDownOutside:f,onFocusOutside:u,onInteractOutside:h,onDismiss:m,children:i.jsx(Za,{asChild:!0,...j,dir:v.dir,orientation:"vertical",loop:r,currentTabStopId:k,onCurrentTabStopIdChange:A,onEntryFocus:B(d,C=>{v.isUsingKeyboardRef.current||C.preventDefault()}),preventScrollOnEntryFocus:!0,children:i.jsx(Ba,{role:"menu","aria-orientation":"vertical","data-state":Ds(y.open),"data-radix-menu-content":"",dir:v.dir,...b,...g,ref:w,style:{outline:"none",...g.style},onKeyDown:B(g.onKeyDown,C=>{const z=C.target.closest("[data-radix-menu-content]")===C.currentTarget,q=C.ctrlKey||C.altKey||C.metaKey,G=C.key.length===1;z&&(C.key==="Tab"&&C.preventDefault(),!q&&G&&D(C.key));const Y=T.current;if(C.target!==Y||!nc.includes(C.key))return;C.preventDefault();const pe=S().filter(H=>!H.disabled).map(H=>H.ref.current);ms.includes(C.key)&&pe.reverse(),Sc(pe)}),onBlur:B(t.onBlur,C=>{C.currentTarget.contains(C.target)||(window.clearTimeout(M.current),O.current="")}),onPointerMove:B(t.onPointerMove,rt(C=>{const V=C.target,z=I.current!==C.clientX;if(C.currentTarget.contains(V)&&z){const q=C.clientX>I.current?"right":"left";$.current=q,I.current=C.clientX}}))})})})})})})});vs.displayName=ue;var pc="MenuGroup",Mn=x.forwardRef((t,e)=>{const{__scopeMenu:n,...r}=t;return i.jsx(fe.div,{role:"group",...r,ref:e})});Mn.displayName=pc;var gc="MenuLabel",ws=x.forwardRef((t,e)=>{const{__scopeMenu:n,...r}=t;return i.jsx(fe.div,{...r,ref:e})});ws.displayName=gc;var Rt="MenuItem",qn="menu.itemSelect",Ft=x.forwardRef((t,e)=>{const{disabled:n=!1,onSelect:r,...s}=t,o=x.useRef(null),a=ut(Rt,t.__scopeMenu),c=Rn(Rt,t.__scopeMenu),d=ve(e,o),l=x.useRef(!1),f=()=>{const u=o.current;if(!n&&u){const h=new CustomEvent(qn,{bubbles:!0,cancelable:!0});u.addEventListener(qn,m=>r==null?void 0:r(m),{once:!0}),Vo(u,h),h.defaultPrevented?l.current=!1:a.onClose()}};return i.jsx(Ns,{...s,ref:d,disabled:n,onClick:B(t.onClick,f),onPointerDown:u=>{var h;(h=t.onPointerDown)==null||h.call(t,u),l.current=!0},onPointerUp:B(t.onPointerUp,u=>{var h;l.current||(h=u.currentTarget)==null||h.click()}),onKeyDown:B(t.onKeyDown,u=>{const h=c.searchRef.current!=="";n||h&&u.key===" "||cn.includes(u.key)&&(u.currentTarget.click(),u.preventDefault())})})});Ft.displayName=Rt;var Ns=x.forwardRef((t,e)=>{const{__scopeMenu:n,disabled:r=!1,textValue:s,...o}=t,a=Rn(Rt,n),c=gs(n),d=x.useRef(null),l=ve(e,d),[f,u]=x.useState(!1),[h,m]=x.useState("");return x.useEffect(()=>{const p=d.current;p&&m((p.textContent??"").trim())},[o.children]),i.jsx(nt.ItemSlot,{scope:n,disabled:r,textValue:s??h,children:i.jsx(ec,{asChild:!0,...c,focusable:!r,children:i.jsx(fe.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...o,ref:l,onPointerMove:B(t.onPointerMove,rt(p=>{r?a.onItemLeave(p):(a.onItemEnter(p),p.defaultPrevented||p.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:B(t.onPointerLeave,rt(p=>a.onItemLeave(p))),onFocus:B(t.onFocus,()=>u(!0)),onBlur:B(t.onBlur,()=>u(!1))})})})}),xc="MenuCheckboxItem",js=x.forwardRef((t,e)=>{const{checked:n=!1,onCheckedChange:r,...s}=t;return i.jsx(Ps,{scope:t.__scopeMenu,checked:n,children:i.jsx(Ft,{role:"menuitemcheckbox","aria-checked":Pt(n)?"mixed":n,...s,ref:e,"data-state":An(n),onSelect:B(s.onSelect,()=>r==null?void 0:r(Pt(n)?!0:!n),{checkForDefaultPrevented:!1})})})});js.displayName=xc;var Ss="MenuRadioGroup",[yc,bc]=De(Ss,{value:void 0,onValueChange:()=>{}}),ks=x.forwardRef((t,e)=>{const{value:n,onValueChange:r,...s}=t,o=Et(r);return i.jsx(yc,{scope:t.__scopeMenu,value:n,onValueChange:o,children:i.jsx(Mn,{...s,ref:e})})});ks.displayName=Ss;var Cs="MenuRadioItem",Rs=x.forwardRef((t,e)=>{const{value:n,...r}=t,s=bc(Cs,t.__scopeMenu),o=n===s.value;return i.jsx(Ps,{scope:t.__scopeMenu,checked:o,children:i.jsx(Ft,{role:"menuitemradio","aria-checked":o,...r,ref:e,"data-state":An(o),onSelect:B(r.onSelect,()=>{var a;return(a=s.onValueChange)==null?void 0:a.call(s,n)},{checkForDefaultPrevented:!1})})})});Rs.displayName=Cs;var On="MenuItemIndicator",[Ps,vc]=De(On,{checked:!1}),Ms=x.forwardRef((t,e)=>{const{__scopeMenu:n,forceMount:r,...s}=t,o=vc(On,n);return i.jsx(Lt,{present:r||Pt(o.checked)||o.checked===!0,children:i.jsx(fe.span,{...s,ref:e,"data-state":An(o.checked)})})});Ms.displayName=On;var wc="MenuSeparator",Os=x.forwardRef((t,e)=>{const{__scopeMenu:n,...r}=t;return i.jsx(fe.div,{role:"separator","aria-orientation":"horizontal",...r,ref:e})});Os.displayName=wc;var Nc="MenuArrow",As=x.forwardRef((t,e)=>{const{__scopeMenu:n,...r}=t,s=$t(n);return i.jsx(Va,{...s,...r,ref:e})});As.displayName=Nc;var jc="MenuSub",[xf,Ts]=De(jc),Qe="MenuSubTrigger",Es=x.forwardRef((t,e)=>{const n=_e(Qe,t.__scopeMenu),r=ut(Qe,t.__scopeMenu),s=Ts(Qe,t.__scopeMenu),o=Rn(Qe,t.__scopeMenu),a=x.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=o,l={__scopeMenu:t.__scopeMenu},f=x.useCallback(()=>{a.current&&window.clearTimeout(a.current),a.current=null},[]);return x.useEffect(()=>f,[f]),x.useEffect(()=>{const u=c.current;return()=>{window.clearTimeout(u),d(null)}},[c,d]),i.jsx(kn,{asChild:!0,...l,children:i.jsx(Ns,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":s.contentId,"data-state":Ds(n.open),...t,ref:jr(e,s.onTriggerChange),onClick:u=>{var h;(h=t.onClick)==null||h.call(t,u),!(t.disabled||u.defaultPrevented)&&(u.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:B(t.onPointerMove,rt(u=>{o.onItemEnter(u),!u.defaultPrevented&&!t.disabled&&!n.open&&!a.current&&(o.onPointerGraceIntentChange(null),a.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:B(t.onPointerLeave,rt(u=>{var m,p;f();const h=(m=n.content)==null?void 0:m.getBoundingClientRect();if(h){const g=(p=n.content)==null?void 0:p.dataset.side,y=g==="right",v=y?-5:5,b=h[y?"left":"right"],j=h[y?"right":"left"];o.onPointerGraceIntentChange({area:[{x:u.clientX+v,y:u.clientY},{x:b,y:h.top},{x:j,y:h.top},{x:j,y:h.bottom},{x:b,y:h.bottom}],side:g}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>o.onPointerGraceIntentChange(null),300)}else{if(o.onTriggerLeave(u),u.defaultPrevented)return;o.onPointerGraceIntentChange(null)}})),onKeyDown:B(t.onKeyDown,u=>{var m;const h=o.searchRef.current!=="";t.disabled||h&&u.key===" "||rc[r.dir].includes(u.key)&&(n.onOpenChange(!0),(m=n.content)==null||m.focus(),u.preventDefault())})})})});Es.displayName=Qe;var Ls="MenuSubContent",Is=x.forwardRef((t,e)=>{const n=ys(ue,t.__scopeMenu),{forceMount:r=n.forceMount,...s}=t,o=_e(ue,t.__scopeMenu),a=ut(ue,t.__scopeMenu),c=Ts(Ls,t.__scopeMenu),d=x.useRef(null),l=ve(e,d);return i.jsx(nt.Provider,{scope:t.__scopeMenu,children:i.jsx(Lt,{present:r||o.open,children:i.jsx(nt.Slot,{scope:t.__scopeMenu,children:i.jsx(Pn,{id:c.contentId,"aria-labelledby":c.triggerId,...s,ref:l,align:"start",side:a.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:f=>{var u;a.isUsingKeyboardRef.current&&((u=d.current)==null||u.focus()),f.preventDefault()},onCloseAutoFocus:f=>f.preventDefault(),onFocusOutside:B(t.onFocusOutside,f=>{f.target!==c.trigger&&o.onOpenChange(!1)}),onEscapeKeyDown:B(t.onEscapeKeyDown,f=>{a.onClose(),f.preventDefault()}),onKeyDown:B(t.onKeyDown,f=>{var m;const u=f.currentTarget.contains(f.target),h=sc[a.dir].includes(f.key);u&&h&&(o.onOpenChange(!1),(m=c.trigger)==null||m.focus(),f.preventDefault())})})})})})});Is.displayName=Ls;function Ds(t){return t?"open":"closed"}function Pt(t){return t==="indeterminate"}function An(t){return Pt(t)?"indeterminate":t?"checked":"unchecked"}function Sc(t){const e=document.activeElement;for(const n of t)if(n===e||(n.focus(),document.activeElement!==e))return}function kc(t,e){return t.map((n,r)=>t[(e+r)%t.length])}function Cc(t,e,n){const s=e.length>1&&Array.from(e).every(l=>l===e[0])?e[0]:e,o=n?t.indexOf(n):-1;let a=kc(t,Math.max(o,0));s.length===1&&(a=a.filter(l=>l!==n));const d=a.find(l=>l.toLowerCase().startsWith(s.toLowerCase()));return d!==n?d:void 0}function Rc(t,e){const{x:n,y:r}=t;let s=!1;for(let o=0,a=e.length-1;o<e.length;a=o++){const c=e[o],d=e[a],l=c.x,f=c.y,u=d.x,h=d.y;f>r!=h>r&&n<(u-l)*(r-f)/(h-f)+l&&(s=!s)}return s}function Pc(t,e){if(!e)return!1;const n={x:t.clientX,y:t.clientY};return Rc(n,e)}function rt(t){return e=>e.pointerType==="mouse"?t(e):void 0}var Mc=xs,Oc=kn,Ac=bs,Tc=vs,Ec=Mn,Lc=ws,Ic=Ft,Dc=js,_c=ks,$c=Rs,Fc=Ms,zc=Os,Bc=As,Vc=Es,Kc=Is,zt="DropdownMenu",[Hc,yf]=at(zt,[ps]),se=ps(),[Uc,_s]=Hc(zt),$s=t=>{const{__scopeDropdownMenu:e,children:n,dir:r,open:s,defaultOpen:o,onOpenChange:a,modal:c=!0}=t,d=se(e),l=x.useRef(null),[f,u]=Nr({prop:s,defaultProp:o??!1,onChange:a,caller:zt});return i.jsx(Uc,{scope:e,triggerId:Qt(),triggerRef:l,contentId:Qt(),open:f,onOpenChange:u,onOpenToggle:x.useCallback(()=>u(h=>!h),[u]),modal:c,children:i.jsx(Mc,{...d,open:f,onOpenChange:u,dir:r,modal:c,children:n})})};$s.displayName=zt;var Fs="DropdownMenuTrigger",zs=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...s}=t,o=_s(Fs,n),a=se(n);return i.jsx(Oc,{asChild:!0,...a,children:i.jsx(fe.button,{type:"button",id:o.triggerId,"aria-haspopup":"menu","aria-expanded":o.open,"aria-controls":o.open?o.contentId:void 0,"data-state":o.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...s,ref:jr(e,o.triggerRef),onPointerDown:B(t.onPointerDown,c=>{!r&&c.button===0&&c.ctrlKey===!1&&(o.onOpenToggle(),o.open||c.preventDefault())}),onKeyDown:B(t.onKeyDown,c=>{r||(["Enter"," "].includes(c.key)&&o.onOpenToggle(),c.key==="ArrowDown"&&o.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(c.key)&&c.preventDefault())})})})});zs.displayName=Fs;var Gc="DropdownMenuPortal",Bs=t=>{const{__scopeDropdownMenu:e,...n}=t,r=se(e);return i.jsx(Ac,{...r,...n})};Bs.displayName=Gc;var Vs="DropdownMenuContent",Ks=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...r}=t,s=_s(Vs,n),o=se(n),a=x.useRef(!1);return i.jsx(Tc,{id:s.contentId,"aria-labelledby":s.triggerId,...o,...r,ref:e,onCloseAutoFocus:B(t.onCloseAutoFocus,c=>{var d;a.current||(d=s.triggerRef.current)==null||d.focus(),a.current=!1,c.preventDefault()}),onInteractOutside:B(t.onInteractOutside,c=>{const d=c.detail.originalEvent,l=d.button===0&&d.ctrlKey===!0,f=d.button===2||l;(!s.modal||f)&&(a.current=!0)}),style:{...t.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Ks.displayName=Vs;var Wc="DropdownMenuGroup",qc=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...r}=t,s=se(n);return i.jsx(Ec,{...s,...r,ref:e})});qc.displayName=Wc;var Yc="DropdownMenuLabel",Hs=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...r}=t,s=se(n);return i.jsx(Lc,{...s,...r,ref:e})});Hs.displayName=Yc;var Xc="DropdownMenuItem",Us=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...r}=t,s=se(n);return i.jsx(Ic,{...s,...r,ref:e})});Us.displayName=Xc;var Jc="DropdownMenuCheckboxItem",Gs=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...r}=t,s=se(n);return i.jsx(Dc,{...s,...r,ref:e})});Gs.displayName=Jc;var Qc="DropdownMenuRadioGroup",Zc=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...r}=t,s=se(n);return i.jsx(_c,{...s,...r,ref:e})});Zc.displayName=Qc;var el="DropdownMenuRadioItem",Ws=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...r}=t,s=se(n);return i.jsx($c,{...s,...r,ref:e})});Ws.displayName=el;var tl="DropdownMenuItemIndicator",qs=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...r}=t,s=se(n);return i.jsx(Fc,{...s,...r,ref:e})});qs.displayName=tl;var nl="DropdownMenuSeparator",Ys=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...r}=t,s=se(n);return i.jsx(zc,{...s,...r,ref:e})});Ys.displayName=nl;var rl="DropdownMenuArrow",sl=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...r}=t,s=se(n);return i.jsx(Bc,{...s,...r,ref:e})});sl.displayName=rl;var ol="DropdownMenuSubTrigger",Xs=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...r}=t,s=se(n);return i.jsx(Vc,{...s,...r,ref:e})});Xs.displayName=ol;var il="DropdownMenuSubContent",Js=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...r}=t,s=se(n);return i.jsx(Kc,{...s,...r,ref:e,style:{...t.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Js.displayName=il;var al=$s,cl=zs,ll=Bs,Qs=Ks,Zs=Hs,eo=Us,to=Gs,no=Ws,ro=qs,so=Ys,oo=Xs,io=Js;/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dl=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ul=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,n,r)=>r?r.toUpperCase():n.toLowerCase()),Yn=t=>{const e=ul(t);return e.charAt(0).toUpperCase()+e.slice(1)},ao=(...t)=>t.filter((e,n,r)=>!!e&&e.trim()!==""&&r.indexOf(e)===n).join(" ").trim(),fl=t=>{for(const e in t)if(e.startsWith("aria-")||e==="role"||e==="title")return!0};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var hl={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ml=x.forwardRef(({color:t="currentColor",size:e=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:s="",children:o,iconNode:a,...c},d)=>x.createElement("svg",{ref:d,...hl,width:e,height:e,stroke:t,strokeWidth:r?Number(n)*24/Number(e):n,className:ao("lucide",s),...!o&&!fl(c)&&{"aria-hidden":"true"},...c},[...a.map(([l,f])=>x.createElement(l,f)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const J=(t,e)=>{const n=x.forwardRef(({className:r,...s},o)=>x.createElement(ml,{ref:o,iconNode:e,className:ao(`lucide-${dl(Yn(t))}`,`lucide-${t}`,r),...s}));return n.displayName=Yn(t),n};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pl=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],gl=J("arrow-left",pl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xl=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],st=J("book-open",xl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yl=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],bl=J("check",yl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vl=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],wl=J("chevron-right",vl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nl=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],jl=J("circle",Nl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sl=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],kl=J("clock",Sl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cl=[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]],Rl=J("github",Cl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pl=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],Ml=J("house",Pl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ol=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],Al=J("mail",Ol);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tl=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],El=J("menu",Tl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ll=[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]],Il=J("monitor",Ll);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dl=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],Xn=J("moon",Dl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _l=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],$l=J("search",_l);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fl=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],Jn=J("star",Fl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zl=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],Qn=J("sun",zl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bl=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],Vl=J("trending-up",Bl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kl=[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]],Hl=J("twitter",Kl);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ul=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],Gl=J("user",Ul),Wl=al,ql=cl,Yl=x.forwardRef(({className:t,inset:e,children:n,...r},s)=>i.jsxs(oo,{ref:s,className:Z("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",e&&"pl-8",t),...r,children:[n,i.jsx(wl,{className:"ml-auto h-4 w-4"})]}));Yl.displayName=oo.displayName;const Xl=x.forwardRef(({className:t,...e},n)=>i.jsx(io,{ref:n,className:Z("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...e}));Xl.displayName=io.displayName;const co=x.forwardRef(({className:t,sideOffset:e=4,...n},r)=>i.jsx(ll,{children:i.jsx(Qs,{ref:r,sideOffset:e,className:Z("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...n})}));co.displayName=Qs.displayName;const bt=x.forwardRef(({className:t,inset:e,...n},r)=>i.jsx(eo,{ref:r,className:Z("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e&&"pl-8",t),...n}));bt.displayName=eo.displayName;const Jl=x.forwardRef(({className:t,children:e,checked:n,...r},s)=>i.jsxs(to,{ref:s,className:Z("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:n,...r,children:[i.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:i.jsx(ro,{children:i.jsx(bl,{className:"h-4 w-4"})})}),e]}));Jl.displayName=to.displayName;const Ql=x.forwardRef(({className:t,children:e,...n},r)=>i.jsxs(no,{ref:r,className:Z("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[i.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:i.jsx(ro,{children:i.jsx(jl,{className:"h-2 w-2 fill-current"})})}),e]}));Ql.displayName=no.displayName;const Zl=x.forwardRef(({className:t,inset:e,...n},r)=>i.jsx(Zs,{ref:r,className:Z("px-2 py-1.5 text-sm font-semibold",e&&"pl-8",t),...n}));Zl.displayName=Zs.displayName;const ed=x.forwardRef(({className:t,...e},n)=>i.jsx(so,{ref:n,className:Z("-mx-1 my-1 h-px bg-muted",t),...e}));ed.displayName=so.displayName;const td=(t,e,n,r)=>{var o,a,c,d;const s=[n,{code:e,...r||{}}];if((a=(o=t==null?void 0:t.services)==null?void 0:o.logger)!=null&&a.forward)return t.services.logger.forward(s,"warn","react-i18next::",!0);Le(s[0])&&(s[0]=`react-i18next:: ${s[0]}`),(d=(c=t==null?void 0:t.services)==null?void 0:c.logger)!=null&&d.warn?t.services.logger.warn(...s):console!=null&&console.warn&&console.warn(...s)},Zn={},ln=(t,e,n,r)=>{Le(n)&&Zn[n]||(Le(n)&&(Zn[n]=new Date),td(t,e,n,r))},lo=(t,e)=>()=>{if(t.isInitialized)e();else{const n=()=>{setTimeout(()=>{t.off("initialized",n)},0),e()};t.on("initialized",n)}},dn=(t,e,n)=>{t.loadNamespaces(e,lo(t,n))},er=(t,e,n,r)=>{if(Le(n)&&(n=[n]),t.options.preload&&t.options.preload.indexOf(e)>-1)return dn(t,n,r);n.forEach(s=>{t.options.ns.indexOf(s)<0&&t.options.ns.push(s)}),t.loadLanguages(e,lo(t,r))},nd=(t,e,n={})=>!e.languages||!e.languages.length?(ln(e,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:e.languages}),!0):e.hasLoadedNamespace(t,{lng:n.lng,precheck:(r,s)=>{var o;if(((o=n.bindI18n)==null?void 0:o.indexOf("languageChanging"))>-1&&r.services.backendConnector.backend&&r.isLanguageChangingTo&&!s(r.isLanguageChangingTo,t))return!1}}),Le=t=>typeof t=="string",rd=t=>typeof t=="object"&&t!==null,sd=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,od={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},id=t=>od[t],ad=t=>t.replace(sd,id);let un={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:ad};const cd=(t={})=>{un={...un,...t}},ld=()=>un;let uo;const dd=t=>{uo=t},ud=()=>uo,fd={type:"3rdParty",init(t){cd(t.options.react),dd(t)}},hd=x.createContext();class md{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(n=>{this.usedNamespaces[n]||(this.usedNamespaces[n]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const pd=(t,e)=>{const n=x.useRef();return x.useEffect(()=>{n.current=t},[t,e]),n.current},fo=(t,e,n,r)=>t.getFixedT(e,n,r),gd=(t,e,n,r)=>x.useCallback(fo(t,e,n,r),[t,e,n,r]),Ae=(t,e={})=>{var S,k,A,T;const{i18n:n}=e,{i18n:r,defaultNS:s}=x.useContext(hd)||{},o=n||r||ud();if(o&&!o.reportNamespaces&&(o.reportNamespaces=new md),!o){ln(o,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const w=(O,F)=>Le(F)?F:rd(F)&&Le(F.defaultValue)?F.defaultValue:Array.isArray(O)?O[O.length-1]:O,M=[w,{},!1];return M.t=w,M.i18n={},M.ready=!1,M}(S=o.options.react)!=null&&S.wait&&ln(o,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const a={...ld(),...o.options.react,...e},{useSuspense:c,keyPrefix:d}=a;let l=t||s||((k=o.options)==null?void 0:k.defaultNS);l=Le(l)?[l]:l||["translation"],(T=(A=o.reportNamespaces).addUsedNamespaces)==null||T.call(A,l);const f=(o.isInitialized||o.initializedStoreOnce)&&l.every(w=>nd(w,o,a)),u=gd(o,e.lng||null,a.nsMode==="fallback"?l:l[0],d),h=()=>u,m=()=>fo(o,e.lng||null,a.nsMode==="fallback"?l:l[0],d),[p,g]=x.useState(h);let y=l.join();e.lng&&(y=`${e.lng}${y}`);const v=pd(y),b=x.useRef(!0);x.useEffect(()=>{const{bindI18n:w,bindI18nStore:M}=a;b.current=!0,!f&&!c&&(e.lng?er(o,e.lng,l,()=>{b.current&&g(m)}):dn(o,l,()=>{b.current&&g(m)})),f&&v&&v!==y&&b.current&&g(m);const O=()=>{b.current&&g(m)};return w&&(o==null||o.on(w,O)),M&&(o==null||o.store.on(M,O)),()=>{b.current=!1,o&&(w==null||w.split(" ").forEach(F=>o.off(F,O))),M&&o&&M.split(" ").forEach(F=>o.store.off(F,O))}},[o,y]),x.useEffect(()=>{b.current&&f&&g(h)},[o,d,f]);const j=[p,o,f];if(j.t=p,j.i18n=o,j.ready=f,f||!f&&!c)return j;throw new Promise(w=>{e.lng?er(o,e.lng,l,()=>w()):dn(o,l,()=>w())})},tr=t=>{let e;const n=new Set,r=(l,f)=>{const u=typeof l=="function"?l(e):l;if(!Object.is(u,e)){const h=e;e=f??(typeof u!="object"||u===null)?u:Object.assign({},e,u),n.forEach(m=>m(e,h))}},s=()=>e,c={setState:r,getState:s,getInitialState:()=>d,subscribe:l=>(n.add(l),()=>n.delete(l))},d=e=t(r,s,c);return c},xd=t=>t?tr(t):tr,yd=t=>t;function bd(t,e=yd){const n=Se.useSyncExternalStore(t.subscribe,()=>e(t.getState()),()=>e(t.getInitialState()));return Se.useDebugValue(n),n}const vd=t=>{const e=xd(t),n=r=>bd(e,r);return Object.assign(n,e),n},ho=t=>vd;function wd(t,e){let n;try{n=t()}catch{return}return{getItem:s=>{var o;const a=d=>d===null?null:JSON.parse(d,void 0),c=(o=n.getItem(s))!=null?o:null;return c instanceof Promise?c.then(a):a(c)},setItem:(s,o)=>n.setItem(s,JSON.stringify(o,void 0)),removeItem:s=>n.removeItem(s)}}const fn=t=>e=>{try{const n=t(e);return n instanceof Promise?n:{then(r){return fn(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return fn(r)(n)}}}},Nd=(t,e)=>(n,r,s)=>{let o={storage:wd(()=>localStorage),partialize:g=>g,version:0,merge:(g,y)=>({...y,...g}),...e},a=!1;const c=new Set,d=new Set;let l=o.storage;if(!l)return t((...g)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),n(...g)},r,s);const f=()=>{const g=o.partialize({...r()});return l.setItem(o.name,{state:g,version:o.version})},u=s.setState;s.setState=(g,y)=>{u(g,y),f()};const h=t((...g)=>{n(...g),f()},r,s);s.getInitialState=()=>h;let m;const p=()=>{var g,y;if(!l)return;a=!1,c.forEach(b=>{var j;return b((j=r())!=null?j:h)});const v=((y=o.onRehydrateStorage)==null?void 0:y.call(o,(g=r())!=null?g:h))||void 0;return fn(l.getItem.bind(l))(o.name).then(b=>{if(b)if(typeof b.version=="number"&&b.version!==o.version){if(o.migrate){const j=o.migrate(b.state,b.version);return j instanceof Promise?j.then(S=>[!0,S]):[!0,j]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,b.state];return[!1,void 0]}).then(b=>{var j;const[S,k]=b;if(m=o.merge(k,(j=r())!=null?j:h),n(m,!0),S)return f()}).then(()=>{v==null||v(m,void 0),m=r(),a=!0,d.forEach(b=>b(m))}).catch(b=>{v==null||v(void 0,b)})};return s.persist={setOptions:g=>{o={...o,...g},g.storage&&(l=g.storage)},clearStorage:()=>{l==null||l.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>p(),hasHydrated:()=>a,onHydrate:g=>(c.add(g),()=>{c.delete(g)}),onFinishHydration:g=>(d.add(g),()=>{d.delete(g)})},o.skipHydration||p(),m||h},mo=Nd,qt=()=>typeof window>"u"?"light":window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light",vt=t=>{const e=document.documentElement;e.classList.remove("light","dark"),e.classList.add(t)},hn=ho()(mo((t,e)=>({theme:"system",actualTheme:qt(),setTheme:n=>{const r=n==="system"?qt():n;vt(r),t({theme:n,actualTheme:r})},toggleTheme:()=>{const{actualTheme:n}=e(),r=n==="light"?"dark":"light";vt(r),t({theme:r,actualTheme:r})}}),{name:"theme-storage",onRehydrateStorage:()=>t=>{if(t){const e=t.theme==="system"?qt():t.theme;vt(e),t.actualTheme=e}}}));typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",t=>{const{theme:e}=hn.getState();if(e==="system"){const n=t.matches?"dark":"light";vt(n),hn.setState({actualTheme:n})}});const po=()=>{const{theme:t,setTheme:e}=hn();return i.jsxs(Wl,{children:[i.jsx(ql,{asChild:!0,children:i.jsxs(W,{variant:"ghost",size:"icon",className:"h-9 w-9",children:[i.jsx(Qn,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),i.jsx(Xn,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),i.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),i.jsxs(co,{align:"end",children:[i.jsxs(bt,{onClick:()=>e("light"),className:t==="light"?"bg-accent":"",children:[i.jsx(Qn,{className:"mr-2 h-4 w-4"}),i.jsx("span",{children:"Light"})]}),i.jsxs(bt,{onClick:()=>e("dark"),className:t==="dark"?"bg-accent":"",children:[i.jsx(Xn,{className:"mr-2 h-4 w-4"}),i.jsx("span",{children:"Dark"})]}),i.jsxs(bt,{onClick:()=>e("system"),className:t==="system"?"bg-accent":"",children:[i.jsx(Il,{className:"mr-2 h-4 w-4"}),i.jsx("span",{children:"System"})]})]})]})},Bt=ho()(mo((t,e)=>({user:null,isAuthenticated:!1,isLoading:!1,login:async n=>{t({isLoading:!0});try{console.log("Login with:",n);const r={id:"1",username:"testuser",email:n.email,role:"reader",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};t({user:r,isAuthenticated:!0,isLoading:!1})}catch(r){throw console.error("Login failed:",r),t({isLoading:!1}),r}},logout:()=>{t({user:null,isAuthenticated:!1})},updateProfile:async n=>{const{user:r}=e();if(r){t({isLoading:!0});try{const s={...r,...n};t({user:s,isLoading:!1})}catch(s){throw console.error("Profile update failed:",s),t({isLoading:!1}),s}}},setUser:n=>{t({user:n,isAuthenticated:!!n})}}),{name:"auth-storage",partialize:t=>({user:t.user,isAuthenticated:t.isAuthenticated})})),go=()=>{const{t}=Ae("common"),{user:e,isAuthenticated:n,logout:r}=Bt();return i.jsx("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:i.jsxs("div",{className:"container flex h-16 items-center",children:[i.jsxs(te,{to:"/",className:"flex items-center space-x-2",children:[i.jsx(st,{className:"h-6 w-6"}),i.jsx("span",{className:"font-bold text-xl",children:"BlogTruyen"})]}),i.jsxs("nav",{className:"hidden md:flex items-center space-x-6 ml-8",children:[i.jsx(te,{to:"/",className:"text-sm font-medium transition-colors hover:text-primary",children:t("navigation.home")}),i.jsx(te,{to:"/browse",className:"text-sm font-medium transition-colors hover:text-primary",children:t("navigation.browse")}),n&&i.jsxs(i.Fragment,{children:[i.jsx(te,{to:"/user/bookmarks",className:"text-sm font-medium transition-colors hover:text-primary",children:t("navigation.bookmarks")}),i.jsx(te,{to:"/user/history",className:"text-sm font-medium transition-colors hover:text-primary",children:t("navigation.history")}),((e==null?void 0:e.role)==="admin"||(e==null?void 0:e.role)==="moderator")&&i.jsx(te,{to:"/admin",className:"text-sm font-medium transition-colors hover:text-primary",children:t("navigation.admin")})]})]}),i.jsx("div",{className:"flex-1 flex justify-center px-4",children:i.jsxs("div",{className:"relative w-full max-w-sm",children:[i.jsx($l,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),i.jsx(Nt,{placeholder:t("actions.search"),className:"pl-8"})]})}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(po,{}),n?i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(te,{to:"/user/profile",children:i.jsxs(W,{variant:"ghost",size:"sm",children:[i.jsx(Gl,{className:"h-4 w-4 mr-2"}),e==null?void 0:e.username]})}),i.jsx(W,{variant:"ghost",size:"sm",onClick:r,children:t("navigation.logout")})]}):i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(te,{to:"/auth/login",children:i.jsx(W,{variant:"ghost",size:"sm",children:t("navigation.login")})}),i.jsx(te,{to:"/auth/register",children:i.jsx(W,{size:"sm",children:t("navigation.register")})})]}),i.jsx(W,{variant:"ghost",size:"icon",className:"md:hidden",children:i.jsx(El,{className:"h-4 w-4"})})]})]})})},jd=()=>{const{t}=Ae("common");return i.jsx("footer",{className:"border-t bg-background",children:i.jsxs("div",{className:"container py-8",children:[i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(st,{className:"h-6 w-6"}),i.jsx("span",{className:"font-bold text-xl",children:"BlogTruyen"})]}),i.jsx("p",{className:"text-sm text-muted-foreground",children:"Nền tảng đọc truyện tranh online hiện đại với trải nghiệm đọc tuyệt vời."})]}),i.jsxs("div",{className:"space-y-4",children:[i.jsx("h3",{className:"font-semibold",children:"Liên kết nhanh"}),i.jsxs("ul",{className:"space-y-2 text-sm",children:[i.jsx("li",{children:i.jsx("a",{href:"/",className:"text-muted-foreground hover:text-primary transition-colors",children:t("navigation.home")})}),i.jsx("li",{children:i.jsx("a",{href:"/browse",className:"text-muted-foreground hover:text-primary transition-colors",children:t("navigation.browse")})}),i.jsx("li",{children:i.jsx("a",{href:"/about",className:"text-muted-foreground hover:text-primary transition-colors",children:"Về chúng tôi"})}),i.jsx("li",{children:i.jsx("a",{href:"/contact",className:"text-muted-foreground hover:text-primary transition-colors",children:"Liên hệ"})}),i.jsx("li",{children:i.jsx("a",{href:"/theme",className:"text-muted-foreground hover:text-primary transition-colors",children:"Theme"})})]})]}),i.jsxs("div",{className:"space-y-4",children:[i.jsx("h3",{className:"font-semibold",children:"Hỗ trợ"}),i.jsxs("ul",{className:"space-y-2 text-sm",children:[i.jsx("li",{children:i.jsx("a",{href:"/help",className:"text-muted-foreground hover:text-primary transition-colors",children:"Trợ giúp"})}),i.jsx("li",{children:i.jsx("a",{href:"/faq",className:"text-muted-foreground hover:text-primary transition-colors",children:"FAQ"})}),i.jsx("li",{children:i.jsx("a",{href:"/privacy",className:"text-muted-foreground hover:text-primary transition-colors",children:"Chính sách bảo mật"})}),i.jsx("li",{children:i.jsx("a",{href:"/terms",className:"text-muted-foreground hover:text-primary transition-colors",children:"Điều khoản sử dụng"})})]})]}),i.jsxs("div",{className:"space-y-4",children:[i.jsx("h3",{className:"font-semibold",children:"Kết nối"}),i.jsxs("div",{className:"flex space-x-4",children:[i.jsx("a",{href:"#",className:"text-muted-foreground hover:text-primary transition-colors",children:i.jsx(Rl,{className:"h-5 w-5"})}),i.jsx("a",{href:"#",className:"text-muted-foreground hover:text-primary transition-colors",children:i.jsx(Hl,{className:"h-5 w-5"})}),i.jsx("a",{href:"#",className:"text-muted-foreground hover:text-primary transition-colors",children:i.jsx(Al,{className:"h-5 w-5"})})]})]})]}),i.jsx("div",{className:"border-t mt-8 pt-8 text-center text-sm text-muted-foreground",children:i.jsx("p",{children:"© 2024 BlogTruyen. Tất cả quyền được bảo lưu."})})]})})},nr=({showHeader:t=!0,showFooter:e=!0,className:n=""})=>i.jsxs("div",{className:`min-h-screen flex flex-col ${n}`,children:[t&&i.jsx(go,{}),i.jsx("main",{className:"flex-1",children:i.jsx(Tt,{})}),e&&i.jsx(jd,{})]}),rr=({requireAuth:t=!1,redirectTo:e="/"})=>{const{isAuthenticated:n}=Bt(),r=Oo();return t&&!n?i.jsx(wt,{to:"/auth/login",replace:!0}):!t&&n&&(r.pathname==="/auth/login"||r.pathname==="/auth/register")?i.jsx(wt,{to:e,replace:!0}):i.jsx(Tt,{})},Sd=({className:t=""})=>i.jsx("div",{className:`min-h-screen bg-background ${t}`,children:i.jsx(Tt,{})}),kd=({className:t=""})=>{const{user:e,isAuthenticated:n}=Bt();return n?(e==null?void 0:e.role)!=="admin"&&(e==null?void 0:e.role)!=="moderator"?i.jsx(wt,{to:"/",replace:!0}):i.jsxs("div",{className:`min-h-screen flex flex-col ${t}`,children:[i.jsx(go,{}),i.jsxs("div",{className:"flex flex-1",children:[i.jsx("aside",{className:"w-64 bg-muted border-r",children:i.jsxs("nav",{className:"p-4",children:[i.jsx("h2",{className:"font-semibold text-lg mb-4",children:"Admin Panel"}),i.jsxs("ul",{className:"space-y-2",children:[i.jsx("li",{children:i.jsx("a",{href:"/admin/dashboard",className:"block p-2 rounded hover:bg-accent",children:"Dashboard"})}),i.jsx("li",{children:i.jsx("a",{href:"/admin/manga",className:"block p-2 rounded hover:bg-accent",children:"Quản lý truyện"})}),i.jsx("li",{children:i.jsx("a",{href:"/admin/users",className:"block p-2 rounded hover:bg-accent",children:"Quản lý người dùng"})}),i.jsx("li",{children:i.jsx("a",{href:"/admin/comments",className:"block p-2 rounded hover:bg-accent",children:"Quản lý bình luận"})})]})]})}),i.jsx("main",{className:"flex-1 p-6",children:i.jsx(Tt,{})})]})]}):i.jsx(wt,{to:"/login",replace:!0})},xo=()=>i.jsxs("div",{className:"space-y-8",children:[i.jsxs(ne,{children:[i.jsxs(le,{children:[i.jsx(ie,{children:"Brand Colors"}),i.jsx(de,{children:"Màu sắc chính của BlogTruyen"})]}),i.jsx(re,{children:i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[i.jsxs("div",{className:"space-y-2",children:[i.jsx("div",{className:"h-20 bg-brand-primary rounded-lg shadow-primary"}),i.jsxs("div",{className:"text-center",children:[i.jsx("p",{className:"font-semibold",children:"Primary"}),i.jsx("p",{className:"text-sm text-muted-foreground",children:"#E40066"}),i.jsx("p",{className:"text-xs text-muted-foreground",children:"Pink/Magenta"})]})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsx("div",{className:"h-20 bg-brand-secondary rounded-lg shadow-secondary"}),i.jsxs("div",{className:"text-center",children:[i.jsx("p",{className:"font-semibold",children:"Secondary"}),i.jsx("p",{className:"text-sm text-muted-foreground",children:"#03CEA4"}),i.jsx("p",{className:"text-xs text-muted-foreground",children:"Teal/Cyan"})]})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsx("div",{className:"h-20 bg-brand-accent rounded-lg shadow-accent"}),i.jsxs("div",{className:"text-center",children:[i.jsx("p",{className:"font-semibold",children:"Accent"}),i.jsx("p",{className:"text-sm text-muted-foreground",children:"#9747FF"}),i.jsx("p",{className:"text-xs text-muted-foreground",children:"Purple"})]})]})]})})]}),i.jsxs(ne,{children:[i.jsxs(le,{children:[i.jsx(ie,{children:"Brand Gradients"}),i.jsx(de,{children:"Gradient combinations cho UI elements"})]}),i.jsx(re,{children:i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.jsxs("div",{className:"space-y-2",children:[i.jsx("div",{className:"h-20 rounded-lg",style:{background:"linear-gradient(135deg, #E40066 0%, #9747FF 100%)"}}),i.jsxs("div",{className:"text-center",children:[i.jsx("p",{className:"font-semibold",children:"Primary Gradient"}),i.jsx("p",{className:"text-xs text-muted-foreground",children:"Pink → Purple"})]})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsx("div",{className:"h-20 rounded-lg",style:{background:"linear-gradient(135deg, #03CEA4 0%, #9747FF 100%)"}}),i.jsxs("div",{className:"text-center",children:[i.jsx("p",{className:"font-semibold",children:"Secondary Gradient"}),i.jsx("p",{className:"text-xs text-muted-foreground",children:"Teal → Purple"})]})]}),i.jsxs("div",{className:"space-y-2 md:col-span-2",children:[i.jsx("div",{className:"h-20 rounded-lg",style:{background:"linear-gradient(135deg, #E40066 0%, #03CEA4 50%, #9747FF 100%)"}}),i.jsxs("div",{className:"text-center",children:[i.jsx("p",{className:"font-semibold",children:"Hero Gradient"}),i.jsx("p",{className:"text-xs text-muted-foreground",children:"Pink → Teal → Purple"})]})]})]})})]}),i.jsxs(ne,{children:[i.jsxs(le,{children:[i.jsx(ie,{children:"Semantic Colors"}),i.jsx(de,{children:"Màu sắc cho các trạng thái và thông báo"})]}),i.jsx(re,{children:i.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[i.jsxs("div",{className:"space-y-2",children:[i.jsx("div",{className:"h-16 bg-success rounded-lg"}),i.jsxs("div",{className:"text-center",children:[i.jsx("p",{className:"font-semibold text-sm",children:"Success"}),i.jsx("p",{className:"text-xs text-muted-foreground",children:"#03CEA4"})]})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsx("div",{className:"h-16 bg-warning rounded-lg"}),i.jsxs("div",{className:"text-center",children:[i.jsx("p",{className:"font-semibold text-sm",children:"Warning"}),i.jsx("p",{className:"text-xs text-muted-foreground",children:"#FFB800"})]})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsx("div",{className:"h-16 bg-error rounded-lg"}),i.jsxs("div",{className:"text-center",children:[i.jsx("p",{className:"font-semibold text-sm",children:"Error"}),i.jsx("p",{className:"text-xs text-muted-foreground",children:"#E40066"})]})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsx("div",{className:"h-16 bg-info rounded-lg"}),i.jsxs("div",{className:"text-center",children:[i.jsx("p",{className:"font-semibold text-sm",children:"Info"}),i.jsx("p",{className:"text-xs text-muted-foreground",children:"#9747FF"})]})]})]})})]}),i.jsxs(ne,{children:[i.jsxs(le,{children:[i.jsx(ie,{children:"Component Examples"}),i.jsx(de,{children:"Ví dụ sử dụng theme trong components"})]}),i.jsx(re,{children:i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"flex flex-wrap gap-2",children:[i.jsx("button",{className:"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors",children:"Primary Button"}),i.jsx("button",{className:"px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 transition-colors",children:"Secondary Button"}),i.jsx("button",{className:"px-4 py-2 bg-accent text-accent-foreground rounded-md hover:bg-accent/90 transition-colors",children:"Accent Button"})]}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[i.jsxs("div",{className:"p-4 border-l-4 border-brand-primary bg-card rounded-lg",children:[i.jsx("h4",{className:"font-semibold text-brand-primary",children:"Primary Card"}),i.jsx("p",{className:"text-sm text-muted-foreground",children:"Card với accent primary color"})]}),i.jsxs("div",{className:"p-4 border-l-4 border-brand-secondary bg-card rounded-lg",children:[i.jsx("h4",{className:"font-semibold text-brand-secondary",children:"Secondary Card"}),i.jsx("p",{className:"text-sm text-muted-foreground",children:"Card với accent secondary color"})]}),i.jsxs("div",{className:"p-4 border-l-4 border-brand-accent bg-card rounded-lg",children:[i.jsx("h4",{className:"font-semibold text-brand-accent",children:"Accent Card"}),i.jsx("p",{className:"text-sm text-muted-foreground",children:"Card với accent color"})]})]})]})})]})]}),Cd=()=>i.jsxs("div",{className:"container py-8",children:[i.jsxs("section",{className:"text-center py-12 relative overflow-hidden",children:[i.jsx("div",{className:"absolute inset-0 opacity-10",style:{background:"linear-gradient(135deg, #E40066 0%, #03CEA4 50%, #9747FF 100%)"}}),i.jsxs("div",{className:"relative z-10",children:[i.jsx("h1",{className:"text-4xl font-bold tracking-tight lg:text-6xl mb-6 bg-gradient-to-r from-brand-primary via-brand-secondary to-brand-accent bg-clip-text text-transparent",children:"Chào mừng đến với BlogTruyen"}),i.jsx("p",{className:"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto",children:"Khám phá thế giới truyện tranh với hàng ngàn bộ manga chất lượng cao. Đọc miễn phí, không quảng cáo, trải nghiệm tuyệt vời."}),i.jsxs("div",{className:"flex gap-4 justify-center",children:[i.jsx(te,{to:"/browse",children:i.jsxs(W,{size:"lg",children:[i.jsx(st,{className:"mr-2 h-4 w-4"}),"Bắt đầu đọc"]})}),i.jsx(te,{to:"/register",children:i.jsx(W,{variant:"outline",size:"lg",children:"Đăng ký miễn phí"})})]})]})]}),i.jsxs("section",{className:"py-12",children:[i.jsx("h2",{className:"text-3xl font-bold text-center mb-8",children:"Tính năng nổi bật"}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[i.jsxs(ne,{children:[i.jsxs(le,{className:"text-center",children:[i.jsx(Vl,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),i.jsx(ie,{className:"text-lg",children:"Xu hướng"})]}),i.jsx(re,{children:i.jsx(de,{className:"text-center",children:"Theo dõi những bộ truyện hot nhất, được cập nhật liên tục"})})]}),i.jsxs(ne,{children:[i.jsxs(le,{className:"text-center",children:[i.jsx(kl,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),i.jsx(ie,{className:"text-lg",children:"Lịch sử đọc"})]}),i.jsx(re,{children:i.jsx(de,{className:"text-center",children:"Lưu lại tiến trình đọc, tiếp tục từ nơi bạn đã dừng lại"})})]}),i.jsxs(ne,{children:[i.jsxs(le,{className:"text-center",children:[i.jsx(Jn,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),i.jsx(ie,{className:"text-lg",children:"Đánh giá"})]}),i.jsx(re,{children:i.jsx(de,{className:"text-center",children:"Đánh giá và bình luận về những bộ truyện yêu thích"})})]}),i.jsxs(ne,{children:[i.jsxs(le,{className:"text-center",children:[i.jsx(st,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),i.jsx(ie,{className:"text-lg",children:"Đa nền tảng"})]}),i.jsx(re,{children:i.jsx(de,{className:"text-center",children:"Đọc mọi lúc mọi nơi trên điện thoại, máy tính bảng, laptop"})})]})]})]}),i.jsxs("section",{className:"py-12",children:[i.jsxs("div",{className:"flex justify-between items-center mb-8",children:[i.jsx("h2",{className:"text-3xl font-bold",children:"Truyện phổ biến"}),i.jsx(te,{to:"/browse",children:i.jsx(W,{variant:"outline",children:"Xem tất cả"})})]}),i.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:Array.from({length:6}).map((t,e)=>i.jsxs(ne,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[i.jsx("div",{className:"aspect-[3/4] bg-muted"}),i.jsxs(re,{className:"p-3",children:[i.jsxs("h3",{className:"font-semibold text-sm truncate",children:["Tên truyện ",e+1]}),i.jsx("p",{className:"text-xs text-muted-foreground",children:"Tác giả"}),i.jsxs("div",{className:"flex items-center mt-1",children:[i.jsx(Jn,{className:"h-3 w-3 fill-yellow-400 text-yellow-400"}),i.jsx("span",{className:"text-xs ml-1",children:"4.5"})]})]})]},e))})]}),i.jsxs("section",{className:"py-12",children:[i.jsxs("div",{className:"flex justify-between items-center mb-8",children:[i.jsx("h2",{className:"text-3xl font-bold",children:"Cập nhật mới nhất"}),i.jsx(te,{to:"/browse?sort=updated",children:i.jsx(W,{variant:"outline",children:"Xem tất cả"})})]}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((t,e)=>i.jsx(ne,{className:"hover:shadow-lg transition-shadow",children:i.jsx(re,{className:"p-4",children:i.jsxs("div",{className:"flex space-x-4",children:[i.jsx("div",{className:"w-16 h-20 bg-muted rounded"}),i.jsxs("div",{className:"flex-1",children:[i.jsxs("h3",{className:"font-semibold mb-1",children:["Tên truyện ",e+1]}),i.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"Chapter 123"}),i.jsx("p",{className:"text-xs text-muted-foreground",children:"2 giờ trước"})]})]})})},e))})]}),i.jsxs("section",{className:"py-12",children:[i.jsx("h2",{className:"text-3xl font-bold text-center mb-8",children:"Theme Showcase"}),i.jsx(xo,{})]})]}),Rd=()=>{const{t}=Ae("common"),e=Ao(),{login:n,isLoading:r}=Bt(),[s,o]=x.useState({email:"",password:""}),[a,c]=x.useState({}),d=u=>{const{name:h,value:m}=u.target;o(p=>({...p,[h]:m})),a[h]&&c(p=>({...p,[h]:""}))},l=()=>{const u={};return s.email?/\S+@\S+\.\S+/.test(s.email)||(u.email=t("forms.invalidEmail")):u.email=t("forms.required"),s.password||(u.password=t("forms.required")),c(u),Object.keys(u).length===0},f=async u=>{if(u.preventDefault(),!!l())try{await n(s),e("/")}catch(h){console.error("Login failed:",h),c({general:"Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin."})}};return i.jsx("div",{className:"container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8",children:i.jsxs(ne,{className:"w-full max-w-md",children:[i.jsxs(le,{className:"text-center",children:[i.jsx("div",{className:"flex justify-center mb-4",children:i.jsx(st,{className:"h-8 w-8 text-primary"})}),i.jsx(ie,{className:"text-2xl",children:t("navigation.login")}),i.jsx(de,{children:"Đăng nhập để truy cập tài khoản của bạn"})]}),i.jsxs(re,{children:[i.jsxs("form",{onSubmit:f,className:"space-y-4",children:[a.general&&i.jsx("div",{className:"text-sm text-destructive text-center p-2 bg-destructive/10 rounded",children:a.general}),i.jsxs("div",{className:"space-y-2",children:[i.jsx(nn,{htmlFor:"email",children:t("forms.email")}),i.jsx(Nt,{id:"email",name:"email",type:"email",value:s.email,onChange:d,placeholder:"<EMAIL>",className:a.email?"border-destructive":""}),a.email&&i.jsx("p",{className:"text-sm text-destructive",children:a.email})]}),i.jsxs("div",{className:"space-y-2",children:[i.jsx(nn,{htmlFor:"password",children:t("forms.password")}),i.jsx(Nt,{id:"password",name:"password",type:"password",value:s.password,onChange:d,className:a.password?"border-destructive":""}),a.password&&i.jsx("p",{className:"text-sm text-destructive",children:a.password})]}),i.jsx(W,{type:"submit",className:"w-full",disabled:r,children:r?"Đang đăng nhập...":t("navigation.login")})]}),i.jsx("div",{className:"mt-6 text-center text-sm",children:i.jsxs("p",{className:"text-muted-foreground",children:["Chưa có tài khoản?"," ",i.jsx(te,{to:"/register",className:"text-primary hover:underline",children:t("navigation.register")})]})})]})]})})},Pd=()=>{const{t}=Ae(["common","manga"]);return i.jsxs("div",{className:"container py-8",children:[i.jsx("h1",{className:"text-3xl font-bold mb-8",children:t("navigation.browse")}),i.jsx("div",{className:"text-center py-12",children:i.jsx("p",{className:"text-muted-foreground",children:"Trang duyệt truyện đang được phát triển..."})})]})},Md=()=>i.jsxs("div",{className:"container py-8",children:[i.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Chi tiết truyện"}),i.jsx("div",{className:"text-center py-12",children:i.jsx("p",{className:"text-muted-foreground",children:"Trang chi tiết truyện đang được phát triển..."})})]}),Od=()=>i.jsxs("div",{className:"container py-8",children:[i.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Đọc truyện"}),i.jsx("div",{className:"text-center py-12",children:i.jsx("p",{className:"text-muted-foreground",children:"Trang đọc truyện đang được phát triển..."})})]}),Ad=()=>{const{t}=Ae("common");return i.jsxs("div",{className:"container py-8",children:[i.jsx("h1",{className:"text-3xl font-bold mb-8",children:t("navigation.register")}),i.jsx("div",{className:"text-center py-12",children:i.jsx("p",{className:"text-muted-foreground",children:"Trang đăng ký đang được phát triển..."})})]})},Td=()=>{const{t}=Ae("common");return i.jsxs("div",{className:"container py-8",children:[i.jsx("h1",{className:"text-3xl font-bold mb-8",children:t("navigation.bookmarks")}),i.jsx("div",{className:"text-center py-12",children:i.jsx("p",{className:"text-muted-foreground",children:"Trang đánh dấu đang được phát triển..."})})]})},Ed=()=>{const{t}=Ae("common");return i.jsxs("div",{className:"container py-8",children:[i.jsx("h1",{className:"text-3xl font-bold mb-8",children:t("navigation.history")}),i.jsx("div",{className:"text-center py-12",children:i.jsx("p",{className:"text-muted-foreground",children:"Trang lịch sử đang được phát triển..."})})]})},Ld=()=>{const{t}=Ae("common");return i.jsxs("div",{className:"container py-8",children:[i.jsx("h1",{className:"text-3xl font-bold mb-8",children:t("navigation.profile")}),i.jsx("div",{className:"text-center py-12",children:i.jsx("p",{className:"text-muted-foreground",children:"Trang hồ sơ đang được phát triển..."})})]})},Id=()=>i.jsx("div",{className:"container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8",children:i.jsxs(ne,{className:"w-full max-w-md text-center",children:[i.jsxs(le,{children:[i.jsx(ie,{className:"text-6xl font-bold text-muted-foreground mb-4",children:"404"}),i.jsx(ie,{className:"text-2xl",children:"Trang không tồn tại"}),i.jsx(de,{children:"Trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển."})]}),i.jsx(re,{className:"space-y-4",children:i.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 justify-center",children:[i.jsx(te,{to:"/",children:i.jsxs(W,{className:"w-full sm:w-auto",children:[i.jsx(Ml,{className:"h-4 w-4 mr-2"}),"Về trang chủ"]})}),i.jsxs(W,{variant:"outline",onClick:()=>window.history.back(),className:"w-full sm:w-auto",children:[i.jsx(gl,{className:"h-4 w-4 mr-2"}),"Quay lại"]})]})})]})}),Dd=()=>i.jsxs("div",{className:"container py-8",children:[i.jsxs("div",{className:"flex justify-between items-center mb-8",children:[i.jsxs("div",{children:[i.jsx("h1",{className:"text-4xl font-bold mb-2",children:"BlogTruyen Theme System"}),i.jsx("p",{className:"text-muted-foreground",children:"Hệ thống màu sắc và theme cho BlogTruyen"})]}),i.jsx(po,{})]}),i.jsx("section",{className:"mb-12",children:i.jsxs(ne,{className:"relative overflow-hidden",children:[i.jsx("div",{className:"absolute inset-0 opacity-20",style:{background:"linear-gradient(135deg, #E40066 0%, #03CEA4 50%, #9747FF 100%)"}}),i.jsxs(re,{className:"relative z-10 p-8 text-center",children:[i.jsx("h2",{className:"text-3xl font-bold mb-4 bg-gradient-to-r from-brand-primary via-brand-secondary to-brand-accent bg-clip-text text-transparent",children:"Brand Colors & Design System"}),i.jsx("p",{className:"text-lg text-muted-foreground mb-6",children:"Khám phá hệ thống màu sắc và thiết kế của BlogTruyen"}),i.jsxs("div",{className:"flex justify-center gap-4",children:[i.jsx(W,{className:"bg-brand-primary hover:bg-brand-primary/90",children:"Primary Action"}),i.jsx(W,{variant:"outline",className:"border-brand-secondary text-brand-secondary hover:bg-brand-secondary hover:text-white",children:"Secondary Action"})]})]})]})}),i.jsx(xo,{}),i.jsx("section",{className:"mt-12",children:i.jsxs(ne,{children:[i.jsxs(le,{children:[i.jsx(ie,{children:"Interactive Examples"}),i.jsx(de,{children:"Các ví dụ tương tác với theme colors"})]}),i.jsx(re,{children:i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{children:[i.jsx("h4",{className:"font-semibold mb-3",children:"Buttons"}),i.jsxs("div",{className:"flex flex-wrap gap-2",children:[i.jsx(W,{children:"Default"}),i.jsx(W,{variant:"secondary",children:"Secondary"}),i.jsx(W,{variant:"outline",children:"Outline"}),i.jsx(W,{variant:"ghost",children:"Ghost"}),i.jsx(W,{variant:"destructive",children:"Destructive"}),i.jsx(W,{className:"bg-brand-primary hover:bg-brand-primary/90",children:"Brand Primary"}),i.jsx(W,{className:"bg-brand-secondary hover:bg-brand-secondary/90",children:"Brand Secondary"}),i.jsx(W,{className:"bg-brand-accent hover:bg-brand-accent/90",children:"Brand Accent"})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"font-semibold mb-3",children:"Status Cards"}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[i.jsxs("div",{className:"p-4 border border-success bg-success/10 rounded-lg",children:[i.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[i.jsx("div",{className:"w-3 h-3 bg-success rounded-full"}),i.jsx("span",{className:"font-semibold text-success",children:"Success"})]}),i.jsx("p",{className:"text-sm",children:"Operation completed successfully"})]}),i.jsxs("div",{className:"p-4 border border-warning bg-warning/10 rounded-lg",children:[i.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[i.jsx("div",{className:"w-3 h-3 bg-warning rounded-full"}),i.jsx("span",{className:"font-semibold text-warning",children:"Warning"})]}),i.jsx("p",{className:"text-sm",children:"Please review this action"})]}),i.jsxs("div",{className:"p-4 border border-error bg-error/10 rounded-lg",children:[i.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[i.jsx("div",{className:"w-3 h-3 bg-error rounded-full"}),i.jsx("span",{className:"font-semibold text-error",children:"Error"})]}),i.jsx("p",{className:"text-sm",children:"Something went wrong"})]}),i.jsxs("div",{className:"p-4 border border-info bg-info/10 rounded-lg",children:[i.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[i.jsx("div",{className:"w-3 h-3 bg-info rounded-full"}),i.jsx("span",{className:"font-semibold text-info",children:"Info"})]}),i.jsx("p",{className:"text-sm",children:"Additional information"})]})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"font-semibold mb-3",children:"Gradient Backgrounds"}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[i.jsx("div",{className:"h-24 bg-gradient-primary rounded-lg flex items-center justify-center text-white font-semibold",children:"Primary Gradient"}),i.jsx("div",{className:"h-24 bg-gradient-secondary rounded-lg flex items-center justify-center text-white font-semibold",children:"Secondary Gradient"}),i.jsx("div",{className:"h-24 bg-gradient-hero rounded-lg flex items-center justify-center text-white font-semibold",children:"Hero Gradient"})]})]})]})})]})})]}),_d=To([{path:"/",element:i.jsx(nr,{}),children:[{index:!0,element:i.jsx(Cd,{})},{path:"browse",element:i.jsx(Pd,{})},{path:"manga/:id",element:i.jsx(Md,{})},{path:"theme",element:i.jsx(Dd,{})},{path:"auth",element:i.jsx(rr,{requireAuth:!1}),children:[{path:"login",element:i.jsx(Rd,{})},{path:"register",element:i.jsx(Ad,{})}]},{path:"user",element:i.jsx(rr,{requireAuth:!0}),children:[{path:"bookmarks",element:i.jsx(Td,{})},{path:"history",element:i.jsx(Ed,{})},{path:"profile",element:i.jsx(Ld,{})}]}]},{path:"/read",element:i.jsx(Sd,{}),children:[{path:":chapterId",element:i.jsx(Od,{})}]},{path:"/admin",element:i.jsx(kd,{}),children:[{index:!0,element:i.jsx("div",{children:"Admin Dashboard"})},{path:"manga",element:i.jsx("div",{children:"Manga Management"})},{path:"users",element:i.jsx("div",{children:"User Management"})},{path:"comments",element:i.jsx("div",{children:"Comment Management"})}]},{path:"*",element:i.jsx(nr,{}),children:[{path:"*",element:i.jsx(Id,{})}]}]),$d=()=>i.jsx(Eo,{router:_d}),L=t=>typeof t=="string",Je=()=>{let t,e;const n=new Promise((r,s)=>{t=r,e=s});return n.resolve=t,n.reject=e,n},sr=t=>t==null?"":""+t,Fd=(t,e,n)=>{t.forEach(r=>{e[r]&&(n[r]=e[r])})},zd=/###/g,or=t=>t&&t.indexOf("###")>-1?t.replace(zd,"."):t,ir=t=>!t||L(t),Ze=(t,e,n)=>{const r=L(e)?e.split("."):e;let s=0;for(;s<r.length-1;){if(ir(t))return{};const o=or(r[s]);!t[o]&&n&&(t[o]=new n),Object.prototype.hasOwnProperty.call(t,o)?t=t[o]:t={},++s}return ir(t)?{}:{obj:t,k:or(r[s])}},ar=(t,e,n)=>{const{obj:r,k:s}=Ze(t,e,Object);if(r!==void 0||e.length===1){r[s]=n;return}let o=e[e.length-1],a=e.slice(0,e.length-1),c=Ze(t,a,Object);for(;c.obj===void 0&&a.length;)o=`${a[a.length-1]}.${o}`,a=a.slice(0,a.length-1),c=Ze(t,a,Object),c!=null&&c.obj&&typeof c.obj[`${c.k}.${o}`]<"u"&&(c.obj=void 0);c.obj[`${c.k}.${o}`]=n},Bd=(t,e,n,r)=>{const{obj:s,k:o}=Ze(t,e,Object);s[o]=s[o]||[],s[o].push(n)},Mt=(t,e)=>{const{obj:n,k:r}=Ze(t,e);if(n&&Object.prototype.hasOwnProperty.call(n,r))return n[r]},Vd=(t,e,n)=>{const r=Mt(t,n);return r!==void 0?r:Mt(e,n)},yo=(t,e,n)=>{for(const r in e)r!=="__proto__"&&r!=="constructor"&&(r in t?L(t[r])||t[r]instanceof String||L(e[r])||e[r]instanceof String?n&&(t[r]=e[r]):yo(t[r],e[r],n):t[r]=e[r]);return t},ze=t=>t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Kd={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const Hd=t=>L(t)?t.replace(/[&<>"'\/]/g,e=>Kd[e]):t;class Ud{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const n=this.regExpMap.get(e);if(n!==void 0)return n;const r=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,r),this.regExpQueue.push(e),r}}const Gd=[" ",",","?","!",";"],Wd=new Ud(20),qd=(t,e,n)=>{e=e||"",n=n||"";const r=Gd.filter(a=>e.indexOf(a)<0&&n.indexOf(a)<0);if(r.length===0)return!0;const s=Wd.getRegExp(`(${r.map(a=>a==="?"?"\\?":a).join("|")})`);let o=!s.test(t);if(!o){const a=t.indexOf(n);a>0&&!s.test(t.substring(0,a))&&(o=!0)}return o},mn=(t,e,n=".")=>{if(!t)return;if(t[e])return Object.prototype.hasOwnProperty.call(t,e)?t[e]:void 0;const r=e.split(n);let s=t;for(let o=0;o<r.length;){if(!s||typeof s!="object")return;let a,c="";for(let d=o;d<r.length;++d)if(d!==o&&(c+=n),c+=r[d],a=s[c],a!==void 0){if(["string","number","boolean"].indexOf(typeof a)>-1&&d<r.length-1)continue;o+=d-o+1;break}s=a}return s},ot=t=>t==null?void 0:t.replace("_","-"),Yd={type:"logger",log(t){this.output("log",t)},warn(t){this.output("warn",t)},error(t){this.output("error",t)},output(t,e){var n,r;(r=(n=console==null?void 0:console[t])==null?void 0:n.apply)==null||r.call(n,console,e)}};class Ot{constructor(e,n={}){this.init(e,n)}init(e,n={}){this.prefix=n.prefix||"i18next:",this.logger=e||Yd,this.options=n,this.debug=n.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,n,r,s){return s&&!this.debug?null:(L(e[0])&&(e[0]=`${r}${this.prefix} ${e[0]}`),this.logger[n](e))}create(e){return new Ot(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new Ot(this.logger,e)}}var ye=new Ot;class Vt{constructor(){this.observers={}}on(e,n){return e.split(" ").forEach(r=>{this.observers[r]||(this.observers[r]=new Map);const s=this.observers[r].get(n)||0;this.observers[r].set(n,s+1)}),this}off(e,n){if(this.observers[e]){if(!n){delete this.observers[e];return}this.observers[e].delete(n)}}emit(e,...n){this.observers[e]&&Array.from(this.observers[e].entries()).forEach(([s,o])=>{for(let a=0;a<o;a++)s(...n)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([s,o])=>{for(let a=0;a<o;a++)s.apply(s,[e,...n])})}}class cr extends Vt{constructor(e,n={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const n=this.options.ns.indexOf(e);n>-1&&this.options.ns.splice(n,1)}getResource(e,n,r,s={}){var l,f;const o=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,a=s.ignoreJSONStructure!==void 0?s.ignoreJSONStructure:this.options.ignoreJSONStructure;let c;e.indexOf(".")>-1?c=e.split("."):(c=[e,n],r&&(Array.isArray(r)?c.push(...r):L(r)&&o?c.push(...r.split(o)):c.push(r)));const d=Mt(this.data,c);return!d&&!n&&!r&&e.indexOf(".")>-1&&(e=c[0],n=c[1],r=c.slice(2).join(".")),d||!a||!L(r)?d:mn((f=(l=this.data)==null?void 0:l[e])==null?void 0:f[n],r,o)}addResource(e,n,r,s,o={silent:!1}){const a=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator;let c=[e,n];r&&(c=c.concat(a?r.split(a):r)),e.indexOf(".")>-1&&(c=e.split("."),s=n,n=c[1]),this.addNamespaces(n),ar(this.data,c,s),o.silent||this.emit("added",e,n,r,s)}addResources(e,n,r,s={silent:!1}){for(const o in r)(L(r[o])||Array.isArray(r[o]))&&this.addResource(e,n,o,r[o],{silent:!0});s.silent||this.emit("added",e,n,r)}addResourceBundle(e,n,r,s,o,a={silent:!1,skipCopy:!1}){let c=[e,n];e.indexOf(".")>-1&&(c=e.split("."),s=r,r=n,n=c[1]),this.addNamespaces(n);let d=Mt(this.data,c)||{};a.skipCopy||(r=JSON.parse(JSON.stringify(r))),s?yo(d,r,o):d={...d,...r},ar(this.data,c,d),a.silent||this.emit("added",e,n,r)}removeResourceBundle(e,n){this.hasResourceBundle(e,n)&&delete this.data[e][n],this.removeNamespaces(n),this.emit("removed",e,n)}hasResourceBundle(e,n){return this.getResource(e,n)!==void 0}getResourceBundle(e,n){return n||(n=this.options.defaultNS),this.getResource(e,n)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const n=this.getDataByLanguage(e);return!!(n&&Object.keys(n)||[]).find(s=>n[s]&&Object.keys(n[s]).length>0)}toJSON(){return this.data}}var bo={processors:{},addPostProcessor(t){this.processors[t.name]=t},handle(t,e,n,r,s){return t.forEach(o=>{var a;e=((a=this.processors[o])==null?void 0:a.process(e,n,r,s))??e}),e}};const lr={},dr=t=>!L(t)&&typeof t!="boolean"&&typeof t!="number";class At extends Vt{constructor(e,n={}){super(),Fd(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=ye.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,n={interpolation:{}}){const r={...n};if(e==null)return!1;const s=this.resolve(e,r);return(s==null?void 0:s.res)!==void 0}extractFromKey(e,n){let r=n.nsSeparator!==void 0?n.nsSeparator:this.options.nsSeparator;r===void 0&&(r=":");const s=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator;let o=n.ns||this.options.defaultNS||[];const a=r&&e.indexOf(r)>-1,c=!this.options.userDefinedKeySeparator&&!n.keySeparator&&!this.options.userDefinedNsSeparator&&!n.nsSeparator&&!qd(e,r,s);if(a&&!c){const d=e.match(this.interpolator.nestingRegexp);if(d&&d.length>0)return{key:e,namespaces:L(o)?[o]:o};const l=e.split(r);(r!==s||r===s&&this.options.ns.indexOf(l[0])>-1)&&(o=l.shift()),e=l.join(s)}return{key:e,namespaces:L(o)?[o]:o}}translate(e,n,r){let s=typeof n=="object"?{...n}:n;if(typeof s!="object"&&this.options.overloadTranslationOptionHandler&&(s=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(s={...s}),s||(s={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const o=s.returnDetails!==void 0?s.returnDetails:this.options.returnDetails,a=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,{key:c,namespaces:d}=this.extractFromKey(e[e.length-1],s),l=d[d.length-1];let f=s.nsSeparator!==void 0?s.nsSeparator:this.options.nsSeparator;f===void 0&&(f=":");const u=s.lng||this.language,h=s.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((u==null?void 0:u.toLowerCase())==="cimode")return h?o?{res:`${l}${f}${c}`,usedKey:c,exactUsedKey:c,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(s)}:`${l}${f}${c}`:o?{res:c,usedKey:c,exactUsedKey:c,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(s)}:c;const m=this.resolve(e,s);let p=m==null?void 0:m.res;const g=(m==null?void 0:m.usedKey)||c,y=(m==null?void 0:m.exactUsedKey)||c,v=["[object Number]","[object Function]","[object RegExp]"],b=s.joinArrays!==void 0?s.joinArrays:this.options.joinArrays,j=!this.i18nFormat||this.i18nFormat.handleAsObject,S=s.count!==void 0&&!L(s.count),k=At.hasDefaultValue(s),A=S?this.pluralResolver.getSuffix(u,s.count,s):"",T=s.ordinal&&S?this.pluralResolver.getSuffix(u,s.count,{ordinal:!1}):"",w=S&&!s.ordinal&&s.count===0,M=w&&s[`defaultValue${this.options.pluralSeparator}zero`]||s[`defaultValue${A}`]||s[`defaultValue${T}`]||s.defaultValue;let O=p;j&&!p&&k&&(O=M);const F=dr(O),K=Object.prototype.toString.apply(O);if(j&&O&&F&&v.indexOf(K)<0&&!(L(b)&&Array.isArray(O))){if(!s.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const $=this.options.returnedObjectHandler?this.options.returnedObjectHandler(g,O,{...s,ns:d}):`key '${c} (${this.language})' returned an object instead of string.`;return o?(m.res=$,m.usedParams=this.getUsedParamsDetails(s),m):$}if(a){const $=Array.isArray(O),I=$?[]:{},U=$?y:g;for(const E in O)if(Object.prototype.hasOwnProperty.call(O,E)){const D=`${U}${a}${E}`;k&&!p?I[E]=this.translate(D,{...s,defaultValue:dr(M)?M[E]:void 0,joinArrays:!1,ns:d}):I[E]=this.translate(D,{...s,joinArrays:!1,ns:d}),I[E]===D&&(I[E]=O[E])}p=I}}else if(j&&L(b)&&Array.isArray(p))p=p.join(b),p&&(p=this.extendTranslation(p,e,s,r));else{let $=!1,I=!1;!this.isValidLookup(p)&&k&&($=!0,p=M),this.isValidLookup(p)||(I=!0,p=c);const E=(s.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&I?void 0:p,D=k&&M!==p&&this.options.updateMissing;if(I||$||D){if(this.logger.log(D?"updateKey":"missingKey",u,l,c,D?M:p),a){const z=this.resolve(c,{...s,keySeparator:!1});z&&z.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let N=[];const C=this.languageUtils.getFallbackCodes(this.options.fallbackLng,s.lng||this.language);if(this.options.saveMissingTo==="fallback"&&C&&C[0])for(let z=0;z<C.length;z++)N.push(C[z]);else this.options.saveMissingTo==="all"?N=this.languageUtils.toResolveHierarchy(s.lng||this.language):N.push(s.lng||this.language);const V=(z,q,G)=>{var ce;const Y=k&&G!==p?G:E;this.options.missingKeyHandler?this.options.missingKeyHandler(z,l,q,Y,D,s):(ce=this.backendConnector)!=null&&ce.saveMissing&&this.backendConnector.saveMissing(z,l,q,Y,D,s),this.emit("missingKey",z,l,q,p)};this.options.saveMissing&&(this.options.saveMissingPlurals&&S?N.forEach(z=>{const q=this.pluralResolver.getSuffixes(z,s);w&&s[`defaultValue${this.options.pluralSeparator}zero`]&&q.indexOf(`${this.options.pluralSeparator}zero`)<0&&q.push(`${this.options.pluralSeparator}zero`),q.forEach(G=>{V([z],c+G,s[`defaultValue${G}`]||M)})}):V(N,c,M))}p=this.extendTranslation(p,e,s,m,r),I&&p===c&&this.options.appendNamespaceToMissingKey&&(p=`${l}${f}${c}`),(I||$)&&this.options.parseMissingKeyHandler&&(p=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}${f}${c}`:c,$?p:void 0,s))}return o?(m.res=p,m.usedParams=this.getUsedParamsDetails(s),m):p}extendTranslation(e,n,r,s,o){var d,l;if((d=this.i18nFormat)!=null&&d.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||s.usedLng,s.usedNS,s.usedKey,{resolved:s});else if(!r.skipInterpolation){r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});const f=L(e)&&(((l=r==null?void 0:r.interpolation)==null?void 0:l.skipOnVariables)!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let u;if(f){const m=e.match(this.interpolator.nestingRegexp);u=m&&m.length}let h=r.replace&&!L(r.replace)?r.replace:r;if(this.options.interpolation.defaultVariables&&(h={...this.options.interpolation.defaultVariables,...h}),e=this.interpolator.interpolate(e,h,r.lng||this.language||s.usedLng,r),f){const m=e.match(this.interpolator.nestingRegexp),p=m&&m.length;u<p&&(r.nest=!1)}!r.lng&&s&&s.res&&(r.lng=this.language||s.usedLng),r.nest!==!1&&(e=this.interpolator.nest(e,(...m)=>(o==null?void 0:o[0])===m[0]&&!r.context?(this.logger.warn(`It seems you are nesting recursively key: ${m[0]} in key: ${n[0]}`),null):this.translate(...m,n),r)),r.interpolation&&this.interpolator.reset()}const a=r.postProcess||this.options.postProcess,c=L(a)?[a]:a;return e!=null&&(c!=null&&c.length)&&r.applyPostProcessor!==!1&&(e=bo.handle(c,e,n,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...s,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),e}resolve(e,n={}){let r,s,o,a,c;return L(e)&&(e=[e]),e.forEach(d=>{if(this.isValidLookup(r))return;const l=this.extractFromKey(d,n),f=l.key;s=f;let u=l.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));const h=n.count!==void 0&&!L(n.count),m=h&&!n.ordinal&&n.count===0,p=n.context!==void 0&&(L(n.context)||typeof n.context=="number")&&n.context!=="",g=n.lngs?n.lngs:this.languageUtils.toResolveHierarchy(n.lng||this.language,n.fallbackLng);u.forEach(y=>{var v,b;this.isValidLookup(r)||(c=y,!lr[`${g[0]}-${y}`]&&((v=this.utils)!=null&&v.hasLoadedNamespace)&&!((b=this.utils)!=null&&b.hasLoadedNamespace(c))&&(lr[`${g[0]}-${y}`]=!0,this.logger.warn(`key "${s}" for languages "${g.join(", ")}" won't get resolved as namespace "${c}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),g.forEach(j=>{var A;if(this.isValidLookup(r))return;a=j;const S=[f];if((A=this.i18nFormat)!=null&&A.addLookupKeys)this.i18nFormat.addLookupKeys(S,f,j,y,n);else{let T;h&&(T=this.pluralResolver.getSuffix(j,n.count,n));const w=`${this.options.pluralSeparator}zero`,M=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(h&&(S.push(f+T),n.ordinal&&T.indexOf(M)===0&&S.push(f+T.replace(M,this.options.pluralSeparator)),m&&S.push(f+w)),p){const O=`${f}${this.options.contextSeparator}${n.context}`;S.push(O),h&&(S.push(O+T),n.ordinal&&T.indexOf(M)===0&&S.push(O+T.replace(M,this.options.pluralSeparator)),m&&S.push(O+w))}}let k;for(;k=S.pop();)this.isValidLookup(r)||(o=k,r=this.getResource(j,y,k,n))}))})}),{res:r,usedKey:s,exactUsedKey:o,usedLng:a,usedNS:c}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,n,r,s={}){var o;return(o=this.i18nFormat)!=null&&o.getResource?this.i18nFormat.getResource(e,n,r,s):this.resourceStore.getResource(e,n,r,s)}getUsedParamsDetails(e={}){const n=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],r=e.replace&&!L(e.replace);let s=r?e.replace:e;if(r&&typeof e.count<"u"&&(s.count=e.count),this.options.interpolation.defaultVariables&&(s={...this.options.interpolation.defaultVariables,...s}),!r){s={...s};for(const o of n)delete s[o]}return s}static hasDefaultValue(e){const n="defaultValue";for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&n===r.substring(0,n.length)&&e[r]!==void 0)return!0;return!1}}class ur{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=ye.create("languageUtils")}getScriptPartFromCode(e){if(e=ot(e),!e||e.indexOf("-")<0)return null;const n=e.split("-");return n.length===2||(n.pop(),n[n.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(n.join("-"))}getLanguagePartFromCode(e){if(e=ot(e),!e||e.indexOf("-")<0)return e;const n=e.split("-");return this.formatLanguageCode(n[0])}formatLanguageCode(e){if(L(e)&&e.indexOf("-")>-1){let n;try{n=Intl.getCanonicalLocales(e)[0]}catch{}return n&&this.options.lowerCaseLng&&(n=n.toLowerCase()),n||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let n;return e.forEach(r=>{if(n)return;const s=this.formatLanguageCode(r);(!this.options.supportedLngs||this.isSupportedCode(s))&&(n=s)}),!n&&this.options.supportedLngs&&e.forEach(r=>{if(n)return;const s=this.getScriptPartFromCode(r);if(this.isSupportedCode(s))return n=s;const o=this.getLanguagePartFromCode(r);if(this.isSupportedCode(o))return n=o;n=this.options.supportedLngs.find(a=>{if(a===o)return a;if(!(a.indexOf("-")<0&&o.indexOf("-")<0)&&(a.indexOf("-")>0&&o.indexOf("-")<0&&a.substring(0,a.indexOf("-"))===o||a.indexOf(o)===0&&o.length>1))return a})}),n||(n=this.getFallbackCodes(this.options.fallbackLng)[0]),n}getFallbackCodes(e,n){if(!e)return[];if(typeof e=="function"&&(e=e(n)),L(e)&&(e=[e]),Array.isArray(e))return e;if(!n)return e.default||[];let r=e[n];return r||(r=e[this.getScriptPartFromCode(n)]),r||(r=e[this.formatLanguageCode(n)]),r||(r=e[this.getLanguagePartFromCode(n)]),r||(r=e.default),r||[]}toResolveHierarchy(e,n){const r=this.getFallbackCodes((n===!1?[]:n)||this.options.fallbackLng||[],e),s=[],o=a=>{a&&(this.isSupportedCode(a)?s.push(a):this.logger.warn(`rejecting language code not found in supportedLngs: ${a}`))};return L(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&o(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&o(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&o(this.getLanguagePartFromCode(e))):L(e)&&o(this.formatLanguageCode(e)),r.forEach(a=>{s.indexOf(a)<0&&o(this.formatLanguageCode(a))}),s}}const fr={zero:0,one:1,two:2,few:3,many:4,other:5},hr={select:t=>t===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class Xd{constructor(e,n={}){this.languageUtils=e,this.options=n,this.logger=ye.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,n){this.rules[e]=n}clearCache(){this.pluralRulesCache={}}getRule(e,n={}){const r=ot(e==="dev"?"en":e),s=n.ordinal?"ordinal":"cardinal",o=JSON.stringify({cleanedCode:r,type:s});if(o in this.pluralRulesCache)return this.pluralRulesCache[o];let a;try{a=new Intl.PluralRules(r,{type:s})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),hr;if(!e.match(/-|_/))return hr;const d=this.languageUtils.getLanguagePartFromCode(e);a=this.getRule(d,n)}return this.pluralRulesCache[o]=a,a}needsPlural(e,n={}){let r=this.getRule(e,n);return r||(r=this.getRule("dev",n)),(r==null?void 0:r.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(e,n,r={}){return this.getSuffixes(e,r).map(s=>`${n}${s}`)}getSuffixes(e,n={}){let r=this.getRule(e,n);return r||(r=this.getRule("dev",n)),r?r.resolvedOptions().pluralCategories.sort((s,o)=>fr[s]-fr[o]).map(s=>`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${s}`):[]}getSuffix(e,n,r={}){const s=this.getRule(e,r);return s?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${s.select(n)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",n,r))}}const mr=(t,e,n,r=".",s=!0)=>{let o=Vd(t,e,n);return!o&&s&&L(n)&&(o=mn(t,n,r),o===void 0&&(o=mn(e,n,r))),o},Yt=t=>t.replace(/\$/g,"$$$$");class Jd{constructor(e={}){var n;this.logger=ye.create("interpolator"),this.options=e,this.format=((n=e==null?void 0:e.interpolation)==null?void 0:n.format)||(r=>r),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});const{escape:n,escapeValue:r,useRawValueToEscape:s,prefix:o,prefixEscaped:a,suffix:c,suffixEscaped:d,formatSeparator:l,unescapeSuffix:f,unescapePrefix:u,nestingPrefix:h,nestingPrefixEscaped:m,nestingSuffix:p,nestingSuffixEscaped:g,nestingOptionsSeparator:y,maxReplaces:v,alwaysFormat:b}=e.interpolation;this.escape=n!==void 0?n:Hd,this.escapeValue=r!==void 0?r:!0,this.useRawValueToEscape=s!==void 0?s:!1,this.prefix=o?ze(o):a||"{{",this.suffix=c?ze(c):d||"}}",this.formatSeparator=l||",",this.unescapePrefix=f?"":u||"-",this.unescapeSuffix=this.unescapePrefix?"":f||"",this.nestingPrefix=h?ze(h):m||ze("$t("),this.nestingSuffix=p?ze(p):g||ze(")"),this.nestingOptionsSeparator=y||",",this.maxReplaces=v||1e3,this.alwaysFormat=b!==void 0?b:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(n,r)=>(n==null?void 0:n.source)===r?(n.lastIndex=0,n):new RegExp(r,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,n,r,s){var m;let o,a,c;const d=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},l=p=>{if(p.indexOf(this.formatSeparator)<0){const b=mr(n,d,p,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(b,void 0,r,{...s,...n,interpolationkey:p}):b}const g=p.split(this.formatSeparator),y=g.shift().trim(),v=g.join(this.formatSeparator).trim();return this.format(mr(n,d,y,this.options.keySeparator,this.options.ignoreJSONStructure),v,r,{...s,...n,interpolationkey:y})};this.resetRegExp();const f=(s==null?void 0:s.missingInterpolationHandler)||this.options.missingInterpolationHandler,u=((m=s==null?void 0:s.interpolation)==null?void 0:m.skipOnVariables)!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:p=>Yt(p)},{regex:this.regexp,safeValue:p=>this.escapeValue?Yt(this.escape(p)):Yt(p)}].forEach(p=>{for(c=0;o=p.regex.exec(e);){const g=o[1].trim();if(a=l(g),a===void 0)if(typeof f=="function"){const v=f(e,o,s);a=L(v)?v:""}else if(s&&Object.prototype.hasOwnProperty.call(s,g))a="";else if(u){a=o[0];continue}else this.logger.warn(`missed to pass in variable ${g} for interpolating ${e}`),a="";else!L(a)&&!this.useRawValueToEscape&&(a=sr(a));const y=p.safeValue(a);if(e=e.replace(o[0],y),u?(p.regex.lastIndex+=a.length,p.regex.lastIndex-=o[0].length):p.regex.lastIndex=0,c++,c>=this.maxReplaces)break}}),e}nest(e,n,r={}){let s,o,a;const c=(d,l)=>{const f=this.nestingOptionsSeparator;if(d.indexOf(f)<0)return d;const u=d.split(new RegExp(`${f}[ ]*{`));let h=`{${u[1]}`;d=u[0],h=this.interpolate(h,a);const m=h.match(/'/g),p=h.match(/"/g);(((m==null?void 0:m.length)??0)%2===0&&!p||p.length%2!==0)&&(h=h.replace(/'/g,'"'));try{a=JSON.parse(h),l&&(a={...l,...a})}catch(g){return this.logger.warn(`failed parsing options string in nesting for key ${d}`,g),`${d}${f}${h}`}return a.defaultValue&&a.defaultValue.indexOf(this.prefix)>-1&&delete a.defaultValue,d};for(;s=this.nestingRegexp.exec(e);){let d=[];a={...r},a=a.replace&&!L(a.replace)?a.replace:a,a.applyPostProcessor=!1,delete a.defaultValue;let l=!1;if(s[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(s[1])){const f=s[1].split(this.formatSeparator).map(u=>u.trim());s[1]=f.shift(),d=f,l=!0}if(o=n(c.call(this,s[1].trim(),a),a),o&&s[0]===e&&!L(o))return o;L(o)||(o=sr(o)),o||(this.logger.warn(`missed to resolve ${s[1]} for nesting ${e}`),o=""),l&&(o=d.reduce((f,u)=>this.format(f,u,r.lng,{...r,interpolationkey:s[1].trim()}),o.trim())),e=e.replace(s[0],o),this.regexp.lastIndex=0}return e}}const Qd=t=>{let e=t.toLowerCase().trim();const n={};if(t.indexOf("(")>-1){const r=t.split("(");e=r[0].toLowerCase().trim();const s=r[1].substring(0,r[1].length-1);e==="currency"&&s.indexOf(":")<0?n.currency||(n.currency=s.trim()):e==="relativetime"&&s.indexOf(":")<0?n.range||(n.range=s.trim()):s.split(";").forEach(a=>{if(a){const[c,...d]=a.split(":"),l=d.join(":").trim().replace(/^'+|'+$/g,""),f=c.trim();n[f]||(n[f]=l),l==="false"&&(n[f]=!1),l==="true"&&(n[f]=!0),isNaN(l)||(n[f]=parseInt(l,10))}})}return{formatName:e,formatOptions:n}},pr=t=>{const e={};return(n,r,s)=>{let o=s;s&&s.interpolationkey&&s.formatParams&&s.formatParams[s.interpolationkey]&&s[s.interpolationkey]&&(o={...o,[s.interpolationkey]:void 0});const a=r+JSON.stringify(o);let c=e[a];return c||(c=t(ot(r),s),e[a]=c),c(n)}},Zd=t=>(e,n,r)=>t(ot(n),r)(e);class eu{constructor(e={}){this.logger=ye.create("formatter"),this.options=e,this.init(e)}init(e,n={interpolation:{}}){this.formatSeparator=n.interpolation.formatSeparator||",";const r=n.cacheInBuiltFormats?pr:Zd;this.formats={number:r((s,o)=>{const a=new Intl.NumberFormat(s,{...o});return c=>a.format(c)}),currency:r((s,o)=>{const a=new Intl.NumberFormat(s,{...o,style:"currency"});return c=>a.format(c)}),datetime:r((s,o)=>{const a=new Intl.DateTimeFormat(s,{...o});return c=>a.format(c)}),relativetime:r((s,o)=>{const a=new Intl.RelativeTimeFormat(s,{...o});return c=>a.format(c,o.range||"day")}),list:r((s,o)=>{const a=new Intl.ListFormat(s,{...o});return c=>a.format(c)})}}add(e,n){this.formats[e.toLowerCase().trim()]=n}addCached(e,n){this.formats[e.toLowerCase().trim()]=pr(n)}format(e,n,r,s={}){const o=n.split(this.formatSeparator);if(o.length>1&&o[0].indexOf("(")>1&&o[0].indexOf(")")<0&&o.find(c=>c.indexOf(")")>-1)){const c=o.findIndex(d=>d.indexOf(")")>-1);o[0]=[o[0],...o.splice(1,c)].join(this.formatSeparator)}return o.reduce((c,d)=>{var u;const{formatName:l,formatOptions:f}=Qd(d);if(this.formats[l]){let h=c;try{const m=((u=s==null?void 0:s.formatParams)==null?void 0:u[s.interpolationkey])||{},p=m.locale||m.lng||s.locale||s.lng||r;h=this.formats[l](c,p,{...f,...s,...m})}catch(m){this.logger.warn(m)}return h}else this.logger.warn(`there was no format function for ${l}`);return c},e)}}const tu=(t,e)=>{t.pending[e]!==void 0&&(delete t.pending[e],t.pendingCount--)};class nu extends Vt{constructor(e,n,r,s={}){var o,a;super(),this.backend=e,this.store=n,this.services=r,this.languageUtils=r.languageUtils,this.options=s,this.logger=ye.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=s.maxParallelReads||10,this.readingCalls=0,this.maxRetries=s.maxRetries>=0?s.maxRetries:5,this.retryTimeout=s.retryTimeout>=1?s.retryTimeout:350,this.state={},this.queue=[],(a=(o=this.backend)==null?void 0:o.init)==null||a.call(o,r,s.backend,s)}queueLoad(e,n,r,s){const o={},a={},c={},d={};return e.forEach(l=>{let f=!0;n.forEach(u=>{const h=`${l}|${u}`;!r.reload&&this.store.hasResourceBundle(l,u)?this.state[h]=2:this.state[h]<0||(this.state[h]===1?a[h]===void 0&&(a[h]=!0):(this.state[h]=1,f=!1,a[h]===void 0&&(a[h]=!0),o[h]===void 0&&(o[h]=!0),d[u]===void 0&&(d[u]=!0)))}),f||(c[l]=!0)}),(Object.keys(o).length||Object.keys(a).length)&&this.queue.push({pending:a,pendingCount:Object.keys(a).length,loaded:{},errors:[],callback:s}),{toLoad:Object.keys(o),pending:Object.keys(a),toLoadLanguages:Object.keys(c),toLoadNamespaces:Object.keys(d)}}loaded(e,n,r){const s=e.split("|"),o=s[0],a=s[1];n&&this.emit("failedLoading",o,a,n),!n&&r&&this.store.addResourceBundle(o,a,r,void 0,void 0,{skipCopy:!0}),this.state[e]=n?-1:2,n&&r&&(this.state[e]=0);const c={};this.queue.forEach(d=>{Bd(d.loaded,[o],a),tu(d,e),n&&d.errors.push(n),d.pendingCount===0&&!d.done&&(Object.keys(d.loaded).forEach(l=>{c[l]||(c[l]={});const f=d.loaded[l];f.length&&f.forEach(u=>{c[l][u]===void 0&&(c[l][u]=!0)})}),d.done=!0,d.errors.length?d.callback(d.errors):d.callback())}),this.emit("loaded",c),this.queue=this.queue.filter(d=>!d.done)}read(e,n,r,s=0,o=this.retryTimeout,a){if(!e.length)return a(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:n,fcName:r,tried:s,wait:o,callback:a});return}this.readingCalls++;const c=(l,f)=>{if(this.readingCalls--,this.waitingReads.length>0){const u=this.waitingReads.shift();this.read(u.lng,u.ns,u.fcName,u.tried,u.wait,u.callback)}if(l&&f&&s<this.maxRetries){setTimeout(()=>{this.read.call(this,e,n,r,s+1,o*2,a)},o);return}a(l,f)},d=this.backend[r].bind(this.backend);if(d.length===2){try{const l=d(e,n);l&&typeof l.then=="function"?l.then(f=>c(null,f)).catch(c):c(null,l)}catch(l){c(l)}return}return d(e,n,c)}prepareLoading(e,n,r={},s){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),s&&s();L(e)&&(e=this.languageUtils.toResolveHierarchy(e)),L(n)&&(n=[n]);const o=this.queueLoad(e,n,r,s);if(!o.toLoad.length)return o.pending.length||s(),null;o.toLoad.forEach(a=>{this.loadOne(a)})}load(e,n,r){this.prepareLoading(e,n,{},r)}reload(e,n,r){this.prepareLoading(e,n,{reload:!0},r)}loadOne(e,n=""){const r=e.split("|"),s=r[0],o=r[1];this.read(s,o,"read",void 0,void 0,(a,c)=>{a&&this.logger.warn(`${n}loading namespace ${o} for language ${s} failed`,a),!a&&c&&this.logger.log(`${n}loaded namespace ${o} for language ${s}`,c),this.loaded(e,a,c)})}saveMissing(e,n,r,s,o,a={},c=()=>{}){var d,l,f,u,h;if((l=(d=this.services)==null?void 0:d.utils)!=null&&l.hasLoadedNamespace&&!((u=(f=this.services)==null?void 0:f.utils)!=null&&u.hasLoadedNamespace(n))){this.logger.warn(`did not save key "${r}" as the namespace "${n}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(r==null||r==="")){if((h=this.backend)!=null&&h.create){const m={...a,isUpdate:o},p=this.backend.create.bind(this.backend);if(p.length<6)try{let g;p.length===5?g=p(e,n,r,s,m):g=p(e,n,r,s),g&&typeof g.then=="function"?g.then(y=>c(null,y)).catch(c):c(null,g)}catch(g){c(g)}else p(e,n,r,s,c,m)}!e||!e[0]||this.store.addResource(e[0],n,r,s)}}}const gr=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:t=>{let e={};if(typeof t[1]=="object"&&(e=t[1]),L(t[1])&&(e.defaultValue=t[1]),L(t[2])&&(e.tDescription=t[2]),typeof t[2]=="object"||typeof t[3]=="object"){const n=t[3]||t[2];Object.keys(n).forEach(r=>{e[r]=n[r]})}return e},interpolation:{escapeValue:!0,format:t=>t,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),xr=t=>{var e,n;return L(t.ns)&&(t.ns=[t.ns]),L(t.fallbackLng)&&(t.fallbackLng=[t.fallbackLng]),L(t.fallbackNS)&&(t.fallbackNS=[t.fallbackNS]),((n=(e=t.supportedLngs)==null?void 0:e.indexOf)==null?void 0:n.call(e,"cimode"))<0&&(t.supportedLngs=t.supportedLngs.concat(["cimode"])),typeof t.initImmediate=="boolean"&&(t.initAsync=t.initImmediate),t},xt=()=>{},ru=t=>{Object.getOwnPropertyNames(Object.getPrototypeOf(t)).forEach(n=>{typeof t[n]=="function"&&(t[n]=t[n].bind(t))})};class it extends Vt{constructor(e={},n){if(super(),this.options=xr(e),this.services={},this.logger=ye,this.modules={external:[]},ru(this),n&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,n),this;setTimeout(()=>{this.init(e,n)},0)}}init(e={},n){this.isInitializing=!0,typeof e=="function"&&(n=e,e={}),e.defaultNS==null&&e.ns&&(L(e.ns)?e.defaultNS=e.ns:e.ns.indexOf("translation")<0&&(e.defaultNS=e.ns[0]));const r=gr();this.options={...r,...this.options,...xr(e)},this.options.interpolation={...r.interpolation,...this.options.interpolation},e.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=e.keySeparator),e.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=e.nsSeparator);const s=l=>l?typeof l=="function"?new l:l:null;if(!this.options.isClone){this.modules.logger?ye.init(s(this.modules.logger),this.options):ye.init(null,this.options);let l;this.modules.formatter?l=this.modules.formatter:l=eu;const f=new ur(this.options);this.store=new cr(this.options.resources,this.options);const u=this.services;u.logger=ye,u.resourceStore=this.store,u.languageUtils=f,u.pluralResolver=new Xd(f,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),l&&(!this.options.interpolation.format||this.options.interpolation.format===r.interpolation.format)&&(u.formatter=s(l),u.formatter.init(u,this.options),this.options.interpolation.format=u.formatter.format.bind(u.formatter)),u.interpolator=new Jd(this.options),u.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},u.backendConnector=new nu(s(this.modules.backend),u.resourceStore,u,this.options),u.backendConnector.on("*",(h,...m)=>{this.emit(h,...m)}),this.modules.languageDetector&&(u.languageDetector=s(this.modules.languageDetector),u.languageDetector.init&&u.languageDetector.init(u,this.options.detection,this.options)),this.modules.i18nFormat&&(u.i18nFormat=s(this.modules.i18nFormat),u.i18nFormat.init&&u.i18nFormat.init(this)),this.translator=new At(this.services,this.options),this.translator.on("*",(h,...m)=>{this.emit(h,...m)}),this.modules.external.forEach(h=>{h.init&&h.init(this)})}if(this.format=this.options.interpolation.format,n||(n=xt),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const l=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);l.length>0&&l[0]!=="dev"&&(this.options.lng=l[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(l=>{this[l]=(...f)=>this.store[l](...f)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(l=>{this[l]=(...f)=>(this.store[l](...f),this)});const c=Je(),d=()=>{const l=(f,u)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),c.resolve(u),n(f,u)};if(this.languages&&!this.isInitialized)return l(null,this.t.bind(this));this.changeLanguage(this.options.lng,l)};return this.options.resources||!this.options.initAsync?d():setTimeout(d,0),c}loadResources(e,n=xt){var o,a;let r=n;const s=L(e)?e:this.language;if(typeof e=="function"&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if((s==null?void 0:s.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return r();const c=[],d=l=>{if(!l||l==="cimode")return;this.services.languageUtils.toResolveHierarchy(l).forEach(u=>{u!=="cimode"&&c.indexOf(u)<0&&c.push(u)})};s?d(s):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(f=>d(f)),(a=(o=this.options.preload)==null?void 0:o.forEach)==null||a.call(o,l=>d(l)),this.services.backendConnector.load(c,this.options.ns,l=>{!l&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),r(l)})}else r(null)}reloadResources(e,n,r){const s=Je();return typeof e=="function"&&(r=e,e=void 0),typeof n=="function"&&(r=n,n=void 0),e||(e=this.languages),n||(n=this.options.ns),r||(r=xt),this.services.backendConnector.reload(e,n,o=>{s.resolve(),r(o)}),s}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&bo.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1)){for(let n=0;n<this.languages.length;n++){const r=this.languages[n];if(!(["cimode","dev"].indexOf(r)>-1)&&this.store.hasLanguageSomeTranslations(r)){this.resolvedLanguage=r;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,n){this.isLanguageChangingTo=e;const r=Je();this.emit("languageChanging",e);const s=c=>{this.language=c,this.languages=this.services.languageUtils.toResolveHierarchy(c),this.resolvedLanguage=void 0,this.setResolvedLanguage(c)},o=(c,d)=>{d?this.isLanguageChangingTo===e&&(s(d),this.translator.changeLanguage(d),this.isLanguageChangingTo=void 0,this.emit("languageChanged",d),this.logger.log("languageChanged",d)):this.isLanguageChangingTo=void 0,r.resolve((...l)=>this.t(...l)),n&&n(c,(...l)=>this.t(...l))},a=c=>{var f,u;!e&&!c&&this.services.languageDetector&&(c=[]);const d=L(c)?c:c&&c[0],l=this.store.hasLanguageSomeTranslations(d)?d:this.services.languageUtils.getBestMatchFromCodes(L(c)?[c]:c);l&&(this.language||s(l),this.translator.language||this.translator.changeLanguage(l),(u=(f=this.services.languageDetector)==null?void 0:f.cacheUserLanguage)==null||u.call(f,l)),this.loadResources(l,h=>{o(h,l)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?a(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(a):this.services.languageDetector.detect(a):a(e),r}getFixedT(e,n,r){const s=(o,a,...c)=>{let d;typeof a!="object"?d=this.options.overloadTranslationOptionHandler([o,a].concat(c)):d={...a},d.lng=d.lng||s.lng,d.lngs=d.lngs||s.lngs,d.ns=d.ns||s.ns,d.keyPrefix!==""&&(d.keyPrefix=d.keyPrefix||r||s.keyPrefix);const l=this.options.keySeparator||".";let f;return d.keyPrefix&&Array.isArray(o)?f=o.map(u=>`${d.keyPrefix}${l}${u}`):f=d.keyPrefix?`${d.keyPrefix}${l}${o}`:o,this.t(f,d)};return L(e)?s.lng=e:s.lngs=e,s.ns=n,s.keyPrefix=r,s}t(...e){var n;return(n=this.translator)==null?void 0:n.translate(...e)}exists(...e){var n;return(n=this.translator)==null?void 0:n.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,n={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const r=n.lng||this.resolvedLanguage||this.languages[0],s=this.options?this.options.fallbackLng:!1,o=this.languages[this.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const a=(c,d)=>{const l=this.services.backendConnector.state[`${c}|${d}`];return l===-1||l===0||l===2};if(n.precheck){const c=n.precheck(this,a);if(c!==void 0)return c}return!!(this.hasResourceBundle(r,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||a(r,e)&&(!s||a(o,e)))}loadNamespaces(e,n){const r=Je();return this.options.ns?(L(e)&&(e=[e]),e.forEach(s=>{this.options.ns.indexOf(s)<0&&this.options.ns.push(s)}),this.loadResources(s=>{r.resolve(),n&&n(s)}),r):(n&&n(),Promise.resolve())}loadLanguages(e,n){const r=Je();L(e)&&(e=[e]);const s=this.options.preload||[],o=e.filter(a=>s.indexOf(a)<0&&this.services.languageUtils.isSupportedCode(a));return o.length?(this.options.preload=s.concat(o),this.loadResources(a=>{r.resolve(),n&&n(a)}),r):(n&&n(),Promise.resolve())}dir(e){var s,o;if(e||(e=this.resolvedLanguage||(((s=this.languages)==null?void 0:s.length)>0?this.languages[0]:this.language)),!e)return"rtl";const n=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],r=((o=this.services)==null?void 0:o.languageUtils)||new ur(gr());return n.indexOf(r.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(e={},n){return new it(e,n)}cloneInstance(e={},n=xt){const r=e.forkResourceStore;r&&delete e.forkResourceStore;const s={...this.options,...e,isClone:!0},o=new it(s);if((e.debug!==void 0||e.prefix!==void 0)&&(o.logger=o.logger.clone(e)),["store","services","language"].forEach(c=>{o[c]=this[c]}),o.services={...this.services},o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},r){const c=Object.keys(this.store.data).reduce((d,l)=>(d[l]={...this.store.data[l]},d[l]=Object.keys(d[l]).reduce((f,u)=>(f[u]={...d[l][u]},f),d[l]),d),{});o.store=new cr(c,s),o.services.resourceStore=o.store}return o.translator=new At(o.services,s),o.translator.on("*",(c,...d)=>{o.emit(c,...d)}),o.init(s,n),o.translator.options=s,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const ee=it.createInstance();ee.createInstance=it.createInstance;ee.createInstance;ee.dir;ee.init;ee.loadResources;ee.reloadResources;ee.use;ee.changeLanguage;ee.getFixedT;ee.t;ee.exists;ee.setDefaultNamespace;ee.hasLoadedNamespace;ee.loadNamespaces;ee.loadLanguages;const{slice:su,forEach:ou}=[];function iu(t){return ou.call(su.call(arguments,1),e=>{if(e)for(const n in e)t[n]===void 0&&(t[n]=e[n])}),t}function au(t){return typeof t!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(n=>n.test(t))}const yr=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,cu=function(t,e){const r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},s=encodeURIComponent(e);let o=`${t}=${s}`;if(r.maxAge>0){const a=r.maxAge-0;if(Number.isNaN(a))throw new Error("maxAge should be a Number");o+=`; Max-Age=${Math.floor(a)}`}if(r.domain){if(!yr.test(r.domain))throw new TypeError("option domain is invalid");o+=`; Domain=${r.domain}`}if(r.path){if(!yr.test(r.path))throw new TypeError("option path is invalid");o+=`; Path=${r.path}`}if(r.expires){if(typeof r.expires.toUTCString!="function")throw new TypeError("option expires is invalid");o+=`; Expires=${r.expires.toUTCString()}`}if(r.httpOnly&&(o+="; HttpOnly"),r.secure&&(o+="; Secure"),r.sameSite)switch(typeof r.sameSite=="string"?r.sameSite.toLowerCase():r.sameSite){case!0:o+="; SameSite=Strict";break;case"lax":o+="; SameSite=Lax";break;case"strict":o+="; SameSite=Strict";break;case"none":o+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return r.partitioned&&(o+="; Partitioned"),o},br={create(t,e,n,r){let s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};n&&(s.expires=new Date,s.expires.setTime(s.expires.getTime()+n*60*1e3)),r&&(s.domain=r),document.cookie=cu(t,encodeURIComponent(e),s)},read(t){const e=`${t}=`,n=document.cookie.split(";");for(let r=0;r<n.length;r++){let s=n[r];for(;s.charAt(0)===" ";)s=s.substring(1,s.length);if(s.indexOf(e)===0)return s.substring(e.length,s.length)}return null},remove(t){this.create(t,"",-1)}};var lu={name:"cookie",lookup(t){let{lookupCookie:e}=t;if(e&&typeof document<"u")return br.read(e)||void 0},cacheUserLanguage(t,e){let{lookupCookie:n,cookieMinutes:r,cookieDomain:s,cookieOptions:o}=e;n&&typeof document<"u"&&br.create(n,t,r,s,o)}},du={name:"querystring",lookup(t){var r;let{lookupQuerystring:e}=t,n;if(typeof window<"u"){let{search:s}=window.location;!window.location.search&&((r=window.location.hash)==null?void 0:r.indexOf("?"))>-1&&(s=window.location.hash.substring(window.location.hash.indexOf("?")));const a=s.substring(1).split("&");for(let c=0;c<a.length;c++){const d=a[c].indexOf("=");d>0&&a[c].substring(0,d)===e&&(n=a[c].substring(d+1))}}return n}};let Be=null;const vr=()=>{if(Be!==null)return Be;try{if(Be=typeof window<"u"&&window.localStorage!==null,!Be)return!1;const t="i18next.translate.boo";window.localStorage.setItem(t,"foo"),window.localStorage.removeItem(t)}catch{Be=!1}return Be};var uu={name:"localStorage",lookup(t){let{lookupLocalStorage:e}=t;if(e&&vr())return window.localStorage.getItem(e)||void 0},cacheUserLanguage(t,e){let{lookupLocalStorage:n}=e;n&&vr()&&window.localStorage.setItem(n,t)}};let Ve=null;const wr=()=>{if(Ve!==null)return Ve;try{if(Ve=typeof window<"u"&&window.sessionStorage!==null,!Ve)return!1;const t="i18next.translate.boo";window.sessionStorage.setItem(t,"foo"),window.sessionStorage.removeItem(t)}catch{Ve=!1}return Ve};var fu={name:"sessionStorage",lookup(t){let{lookupSessionStorage:e}=t;if(e&&wr())return window.sessionStorage.getItem(e)||void 0},cacheUserLanguage(t,e){let{lookupSessionStorage:n}=e;n&&wr()&&window.sessionStorage.setItem(n,t)}},hu={name:"navigator",lookup(t){const e=[];if(typeof navigator<"u"){const{languages:n,userLanguage:r,language:s}=navigator;if(n)for(let o=0;o<n.length;o++)e.push(n[o]);r&&e.push(r),s&&e.push(s)}return e.length>0?e:void 0}},mu={name:"htmlTag",lookup(t){let{htmlTag:e}=t,n;const r=e||(typeof document<"u"?document.documentElement:null);return r&&typeof r.getAttribute=="function"&&(n=r.getAttribute("lang")),n}},pu={name:"path",lookup(t){var s;let{lookupFromPathIndex:e}=t;if(typeof window>"u")return;const n=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(n)?(s=n[typeof e=="number"?e:0])==null?void 0:s.replace("/",""):void 0}},gu={name:"subdomain",lookup(t){var s,o;let{lookupFromSubdomainIndex:e}=t;const n=typeof e=="number"?e+1:1,r=typeof window<"u"&&((o=(s=window.location)==null?void 0:s.hostname)==null?void 0:o.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(r)return r[n]}};let vo=!1;try{document.cookie,vo=!0}catch{}const wo=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];vo||wo.splice(1,1);const xu=()=>({order:wo,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:t=>t});class No{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(e,n)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=e,this.options=iu(n,this.options||{},xu()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=s=>s.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=r,this.addDetector(lu),this.addDetector(du),this.addDetector(uu),this.addDetector(fu),this.addDetector(hu),this.addDetector(mu),this.addDetector(pu),this.addDetector(gu)}addDetector(e){return this.detectors[e.name]=e,this}detect(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,n=[];return e.forEach(r=>{if(this.detectors[r]){let s=this.detectors[r].lookup(this.options);s&&typeof s=="string"&&(s=[s]),s&&(n=n.concat(s))}}),n=n.filter(r=>r!=null&&!au(r)).map(r=>this.options.convertDetectedLanguage(r)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?n:n.length>0?n[0]:null}cacheUserLanguage(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;n&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||n.forEach(r=>{this.detectors[r]&&this.detectors[r].cacheUserLanguage(e,this.options)}))}}No.type="languageDetector";const yu={home:"Home",browse:"Browse",bookmarks:"Bookmarks",history:"History",profile:"Profile",admin:"Admin",login:"Login",logout:"Logout",register:"Register"},bu={search:"Search",filter:"Filter",sort:"Sort",save:"Save",cancel:"Cancel",delete:"Delete",edit:"Edit",view:"View",back:"Back",next:"Next",previous:"Previous",submit:"Submit",reset:"Reset"},vu={loading:"Loading...",error:"Error",success:"Success",noData:"No data available",notFound:"Not found"},wu={email:"Email",password:"Password",confirmPassword:"Confirm Password",username:"Username",required:"This field is required",invalidEmail:"Invalid email format",passwordMismatch:"Passwords do not match",minLength:"Minimum {{count}} characters required"},Nu={light:"Light",dark:"Dark",system:"System"},ju={en:"English",vi:"Tiếng Việt"},Su={navigation:yu,actions:bu,status:vu,forms:wu,theme:Nu,language:ju},ku="Manga",Cu={author:"Author",artist:"Artist",status:"Status",genres:"Genres",rating:"Rating",chapters:"Chapters",views:"Views",description:"Description",alternativeTitle:"Alternative Title"},Ru={ongoing:"Ongoing",completed:"Completed",hiatus:"Hiatus",cancelled:"Cancelled"},Pu={readNow:"Read Now",addBookmark:"Add to Bookmarks",removeBookmark:"Remove from Bookmarks",continueReading:"Continue Reading",startReading:"Start Reading",rate:"Rate this manga",comment:"Leave a comment"},Mu={placeholder:"Search manga...",noResults:"No manga found",filters:{all:"All",genre:"Genre",status:"Status",sortBy:"Sort by",sortOrder:"Order"},sortOptions:{title:"Title",rating:"Rating",views:"Views",updated:"Last Updated",created:"Date Added"}},Ou={title:"Comments",noComments:"No comments yet",writeComment:"Write a comment...",rating:"Your rating"},Au={title:ku,details:Cu,status:Ru,actions:Pu,search:Mu,comments:Ou},Tu="Reader",Eu={previousChapter:"Previous Chapter",nextChapter:"Next Chapter",chapterList:"Chapter List",goToPage:"Go to page",pageOf:"Page {{current}} of {{total}}"},Lu={title:"Reading Settings",readingMode:"Reading Mode",readingDirection:"Reading Direction",pageFit:"Page Fit",brightness:"Brightness",backgroundColor:"Background Color"},Iu={single:"Single Page",double:"Double Page",webtoon:"Webtoon"},Du={ltr:"Left to Right",rtl:"Right to Left"},_u={width:"Fit Width",height:"Fit Height",auto:"Auto Fit"},$u={fullscreen:"Fullscreen",exitFullscreen:"Exit Fullscreen",settings:"Settings",bookmark:"Bookmark",share:"Share"},Fu={title:"Keyboard Shortcuts",nextPage:"Next Page",previousPage:"Previous Page",firstPage:"First Page",lastPage:"Last Page",toggleFullscreen:"Toggle Fullscreen",toggleSettings:"Toggle Settings"},zu={title:Tu,navigation:Eu,settings:Lu,modes:Iu,directions:Du,pageFit:_u,controls:$u,shortcuts:Fu},Bu={home:"Trang chủ",browse:"Duyệt",bookmarks:"Đánh dấu",history:"Lịch sử",profile:"Hồ sơ",admin:"Quản trị",login:"Đăng nhập",logout:"Đăng xuất",register:"Đăng ký"},Vu={search:"Tìm kiếm",filter:"Lọc",sort:"Sắp xếp",save:"Lưu",cancel:"Hủy",delete:"Xóa",edit:"Sửa",view:"Xem",back:"Quay lại",next:"Tiếp theo",previous:"Trước đó",submit:"Gửi",reset:"Đặt lại"},Ku={loading:"Đang tải...",error:"Lỗi",success:"Thành công",noData:"Không có dữ liệu",notFound:"Không tìm thấy"},Hu={email:"Email",password:"Mật khẩu",confirmPassword:"Xác nhận mật khẩu",username:"Tên người dùng",required:"Trường này là bắt buộc",invalidEmail:"Định dạng email không hợp lệ",passwordMismatch:"Mật khẩu không khớp",minLength:"Tối thiểu {{count}} ký tự"},Uu={light:"Sáng",dark:"Tối",system:"Hệ thống"},Gu={en:"English",vi:"Tiếng Việt"},Wu={navigation:Bu,actions:Vu,status:Ku,forms:Hu,theme:Uu,language:Gu},qu="Truyện tranh",Yu={author:"Tác giả",artist:"Họa sĩ",status:"Trạng thái",genres:"Thể loại",rating:"Đánh giá",chapters:"Chương",views:"Lượt xem",description:"Mô tả",alternativeTitle:"Tên khác"},Xu={ongoing:"Đang tiến hành",completed:"Hoàn thành",hiatus:"Tạm dừng",cancelled:"Đã hủy"},Ju={readNow:"Đọc ngay",addBookmark:"Thêm vào đánh dấu",removeBookmark:"Xóa khỏi đánh dấu",continueReading:"Tiếp tục đọc",startReading:"Bắt đầu đọc",rate:"Đánh giá truyện này",comment:"Để lại bình luận"},Qu={placeholder:"Tìm kiếm truyện...",noResults:"Không tìm thấy truyện nào",filters:{all:"Tất cả",genre:"Thể loại",status:"Trạng thái",sortBy:"Sắp xếp theo",sortOrder:"Thứ tự"},sortOptions:{title:"Tiêu đề",rating:"Đánh giá",views:"Lượt xem",updated:"Cập nhật gần nhất",created:"Ngày thêm"}},Zu={title:"Bình luận",noComments:"Chưa có bình luận nào",writeComment:"Viết bình luận...",rating:"Đánh giá của bạn"},ef={title:qu,details:Yu,status:Xu,actions:Ju,search:Qu,comments:Zu},tf="Đọc truyện",nf={previousChapter:"Chương trước",nextChapter:"Chương sau",chapterList:"Danh sách chương",goToPage:"Đến trang",pageOf:"Trang {{current}} / {{total}}"},rf={title:"Cài đặt đọc",readingMode:"Chế độ đọc",readingDirection:"Hướng đọc",pageFit:"Khớp trang",brightness:"Độ sáng",backgroundColor:"Màu nền"},sf={single:"Một trang",double:"Hai trang",webtoon:"Webtoon"},of={ltr:"Trái sang phải",rtl:"Phải sang trái"},af={width:"Khớp chiều rộng",height:"Khớp chiều cao",auto:"Tự động"},cf={fullscreen:"Toàn màn hình",exitFullscreen:"Thoát toàn màn hình",settings:"Cài đặt",bookmark:"Đánh dấu",share:"Chia sẻ"},lf={title:"Phím tắt",nextPage:"Trang sau",previousPage:"Trang trước",firstPage:"Trang đầu",lastPage:"Trang cuối",toggleFullscreen:"Bật/tắt toàn màn hình",toggleSettings:"Bật/tắt cài đặt"},df={title:tf,navigation:nf,settings:rf,modes:sf,directions:of,pageFit:af,controls:cf,shortcuts:lf},uf={en:{common:Su,manga:Au,reader:zu},vi:{common:Wu,manga:ef,reader:df}};ee.use(No).use(fd).init({resources:uf,fallbackLng:"en",debug:!1,defaultNS:"common",ns:["common","manga","reader"],interpolation:{escapeValue:!1},detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"]}});function ff(){return i.jsxs(Po,{client:Uo,children:[i.jsx($d,{}),!1]})}Ho.createRoot(document.getElementById("root")).render(i.jsx(x.StrictMode,{children:i.jsx(ff,{})}));
//# sourceMappingURL=index-t3kH4ZRH.js.map
