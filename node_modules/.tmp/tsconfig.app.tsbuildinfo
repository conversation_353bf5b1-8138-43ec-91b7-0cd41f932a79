{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/index.ts", "../../src/components/layout/footer.tsx", "../../src/components/layout/header.tsx", "../../src/components/layout/layout.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/index.ts", "../../src/components/ui/input.tsx", "../../src/components/ui/label.tsx", "../../src/constants/index.ts", "../../src/hooks/usechapter.ts", "../../src/hooks/usemanga.ts", "../../src/i18n/index.ts", "../../src/pages/bookmarkspage.tsx", "../../src/pages/browsepage.tsx", "../../src/pages/historypage.tsx", "../../src/pages/homepage.tsx", "../../src/pages/loginpage.tsx", "../../src/pages/mangadetailpage.tsx", "../../src/pages/profilepage.tsx", "../../src/pages/readerpage.tsx", "../../src/pages/registerpage.tsx", "../../src/pages/index.ts", "../../src/router/index.tsx", "../../src/services/api.ts", "../../src/services/queryclient.ts", "../../src/stores/authstore.ts", "../../src/stores/mangastore.ts", "../../src/stores/readerstore.ts", "../../src/types/index.ts", "../../src/utils/cn.ts", "../../src/utils/index.ts"], "version": "5.8.3"}