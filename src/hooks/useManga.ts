import { useQuery, useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '../services/api';
import type { MangaFilters } from '../types';

// Query keys
export const mangaKeys = {
  all: ['manga'] as const,
  lists: () => [...mangaKeys.all, 'list'] as const,
  list: (filters: MangaFilters) => [...mangaKeys.lists(), filters] as const,
  details: () => [...mangaKeys.all, 'detail'] as const,
  detail: (id: string) => [...mangaKeys.details(), id] as const,
  chapters: (id: string) => [...mangaKeys.detail(id), 'chapters'] as const,
};

// Get manga list with filters
export const useMangaListQuery = (filters: MangaFilters = {}) => {
  return useInfiniteQuery({
    queryKey: mangaKeys.list(filters),
    queryFn: ({ pageParam = 1 }) => 
      apiClient.getMangaList({ ...filters, page: pageParam }),
    getNextPageParam: (lastPage) => {
      const { pagination } = lastPage;
      return pagination.page < pagination.totalPages 
        ? pagination.page + 1 
        : undefined;
    },
    initialPageParam: 1,
  });
};

// Get single manga details
export const useMangaQuery = (mangaId: string) => {
  return useQuery({
    queryKey: mangaKeys.detail(mangaId),
    queryFn: () => apiClient.getMangaById(mangaId),
    enabled: !!mangaId,
  });
};

// Get manga chapters
export const useMangaChaptersQuery = (mangaId: string) => {
  return useQuery({
    queryKey: mangaKeys.chapters(mangaId),
    queryFn: () => apiClient.getMangaChapters(mangaId),
    enabled: !!mangaId,
  });
};

// Bookmark mutations
export const useBookmarkMutation = () => {
  const queryClient = useQueryClient();

  const addBookmark = useMutation({
    mutationFn: (mangaId: string) => apiClient.addBookmark(mangaId),
    onSuccess: () => {
      // Invalidate bookmarks query
      queryClient.invalidateQueries({ queryKey: ['user', 'bookmarks'] });
    },
  });

  const removeBookmark = useMutation({
    mutationFn: (mangaId: string) => apiClient.removeBookmark(mangaId),
    onSuccess: () => {
      // Invalidate bookmarks query
      queryClient.invalidateQueries({ queryKey: ['user', 'bookmarks'] });
    },
  });

  return { addBookmark, removeBookmark };
};

// Reading progress mutation
export const useReadingProgressMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      mangaId, 
      chapterId, 
      pageNumber 
    }: { 
      mangaId: string; 
      chapterId: string; 
      pageNumber: number; 
    }) => apiClient.updateReadingProgress(mangaId, chapterId, pageNumber),
    onSuccess: () => {
      // Invalidate reading history
      queryClient.invalidateQueries({ queryKey: ['user', 'history'] });
    },
  });
};
