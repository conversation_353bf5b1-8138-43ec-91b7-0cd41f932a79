import React from "react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Search, User, Menu, BookOpen } from "lucide-react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { useAuthStore } from "../../stores/authStore";

export const Header: React.FC = () => {
  const { t } = useTranslation("common");
  const { user, isAuthenticated, logout } = useAuthStore();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center">
        {/* Logo */}
        <Link to="/" className="flex items-center space-x-2">
          <BookOpen className="h-6 w-6" />
          <span className="font-bold text-xl">BlogTruyen</span>
        </Link>

        {/* Navigation */}
        <nav className="hidden md:flex items-center space-x-6 ml-8">
          <Link
            to="/"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            {t("navigation.home")}
          </Link>
          <Link
            to="/browse"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            {t("navigation.browse")}
          </Link>
          {isAuthenticated && (
            <>
              <Link
                to="/user/bookmarks"
                className="text-sm font-medium transition-colors hover:text-primary"
              >
                {t("navigation.bookmarks")}
              </Link>
              <Link
                to="/user/history"
                className="text-sm font-medium transition-colors hover:text-primary"
              >
                {t("navigation.history")}
              </Link>
              {(user?.role === "admin" || user?.role === "moderator") && (
                <Link
                  to="/admin"
                  className="text-sm font-medium transition-colors hover:text-primary"
                >
                  {t("navigation.admin")}
                </Link>
              )}
            </>
          )}
        </nav>

        {/* Search */}
        <div className="flex-1 flex justify-center px-4">
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder={t("actions.search")} className="pl-8" />
          </div>
        </div>

        {/* User Actions */}
        <div className="flex items-center space-x-2">
          {isAuthenticated ? (
            <div className="flex items-center space-x-2">
              <Link to="/user/profile">
                <Button variant="ghost" size="sm">
                  <User className="h-4 w-4 mr-2" />
                  {user?.username}
                </Button>
              </Link>
              <Button variant="ghost" size="sm" onClick={logout}>
                {t("navigation.logout")}
              </Button>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <Link to="/auth/login">
                <Button variant="ghost" size="sm">
                  {t("navigation.login")}
                </Button>
              </Link>
              <Link to="/auth/register">
                <Button size="sm">{t("navigation.register")}</Button>
              </Link>
            </div>
          )}

          {/* Mobile menu */}
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </header>
  );
};
