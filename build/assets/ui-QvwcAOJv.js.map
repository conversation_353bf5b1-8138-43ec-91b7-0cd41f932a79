{"version": 3, "file": "ui-QvwcAOJv.js", "sources": ["../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "../../node_modules/@radix-ui/react-slot/dist/index.mjs", "../../node_modules/@radix-ui/react-primitive/dist/index.mjs"], "sourcesContent": ["// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment as Fragment2, jsx } from \"react/jsx-runtime\";\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== React.Fragment) {\n        props2.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props2);\n    }\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ jsx(Fragment2, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return React.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nexport {\n  Slot as Root,\n  Slot,\n  Slottable,\n  createSlot,\n  createSlottable\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["setRef", "ref", "value", "composeRefs", "refs", "node", "hasCleanup", "cleanups", "cleanup", "i", "createSlot", "ownerName", "SlotClone", "createSlotClone", "Slot2", "React.forwardRef", "props", "forwardedRef", "children", "slotProps", "childrenA<PERSON>y", "React.Children", "slottable", "isSlottable", "newElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "React.isValidElement", "jsx", "React.cloneElement", "Slot", "childrenRef", "getElementRef", "props2", "mergeProps", "React.Fragment", "SLOTTABLE_IDENTIFIER", "childProps", "overrideProps", "propName", "slotPropValue", "childPropV<PERSON>ue", "args", "result", "element", "getter", "_a", "<PERSON><PERSON><PERSON><PERSON>", "_b", "NODES", "Primitive", "primitive", "Node", "<PERSON><PERSON><PERSON><PERSON>", "primitiveProps", "Comp"], "mappings": "iFAEA,SAASA,EAAOC,EAAKC,EAAO,CAC1B,GAAI,OAAOD,GAAQ,WACjB,OAAOA,EAAIC,CAAK,EACPD,GAAQ,OACjBA,EAAI,QAAUC,EAElB,CACA,SAASC,KAAeC,EAAM,CAC5B,OAAQC,GAAS,CACf,IAAIC,EAAa,GACjB,MAAMC,EAAWH,EAAK,IAAKH,GAAQ,CACjC,MAAMO,EAAUR,EAAOC,EAAKI,CAAI,EAChC,MAAI,CAACC,GAAc,OAAOE,GAAW,aACnCF,EAAa,IAERE,CACb,CAAK,EACD,GAAIF,EACF,MAAO,IAAM,CACX,QAASG,EAAI,EAAGA,EAAIF,EAAS,OAAQE,IAAK,CACxC,MAAMD,EAAUD,EAASE,CAAC,EACtB,OAAOD,GAAW,WACpBA,EAAS,EAETR,EAAOI,EAAKK,CAAC,EAAG,IAAI,CAEhC,CACO,CAEJ,CACH,CC3BA,SAASC,EAAWC,EAAW,CAC7B,MAAMC,EAA4BC,EAAgBF,CAAS,EACrDG,EAAQC,EAAAA,WAAiB,CAACC,EAAOC,IAAiB,CACtD,KAAM,CAAE,SAAAC,EAAU,GAAGC,CAAS,EAAKH,EAC7BI,EAAgBC,EAAAA,SAAe,QAAQH,CAAQ,EAC/CI,EAAYF,EAAc,KAAKG,CAAW,EAChD,GAAID,EAAW,CACb,MAAME,EAAaF,EAAU,MAAM,SAC7BG,EAAcL,EAAc,IAAKM,GACjCA,IAAUJ,EACRD,EAAc,SAAC,MAAMG,CAAU,EAAI,EAAUH,EAAc,SAAC,KAAK,IAAI,EAClEM,EAAAA,eAAqBH,CAAU,EAAIA,EAAW,MAAM,SAAW,KAE/DE,CAEV,EACD,OAAuBE,EAAAA,IAAIhB,EAAW,CAAE,GAAGO,EAAW,IAAKF,EAAc,SAAUU,EAAoB,eAACH,CAAU,EAAIK,EAAAA,aAAmBL,EAAY,OAAQC,CAAW,EAAI,KAAM,CACxL,CACI,OAAuBG,EAAAA,IAAIhB,EAAW,CAAE,GAAGO,EAAW,IAAKF,EAAc,SAAAC,EAAU,CACvF,CAAG,EACD,OAAAJ,EAAM,YAAc,GAAGH,CAAS,QACzBG,CACT,CACG,IAACgB,EAAuBpB,EAAW,MAAM,EAE5C,SAASG,EAAgBF,EAAW,CAClC,MAAMC,EAAYG,EAAAA,WAAiB,CAACC,EAAOC,IAAiB,CAC1D,KAAM,CAAE,SAAAC,EAAU,GAAGC,CAAS,EAAKH,EACnC,GAAIW,EAAAA,eAAqBT,CAAQ,EAAG,CAClC,MAAMa,EAAcC,EAAcd,CAAQ,EACpCe,EAASC,EAAWf,EAAWD,EAAS,KAAK,EACnD,OAAIA,EAAS,OAASiB,aACpBF,EAAO,IAAMhB,EAAed,EAAYc,EAAcc,CAAW,EAAIA,GAEhEF,EAAkB,aAACX,EAAUe,CAAM,CAChD,CACI,OAAOZ,EAAc,SAAC,MAAMH,CAAQ,EAAI,EAAIG,WAAe,KAAK,IAAI,EAAI,IAC5E,CAAG,EACD,OAAAT,EAAU,YAAc,GAAGD,CAAS,aAC7BC,CACT,CACA,IAAIwB,EAAuB,OAAO,iBAAiB,EAWnD,SAASb,EAAYG,EAAO,CAC1B,OAAOC,EAAoB,eAACD,CAAK,GAAK,OAAOA,EAAM,MAAS,YAAc,cAAeA,EAAM,MAAQA,EAAM,KAAK,YAAcU,CAClI,CACA,SAASF,EAAWf,EAAWkB,EAAY,CACzC,MAAMC,EAAgB,CAAE,GAAGD,CAAY,EACvC,UAAWE,KAAYF,EAAY,CACjC,MAAMG,EAAgBrB,EAAUoB,CAAQ,EAClCE,EAAiBJ,EAAWE,CAAQ,EACxB,WAAW,KAAKA,CAAQ,EAEpCC,GAAiBC,EACnBH,EAAcC,CAAQ,EAAI,IAAIG,IAAS,CACrC,MAAMC,EAASF,EAAe,GAAGC,CAAI,EACrC,OAAAF,EAAc,GAAGE,CAAI,EACdC,CACR,EACQH,IACTF,EAAcC,CAAQ,EAAIC,GAEnBD,IAAa,QACtBD,EAAcC,CAAQ,EAAI,CAAE,GAAGC,EAAe,GAAGC,CAAgB,EACxDF,IAAa,cACtBD,EAAcC,CAAQ,EAAI,CAACC,EAAeC,CAAc,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG,EAExF,CACE,MAAO,CAAE,GAAGtB,EAAW,GAAGmB,CAAe,CAC3C,CACA,SAASN,EAAcY,EAAS,SAC9B,IAAIC,GAASC,EAAA,OAAO,yBAAyBF,EAAQ,MAAO,KAAK,IAApD,YAAAE,EAAuD,IAChEC,EAAUF,GAAU,mBAAoBA,GAAUA,EAAO,eAC7D,OAAIE,EACKH,EAAQ,KAEjBC,GAASG,EAAA,OAAO,yBAAyBJ,EAAS,KAAK,IAA9C,YAAAI,EAAiD,IAC1DD,EAAUF,GAAU,mBAAoBA,GAAUA,EAAO,eACrDE,EACKH,EAAQ,MAAM,IAEhBA,EAAQ,MAAM,KAAOA,EAAQ,IACtC,CC3FA,IAAIK,EAAQ,CACV,IACA,SACA,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,SACA,OACA,MACA,IACF,EACIC,EAAYD,EAAM,OAAO,CAACE,EAAW9C,IAAS,CAChD,MAAMyB,EAAOpB,EAAW,aAAaL,CAAI,EAAE,EACrC+C,EAAOrC,EAAAA,WAAiB,CAACC,EAAOC,IAAiB,CACrD,KAAM,CAAE,QAAAoC,EAAS,GAAGC,CAAc,EAAKtC,EACjCuC,EAAOF,EAAUvB,EAAOzB,EAC9B,OAAI,OAAO,OAAW,MACpB,OAAO,OAAO,IAAI,UAAU,CAAC,EAAI,IAEZuB,EAAAA,IAAI2B,EAAM,CAAE,GAAGD,EAAgB,IAAKrC,EAAc,CAC7E,CAAG,EACD,OAAAmC,EAAK,YAAc,aAAa/C,CAAI,GAC7B,CAAE,GAAG8C,EAAW,CAAC9C,CAAI,EAAG+C,CAAM,CACvC,EAAG,CAAE,CAAA", "x_google_ignoreList": [0, 1, 2]}