{"version": 3, "file": "index-DchgA8mS.js", "sources": ["../../node_modules/react-dom/client.js", "../../src/services/queryClient.ts", "../../node_modules/clsx/dist/clsx.mjs", "../../node_modules/class-variance-authority/dist/index.mjs", "../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "../../src/utils/cn.ts", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/input.tsx", "../../node_modules/@radix-ui/react-label/dist/index.mjs", "../../src/components/ui/label.tsx", "../../node_modules/react-i18next/dist/es/utils.js", "../../node_modules/react-i18next/dist/es/unescape.js", "../../node_modules/react-i18next/dist/es/defaults.js", "../../node_modules/react-i18next/dist/es/i18nInstance.js", "../../node_modules/react-i18next/dist/es/initReactI18next.js", "../../node_modules/react-i18next/dist/es/context.js", "../../node_modules/react-i18next/dist/es/useTranslation.js", "../../node_modules/lucide-react/dist/esm/shared/src/utils.js", "../../node_modules/lucide-react/dist/esm/defaultAttributes.js", "../../node_modules/lucide-react/dist/esm/Icon.js", "../../node_modules/lucide-react/dist/esm/createLucideIcon.js", "../../node_modules/lucide-react/dist/esm/icons/arrow-left.js", "../../node_modules/lucide-react/dist/esm/icons/book-open.js", "../../node_modules/lucide-react/dist/esm/icons/clock.js", "../../node_modules/lucide-react/dist/esm/icons/github.js", "../../node_modules/lucide-react/dist/esm/icons/house.js", "../../node_modules/lucide-react/dist/esm/icons/mail.js", "../../node_modules/lucide-react/dist/esm/icons/menu.js", "../../node_modules/lucide-react/dist/esm/icons/search.js", "../../node_modules/lucide-react/dist/esm/icons/star.js", "../../node_modules/lucide-react/dist/esm/icons/trending-up.js", "../../node_modules/lucide-react/dist/esm/icons/twitter.js", "../../node_modules/lucide-react/dist/esm/icons/user.js", "../../node_modules/zustand/esm/vanilla.mjs", "../../node_modules/zustand/esm/react.mjs", "../../node_modules/zustand/esm/middleware.mjs", "../../src/stores/authStore.ts", "../../src/components/layout/Header.tsx", "../../src/components/layout/Footer.tsx", "../../src/components/layout/MainLayout.tsx", "../../src/components/layout/AuthLayout.tsx", "../../src/components/layout/ReaderLayout.tsx", "../../src/components/layout/AdminLayout.tsx", "../../src/pages/HomePage.tsx", "../../src/pages/LoginPage.tsx", "../../src/pages/BrowsePage.tsx", "../../src/pages/MangaDetailPage.tsx", "../../src/pages/ReaderPage.tsx", "../../src/pages/RegisterPage.tsx", "../../src/pages/BookmarksPage.tsx", "../../src/pages/HistoryPage.tsx", "../../src/pages/ProfilePage.tsx", "../../src/pages/NotFoundPage.tsx", "../../src/router/index.tsx", "../../node_modules/i18next/dist/esm/i18next.js", "../../node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js", "../../src/i18n/index.ts", "../../src/App.tsx", "../../src/main.tsx"], "sourcesContent": ["'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n", "import { QueryClient } from '@tanstack/react-query';\n\nexport const queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)\n      retry: (failureCount, error: any) => {\n        // Don't retry on 4xx errors\n        if (error?.status >= 400 && error?.status < 500) {\n          return false;\n        }\n        return failureCount < 3;\n      },\n      refetchOnWindowFocus: false,\n    },\n    mutations: {\n      retry: false,\n    },\n  },\n});\n", "function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;", "/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n", "const CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = config => {\n  const classMap = createClassMap(config);\n  const {\n    conflictingClassGroups,\n    conflictingClassGroupModifiers\n  } = config;\n  const getClassGroupId = className => {\n    const classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  };\n  const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier) => {\n    const conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]];\n    }\n    return conflicts;\n  };\n  return {\n    getClassGroupId,\n    getConflictingClassGroupIds\n  };\n};\nconst getGroupRecursive = (classParts, classPartObject) => {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  const currentClassPart = classParts[0];\n  const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  const classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(({\n    validator\n  }) => validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = className => {\n  if (arbitraryPropertyRegex.test(className)) {\n    const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n};\n/**\n * Exported for testing only\n */\nconst createClassMap = config => {\n  const {\n    theme,\n    classGroups\n  } = config;\n  const classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  for (const classGroupId in classGroups) {\n    processClassesRecursively(classGroups[classGroupId], classMap, classGroupId, theme);\n  }\n  return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme) => {\n  classGroup.forEach(classDefinition => {\n    if (typeof classDefinition === 'string') {\n      const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(([key, classGroup]) => {\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n};\nconst getPart = (classPartObject, path) => {\n  let currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(pathPart => {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n};\nconst isThemeGetter = func => func.isThemeGetter;\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = maxCacheSize => {\n  if (maxCacheSize < 1) {\n    return {\n      get: () => undefined,\n      set: () => {}\n    };\n  }\n  let cacheSize = 0;\n  let cache = new Map();\n  let previousCache = new Map();\n  const update = (key, value) => {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  };\n  return {\n    get(key) {\n      let value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst MODIFIER_SEPARATOR = ':';\nconst MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length;\nconst createParseClassName = config => {\n  const {\n    prefix,\n    experimentalParseClassName\n  } = config;\n  /**\n   * Parse class name into parts.\n   *\n   * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS\n   * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n   */\n  let parseClassName = className => {\n    const modifiers = [];\n    let bracketDepth = 0;\n    let parenDepth = 0;\n    let modifierStart = 0;\n    let postfixModifierPosition;\n    for (let index = 0; index < className.length; index++) {\n      let currentCharacter = className[index];\n      if (bracketDepth === 0 && parenDepth === 0) {\n        if (currentCharacter === MODIFIER_SEPARATOR) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + MODIFIER_SEPARATOR_LENGTH;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      } else if (currentCharacter === '(') {\n        parenDepth++;\n      } else if (currentCharacter === ')') {\n        parenDepth--;\n      }\n    }\n    const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier);\n    const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier;\n    const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    };\n  };\n  if (prefix) {\n    const fullPrefix = prefix + MODIFIER_SEPARATOR;\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => className.startsWith(fullPrefix) ? parseClassNameOriginal(className.substring(fullPrefix.length)) : {\n      isExternal: true,\n      modifiers: [],\n      hasImportantModifier: false,\n      baseClassName: className,\n      maybePostfixModifierPosition: undefined\n    };\n  }\n  if (experimentalParseClassName) {\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => experimentalParseClassName({\n      className,\n      parseClassName: parseClassNameOriginal\n    });\n  }\n  return parseClassName;\n};\nconst stripImportantModifier = baseClassName => {\n  if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(0, baseClassName.length - 1);\n  }\n  /**\n   * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.\n   * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864\n   */\n  if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(1);\n  }\n  return baseClassName;\n};\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nconst createSortModifiers = config => {\n  const orderSensitiveModifiers = Object.fromEntries(config.orderSensitiveModifiers.map(modifier => [modifier, true]));\n  const sortModifiers = modifiers => {\n    if (modifiers.length <= 1) {\n      return modifiers;\n    }\n    const sortedModifiers = [];\n    let unsortedModifiers = [];\n    modifiers.forEach(modifier => {\n      const isPositionSensitive = modifier[0] === '[' || orderSensitiveModifiers[modifier];\n      if (isPositionSensitive) {\n        sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n        unsortedModifiers = [];\n      } else {\n        unsortedModifiers.push(modifier);\n      }\n    });\n    sortedModifiers.push(...unsortedModifiers.sort());\n    return sortedModifiers;\n  };\n  return sortModifiers;\n};\nconst createConfigUtils = config => ({\n  cache: createLruCache(config.cacheSize),\n  parseClassName: createParseClassName(config),\n  sortModifiers: createSortModifiers(config),\n  ...createClassGroupUtils(config)\n});\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils) => {\n  const {\n    parseClassName,\n    getClassGroupId,\n    getConflictingClassGroupIds,\n    sortModifiers\n  } = configUtils;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  const classGroupsInConflict = [];\n  const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n  let result = '';\n  for (let index = classNames.length - 1; index >= 0; index -= 1) {\n    const originalClassName = classNames[index];\n    const {\n      isExternal,\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    } = parseClassName(originalClassName);\n    if (isExternal) {\n      result = originalClassName + (result.length > 0 ? ' ' + result : result);\n      continue;\n    }\n    let hasPostfixModifier = !!maybePostfixModifierPosition;\n    let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    if (!classGroupId) {\n      if (!hasPostfixModifier) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      hasPostfixModifier = false;\n    }\n    const variantModifier = sortModifiers(modifiers).join(':');\n    const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    const classId = modifierId + classGroupId;\n    if (classGroupsInConflict.includes(classId)) {\n      // Tailwind class omitted due to conflict\n      continue;\n    }\n    classGroupsInConflict.push(classId);\n    const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n    for (let i = 0; i < conflictGroups.length; ++i) {\n      const group = conflictGroups[i];\n      classGroupsInConflict.push(modifierId + group);\n    }\n    // Tailwind class not in conflict\n    result = originalClassName + (result.length > 0 ? ' ' + result : result);\n  }\n  return result;\n};\n\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  let index = 0;\n  let argument;\n  let resolvedValue;\n  let string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nconst toValue = mix => {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  let resolvedValue;\n  let string = '';\n  for (let k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n  let configUtils;\n  let cacheGet;\n  let cacheSet;\n  let functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    const config = createConfigRest.reduce((previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig), createConfigFirst());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    const cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    const result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nconst fromTheme = key => {\n  const themeGetter = theme => theme[key] || [];\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:(\\w[\\w-]*):)?(.+)\\]$/i;\nconst arbitraryVariableRegex = /^\\((?:(\\w[\\w-]*):)?(.+)\\)$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isFraction = value => fractionRegex.test(value);\nconst isNumber = value => !!value && !Number.isNaN(Number(value));\nconst isInteger = value => !!value && Number.isInteger(Number(value));\nconst isPercent = value => value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isTshirtSize = value => tshirtUnitRegex.test(value);\nconst isAny = () => true;\nconst isLengthOnly = value =>\n// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n// For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n// I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\nlengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = () => false;\nconst isShadow = value => shadowRegex.test(value);\nconst isImage = value => imageRegex.test(value);\nconst isAnyNonArbitrary = value => !isArbitraryValue(value) && !isArbitraryVariable(value);\nconst isArbitrarySize = value => getIsArbitraryValue(value, isLabelSize, isNever);\nconst isArbitraryValue = value => arbitraryValueRegex.test(value);\nconst isArbitraryLength = value => getIsArbitraryValue(value, isLabelLength, isLengthOnly);\nconst isArbitraryNumber = value => getIsArbitraryValue(value, isLabelNumber, isNumber);\nconst isArbitraryPosition = value => getIsArbitraryValue(value, isLabelPosition, isNever);\nconst isArbitraryImage = value => getIsArbitraryValue(value, isLabelImage, isImage);\nconst isArbitraryShadow = value => getIsArbitraryValue(value, isLabelShadow, isShadow);\nconst isArbitraryVariable = value => arbitraryVariableRegex.test(value);\nconst isArbitraryVariableLength = value => getIsArbitraryVariable(value, isLabelLength);\nconst isArbitraryVariableFamilyName = value => getIsArbitraryVariable(value, isLabelFamilyName);\nconst isArbitraryVariablePosition = value => getIsArbitraryVariable(value, isLabelPosition);\nconst isArbitraryVariableSize = value => getIsArbitraryVariable(value, isLabelSize);\nconst isArbitraryVariableImage = value => getIsArbitraryVariable(value, isLabelImage);\nconst isArbitraryVariableShadow = value => getIsArbitraryVariable(value, isLabelShadow, true);\n// Helpers\nconst getIsArbitraryValue = (value, testLabel, testValue) => {\n  const result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return testValue(result[2]);\n  }\n  return false;\n};\nconst getIsArbitraryVariable = (value, testLabel, shouldMatchNoLabel = false) => {\n  const result = arbitraryVariableRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return shouldMatchNoLabel;\n  }\n  return false;\n};\n// Labels\nconst isLabelPosition = label => label === 'position' || label === 'percentage';\nconst isLabelImage = label => label === 'image' || label === 'url';\nconst isLabelSize = label => label === 'length' || label === 'size' || label === 'bg-size';\nconst isLabelLength = label => label === 'length';\nconst isLabelNumber = label => label === 'number';\nconst isLabelFamilyName = label => label === 'family-name';\nconst isLabelShadow = label => label === 'shadow';\nconst validators = /*#__PURE__*/Object.defineProperty({\n  __proto__: null,\n  isAny,\n  isAnyNonArbitrary,\n  isArbitraryImage,\n  isArbitraryLength,\n  isArbitraryNumber,\n  isArbitraryPosition,\n  isArbitraryShadow,\n  isArbitrarySize,\n  isArbitraryValue,\n  isArbitraryVariable,\n  isArbitraryVariableFamilyName,\n  isArbitraryVariableImage,\n  isArbitraryVariableLength,\n  isArbitraryVariablePosition,\n  isArbitraryVariableShadow,\n  isArbitraryVariableSize,\n  isFraction,\n  isInteger,\n  isNumber,\n  isPercent,\n  isTshirtSize\n}, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getDefaultConfig = () => {\n  /**\n   * Theme getters for theme variable namespaces\n   * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces\n   */\n  /***/\n  const themeColor = fromTheme('color');\n  const themeFont = fromTheme('font');\n  const themeText = fromTheme('text');\n  const themeFontWeight = fromTheme('font-weight');\n  const themeTracking = fromTheme('tracking');\n  const themeLeading = fromTheme('leading');\n  const themeBreakpoint = fromTheme('breakpoint');\n  const themeContainer = fromTheme('container');\n  const themeSpacing = fromTheme('spacing');\n  const themeRadius = fromTheme('radius');\n  const themeShadow = fromTheme('shadow');\n  const themeInsetShadow = fromTheme('inset-shadow');\n  const themeTextShadow = fromTheme('text-shadow');\n  const themeDropShadow = fromTheme('drop-shadow');\n  const themeBlur = fromTheme('blur');\n  const themePerspective = fromTheme('perspective');\n  const themeAspect = fromTheme('aspect');\n  const themeEase = fromTheme('ease');\n  const themeAnimate = fromTheme('animate');\n  /**\n   * Helpers to avoid repeating the same scales\n   *\n   * We use functions that create a new array every time they're called instead of static arrays.\n   * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.\n   */\n  /***/\n  const scaleBreak = () => ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'];\n  const scalePosition = () => ['center', 'top', 'bottom', 'left', 'right', 'top-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-top', 'top-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-top', 'bottom-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-bottom', 'bottom-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-bottom'];\n  const scalePositionWithArbitrary = () => [...scalePosition(), isArbitraryVariable, isArbitraryValue];\n  const scaleOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'];\n  const scaleOverscroll = () => ['auto', 'contain', 'none'];\n  const scaleUnambiguousSpacing = () => [isArbitraryVariable, isArbitraryValue, themeSpacing];\n  const scaleInset = () => [isFraction, 'full', 'auto', ...scaleUnambiguousSpacing()];\n  const scaleGridTemplateColsRows = () => [isInteger, 'none', 'subgrid', isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartAndEnd = () => ['auto', {\n    span: ['full', isInteger, isArbitraryVariable, isArbitraryValue]\n  }, isInteger, isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartOrEnd = () => [isInteger, 'auto', isArbitraryVariable, isArbitraryValue];\n  const scaleGridAutoColsRows = () => ['auto', 'min', 'max', 'fr', isArbitraryVariable, isArbitraryValue];\n  const scaleAlignPrimaryAxis = () => ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch', 'baseline', 'center-safe', 'end-safe'];\n  const scaleAlignSecondaryAxis = () => ['start', 'end', 'center', 'stretch', 'center-safe', 'end-safe'];\n  const scaleMargin = () => ['auto', ...scaleUnambiguousSpacing()];\n  const scaleSizing = () => [isFraction, 'auto', 'full', 'dvw', 'dvh', 'lvw', 'lvh', 'svw', 'svh', 'min', 'max', 'fit', ...scaleUnambiguousSpacing()];\n  const scaleColor = () => [themeColor, isArbitraryVariable, isArbitraryValue];\n  const scaleBgPosition = () => [...scalePosition(), isArbitraryVariablePosition, isArbitraryPosition, {\n    position: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleBgRepeat = () => ['no-repeat', {\n    repeat: ['', 'x', 'y', 'space', 'round']\n  }];\n  const scaleBgSize = () => ['auto', 'cover', 'contain', isArbitraryVariableSize, isArbitrarySize, {\n    size: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleGradientStopPosition = () => [isPercent, isArbitraryVariableLength, isArbitraryLength];\n  const scaleRadius = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', 'full', themeRadius, isArbitraryVariable, isArbitraryValue];\n  const scaleBorderWidth = () => ['', isNumber, isArbitraryVariableLength, isArbitraryLength];\n  const scaleLineStyle = () => ['solid', 'dashed', 'dotted', 'double'];\n  const scaleBlendMode = () => ['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten', 'color-dodge', 'color-burn', 'hard-light', 'soft-light', 'difference', 'exclusion', 'hue', 'saturation', 'color', 'luminosity'];\n  const scaleMaskImagePosition = () => [isNumber, isPercent, isArbitraryVariablePosition, isArbitraryPosition];\n  const scaleBlur = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', themeBlur, isArbitraryVariable, isArbitraryValue];\n  const scaleRotate = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleScale = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleSkew = () => [isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleTranslate = () => [isFraction, 'full', ...scaleUnambiguousSpacing()];\n  return {\n    cacheSize: 500,\n    theme: {\n      animate: ['spin', 'ping', 'pulse', 'bounce'],\n      aspect: ['video'],\n      blur: [isTshirtSize],\n      breakpoint: [isTshirtSize],\n      color: [isAny],\n      container: [isTshirtSize],\n      'drop-shadow': [isTshirtSize],\n      ease: ['in', 'out', 'in-out'],\n      font: [isAnyNonArbitrary],\n      'font-weight': ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black'],\n      'inset-shadow': [isTshirtSize],\n      leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose'],\n      perspective: ['dramatic', 'near', 'normal', 'midrange', 'distant', 'none'],\n      radius: [isTshirtSize],\n      shadow: [isTshirtSize],\n      spacing: ['px', isNumber],\n      text: [isTshirtSize],\n      'text-shadow': [isTshirtSize],\n      tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest']\n    },\n    classGroups: {\n      // --------------\n      // --- Layout ---\n      // --------------\n      /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */\n      aspect: [{\n        aspect: ['auto', 'square', isFraction, isArbitraryValue, isArbitraryVariable, themeAspect]\n      }],\n      /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       * @deprecated since Tailwind CSS v4.0.0\n       */\n      container: ['container'],\n      /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */\n      columns: [{\n        columns: [isNumber, isArbitraryValue, isArbitraryVariable, themeContainer]\n      }],\n      /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */\n      'break-after': [{\n        'break-after': scaleBreak()\n      }],\n      /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */\n      'break-before': [{\n        'break-before': scaleBreak()\n      }],\n      /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */\n      'break-inside': [{\n        'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column']\n      }],\n      /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */\n      'box-decoration': [{\n        'box-decoration': ['slice', 'clone']\n      }],\n      /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */\n      box: [{\n        box: ['border', 'content']\n      }],\n      /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */\n      display: ['block', 'inline-block', 'inline', 'flex', 'inline-flex', 'table', 'inline-table', 'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row-group', 'table-row', 'flow-root', 'grid', 'inline-grid', 'contents', 'list-item', 'hidden'],\n      /**\n       * Screen Reader Only\n       * @see https://tailwindcss.com/docs/display#screen-reader-only\n       */\n      sr: ['sr-only', 'not-sr-only'],\n      /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */\n      float: [{\n        float: ['right', 'left', 'none', 'start', 'end']\n      }],\n      /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */\n      clear: [{\n        clear: ['left', 'right', 'both', 'none', 'start', 'end']\n      }],\n      /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */\n      isolation: ['isolate', 'isolation-auto'],\n      /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */\n      'object-fit': [{\n        object: ['contain', 'cover', 'fill', 'none', 'scale-down']\n      }],\n      /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */\n      'object-position': [{\n        object: scalePositionWithArbitrary()\n      }],\n      /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      overflow: [{\n        overflow: scaleOverflow()\n      }],\n      /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-x': [{\n        'overflow-x': scaleOverflow()\n      }],\n      /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-y': [{\n        'overflow-y': scaleOverflow()\n      }],\n      /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      overscroll: [{\n        overscroll: scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-x': [{\n        'overscroll-x': scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-y': [{\n        'overscroll-y': scaleOverscroll()\n      }],\n      /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */\n      position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n      /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      inset: [{\n        inset: scaleInset()\n      }],\n      /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-x': [{\n        'inset-x': scaleInset()\n      }],\n      /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-y': [{\n        'inset-y': scaleInset()\n      }],\n      /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      start: [{\n        start: scaleInset()\n      }],\n      /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      end: [{\n        end: scaleInset()\n      }],\n      /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      top: [{\n        top: scaleInset()\n      }],\n      /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      right: [{\n        right: scaleInset()\n      }],\n      /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      bottom: [{\n        bottom: scaleInset()\n      }],\n      /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      left: [{\n        left: scaleInset()\n      }],\n      /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */\n      visibility: ['visible', 'invisible', 'collapse'],\n      /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */\n      z: [{\n        z: [isInteger, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------------\n      // --- Flexbox and Grid ---\n      // ------------------------\n      /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */\n      basis: [{\n        basis: [isFraction, 'full', 'auto', themeContainer, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */\n      'flex-direction': [{\n        flex: ['row', 'row-reverse', 'col', 'col-reverse']\n      }],\n      /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */\n      'flex-wrap': [{\n        flex: ['nowrap', 'wrap', 'wrap-reverse']\n      }],\n      /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */\n      flex: [{\n        flex: [isNumber, isFraction, 'auto', 'initial', 'none', isArbitraryValue]\n      }],\n      /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */\n      grow: [{\n        grow: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */\n      shrink: [{\n        shrink: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */\n      order: [{\n        order: [isInteger, 'first', 'last', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */\n      'grid-cols': [{\n        'grid-cols': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start-end': [{\n        col: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start': [{\n        'col-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-end': [{\n        'col-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */\n      'grid-rows': [{\n        'grid-rows': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start-end': [{\n        row: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start': [{\n        'row-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-end': [{\n        'row-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */\n      'grid-flow': [{\n        'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense']\n      }],\n      /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */\n      'auto-cols': [{\n        'auto-cols': scaleGridAutoColsRows()\n      }],\n      /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */\n      'auto-rows': [{\n        'auto-rows': scaleGridAutoColsRows()\n      }],\n      /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */\n      gap: [{\n        gap: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-x': [{\n        'gap-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-y': [{\n        'gap-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */\n      'justify-content': [{\n        justify: [...scaleAlignPrimaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */\n      'justify-items': [{\n        'justify-items': [...scaleAlignSecondaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */\n      'justify-self': [{\n        'justify-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */\n      'align-content': [{\n        content: ['normal', ...scaleAlignPrimaryAxis()]\n      }],\n      /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */\n      'align-items': [{\n        items: [...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */\n      'align-self': [{\n        self: ['auto', ...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */\n      'place-content': [{\n        'place-content': scaleAlignPrimaryAxis()\n      }],\n      /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */\n      'place-items': [{\n        'place-items': [...scaleAlignSecondaryAxis(), 'baseline']\n      }],\n      /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */\n      'place-self': [{\n        'place-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      // Spacing\n      /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */\n      p: [{\n        p: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */\n      px: [{\n        px: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */\n      py: [{\n        py: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */\n      ps: [{\n        ps: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pe: [{\n        pe: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pt: [{\n        pt: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pr: [{\n        pr: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pb: [{\n        pb: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pl: [{\n        pl: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */\n      m: [{\n        m: scaleMargin()\n      }],\n      /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mx: [{\n        mx: scaleMargin()\n      }],\n      /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */\n      my: [{\n        my: scaleMargin()\n      }],\n      /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ms: [{\n        ms: scaleMargin()\n      }],\n      /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */\n      me: [{\n        me: scaleMargin()\n      }],\n      /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mt: [{\n        mt: scaleMargin()\n      }],\n      /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mr: [{\n        mr: scaleMargin()\n      }],\n      /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mb: [{\n        mb: scaleMargin()\n      }],\n      /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ml: [{\n        ml: scaleMargin()\n      }],\n      /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x': [{\n        'space-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x-reverse': ['space-x-reverse'],\n      /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y': [{\n        'space-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y-reverse': ['space-y-reverse'],\n      // --------------\n      // --- Sizing ---\n      // --------------\n      /**\n       * Size\n       * @see https://tailwindcss.com/docs/width#setting-both-width-and-height\n       */\n      size: [{\n        size: scaleSizing()\n      }],\n      /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */\n      w: [{\n        w: [themeContainer, 'screen', ...scaleSizing()]\n      }],\n      /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */\n      'min-w': [{\n        'min-w': [themeContainer, 'screen', /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */\n      'max-w': [{\n        'max-w': [themeContainer, 'screen', 'none', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'prose', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        {\n          screen: [themeBreakpoint]\n        }, ...scaleSizing()]\n      }],\n      /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */\n      h: [{\n        h: ['screen', 'lh', ...scaleSizing()]\n      }],\n      /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */\n      'min-h': [{\n        'min-h': ['screen', 'lh', 'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */\n      'max-h': [{\n        'max-h': ['screen', 'lh', ...scaleSizing()]\n      }],\n      // ------------------\n      // --- Typography ---\n      // ------------------\n      /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */\n      'font-size': [{\n        text: ['base', themeText, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */\n      'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n      /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */\n      'font-style': ['italic', 'not-italic'],\n      /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */\n      'font-weight': [{\n        font: [themeFontWeight, isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Font Stretch\n       * @see https://tailwindcss.com/docs/font-stretch\n       */\n      'font-stretch': [{\n        'font-stretch': ['ultra-condensed', 'extra-condensed', 'condensed', 'semi-condensed', 'normal', 'semi-expanded', 'expanded', 'extra-expanded', 'ultra-expanded', isPercent, isArbitraryValue]\n      }],\n      /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */\n      'font-family': [{\n        font: [isArbitraryVariableFamilyName, isArbitraryValue, themeFont]\n      }],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-normal': ['normal-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-ordinal': ['ordinal'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-slashed-zero': ['slashed-zero'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n      /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */\n      tracking: [{\n        tracking: [themeTracking, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */\n      'line-clamp': [{\n        'line-clamp': [isNumber, 'none', isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */\n      leading: [{\n        leading: [/** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        themeLeading, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */\n      'list-image': [{\n        'list-image': ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */\n      'list-style-position': [{\n        list: ['inside', 'outside']\n      }],\n      /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */\n      'list-style-type': [{\n        list: ['disc', 'decimal', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */\n      'text-alignment': [{\n        text: ['left', 'center', 'right', 'justify', 'start', 'end']\n      }],\n      /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://v3.tailwindcss.com/docs/placeholder-color\n       */\n      'placeholder-color': [{\n        placeholder: scaleColor()\n      }],\n      /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */\n      'text-color': [{\n        text: scaleColor()\n      }],\n      /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */\n      'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n      /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */\n      'text-decoration-style': [{\n        decoration: [...scaleLineStyle(), 'wavy']\n      }],\n      /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */\n      'text-decoration-thickness': [{\n        decoration: [isNumber, 'from-font', 'auto', isArbitraryVariable, isArbitraryLength]\n      }],\n      /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */\n      'text-decoration-color': [{\n        decoration: scaleColor()\n      }],\n      /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */\n      'underline-offset': [{\n        'underline-offset': [isNumber, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */\n      'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n      /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */\n      'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n      /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */\n      'text-wrap': [{\n        text: ['wrap', 'nowrap', 'balance', 'pretty']\n      }],\n      /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */\n      indent: [{\n        indent: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */\n      'vertical-align': [{\n        align: ['baseline', 'top', 'middle', 'bottom', 'text-top', 'text-bottom', 'sub', 'super', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */\n      whitespace: [{\n        whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces']\n      }],\n      /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */\n      break: [{\n        break: ['normal', 'words', 'all', 'keep']\n      }],\n      /**\n       * Overflow Wrap\n       * @see https://tailwindcss.com/docs/overflow-wrap\n       */\n      wrap: [{\n        wrap: ['break-word', 'anywhere', 'normal']\n      }],\n      /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */\n      hyphens: [{\n        hyphens: ['none', 'manual', 'auto']\n      }],\n      /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */\n      content: [{\n        content: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -------------------\n      // --- Backgrounds ---\n      // -------------------\n      /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */\n      'bg-attachment': [{\n        bg: ['fixed', 'local', 'scroll']\n      }],\n      /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */\n      'bg-clip': [{\n        'bg-clip': ['border', 'padding', 'content', 'text']\n      }],\n      /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */\n      'bg-origin': [{\n        'bg-origin': ['border', 'padding', 'content']\n      }],\n      /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */\n      'bg-position': [{\n        bg: scaleBgPosition()\n      }],\n      /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */\n      'bg-repeat': [{\n        bg: scaleBgRepeat()\n      }],\n      /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */\n      'bg-size': [{\n        bg: scaleBgSize()\n      }],\n      /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */\n      'bg-image': [{\n        bg: ['none', {\n          linear: [{\n            to: ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl']\n          }, isInteger, isArbitraryVariable, isArbitraryValue],\n          radial: ['', isArbitraryVariable, isArbitraryValue],\n          conic: [isInteger, isArbitraryVariable, isArbitraryValue]\n        }, isArbitraryVariableImage, isArbitraryImage]\n      }],\n      /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */\n      'bg-color': [{\n        bg: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from-pos': [{\n        from: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via-pos': [{\n        via: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to-pos': [{\n        to: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from': [{\n        from: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via': [{\n        via: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to': [{\n        to: scaleColor()\n      }],\n      // ---------------\n      // --- Borders ---\n      // ---------------\n      /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      rounded: [{\n        rounded: scaleRadius()\n      }],\n      /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-s': [{\n        'rounded-s': scaleRadius()\n      }],\n      /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-e': [{\n        'rounded-e': scaleRadius()\n      }],\n      /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-t': [{\n        'rounded-t': scaleRadius()\n      }],\n      /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-r': [{\n        'rounded-r': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-b': [{\n        'rounded-b': scaleRadius()\n      }],\n      /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-l': [{\n        'rounded-l': scaleRadius()\n      }],\n      /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ss': [{\n        'rounded-ss': scaleRadius()\n      }],\n      /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-se': [{\n        'rounded-se': scaleRadius()\n      }],\n      /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ee': [{\n        'rounded-ee': scaleRadius()\n      }],\n      /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-es': [{\n        'rounded-es': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tl': [{\n        'rounded-tl': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tr': [{\n        'rounded-tr': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-br': [{\n        'rounded-br': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-bl': [{\n        'rounded-bl': scaleRadius()\n      }],\n      /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w': [{\n        border: scaleBorderWidth()\n      }],\n      /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-x': [{\n        'border-x': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-y': [{\n        'border-y': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-s': [{\n        'border-s': scaleBorderWidth()\n      }],\n      /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-e': [{\n        'border-e': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-t': [{\n        'border-t': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-r': [{\n        'border-r': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-b': [{\n        'border-b': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-l': [{\n        'border-l': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x': [{\n        'divide-x': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x-reverse': ['divide-x-reverse'],\n      /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y': [{\n        'divide-y': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y-reverse': ['divide-y-reverse'],\n      /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */\n      'border-style': [{\n        border: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style\n       */\n      'divide-style': [{\n        divide: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color': [{\n        border: scaleColor()\n      }],\n      /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-x': [{\n        'border-x': scaleColor()\n      }],\n      /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-y': [{\n        'border-y': scaleColor()\n      }],\n      /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-s': [{\n        'border-s': scaleColor()\n      }],\n      /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-e': [{\n        'border-e': scaleColor()\n      }],\n      /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-t': [{\n        'border-t': scaleColor()\n      }],\n      /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-r': [{\n        'border-r': scaleColor()\n      }],\n      /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-b': [{\n        'border-b': scaleColor()\n      }],\n      /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-l': [{\n        'border-l': scaleColor()\n      }],\n      /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */\n      'divide-color': [{\n        divide: scaleColor()\n      }],\n      /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */\n      'outline-style': [{\n        outline: [...scaleLineStyle(), 'none', 'hidden']\n      }],\n      /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */\n      'outline-offset': [{\n        'outline-offset': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */\n      'outline-w': [{\n        outline: ['', isNumber, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */\n      'outline-color': [{\n        outline: scaleColor()\n      }],\n      // ---------------\n      // --- Effects ---\n      // ---------------\n      /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */\n      shadow: [{\n        shadow: [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color\n       */\n      'shadow-color': [{\n        shadow: scaleColor()\n      }],\n      /**\n       * Inset Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow\n       */\n      'inset-shadow': [{\n        'inset-shadow': ['none', themeInsetShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Inset Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color\n       */\n      'inset-shadow-color': [{\n        'inset-shadow': scaleColor()\n      }],\n      /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring\n       */\n      'ring-w': [{\n        ring: scaleBorderWidth()\n      }],\n      /**\n       * Ring Width Inset\n       * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-w-inset': ['ring-inset'],\n      /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color\n       */\n      'ring-color': [{\n        ring: scaleColor()\n      }],\n      /**\n       * Ring Offset Width\n       * @see https://v3.tailwindcss.com/docs/ring-offset-width\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-w': [{\n        'ring-offset': [isNumber, isArbitraryLength]\n      }],\n      /**\n       * Ring Offset Color\n       * @see https://v3.tailwindcss.com/docs/ring-offset-color\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-color': [{\n        'ring-offset': scaleColor()\n      }],\n      /**\n       * Inset Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring\n       */\n      'inset-ring-w': [{\n        'inset-ring': scaleBorderWidth()\n      }],\n      /**\n       * Inset Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color\n       */\n      'inset-ring-color': [{\n        'inset-ring': scaleColor()\n      }],\n      /**\n       * Text Shadow\n       * @see https://tailwindcss.com/docs/text-shadow\n       */\n      'text-shadow': [{\n        'text-shadow': ['none', themeTextShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Text Shadow Color\n       * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color\n       */\n      'text-shadow-color': [{\n        'text-shadow': scaleColor()\n      }],\n      /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */\n      opacity: [{\n        opacity: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */\n      'mix-blend': [{\n        'mix-blend': [...scaleBlendMode(), 'plus-darker', 'plus-lighter']\n      }],\n      /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */\n      'bg-blend': [{\n        'bg-blend': scaleBlendMode()\n      }],\n      /**\n       * Mask Clip\n       * @see https://tailwindcss.com/docs/mask-clip\n       */\n      'mask-clip': [{\n        'mask-clip': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }, 'mask-no-clip'],\n      /**\n       * Mask Composite\n       * @see https://tailwindcss.com/docs/mask-composite\n       */\n      'mask-composite': [{\n        mask: ['add', 'subtract', 'intersect', 'exclude']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image-linear-pos': [{\n        'mask-linear': [isNumber]\n      }],\n      'mask-image-linear-from-pos': [{\n        'mask-linear-from': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-to-pos': [{\n        'mask-linear-to': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-from-color': [{\n        'mask-linear-from': scaleColor()\n      }],\n      'mask-image-linear-to-color': [{\n        'mask-linear-to': scaleColor()\n      }],\n      'mask-image-t-from-pos': [{\n        'mask-t-from': scaleMaskImagePosition()\n      }],\n      'mask-image-t-to-pos': [{\n        'mask-t-to': scaleMaskImagePosition()\n      }],\n      'mask-image-t-from-color': [{\n        'mask-t-from': scaleColor()\n      }],\n      'mask-image-t-to-color': [{\n        'mask-t-to': scaleColor()\n      }],\n      'mask-image-r-from-pos': [{\n        'mask-r-from': scaleMaskImagePosition()\n      }],\n      'mask-image-r-to-pos': [{\n        'mask-r-to': scaleMaskImagePosition()\n      }],\n      'mask-image-r-from-color': [{\n        'mask-r-from': scaleColor()\n      }],\n      'mask-image-r-to-color': [{\n        'mask-r-to': scaleColor()\n      }],\n      'mask-image-b-from-pos': [{\n        'mask-b-from': scaleMaskImagePosition()\n      }],\n      'mask-image-b-to-pos': [{\n        'mask-b-to': scaleMaskImagePosition()\n      }],\n      'mask-image-b-from-color': [{\n        'mask-b-from': scaleColor()\n      }],\n      'mask-image-b-to-color': [{\n        'mask-b-to': scaleColor()\n      }],\n      'mask-image-l-from-pos': [{\n        'mask-l-from': scaleMaskImagePosition()\n      }],\n      'mask-image-l-to-pos': [{\n        'mask-l-to': scaleMaskImagePosition()\n      }],\n      'mask-image-l-from-color': [{\n        'mask-l-from': scaleColor()\n      }],\n      'mask-image-l-to-color': [{\n        'mask-l-to': scaleColor()\n      }],\n      'mask-image-x-from-pos': [{\n        'mask-x-from': scaleMaskImagePosition()\n      }],\n      'mask-image-x-to-pos': [{\n        'mask-x-to': scaleMaskImagePosition()\n      }],\n      'mask-image-x-from-color': [{\n        'mask-x-from': scaleColor()\n      }],\n      'mask-image-x-to-color': [{\n        'mask-x-to': scaleColor()\n      }],\n      'mask-image-y-from-pos': [{\n        'mask-y-from': scaleMaskImagePosition()\n      }],\n      'mask-image-y-to-pos': [{\n        'mask-y-to': scaleMaskImagePosition()\n      }],\n      'mask-image-y-from-color': [{\n        'mask-y-from': scaleColor()\n      }],\n      'mask-image-y-to-color': [{\n        'mask-y-to': scaleColor()\n      }],\n      'mask-image-radial': [{\n        'mask-radial': [isArbitraryVariable, isArbitraryValue]\n      }],\n      'mask-image-radial-from-pos': [{\n        'mask-radial-from': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-to-pos': [{\n        'mask-radial-to': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-from-color': [{\n        'mask-radial-from': scaleColor()\n      }],\n      'mask-image-radial-to-color': [{\n        'mask-radial-to': scaleColor()\n      }],\n      'mask-image-radial-shape': [{\n        'mask-radial': ['circle', 'ellipse']\n      }],\n      'mask-image-radial-size': [{\n        'mask-radial': [{\n          closest: ['side', 'corner'],\n          farthest: ['side', 'corner']\n        }]\n      }],\n      'mask-image-radial-pos': [{\n        'mask-radial-at': scalePosition()\n      }],\n      'mask-image-conic-pos': [{\n        'mask-conic': [isNumber]\n      }],\n      'mask-image-conic-from-pos': [{\n        'mask-conic-from': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-to-pos': [{\n        'mask-conic-to': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-from-color': [{\n        'mask-conic-from': scaleColor()\n      }],\n      'mask-image-conic-to-color': [{\n        'mask-conic-to': scaleColor()\n      }],\n      /**\n       * Mask Mode\n       * @see https://tailwindcss.com/docs/mask-mode\n       */\n      'mask-mode': [{\n        mask: ['alpha', 'luminance', 'match']\n      }],\n      /**\n       * Mask Origin\n       * @see https://tailwindcss.com/docs/mask-origin\n       */\n      'mask-origin': [{\n        'mask-origin': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }],\n      /**\n       * Mask Position\n       * @see https://tailwindcss.com/docs/mask-position\n       */\n      'mask-position': [{\n        mask: scaleBgPosition()\n      }],\n      /**\n       * Mask Repeat\n       * @see https://tailwindcss.com/docs/mask-repeat\n       */\n      'mask-repeat': [{\n        mask: scaleBgRepeat()\n      }],\n      /**\n       * Mask Size\n       * @see https://tailwindcss.com/docs/mask-size\n       */\n      'mask-size': [{\n        mask: scaleBgSize()\n      }],\n      /**\n       * Mask Type\n       * @see https://tailwindcss.com/docs/mask-type\n       */\n      'mask-type': [{\n        'mask-type': ['alpha', 'luminance']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image': [{\n        mask: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ---------------\n      // --- Filters ---\n      // ---------------\n      /**\n       * Filter\n       * @see https://tailwindcss.com/docs/filter\n       */\n      filter: [{\n        filter: [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */\n      blur: [{\n        blur: scaleBlur()\n      }],\n      /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */\n      brightness: [{\n        brightness: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */\n      contrast: [{\n        contrast: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */\n      'drop-shadow': [{\n        'drop-shadow': [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeDropShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Drop Shadow Color\n       * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color\n       */\n      'drop-shadow-color': [{\n        'drop-shadow': scaleColor()\n      }],\n      /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */\n      grayscale: [{\n        grayscale: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */\n      'hue-rotate': [{\n        'hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */\n      invert: [{\n        invert: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */\n      saturate: [{\n        saturate: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */\n      sepia: [{\n        sepia: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Filter\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */\n      'backdrop-filter': [{\n        'backdrop-filter': [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */\n      'backdrop-blur': [{\n        'backdrop-blur': scaleBlur()\n      }],\n      /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */\n      'backdrop-brightness': [{\n        'backdrop-brightness': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */\n      'backdrop-contrast': [{\n        'backdrop-contrast': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */\n      'backdrop-grayscale': [{\n        'backdrop-grayscale': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */\n      'backdrop-hue-rotate': [{\n        'backdrop-hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */\n      'backdrop-invert': [{\n        'backdrop-invert': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */\n      'backdrop-opacity': [{\n        'backdrop-opacity': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */\n      'backdrop-saturate': [{\n        'backdrop-saturate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */\n      'backdrop-sepia': [{\n        'backdrop-sepia': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      // --------------\n      // --- Tables ---\n      // --------------\n      /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */\n      'border-collapse': [{\n        border: ['collapse', 'separate']\n      }],\n      /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing': [{\n        'border-spacing': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-x': [{\n        'border-spacing-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-y': [{\n        'border-spacing-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */\n      'table-layout': [{\n        table: ['auto', 'fixed']\n      }],\n      /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */\n      caption: [{\n        caption: ['top', 'bottom']\n      }],\n      // ---------------------------------\n      // --- Transitions and Animation ---\n      // ---------------------------------\n      /**\n       * Transition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */\n      transition: [{\n        transition: ['', 'all', 'colors', 'opacity', 'shadow', 'transform', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Behavior\n       * @see https://tailwindcss.com/docs/transition-behavior\n       */\n      'transition-behavior': [{\n        transition: ['normal', 'discrete']\n      }],\n      /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */\n      duration: [{\n        duration: [isNumber, 'initial', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */\n      ease: [{\n        ease: ['linear', 'initial', themeEase, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */\n      delay: [{\n        delay: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */\n      animate: [{\n        animate: ['none', themeAnimate, isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------\n      // --- Transforms ---\n      // ------------------\n      /**\n       * Backface Visibility\n       * @see https://tailwindcss.com/docs/backface-visibility\n       */\n      backface: [{\n        backface: ['hidden', 'visible']\n      }],\n      /**\n       * Perspective\n       * @see https://tailwindcss.com/docs/perspective\n       */\n      perspective: [{\n        perspective: [themePerspective, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Perspective Origin\n       * @see https://tailwindcss.com/docs/perspective-origin\n       */\n      'perspective-origin': [{\n        'perspective-origin': scalePositionWithArbitrary()\n      }],\n      /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      rotate: [{\n        rotate: scaleRotate()\n      }],\n      /**\n       * Rotate X\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-x': [{\n        'rotate-x': scaleRotate()\n      }],\n      /**\n       * Rotate Y\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-y': [{\n        'rotate-y': scaleRotate()\n      }],\n      /**\n       * Rotate Z\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-z': [{\n        'rotate-z': scaleRotate()\n      }],\n      /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */\n      scale: [{\n        scale: scaleScale()\n      }],\n      /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-x': [{\n        'scale-x': scaleScale()\n      }],\n      /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-y': [{\n        'scale-y': scaleScale()\n      }],\n      /**\n       * Scale Z\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-z': [{\n        'scale-z': scaleScale()\n      }],\n      /**\n       * Scale 3D\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-3d': ['scale-3d'],\n      /**\n       * Skew\n       * @see https://tailwindcss.com/docs/skew\n       */\n      skew: [{\n        skew: scaleSkew()\n      }],\n      /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-x': [{\n        'skew-x': scaleSkew()\n      }],\n      /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-y': [{\n        'skew-y': scaleSkew()\n      }],\n      /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */\n      transform: [{\n        transform: [isArbitraryVariable, isArbitraryValue, '', 'none', 'gpu', 'cpu']\n      }],\n      /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */\n      'transform-origin': [{\n        origin: scalePositionWithArbitrary()\n      }],\n      /**\n       * Transform Style\n       * @see https://tailwindcss.com/docs/transform-style\n       */\n      'transform-style': [{\n        transform: ['3d', 'flat']\n      }],\n      /**\n       * Translate\n       * @see https://tailwindcss.com/docs/translate\n       */\n      translate: [{\n        translate: scaleTranslate()\n      }],\n      /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-x': [{\n        'translate-x': scaleTranslate()\n      }],\n      /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-y': [{\n        'translate-y': scaleTranslate()\n      }],\n      /**\n       * Translate Z\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-z': [{\n        'translate-z': scaleTranslate()\n      }],\n      /**\n       * Translate None\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-none': ['translate-none'],\n      // ---------------------\n      // --- Interactivity ---\n      // ---------------------\n      /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */\n      accent: [{\n        accent: scaleColor()\n      }],\n      /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */\n      appearance: [{\n        appearance: ['none', 'auto']\n      }],\n      /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */\n      'caret-color': [{\n        caret: scaleColor()\n      }],\n      /**\n       * Color Scheme\n       * @see https://tailwindcss.com/docs/color-scheme\n       */\n      'color-scheme': [{\n        scheme: ['normal', 'dark', 'light', 'light-dark', 'only-dark', 'only-light']\n      }],\n      /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */\n      cursor: [{\n        cursor: ['auto', 'default', 'pointer', 'wait', 'text', 'move', 'help', 'not-allowed', 'none', 'context-menu', 'progress', 'cell', 'crosshair', 'vertical-text', 'alias', 'copy', 'no-drop', 'grab', 'grabbing', 'all-scroll', 'col-resize', 'row-resize', 'n-resize', 'e-resize', 's-resize', 'w-resize', 'ne-resize', 'nw-resize', 'se-resize', 'sw-resize', 'ew-resize', 'ns-resize', 'nesw-resize', 'nwse-resize', 'zoom-in', 'zoom-out', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Field Sizing\n       * @see https://tailwindcss.com/docs/field-sizing\n       */\n      'field-sizing': [{\n        'field-sizing': ['fixed', 'content']\n      }],\n      /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */\n      'pointer-events': [{\n        'pointer-events': ['auto', 'none']\n      }],\n      /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */\n      resize: [{\n        resize: ['none', '', 'y', 'x']\n      }],\n      /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */\n      'scroll-behavior': [{\n        scroll: ['auto', 'smooth']\n      }],\n      /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-m': [{\n        'scroll-m': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mx': [{\n        'scroll-mx': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-my': [{\n        'scroll-my': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ms': [{\n        'scroll-ms': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-me': [{\n        'scroll-me': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mt': [{\n        'scroll-mt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mr': [{\n        'scroll-mr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mb': [{\n        'scroll-mb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ml': [{\n        'scroll-ml': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-p': [{\n        'scroll-p': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-px': [{\n        'scroll-px': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-py': [{\n        'scroll-py': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-ps': [{\n        'scroll-ps': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pe': [{\n        'scroll-pe': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pt': [{\n        'scroll-pt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pr': [{\n        'scroll-pr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pb': [{\n        'scroll-pb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pl': [{\n        'scroll-pl': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */\n      'snap-align': [{\n        snap: ['start', 'end', 'center', 'align-none']\n      }],\n      /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */\n      'snap-stop': [{\n        snap: ['normal', 'always']\n      }],\n      /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-type': [{\n        snap: ['none', 'x', 'y', 'both']\n      }],\n      /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-strictness': [{\n        snap: ['mandatory', 'proximity']\n      }],\n      /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      touch: [{\n        touch: ['auto', 'none', 'manipulation']\n      }],\n      /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-x': [{\n        'touch-pan': ['x', 'left', 'right']\n      }],\n      /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-y': [{\n        'touch-pan': ['y', 'up', 'down']\n      }],\n      /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-pz': ['touch-pinch-zoom'],\n      /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */\n      select: [{\n        select: ['none', 'text', 'all', 'auto']\n      }],\n      /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */\n      'will-change': [{\n        'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -----------\n      // --- SVG ---\n      // -----------\n      /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */\n      fill: [{\n        fill: ['none', ...scaleColor()]\n      }],\n      /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */\n      'stroke-w': [{\n        stroke: [isNumber, isArbitraryVariableLength, isArbitraryLength, isArbitraryNumber]\n      }],\n      /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */\n      stroke: [{\n        stroke: ['none', ...scaleColor()]\n      }],\n      // ---------------------\n      // --- Accessibility ---\n      // ---------------------\n      /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */\n      'forced-color-adjust': [{\n        'forced-color-adjust': ['auto', 'none']\n      }]\n    },\n    conflictingClassGroups: {\n      overflow: ['overflow-x', 'overflow-y'],\n      overscroll: ['overscroll-x', 'overscroll-y'],\n      inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n      'inset-x': ['right', 'left'],\n      'inset-y': ['top', 'bottom'],\n      flex: ['basis', 'grow', 'shrink'],\n      gap: ['gap-x', 'gap-y'],\n      p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n      px: ['pr', 'pl'],\n      py: ['pt', 'pb'],\n      m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n      mx: ['mr', 'ml'],\n      my: ['mt', 'mb'],\n      size: ['w', 'h'],\n      'font-size': ['leading'],\n      'fvn-normal': ['fvn-ordinal', 'fvn-slashed-zero', 'fvn-figure', 'fvn-spacing', 'fvn-fraction'],\n      'fvn-ordinal': ['fvn-normal'],\n      'fvn-slashed-zero': ['fvn-normal'],\n      'fvn-figure': ['fvn-normal'],\n      'fvn-spacing': ['fvn-normal'],\n      'fvn-fraction': ['fvn-normal'],\n      'line-clamp': ['display', 'overflow'],\n      rounded: ['rounded-s', 'rounded-e', 'rounded-t', 'rounded-r', 'rounded-b', 'rounded-l', 'rounded-ss', 'rounded-se', 'rounded-ee', 'rounded-es', 'rounded-tl', 'rounded-tr', 'rounded-br', 'rounded-bl'],\n      'rounded-s': ['rounded-ss', 'rounded-es'],\n      'rounded-e': ['rounded-se', 'rounded-ee'],\n      'rounded-t': ['rounded-tl', 'rounded-tr'],\n      'rounded-r': ['rounded-tr', 'rounded-br'],\n      'rounded-b': ['rounded-br', 'rounded-bl'],\n      'rounded-l': ['rounded-tl', 'rounded-bl'],\n      'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n      'border-w': ['border-w-x', 'border-w-y', 'border-w-s', 'border-w-e', 'border-w-t', 'border-w-r', 'border-w-b', 'border-w-l'],\n      'border-w-x': ['border-w-r', 'border-w-l'],\n      'border-w-y': ['border-w-t', 'border-w-b'],\n      'border-color': ['border-color-x', 'border-color-y', 'border-color-s', 'border-color-e', 'border-color-t', 'border-color-r', 'border-color-b', 'border-color-l'],\n      'border-color-x': ['border-color-r', 'border-color-l'],\n      'border-color-y': ['border-color-t', 'border-color-b'],\n      translate: ['translate-x', 'translate-y', 'translate-none'],\n      'translate-none': ['translate', 'translate-x', 'translate-y', 'translate-z'],\n      'scroll-m': ['scroll-mx', 'scroll-my', 'scroll-ms', 'scroll-me', 'scroll-mt', 'scroll-mr', 'scroll-mb', 'scroll-ml'],\n      'scroll-mx': ['scroll-mr', 'scroll-ml'],\n      'scroll-my': ['scroll-mt', 'scroll-mb'],\n      'scroll-p': ['scroll-px', 'scroll-py', 'scroll-ps', 'scroll-pe', 'scroll-pt', 'scroll-pr', 'scroll-pb', 'scroll-pl'],\n      'scroll-px': ['scroll-pr', 'scroll-pl'],\n      'scroll-py': ['scroll-pt', 'scroll-pb'],\n      touch: ['touch-x', 'touch-y', 'touch-pz'],\n      'touch-x': ['touch'],\n      'touch-y': ['touch'],\n      'touch-pz': ['touch']\n    },\n    conflictingClassGroupModifiers: {\n      'font-size': ['leading']\n    },\n    orderSensitiveModifiers: ['*', '**', 'after', 'backdrop', 'before', 'details-content', 'file', 'first-letter', 'first-line', 'marker', 'placeholder', 'selection']\n  };\n};\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nconst mergeConfigs = (baseConfig, {\n  cacheSize,\n  prefix,\n  experimentalParseClassName,\n  extend = {},\n  override = {}\n}) => {\n  overrideProperty(baseConfig, 'cacheSize', cacheSize);\n  overrideProperty(baseConfig, 'prefix', prefix);\n  overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n  overrideConfigProperties(baseConfig.theme, override.theme);\n  overrideConfigProperties(baseConfig.classGroups, override.classGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroupModifiers, override.conflictingClassGroupModifiers);\n  overrideProperty(baseConfig, 'orderSensitiveModifiers', override.orderSensitiveModifiers);\n  mergeConfigProperties(baseConfig.theme, extend.theme);\n  mergeConfigProperties(baseConfig.classGroups, extend.classGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroupModifiers, extend.conflictingClassGroupModifiers);\n  mergeArrayProperties(baseConfig, extend, 'orderSensitiveModifiers');\n  return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue) => {\n  if (overrideValue !== undefined) {\n    baseObject[overrideKey] = overrideValue;\n  }\n};\nconst overrideConfigProperties = (baseObject, overrideObject) => {\n  if (overrideObject) {\n    for (const key in overrideObject) {\n      overrideProperty(baseObject, key, overrideObject[key]);\n    }\n  }\n};\nconst mergeConfigProperties = (baseObject, mergeObject) => {\n  if (mergeObject) {\n    for (const key in mergeObject) {\n      mergeArrayProperties(baseObject, mergeObject, key);\n    }\n  }\n};\nconst mergeArrayProperties = (baseObject, mergeObject, key) => {\n  const mergeValue = mergeObject[key];\n  if (mergeValue !== undefined) {\n    baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue;\n  }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig) => typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(() => mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\nexport { createTailwindMerge, extendTailwindMerge, fromTheme, getDefaultConfig, mergeConfigs, twJoin, twMerge, validators };\n//# sourceMappingURL=bundle-mjs.mjs.map\n", "import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../utils/cn\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "import * as React from \"react\"\n\nimport { cn } from \"../../utils/cn\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n", "import * as React from \"react\"\n\nimport { cn } from \"../../utils/cn\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n", "\"use client\";\n\n// src/label.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Label\";\nvar Label = React.forwardRef((props, forwardedRef) => {\n  return /* @__PURE__ */ jsx(\n    Primitive.label,\n    {\n      ...props,\n      ref: forwardedRef,\n      onMouseDown: (event) => {\n        const target = event.target;\n        if (target.closest(\"button, input, select, textarea\")) return;\n        props.onMouseDown?.(event);\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }\n    }\n  );\n});\nLabel.displayName = NAME;\nvar Root = Label;\nexport {\n  Label,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../utils/cn\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n", "export const warn = (i18n, code, msg, rest) => {\n  const args = [msg, {\n    code,\n    ...(rest || {})\n  }];\n  if (i18n?.services?.logger?.forward) {\n    return i18n.services.logger.forward(args, 'warn', 'react-i18next::', true);\n  }\n  if (isString(args[0])) args[0] = `react-i18next:: ${args[0]}`;\n  if (i18n?.services?.logger?.warn) {\n    i18n.services.logger.warn(...args);\n  } else if (console?.warn) {\n    console.warn(...args);\n  }\n};\nconst alreadyWarned = {};\nexport const warnOnce = (i18n, code, msg, rest) => {\n  if (isString(msg) && alreadyWarned[msg]) return;\n  if (isString(msg)) alreadyWarned[msg] = new Date();\n  warn(i18n, code, msg, rest);\n};\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nexport const loadNamespaces = (i18n, ns, cb) => {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n};\nexport const loadLanguages = (i18n, lng, ns, cb) => {\n  if (isString(ns)) ns = [ns];\n  if (i18n.options.preload && i18n.options.preload.indexOf(lng) > -1) return loadNamespaces(i18n, ns, cb);\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n};\nexport const hasLoadedNamespace = (ns, i18n, options = {}) => {\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce(i18n, 'NO_LANGUAGES', 'i18n.languages were undefined or empty', {\n      languages: i18n.languages\n    });\n    return true;\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n?.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n};\nexport const getDisplayName = Component => Component.displayName || Component.name || (isString(Component) && Component.length > 0 ? Component : 'Unknown');\nexport const isString = obj => typeof obj === 'string';\nexport const isObject = obj => typeof obj === 'object' && obj !== null;", "const matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nexport const unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);", "import { unescape } from './unescape.js';\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape\n};\nexport const setDefaults = (options = {}) => {\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n};\nexport const getDefaults = () => defaultOptions;", "let i18nInstance;\nexport const setI18n = instance => {\n  i18nInstance = instance;\n};\nexport const getI18n = () => i18nInstance;", "import { setDefaults } from './defaults.js';\nimport { setI18n } from './i18nInstance.js';\nexport const initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    setDefaults(instance.options.react);\n    setI18n(instance);\n  }\n};", "import { createContext } from 'react';\nimport { getDefaults, setDefaults } from './defaults.js';\nimport { getI18n, setI18n } from './i18nInstance.js';\nimport { initReactI18next } from './initReactI18next.js';\nexport { getDefaults, setDefaults, getI18n, setI18n, initReactI18next };\nexport const I18nContext = createContext();\nexport class ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nexport const composeInitialProps = ForComponent => async ctx => {\n  const componentsInitialProps = (await ForComponent.getInitialProps?.(ctx)) ?? {};\n  const i18nInitialProps = getInitialProps();\n  return {\n    ...componentsInitialProps,\n    ...i18nInitialProps\n  };\n};\nexport const getInitialProps = () => {\n  const i18n = getI18n();\n  const namespaces = i18n.reportNamespaces?.getUsedNamespaces() ?? [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n};", "import { useState, useEffect, useContext, useRef, useCallback } from 'react';\nimport { getI18n, getDefaults, ReportNamespaces, I18nContext } from './context.js';\nimport { warnOnce, loadNamespaces, loadLanguages, hasLoadedNamespace, isString, isObject } from './utils.js';\nconst usePrevious = (value, ignore) => {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nconst alwaysNewT = (i18n, language, namespace, keyPrefix) => i18n.getFixedT(language, namespace, keyPrefix);\nconst useMemoizedT = (i18n, language, namespace, keyPrefix) => useCallback(alwaysNewT(i18n, language, namespace, keyPrefix), [i18n, language, namespace, keyPrefix]);\nexport const useTranslation = (ns, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new ReportNamespaces();\n  if (!i18n) {\n    warnOnce(i18n, 'NO_I18NEXT_INSTANCE', 'useTranslation: You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if (isString(optsOrDefaultValue)) return optsOrDefaultValue;\n      if (isObject(optsOrDefaultValue) && isString(optsOrDefaultValue.defaultValue)) return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react?.wait) warnOnce(i18n, 'DEPRECATED_OPTION', 'useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...getDefaults(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options?.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  i18n.reportNamespaces.addUsedNamespaces?.(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => hasLoadedNamespace(n, i18n, i18nOptions));\n  const memoGetT = useMemoizedT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const getT = () => memoGetT;\n  const getNewT = () => alwaysNewT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const [t, setT] = useState(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = useRef(true);\n  useEffect(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        loadLanguages(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      } else {\n        loadNamespaces(i18n, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getNewT);\n    }\n    const boundReset = () => {\n      if (isMounted.current) setT(getNewT);\n    };\n    if (bindI18n) i18n?.on(bindI18n, boundReset);\n    if (bindI18nStore) i18n?.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (i18n) bindI18n?.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  useEffect(() => {\n    if (isMounted.current && ready) {\n      setT(getT);\n    }\n  }, [i18n, keyPrefix, ready]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      loadLanguages(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      loadNamespaces(i18n, namespaces, () => resolve());\n    }\n  });\n};", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nconst toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string) => string.replace(\n  /^([A-Z])|[\\s-_]+(\\w)/g,\n  (match, p1, p2) => p2 ? p2.toUpperCase() : p1.toLowerCase()\n);\nconst toPascalCase = (string) => {\n  const camelCase = toCamelCase(string);\n  return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = (...classes) => classes.filter((className, index, array) => {\n  return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n}).join(\" \").trim();\nconst hasA11yProp = (props) => {\n  for (const prop in props) {\n    if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n      return true;\n    }\n  }\n};\n\nexport { hasA11yProp, mergeClasses, toCamelCase, toKebabCase, toPascalCase };\n//# sourceMappingURL=utils.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nvar defaultAttributes = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: 24,\n  height: 24,\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: 2,\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\"\n};\n\nexport { defaultAttributes as default };\n//# sourceMappingURL=defaultAttributes.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\n\nconst Icon = forwardRef(\n  ({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    className = \"\",\n    children,\n    iconNode,\n    ...rest\n  }, ref) => createElement(\n    \"svg\",\n    {\n      ref,\n      ...defaultAttributes,\n      width: size,\n      height: size,\n      stroke: color,\n      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      className: mergeClasses(\"lucide\", className),\n      ...!children && !hasA11yProp(rest) && { \"aria-hidden\": \"true\" },\n      ...rest\n    },\n    [\n      ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n      ...Array.isArray(children) ? children : [children]\n    ]\n  )\n);\n\nexport { Icon as default };\n//# sourceMappingURL=Icon.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\n\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(\n    ({ className, ...props }, ref) => createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className\n      ),\n      ...props\n    })\n  );\n  Component.displayName = toPascalCase(iconName);\n  return Component;\n};\n\nexport { createLucideIcon as default };\n//# sourceMappingURL=createLucideIcon.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m12 19-7-7 7-7\", key: \"1l729n\" }],\n  [\"path\", { d: \"M19 12H5\", key: \"x3x0zl\" }]\n];\nconst ArrowLeft = createLucideIcon(\"arrow-left\", __iconNode);\n\nexport { __iconNode, ArrowLeft as default };\n//# sourceMappingURL=arrow-left.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M12 7v14\", key: \"1akyts\" }],\n  [\n    \"path\",\n    {\n      d: \"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z\",\n      key: \"ruj8y\"\n    }\n  ]\n];\nconst BookOpen = createLucideIcon(\"book-open\", __iconNode);\n\nexport { __iconNode, BookOpen as default };\n//# sourceMappingURL=book-open.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"polyline\", { points: \"12 6 12 12 16 14\", key: \"68esgv\" }]\n];\nconst Clock = createLucideIcon(\"clock\", __iconNode);\n\nexport { __iconNode, Clock as default };\n//# sourceMappingURL=clock.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4\",\n      key: \"tonef\"\n    }\n  ],\n  [\"path\", { d: \"M9 18c-4.51 2-5-2-7-2\", key: \"9comsn\" }]\n];\nconst Github = createLucideIcon(\"github\", __iconNode);\n\nexport { __iconNode, Github as default };\n//# sourceMappingURL=github.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\", key: \"5wwlr5\" }],\n  [\n    \"path\",\n    {\n      d: \"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\",\n      key: \"1d0kgt\"\n    }\n  ]\n];\nconst House = createLucideIcon(\"house\", __iconNode);\n\nexport { __iconNode, House as default };\n//# sourceMappingURL=house.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7\", key: \"132q7q\" }],\n  [\"rect\", { x: \"2\", y: \"4\", width: \"20\", height: \"16\", rx: \"2\", key: \"izxlao\" }]\n];\nconst Mail = createLucideIcon(\"mail\", __iconNode);\n\nexport { __iconNode, Mail as default };\n//# sourceMappingURL=mail.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M4 12h16\", key: \"1lakjw\" }],\n  [\"path\", { d: \"M4 18h16\", key: \"19g7jn\" }],\n  [\"path\", { d: \"M4 6h16\", key: \"1o0s65\" }]\n];\nconst Menu = createLucideIcon(\"menu\", __iconNode);\n\nexport { __iconNode, Menu as default };\n//# sourceMappingURL=menu.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m21 21-4.34-4.34\", key: \"14j7rj\" }],\n  [\"circle\", { cx: \"11\", cy: \"11\", r: \"8\", key: \"4ej97u\" }]\n];\nconst Search = createLucideIcon(\"search\", __iconNode);\n\nexport { __iconNode, Search as default };\n//# sourceMappingURL=search.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z\",\n      key: \"r04s7s\"\n    }\n  ]\n];\nconst Star = createLucideIcon(\"star\", __iconNode);\n\nexport { __iconNode, Star as default };\n//# sourceMappingURL=star.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M16 7h6v6\", key: \"box55l\" }],\n  [\"path\", { d: \"m22 7-8.5 8.5-5-5L2 17\", key: \"1t1m79\" }]\n];\nconst TrendingUp = createLucideIcon(\"trending-up\", __iconNode);\n\nexport { __iconNode, TrendingUp as default };\n//# sourceMappingURL=trending-up.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z\",\n      key: \"pff0z6\"\n    }\n  ]\n];\nconst Twitter = createLucideIcon(\"twitter\", __iconNode);\n\nexport { __iconNode, Twitter as default };\n//# sourceMappingURL=twitter.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2\", key: \"975kel\" }],\n  [\"circle\", { cx: \"12\", cy: \"7\", r: \"4\", key: \"17ys0d\" }]\n];\nconst User = createLucideIcon(\"user\", __iconNode);\n\nexport { __iconNode, User as default };\n//# sourceMappingURL=user.js.map\n", "const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n", "import React from 'react';\nimport { createStore } from 'zustand/vanilla';\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = React.useSyncExternalStore(\n    api.subscribe,\n    () => selector(api.getState()),\n    () => selector(api.getInitialState())\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = createStore(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\n\nexport { create, useStore };\n", "const reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...args) => api.dispatch(...args), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst removeStoreFromTrackedConnections = (name, store) => {\n  if (store === void 0) return;\n  const connectionInfo = trackedConnections.get(name);\n  if (!connectionInfo) return;\n  delete connectionInfo.stores[store];\n  if (Object.keys(connectionInfo.stores).length === 0) {\n    trackedConnections.delete(name);\n  }\n};\nconst findCallerName = (stack) => {\n  var _a, _b;\n  if (!stack) return void 0;\n  const traceLines = stack.split(\"\\n\");\n  const apiSetStateLineIndex = traceLines.findIndex(\n    (traceLine) => traceLine.includes(\"api.setState\")\n  );\n  if (apiSetStateLineIndex < 0) return void 0;\n  const callerLine = ((_a = traceLines[apiSetStateLineIndex + 1]) == null ? void 0 : _a.trim()) || \"\";\n  return (_b = /.+ (.+) .+/.exec(callerLine)) == null ? void 0 : _b[1];\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extensionConnector) {\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const inferredActionType = findCallerName(new Error().stack);\n    const action = nameOrAction === void 0 ? { type: anonymousActionType || inferredActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  api.devtools = {\n    cleanup: () => {\n      if (connection && typeof connection.unsubscribe === \"function\") {\n        connection.unsubscribe();\n      }\n      removeStoreFromTrackedConnections(options.name, store);\n    }\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...args) => {\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && args[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...args);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, fn) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) fn(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nfunction combine(initialState, create) {\n  return (...args) => Object.assign({}, initialState, create(...args));\n}\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(name, JSON.stringify(newValue, options == null ? void 0 : options.replacer)),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persistImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            const migration = options.migrate(\n              deserializedStorageValue.state,\n              deserializedStorageValue.version\n            );\n            if (migration instanceof Promise) {\n              return migration.then((result) => [true, result]);\n            }\n            return [true, migration];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persist = persistImpl;\n\nexport { combine, createJSONStorage, devtools, persist, redux, subscribeWithSelector };\n", "import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport type { User, LoginCredentials } from '../types';\n\ninterface AuthState {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (credentials: LoginCredentials) => Promise<void>;\n  logout: () => void;\n  updateProfile: (data: Partial<User>) => Promise<void>;\n  setUser: (user: User | null) => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n\n      login: async (credentials: LoginCredentials) => {\n        set({ isLoading: true });\n        try {\n          // TODO: Implement actual API call\n          console.log('Login with:', credentials);\n          \n          // Mock user data\n          const mockUser: User = {\n            id: '1',\n            username: 'testuser',\n            email: credentials.email,\n            role: 'reader',\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n          };\n\n          set({ \n            user: mockUser, \n            isAuthenticated: true, \n            isLoading: false \n          });\n        } catch (error) {\n          console.error('Login failed:', error);\n          set({ isLoading: false });\n          throw error;\n        }\n      },\n\n      logout: () => {\n        set({ \n          user: null, \n          isAuthenticated: false \n        });\n      },\n\n      updateProfile: async (data: Partial<User>) => {\n        const { user } = get();\n        if (!user) return;\n\n        set({ isLoading: true });\n        try {\n          // TODO: Implement actual API call\n          const updatedUser = { ...user, ...data };\n          set({ \n            user: updatedUser, \n            isLoading: false \n          });\n        } catch (error) {\n          console.error('Profile update failed:', error);\n          set({ isLoading: false });\n          throw error;\n        }\n      },\n\n      setUser: (user: User | null) => {\n        set({ \n          user, \n          isAuthenticated: !!user \n        });\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({ \n        user: state.user, \n        isAuthenticated: state.isAuthenticated \n      }),\n    }\n  )\n);\n", "import React from \"react\";\nimport { <PERSON> } from \"react-router-dom\";\nimport { useTranslation } from \"react-i18next\";\nimport { Search, User, Menu, BookOpen } from \"lucide-react\";\nimport { Button } from \"../ui/button\";\nimport { Input } from \"../ui/input\";\nimport { useAuthStore } from \"../../stores/authStore\";\n\nexport const Header: React.FC = () => {\n  const { t } = useTranslation(\"common\");\n  const { user, isAuthenticated, logout } = useAuthStore();\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-16 items-center\">\n        {/* Logo */}\n        <Link to=\"/\" className=\"flex items-center space-x-2\">\n          <BookOpen className=\"h-6 w-6\" />\n          <span className=\"font-bold text-xl\">BlogTruyen</span>\n        </Link>\n\n        {/* Navigation */}\n        <nav className=\"hidden md:flex items-center space-x-6 ml-8\">\n          <Link\n            to=\"/\"\n            className=\"text-sm font-medium transition-colors hover:text-primary\"\n          >\n            {t(\"navigation.home\")}\n          </Link>\n          <Link\n            to=\"/browse\"\n            className=\"text-sm font-medium transition-colors hover:text-primary\"\n          >\n            {t(\"navigation.browse\")}\n          </Link>\n          {isAuthenticated && (\n            <>\n              <Link\n                to=\"/user/bookmarks\"\n                className=\"text-sm font-medium transition-colors hover:text-primary\"\n              >\n                {t(\"navigation.bookmarks\")}\n              </Link>\n              <Link\n                to=\"/user/history\"\n                className=\"text-sm font-medium transition-colors hover:text-primary\"\n              >\n                {t(\"navigation.history\")}\n              </Link>\n              {(user?.role === \"admin\" || user?.role === \"moderator\") && (\n                <Link\n                  to=\"/admin\"\n                  className=\"text-sm font-medium transition-colors hover:text-primary\"\n                >\n                  {t(\"navigation.admin\")}\n                </Link>\n              )}\n            </>\n          )}\n        </nav>\n\n        {/* Search */}\n        <div className=\"flex-1 flex justify-center px-4\">\n          <div className=\"relative w-full max-w-sm\">\n            <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\n            <Input placeholder={t(\"actions.search\")} className=\"pl-8\" />\n          </div>\n        </div>\n\n        {/* User Actions */}\n        <div className=\"flex items-center space-x-2\">\n          {isAuthenticated ? (\n            <div className=\"flex items-center space-x-2\">\n              <Link to=\"/user/profile\">\n                <Button variant=\"ghost\" size=\"sm\">\n                  <User className=\"h-4 w-4 mr-2\" />\n                  {user?.username}\n                </Button>\n              </Link>\n              <Button variant=\"ghost\" size=\"sm\" onClick={logout}>\n                {t(\"navigation.logout\")}\n              </Button>\n            </div>\n          ) : (\n            <div className=\"flex items-center space-x-2\">\n              <Link to=\"/auth/login\">\n                <Button variant=\"ghost\" size=\"sm\">\n                  {t(\"navigation.login\")}\n                </Button>\n              </Link>\n              <Link to=\"/auth/register\">\n                <Button size=\"sm\">{t(\"navigation.register\")}</Button>\n              </Link>\n            </div>\n          )}\n\n          {/* Mobile menu */}\n          <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\n            <Menu className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n    </header>\n  );\n};\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { BookOpen, Github, Twitter, Mail } from 'lucide-react';\n\nexport const Footer: React.FC = () => {\n  const { t } = useTranslation('common');\n\n  return (\n    <footer className=\"border-t bg-background\">\n      <div className=\"container py-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Logo & Description */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <BookOpen className=\"h-6 w-6\" />\n              <span className=\"font-bold text-xl\">BlogTruyen</span>\n            </div>\n            <p className=\"text-sm text-muted-foreground\">\n              Nền tảng đọc truyện tranh online hiện đại với trải nghiệm đọc tuyệt vời.\n            </p>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-semibold\"><PERSON><PERSON><PERSON> k<PERSON><PERSON> n<PERSON>h</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <a href=\"/\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  {t('navigation.home')}\n                </a>\n              </li>\n              <li>\n                <a href=\"/browse\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  {t('navigation.browse')}\n                </a>\n              </li>\n              <li>\n                <a href=\"/about\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Về chúng tôi\n                </a>\n              </li>\n              <li>\n                <a href=\"/contact\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Liên hệ\n                </a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-semibold\">Hỗ trợ</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <a href=\"/help\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Trợ giúp\n                </a>\n              </li>\n              <li>\n                <a href=\"/faq\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  FAQ\n                </a>\n              </li>\n              <li>\n                <a href=\"/privacy\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Chính sách bảo mật\n                </a>\n              </li>\n              <li>\n                <a href=\"/terms\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Điều khoản sử dụng\n                </a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Social Links */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-semibold\">Kết nối</h3>\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                <Github className=\"h-5 w-5\" />\n              </a>\n              <a href=\"#\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                <Twitter className=\"h-5 w-5\" />\n              </a>\n              <a href=\"#\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                <Mail className=\"h-5 w-5\" />\n              </a>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t mt-8 pt-8 text-center text-sm text-muted-foreground\">\n          <p>&copy; 2024 BlogTruyen. Tất cả quyền được bảo lưu.</p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n", "import React from 'react';\nimport { Outlet } from 'react-router-dom';\nimport { Header } from './Header';\nimport { Footer } from './Footer';\n\ninterface MainLayoutProps {\n  showHeader?: boolean;\n  showFooter?: boolean;\n  className?: string;\n}\n\nexport const MainLayout: React.FC<MainLayoutProps> = ({ \n  showHeader = true, \n  showFooter = true,\n  className = ''\n}) => {\n  return (\n    <div className={`min-h-screen flex flex-col ${className}`}>\n      {showHeader && <Header />}\n      <main className=\"flex-1\">\n        <Outlet />\n      </main>\n      {showFooter && <Footer />}\n    </div>\n  );\n};\n", "import React from \"react\";\nimport { Navigate, Outlet, useLocation } from \"react-router-dom\";\nimport { useAuthStore } from \"../../stores/authStore\";\n\ninterface AuthLayoutProps {\n  requireAuth?: boolean;\n  redirectTo?: string;\n}\n\nexport const AuthLayout: React.FC<AuthLayoutProps> = ({\n  requireAuth = false,\n  redirectTo = \"/\",\n}) => {\n  const { isAuthenticated } = useAuthStore();\n  const location = useLocation();\n\n  // If authentication is required but user is not authenticated\n  if (requireAuth && !isAuthenticated) {\n    return <Navigate to=\"/auth/login\" replace />;\n  }\n\n  // If user is authenticated but trying to access auth pages (login/register)\n  if (\n    !requireAuth &&\n    isAuthenticated &&\n    (location.pathname === \"/auth/login\" ||\n      location.pathname === \"/auth/register\")\n  ) {\n    return <Navigate to={redirectTo} replace />;\n  }\n\n  return <Outlet />;\n};\n", "import React from 'react';\nimport { Outlet } from 'react-router-dom';\n\ninterface ReaderLayoutProps {\n  className?: string;\n}\n\nexport const ReaderLayout: React.FC<ReaderLayoutProps> = ({ \n  className = '' \n}) => {\n  return (\n    <div className={`min-h-screen bg-background ${className}`}>\n      {/* Reader layout không có header/footer để tối đa hóa không gian đọc */}\n      <Outlet />\n    </div>\n  );\n};\n", "import React from 'react';\nimport { Navigate, Outlet } from 'react-router-dom';\nimport { useAuthStore } from '../../stores/authStore';\nimport { Header } from './Header';\n\ninterface AdminLayoutProps {\n  className?: string;\n}\n\nexport const AdminLayout: React.FC<AdminLayoutProps> = ({ \n  className = '' \n}) => {\n  const { user, isAuthenticated } = useAuthStore();\n\n  // Check if user is authenticated and has admin role\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  if (user?.role !== 'admin' && user?.role !== 'moderator') {\n    return <Navigate to=\"/\" replace />;\n  }\n\n  return (\n    <div className={`min-h-screen flex flex-col ${className}`}>\n      <Header />\n      <div className=\"flex flex-1\">\n        {/* Admin Sidebar */}\n        <aside className=\"w-64 bg-muted border-r\">\n          <nav className=\"p-4\">\n            <h2 className=\"font-semibold text-lg mb-4\">Admin Panel</h2>\n            <ul className=\"space-y-2\">\n              <li>\n                <a href=\"/admin/dashboard\" className=\"block p-2 rounded hover:bg-accent\">\n                  Dashboard\n                </a>\n              </li>\n              <li>\n                <a href=\"/admin/manga\" className=\"block p-2 rounded hover:bg-accent\">\n                  Quản lý truyện\n                </a>\n              </li>\n              <li>\n                <a href=\"/admin/users\" className=\"block p-2 rounded hover:bg-accent\">\n                  Quản lý người dùng\n                </a>\n              </li>\n              <li>\n                <a href=\"/admin/comments\" className=\"block p-2 rounded hover:bg-accent\">\n                  Quản lý bình luận\n                </a>\n              </li>\n            </ul>\n          </nav>\n        </aside>\n        \n        {/* Admin Content */}\n        <main className=\"flex-1 p-6\">\n          <Outlet />\n        </main>\n      </div>\n    </div>\n  );\n};\n", "import React from \"react\";\n// import { useTranslation } from \"react-i18next\";\nimport { <PERSON> } from \"react-router-dom\";\nimport { Button } from \"../components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"../components/ui/card\";\nimport { BookO<PERSON>, TrendingUp, Clock, Star } from \"lucide-react\";\n\nexport const HomePage: React.FC = () => {\n  // const { t } = useTranslation(['common', 'manga']);\n\n  return (\n    <div className=\"container py-8\">\n      {/* Hero Section */}\n      <section className=\"text-center py-12\">\n        <h1 className=\"text-4xl font-bold tracking-tight lg:text-6xl mb-6\">\n          Chào mừng đến với BlogTruyen\n        </h1>\n        <p className=\"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto\">\n          <PERSON>h<PERSON><PERSON> ph<PERSON> thế giới truyện tranh với hàng ngàn bộ manga chất lượng cao.\n          <PERSON><PERSON><PERSON>h<PERSON>, kh<PERSON><PERSON> qu<PERSON> c<PERSON>o, tr<PERSON><PERSON> nghi<PERSON> tuy<PERSON> vời.\n        </p>\n        <div className=\"flex gap-4 justify-center\">\n          <Link to=\"/browse\">\n            <Button size=\"lg\">\n              <BookOpen className=\"mr-2 h-4 w-4\" />\n              Bắt đầu đọc\n            </Button>\n          </Link>\n          <Link to=\"/register\">\n            <Button variant=\"outline\" size=\"lg\">\n              Đăng ký miễn phí\n            </Button>\n          </Link>\n        </div>\n      </section>\n\n      {/* Features */}\n      <section className=\"py-12\">\n        <h2 className=\"text-3xl font-bold text-center mb-8\">\n          Tính năng nổi bật\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card>\n            <CardHeader className=\"text-center\">\n              <TrendingUp className=\"h-8 w-8 mx-auto mb-2 text-primary\" />\n              <CardTitle className=\"text-lg\">Xu hướng</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"text-center\">\n                Theo dõi những bộ truyện hot nhất, được cập nhật liên tục\n              </CardDescription>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"text-center\">\n              <Clock className=\"h-8 w-8 mx-auto mb-2 text-primary\" />\n              <CardTitle className=\"text-lg\">Lịch sử đọc</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"text-center\">\n                Lưu lại tiến trình đọc, tiếp tục từ nơi bạn đã dừng lại\n              </CardDescription>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"text-center\">\n              <Star className=\"h-8 w-8 mx-auto mb-2 text-primary\" />\n              <CardTitle className=\"text-lg\">Đánh giá</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"text-center\">\n                Đánh giá và bình luận về những bộ truyện yêu thích\n              </CardDescription>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"text-center\">\n              <BookOpen className=\"h-8 w-8 mx-auto mb-2 text-primary\" />\n              <CardTitle className=\"text-lg\">Đa nền tảng</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"text-center\">\n                Đọc mọi lúc mọi nơi trên điện thoại, máy tính bảng, laptop\n              </CardDescription>\n            </CardContent>\n          </Card>\n        </div>\n      </section>\n\n      {/* Popular Manga Section */}\n      <section className=\"py-12\">\n        <div className=\"flex justify-between items-center mb-8\">\n          <h2 className=\"text-3xl font-bold\">Truyện phổ biến</h2>\n          <Link to=\"/browse\">\n            <Button variant=\"outline\">Xem tất cả</Button>\n          </Link>\n        </div>\n\n        <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\">\n          {/* Mock manga cards */}\n          {Array.from({ length: 6 }).map((_, index) => (\n            <Card\n              key={index}\n              className=\"overflow-hidden hover:shadow-lg transition-shadow\"\n            >\n              <div className=\"aspect-[3/4] bg-muted\"></div>\n              <CardContent className=\"p-3\">\n                <h3 className=\"font-semibold text-sm truncate\">\n                  Tên truyện {index + 1}\n                </h3>\n                <p className=\"text-xs text-muted-foreground\">Tác giả</p>\n                <div className=\"flex items-center mt-1\">\n                  <Star className=\"h-3 w-3 fill-yellow-400 text-yellow-400\" />\n                  <span className=\"text-xs ml-1\">4.5</span>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </section>\n\n      {/* Latest Updates */}\n      <section className=\"py-12\">\n        <div className=\"flex justify-between items-center mb-8\">\n          <h2 className=\"text-3xl font-bold\">Cập nhật mới nhất</h2>\n          <Link to=\"/browse?sort=updated\">\n            <Button variant=\"outline\">Xem tất cả</Button>\n          </Link>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {Array.from({ length: 6 }).map((_, index) => (\n            <Card key={index} className=\"hover:shadow-lg transition-shadow\">\n              <CardContent className=\"p-4\">\n                <div className=\"flex space-x-4\">\n                  <div className=\"w-16 h-20 bg-muted rounded\"></div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"font-semibold mb-1\">\n                      Tên truyện {index + 1}\n                    </h3>\n                    <p className=\"text-sm text-muted-foreground mb-2\">\n                      Chapter 123\n                    </p>\n                    <p className=\"text-xs text-muted-foreground\">2 giờ trước</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </section>\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport { Button } from '../components/ui/button';\nimport { Input } from '../components/ui/input';\nimport { Label } from '../components/ui/label';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';\nimport { useAuthStore } from '../stores/authStore';\nimport { BookOpen } from 'lucide-react';\n\nexport const LoginPage: React.FC = () => {\n  const { t } = useTranslation('common');\n  const navigate = useNavigate();\n  const { login, isLoading } = useAuthStore();\n  \n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n  });\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.email) {\n      newErrors.email = t('forms.required');\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = t('forms.invalidEmail');\n    }\n\n    if (!formData.password) {\n      newErrors.password = t('forms.required');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    try {\n      await login(formData);\n      navigate('/');\n    } catch (error) {\n      console.error('Login failed:', error);\n      setErrors({ general: 'Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin.' });\n    }\n  };\n\n  return (\n    <div className=\"container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"text-center\">\n          <div className=\"flex justify-center mb-4\">\n            <BookOpen className=\"h-8 w-8 text-primary\" />\n          </div>\n          <CardTitle className=\"text-2xl\">{t('navigation.login')}</CardTitle>\n          <CardDescription>\n            Đăng nhập để truy cập tài khoản của bạn\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {errors.general && (\n              <div className=\"text-sm text-destructive text-center p-2 bg-destructive/10 rounded\">\n                {errors.general}\n              </div>\n            )}\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">{t('forms.email')}</Label>\n              <Input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                value={formData.email}\n                onChange={handleChange}\n                placeholder=\"<EMAIL>\"\n                className={errors.email ? 'border-destructive' : ''}\n              />\n              {errors.email && (\n                <p className=\"text-sm text-destructive\">{errors.email}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"password\">{t('forms.password')}</Label>\n              <Input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                value={formData.password}\n                onChange={handleChange}\n                className={errors.password ? 'border-destructive' : ''}\n              />\n              {errors.password && (\n                <p className=\"text-sm text-destructive\">{errors.password}</p>\n              )}\n            </div>\n\n            <Button \n              type=\"submit\" \n              className=\"w-full\" \n              disabled={isLoading}\n            >\n              {isLoading ? 'Đang đăng nhập...' : t('navigation.login')}\n            </Button>\n          </form>\n\n          <div className=\"mt-6 text-center text-sm\">\n            <p className=\"text-muted-foreground\">\n              Chưa có tài khoản?{' '}\n              <Link to=\"/register\" className=\"text-primary hover:underline\">\n                {t('navigation.register')}\n              </Link>\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nexport const BrowsePage: React.FC = () => {\n  const { t } = useTranslation(['common', 'manga']);\n\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">{t('navigation.browse')}</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          Trang duyệt truyện đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from 'react';\n\nexport const MangaDetailPage: React.FC = () => {\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">Chi tiết truyện</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          Trang chi tiết truyện đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from 'react';\n\nexport const ReaderPage: React.FC = () => {\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\"><PERSON><PERSON><PERSON> truy<PERSON>n</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          Trang đọc truyện đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nexport const RegisterPage: React.FC = () => {\n  const { t } = useTranslation('common');\n\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">{t('navigation.register')}</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          Trang đăng ký đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nexport const BookmarksPage: React.FC = () => {\n  const { t } = useTranslation('common');\n\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">{t('navigation.bookmarks')}</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          Trang đánh dấu đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nexport const HistoryPage: React.FC = () => {\n  const { t } = useTranslation('common');\n\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">{t('navigation.history')}</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          <PERSON><PERSON> lịch sử đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nexport const ProfilePage: React.FC = () => {\n  const { t } = useTranslation('common');\n\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">{t('navigation.profile')}</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          <PERSON><PERSON> hồ sơ đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from \"react\";\nimport { Link } from \"react-router-dom\";\n// import { useTranslation } from \"react-i18next\";\nimport { Button } from \"../components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"../components/ui/card\";\nimport { Home, ArrowLeft } from \"lucide-react\";\n\nexport const NotFoundPage: React.FC = () => {\n  // const { t } = useTranslation('common');\n\n  return (\n    <div className=\"container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8\">\n      <Card className=\"w-full max-w-md text-center\">\n        <CardHeader>\n          <CardTitle className=\"text-6xl font-bold text-muted-foreground mb-4\">\n            404\n          </CardTitle>\n          <CardTitle className=\"text-2xl\">Trang không tồn tại</CardTitle>\n          <CardDescription>\n            Trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển.\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex flex-col sm:flex-row gap-2 justify-center\">\n            <Link to=\"/\">\n              <Button className=\"w-full sm:w-auto\">\n                <Home className=\"h-4 w-4 mr-2\" />\n                Về trang chủ\n              </Button>\n            </Link>\n            <Button\n              variant=\"outline\"\n              onClick={() => window.history.back()}\n              className=\"w-full sm:w-auto\"\n            >\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Quay lại\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n", "import React from \"react\";\nimport { createBrowser<PERSON>outer, RouterProvider } from \"react-router-dom\";\nimport {\n  MainLayout,\n  AuthLayout,\n  ReaderLayout,\n  AdminLayout,\n} from \"../components\";\nimport { HomePage } from \"../pages/HomePage\";\nimport { LoginPage } from \"../pages/LoginPage\";\n\n// Import pages directly for now (can be lazy loaded later)\nimport {\n  BrowsePage,\n  MangaDetailPage,\n  ReaderPage,\n  RegisterPage,\n  BookmarksPage,\n  HistoryPage,\n  ProfilePage,\n} from \"../pages\";\nimport { NotFoundPage } from \"../pages/NotFoundPage\";\n\nconst router = createBrowserRouter([\n  {\n    path: \"/\",\n    element: <MainLayout />,\n    children: [\n      // Public routes\n      {\n        index: true,\n        element: <HomePage />,\n      },\n      {\n        path: \"browse\",\n        element: <BrowsePage />,\n      },\n      {\n        path: \"manga/:id\",\n        element: <MangaDetailPage />,\n      },\n\n      // Auth routes (redirect if already logged in)\n      {\n        path: \"auth\",\n        element: <AuthLayout requireAuth={false} />,\n        children: [\n          {\n            path: \"login\",\n            element: <LoginPage />,\n          },\n          {\n            path: \"register\",\n            element: <RegisterPage />,\n          },\n        ],\n      },\n\n      // Protected routes (require authentication)\n      {\n        path: \"user\",\n        element: <AuthLayout requireAuth={true} />,\n        children: [\n          {\n            path: \"bookmarks\",\n            element: <BookmarksPage />,\n          },\n          {\n            path: \"history\",\n            element: <HistoryPage />,\n          },\n          {\n            path: \"profile\",\n            element: <ProfilePage />,\n          },\n        ],\n      },\n    ],\n  },\n\n  // Reader layout (fullscreen, no header/footer)\n  {\n    path: \"/read\",\n    element: <ReaderLayout />,\n    children: [\n      {\n        path: \":chapterId\",\n        element: <ReaderPage />,\n      },\n    ],\n  },\n\n  // Admin layout (protected, with sidebar)\n  {\n    path: \"/admin\",\n    element: <AdminLayout />,\n    children: [\n      {\n        index: true,\n        element: <div>Admin Dashboard</div>,\n      },\n      {\n        path: \"manga\",\n        element: <div>Manga Management</div>,\n      },\n      {\n        path: \"users\",\n        element: <div>User Management</div>,\n      },\n      {\n        path: \"comments\",\n        element: <div>Comment Management</div>,\n      },\n    ],\n  },\n\n  // 404 page\n  {\n    path: \"*\",\n    element: <MainLayout />,\n    children: [\n      {\n        path: \"*\",\n        element: <NotFoundPage />,\n      },\n    ],\n  },\n]);\n\nexport const AppRouter: React.FC = () => {\n  return <RouterProvider router={router} />;\n};\n", "const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last?.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  if (!Object.prototype.hasOwnProperty.call(obj, k)) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = (obj, path, keySeparator = '.') => {\n  if (!obj) return undefined;\n  if (obj[path]) {\n    if (!Object.prototype.hasOwnProperty.call(obj, path)) return undefined;\n    return obj[path];\n  }\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code?.replace('_', '-');\n\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    console?.[type]?.apply?.(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger, options = {}) {\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger, options = {}) {\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log(...args) {\n    return this.forward(args, 'log', '', true);\n  }\n  warn(...args) {\n    return this.forward(args, 'warn', '', true);\n  }\n  error(...args) {\n    return this.forward(args, 'error', '');\n  }\n  deprecate(...args) {\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\n\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event, ...args) {\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\n\nclass ResourceStore extends EventEmitter {\n  constructor(data, options = {\n    ns: ['translation'],\n    defaultNS: 'translation'\n  }) {\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key, options = {}) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data?.[lng]?.[ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value, options = {\n    silent: false\n  }) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources, options = {\n    silent: false\n  }) {\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite, options = {\n    silent: false,\n    skipCopy: false\n  }) {\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\n\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      value = this.processors[processor]?.process(value, key, options, translator) ?? value;\n    });\n    return value;\n  }\n};\n\nconst checkedLoadedFor = {};\nconst shouldHandleAsObject = res => !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\nclass Translator extends EventEmitter {\n  constructor(services, options = {}) {\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key, o = {\n    interpolation: {}\n  }) {\n    const opt = {\n      ...o\n    };\n    if (key == null) return false;\n    const resolved = this.resolve(key, opt);\n    return resolved?.res !== undefined;\n  }\n  extractFromKey(key, opt) {\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    let namespaces = opt.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !opt.keySeparator && !this.options.userDefinedNsSeparator && !opt.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, o, lastKey) {\n    let opt = typeof o === 'object' ? {\n      ...o\n    } : o;\n    if (typeof opt !== 'object' && this.options.overloadTranslationOptionHandler) {\n      opt = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') opt = {\n      ...opt\n    };\n    if (!opt) opt = {};\n    if (keys == null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = opt.returnDetails !== undefined ? opt.returnDetails : this.options.returnDetails;\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], opt);\n    const namespace = namespaces[namespaces.length - 1];\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const lng = opt.lng || this.language;\n    const appendNamespaceToCIMode = opt.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng?.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(opt)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(opt)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, opt);\n    let res = resolved?.res;\n    const resUsedKey = resolved?.usedKey || key;\n    const resExactUsedKey = resolved?.exactUsedKey || key;\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = opt.joinArrays !== undefined ? opt.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n    const hasDefaultValue = Translator.hasDefaultValue(opt);\n    const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, opt) : '';\n    const defaultValueSuffixOrdinalFallback = opt.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, {\n      ordinal: false\n    }) : '';\n    const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n    const defaultValue = needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] || opt[`defaultValue${defaultValueSuffix}`] || opt[`defaultValue${defaultValueSuffixOrdinalFallback}`] || opt.defaultValue;\n    let resForObjHndl = res;\n    if (handleAsObjectInI18nFormat && !res && hasDefaultValue) {\n      resForObjHndl = defaultValue;\n    }\n    const handleAsObject = shouldHandleAsObject(resForObjHndl);\n    const resType = Object.prototype.toString.apply(resForObjHndl);\n    if (handleAsObjectInI18nFormat && resForObjHndl && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(resForObjHndl))) {\n      if (!opt.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, resForObjHndl, {\n          ...opt,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(opt);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(resForObjHndl);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in resForObjHndl) {\n          if (Object.prototype.hasOwnProperty.call(resForObjHndl, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            if (hasDefaultValue && !res) {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                defaultValue: shouldHandleAsObject(defaultValue) ? defaultValue[m] : undefined,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            } else {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            }\n            if (copy[m] === deepKey) copy[m] = resForObjHndl[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, opt, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = opt.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...opt,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, opt.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(opt.lng || this.language);\n        } else {\n          lngs.push(opt.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, opt);\n          } else if (this.backendConnector?.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, opt);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, opt);\n              if (needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, opt[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, opt, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) {\n        res = `${namespace}${nsSeparator}${key}`;\n      }\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}${nsSeparator}${key}` : key, usedDefault ? res : undefined, opt);\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(opt);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, opt, resolved, lastKey) {\n    if (this.i18nFormat?.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...opt\n      }, opt.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!opt.skipInterpolation) {\n      if (opt.interpolation) this.interpolator.init({\n        ...opt,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...opt.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (opt?.interpolation?.skipOnVariables !== undefined ? opt.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = opt.replace && !isString(opt.replace) ? opt.replace : opt;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, opt.lng || this.language || resolved.usedLng, opt);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) opt.nest = false;\n      }\n      if (!opt.lng && resolved && resolved.res) opt.lng = this.language || resolved.usedLng;\n      if (opt.nest !== false) res = this.interpolator.nest(res, (...args) => {\n        if (lastKey?.[0] === args[0] && !opt.context) {\n          this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return this.translate(...args, key);\n      }, opt);\n      if (opt.interpolation) this.interpolator.reset();\n    }\n    const postProcess = opt.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res != null && postProcessorNames?.length && opt.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(opt)\n        },\n        ...opt\n      } : opt, this);\n    }\n    return res;\n  }\n  resolve(keys, opt = {}) {\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, opt);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n      const needsContextHandling = opt.context !== undefined && (isString(opt.context) || typeof opt.context === 'number') && opt.context !== '';\n      const codes = opt.lngs ? opt.lngs : this.languageUtils.toResolveHierarchy(opt.lng || this.language, opt.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils?.hasLoadedNamespace && !this.utils?.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat?.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, opt);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, opt.count, opt);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${opt.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, opt);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key, options = {}) {\n    if (this.i18nFormat?.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails(options = {}) {\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      let formattedCode;\n      try {\n        formattedCode = Intl.getCanonicalLocales(code)[0];\n      } catch (e) {}\n      if (formattedCode && this.options.lowerCaseLng) {\n        formattedCode = formattedCode.toLowerCase();\n      }\n      if (formattedCode) return formattedCode;\n      if (this.options.lowerCaseLng) {\n        return code.toLowerCase();\n      }\n      return code;\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngScOnly = this.getScriptPartFromCode(code);\n        if (this.isSupportedCode(lngScOnly)) return found = lngScOnly;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes((fallbackCode === false ? [] : fallbackCode) || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\n\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst dummyRule = {\n  select: count => count === 1 ? 'one' : 'other',\n  resolvedOptions: () => ({\n    pluralCategories: ['one', 'other']\n  })\n};\nclass PluralResolver {\n  constructor(languageUtils, options = {}) {\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code, options = {}) {\n    const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n    const type = options.ordinal ? 'ordinal' : 'cardinal';\n    const cacheKey = JSON.stringify({\n      cleanedCode,\n      type\n    });\n    if (cacheKey in this.pluralRulesCache) {\n      return this.pluralRulesCache[cacheKey];\n    }\n    let rule;\n    try {\n      rule = new Intl.PluralRules(cleanedCode, {\n        type\n      });\n    } catch (err) {\n      if (!Intl) {\n        this.logger.error('No Intl support, please use an Intl polyfill!');\n        return dummyRule;\n      }\n      if (!code.match(/-|_/)) return dummyRule;\n      const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n      rule = this.getRule(lngPart, options);\n    }\n    this.pluralRulesCache[cacheKey] = rule;\n    return rule;\n  }\n  needsPlural(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    return rule?.resolvedOptions().pluralCategories.length > 1;\n  }\n  getPluralFormsOfKey(code, key, options = {}) {\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    if (!rule) return [];\n    return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n  }\n  getSuffix(code, count, options = {}) {\n    const rule = this.getRule(code, options);\n    if (rule) {\n      return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return this.getSuffix('dev', count, options);\n  }\n}\n\nconst deepFindWithDefaults = (data, defaultData, key, keySeparator = '.', ignoreJSONStructure = true) => {\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options?.interpolation?.format || (value => value);\n    this.init(options);\n  }\n  init(options = {}) {\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp?.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options?.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options?.interpolation?.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc, options = {}) {\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if ((matchedSingleQuotes?.length ?? 0) % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      let doReduce = false;\n      if (match[0].indexOf(this.formatSeparator) !== -1 && !/{.*}/.test(match[1])) {\n        const r = match[1].split(this.formatSeparator).map(elem => elem.trim());\n        match[1] = r.shift();\n        formatters = r;\n        doReduce = true;\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (doReduce) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\n\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (v, l, o) => {\n    let optForCache = o;\n    if (o && o.interpolationkey && o.formatParams && o.formatParams[o.interpolationkey] && o[o.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [o.interpolationkey]: undefined\n      };\n    }\n    const key = l + JSON.stringify(optForCache);\n    let frm = cache[key];\n    if (!frm) {\n      frm = fn(getCleanedCode(l), o);\n      cache[key] = frm;\n    }\n    return frm(v);\n  };\n};\nconst createNonCachedFormatter = fn => (v, l, o) => fn(getCleanedCode(l), o)(v);\nclass Formatter {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.init(options);\n  }\n  init(services, options = {\n    interpolation: {}\n  }) {\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n    const cf = options.cacheInBuiltFormats ? createCachedFormatter : createNonCachedFormatter;\n    this.formats = {\n      number: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: cf((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: cf((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: cf((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng, options = {}) {\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options?.formatParams?.[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\n\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services, options = {}) {\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    this.backend?.init?.(services, options.backend, options);\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ; else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName, tried = 0, wait = this.retryTimeout, callback) {\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces, options = {}, callback) {\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name, prefix = '') {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate, options = {}, clb = () => {}) {\n    if (this.services?.utils?.hasLoadedNamespace && !this.services?.utils?.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend?.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\n\nconst get = () => ({\n  debug: false,\n  initAsync: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  },\n  cacheInBuiltFormats: true\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs?.indexOf?.('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  if (typeof options.initImmediate === 'boolean') options.initAsync = options.initImmediate;\n  return options;\n};\n\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor(options = {}, callback) {\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initAsync) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init(options = {}, callback) {\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (options.defaultNS == null && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    this.options.interpolation = {\n      ...defOpts.interpolation,\n      ...this.options.interpolation\n    };\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = (...args) => this.store[fcName](...args);\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = (...args) => {\n        this.store[fcName](...args);\n        return this;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initAsync) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language, callback = noop) {\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng?.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      this.options.preload?.forEach?.(l => append(l));\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n    if (!this.resolvedLanguage && this.languages.indexOf(l) < 0 && this.store.hasLanguageSomeTranslations(l)) {\n      this.resolvedLanguage = l;\n      this.languages.unshift(l);\n    }\n  }\n  changeLanguage(lng, callback) {\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        if (this.isLanguageChangingTo === lng) {\n          setLngProps(l);\n          this.translator.changeLanguage(l);\n          this.isLanguageChangingTo = undefined;\n          this.emit('languageChanged', l);\n          this.logger.log('languageChanged', l);\n        }\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve((...args) => this.t(...args));\n      if (callback) callback(err, (...args) => this.t(...args));\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const fl = isString(lngs) ? lngs : lngs && lngs[0];\n      const l = this.store.hasLanguageSomeTranslations(fl) ? fl : this.services.languageUtils.getBestMatchFromCodes(isString(lngs) ? [lngs] : lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        this.services.languageDetector?.cacheUserLanguage?.(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    const fixedT = (key, opts, ...rest) => {\n      let o;\n      if (typeof opts !== 'object') {\n        o = this.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        o = {\n          ...opts\n        };\n      }\n      o.lng = o.lng || fixedT.lng;\n      o.lngs = o.lngs || fixedT.lngs;\n      o.ns = o.ns || fixedT.ns;\n      if (o.keyPrefix !== '') o.keyPrefix = o.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = this.options.keySeparator || '.';\n      let resultKey;\n      if (o.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${o.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = o.keyPrefix ? `${o.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return this.t(resultKey, o);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t(...args) {\n    return this.translator?.translate(...args);\n  }\n  exists(...args) {\n    return this.translator?.exists(...args);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns, options = {}) {\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages?.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services?.languageUtils || new LanguageUtil(get());\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance(options = {}, callback) {\n    return new I18n(options, callback);\n  }\n  cloneInstance(options = {}, callback = noop) {\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      const clonedData = Object.keys(this.store.data).reduce((prev, l) => {\n        prev[l] = {\n          ...this.store.data[l]\n        };\n        prev[l] = Object.keys(prev[l]).reduce((acc, n) => {\n          acc[n] = {\n            ...prev[l][n]\n          };\n          return acc;\n        }, prev[l]);\n        return prev;\n      }, {});\n      clone.store = new ResourceStore(clonedData, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', (event, ...args) => {\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\n\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\n\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };\n", "const {\n  slice,\n  forEach\n} = [];\nfunction defaults(obj) {\n  forEach.call(slice.call(arguments, 1), source => {\n    if (source) {\n      for (const prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nfunction hasXSS(input) {\n  if (typeof input !== 'string') return false;\n\n  // Common XSS attack patterns\n  const xssPatterns = [/<\\s*script.*?>/i, /<\\s*\\/\\s*script\\s*>/i, /<\\s*img.*?on\\w+\\s*=/i, /<\\s*\\w+\\s*on\\w+\\s*=.*?>/i, /javascript\\s*:/i, /vbscript\\s*:/i, /expression\\s*\\(/i, /eval\\s*\\(/i, /alert\\s*\\(/i, /document\\.cookie/i, /document\\.write\\s*\\(/i, /window\\.location/i, /innerHTML/i];\n  return xssPatterns.some(pattern => pattern.test(input));\n}\n\n// eslint-disable-next-line no-control-regex\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nconst serializeCookie = function (name, val) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    path: '/'\n  };\n  const opt = options;\n  const value = encodeURIComponent(val);\n  let str = `${name}=${value}`;\n  if (opt.maxAge > 0) {\n    const maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += `; Max-Age=${Math.floor(maxAge)}`;\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    const sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  if (opt.partitioned) str += '; Partitioned';\n  return str;\n};\nconst cookie = {\n  create(name, value, minutes, domain) {\n    let cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, encodeURIComponent(value), cookieOptions);\n  },\n  read(name) {\n    const nameEQ = `${name}=`;\n    const ca = document.cookie.split(';');\n    for (let i = 0; i < ca.length; i++) {\n      let c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove(name) {\n    this.create(name, '', -1);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  // Deconstruct the options object and extract the lookupCookie property\n  lookup(_ref) {\n    let {\n      lookupCookie\n    } = _ref;\n    if (lookupCookie && typeof document !== 'undefined') {\n      return cookie.read(lookupCookie) || undefined;\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupCookie, cookieMinutes, cookieDomain, and cookieOptions properties\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupCookie,\n      cookieMinutes,\n      cookieDomain,\n      cookieOptions\n    } = _ref2;\n    if (lookupCookie && typeof document !== 'undefined') {\n      cookie.create(lookupCookie, lng, cookieMinutes, cookieDomain, cookieOptions);\n    }\n  }\n};\n\nvar querystring = {\n  name: 'querystring',\n  // Deconstruct the options object and extract the lookupQuerystring property\n  lookup(_ref) {\n    let {\n      lookupQuerystring\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      let {\n        search\n      } = window.location;\n      if (!window.location.search && window.location.hash?.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      const query = search.substring(1);\n      const params = query.split('&');\n      for (let i = 0; i < params.length; i++) {\n        const pos = params[i].indexOf('=');\n        if (pos > 0) {\n          const key = params[i].substring(0, pos);\n          if (key === lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\n\nlet hasLocalStorageSupport = null;\nconst localStorageAvailable = () => {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = typeof window !== 'undefined' && window.localStorage !== null;\n    if (!hasLocalStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  lookup(_ref) {\n    let {\n      lookupLocalStorage\n    } = _ref;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      return window.localStorage.getItem(lookupLocalStorage) || undefined; // Undefined ensures type consistency with the previous version of this function\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupLocalStorage\n    } = _ref2;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(lookupLocalStorage, lng);\n    }\n  }\n};\n\nlet hasSessionStorageSupport = null;\nconst sessionStorageAvailable = () => {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = typeof window !== 'undefined' && window.sessionStorage !== null;\n    if (!hasSessionStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup(_ref) {\n    let {\n      lookupSessionStorage\n    } = _ref;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      return window.sessionStorage.getItem(lookupSessionStorage) || undefined;\n    }\n    return undefined;\n  },\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupSessionStorage\n    } = _ref2;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(lookupSessionStorage, lng);\n    }\n  }\n};\n\nvar navigator$1 = {\n  name: 'navigator',\n  lookup(options) {\n    const found = [];\n    if (typeof navigator !== 'undefined') {\n      const {\n        languages,\n        userLanguage,\n        language\n      } = navigator;\n      if (languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (let i = 0; i < languages.length; i++) {\n          found.push(languages[i]);\n        }\n      }\n      if (userLanguage) {\n        found.push(userLanguage);\n      }\n      if (language) {\n        found.push(language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\n\nvar htmlTag = {\n  name: 'htmlTag',\n  // Deconstruct the options object and extract the htmlTag property\n  lookup(_ref) {\n    let {\n      htmlTag\n    } = _ref;\n    let found;\n    const internalHtmlTag = htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (internalHtmlTag && typeof internalHtmlTag.getAttribute === 'function') {\n      found = internalHtmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\n\nvar path = {\n  name: 'path',\n  // Deconstruct the options object and extract the lookupFromPathIndex property\n  lookup(_ref) {\n    let {\n      lookupFromPathIndex\n    } = _ref;\n    if (typeof window === 'undefined') return undefined;\n    const language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n    if (!Array.isArray(language)) return undefined;\n    const index = typeof lookupFromPathIndex === 'number' ? lookupFromPathIndex : 0;\n    return language[index]?.replace('/', '');\n  }\n};\n\nvar subdomain = {\n  name: 'subdomain',\n  lookup(_ref) {\n    let {\n      lookupFromSubdomainIndex\n    } = _ref;\n    // If given get the subdomain index else 1\n    const internalLookupFromSubdomainIndex = typeof lookupFromSubdomainIndex === 'number' ? lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group match which should be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    const language = typeof window !== 'undefined' && window.location?.hostname?.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[internalLookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nlet canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nconst order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nconst getDefaults = () => ({\n  order,\n  lookupQuerystring: 'lng',\n  lookupCookie: 'i18next',\n  lookupLocalStorage: 'i18nextLng',\n  lookupSessionStorage: 'i18nextLng',\n  // cache user language\n  caches: ['localStorage'],\n  excludeCacheFor: ['cimode'],\n  // cookieMinutes: 10,\n  // cookieDomain: 'myDomain'\n\n  convertDetectedLanguage: l => l\n});\nclass Browser {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  init() {\n    let services = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      languageUtils: {}\n    };\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.services = services;\n    this.options = defaults(options, this.options || {}, getDefaults());\n    if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n      this.options.convertDetectedLanguage = l => l.replace('-', '_');\n    }\n\n    // backwards compatibility\n    if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n    this.i18nOptions = i18nOptions;\n    this.addDetector(cookie$1);\n    this.addDetector(querystring);\n    this.addDetector(localStorage);\n    this.addDetector(sessionStorage);\n    this.addDetector(navigator$1);\n    this.addDetector(htmlTag);\n    this.addDetector(path);\n    this.addDetector(subdomain);\n  }\n  addDetector(detector) {\n    this.detectors[detector.name] = detector;\n    return this;\n  }\n  detect() {\n    let detectionOrder = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.options.order;\n    let detected = [];\n    detectionOrder.forEach(detectorName => {\n      if (this.detectors[detectorName]) {\n        let lookup = this.detectors[detectorName].lookup(this.options);\n        if (lookup && typeof lookup === 'string') lookup = [lookup];\n        if (lookup) detected = detected.concat(lookup);\n      }\n    });\n    detected = detected.filter(d => d !== undefined && d !== null && !hasXSS(d)).map(d => this.options.convertDetectedLanguage(d));\n    if (this.services && this.services.languageUtils && this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n    return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n  }\n  cacheUserLanguage(lng) {\n    let caches = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.options.caches;\n    if (!caches) return;\n    if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n    caches.forEach(cacheName => {\n      if (this.detectors[cacheName]) this.detectors[cacheName].cacheUserLanguage(lng, this.options);\n    });\n  }\n}\nBrowser.type = 'languageDetector';\n\nexport { Browser as default };\n", "import i18n from 'i18next';\nimport { initReactI18next } from 'react-i18next';\nimport LanguageDetector from 'i18next-browser-languagedetector';\n\n// Import translation files\nimport enCommon from './locales/en/common.json';\nimport enManga from './locales/en/manga.json';\nimport enReader from './locales/en/reader.json';\nimport viCommon from './locales/vi/common.json';\nimport viManga from './locales/vi/manga.json';\nimport viReader from './locales/vi/reader.json';\n\nconst resources = {\n  en: {\n    common: enCommon,\n    manga: enManga,\n    reader: enReader,\n  },\n  vi: {\n    common: viCommon,\n    manga: viManga,\n    reader: viReader,\n  },\n};\n\ni18n\n  .use(LanguageDetector)\n  .use(initReactI18next)\n  .init({\n    resources,\n    fallbackLng: 'en',\n    debug: import.meta.env.DEV,\n    \n    // Namespace configuration\n    defaultNS: 'common',\n    ns: ['common', 'manga', 'reader'],\n    \n    interpolation: {\n      escapeValue: false, // React already escapes values\n    },\n    \n    detection: {\n      order: ['localStorage', 'navigator', 'htmlTag'],\n      caches: ['localStorage'],\n    },\n  });\n\nexport default i18n;\n", "import { QueryClientProvider } from \"@tanstack/react-query\";\nimport { ReactQueryDevtools } from \"@tanstack/react-query-devtools\";\nimport { queryClient } from \"./services/queryClient\";\nimport { AppRouter } from \"./router\";\nimport \"./i18n\";\n\nfunction App() {\n  return (\n    <QueryClientProvider client={queryClient}>\n      <AppRouter />\n      {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}\n    </QueryClientProvider>\n  );\n}\n\nexport default App;\n", "import { StrictMode } from 'react'\nimport { createRoot } from 'react-dom/client'\nimport './index.css'\nimport App from './App.tsx'\n\ncreateRoot(document.getElementById('root')!).render(\n  <StrictMode>\n    <App />\n  </StrictMode>,\n)\n"], "names": ["m", "require$$0", "client", "queryClient", "QueryClient", "failureCount", "error", "r", "e", "t", "f", "n", "o", "clsx", "falsyToString", "value", "cx", "cva", "base", "config", "props", "_config_compoundVariants", "variants", "defaultVariants", "getVariantClassNames", "variant", "variantProp", "defaultVariantProp", "variant<PERSON><PERSON>", "propsWithoutUndefined", "acc", "param", "key", "getCompoundVariantClassNames", "cvClass", "cvClassName", "compoundVariantOptions", "CLASS_PART_SEPARATOR", "createClassGroupUtils", "classMap", "createClassMap", "conflictingClassGroups", "conflictingClassGroupModifiers", "className", "classParts", "getGroupRecursive", "getGroupIdForArbitraryProperty", "classGroupId", "hasPostfixModifier", "conflicts", "classPartObject", "currentClassPart", "nextClassPartObject", "classGroupFromNextClassPart", "classRest", "_a", "validator", "arbitraryPropertyRegex", "arbitraryPropertyClassName", "property", "theme", "classGroups", "processClassesRecursively", "classGroup", "classDefinition", "classPartObjectToEdit", "get<PERSON>art", "isThemeGetter", "path", "currentClassPartObject", "pathPart", "func", "createLruCache", "maxCacheSize", "cacheSize", "cache", "previousCache", "update", "IMPORTANT_MODIFIER", "MODIFIER_SEPARATOR", "MODIFIER_SEPARATOR_LENGTH", "createParseClassName", "prefix", "experimentalParseClassName", "parseClassName", "modifiers", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modifierStart", "postfixModifierPosition", "index", "currentCharacter", "baseClassNameWithImportantModifier", "baseClassName", "stripImportantModifier", "hasImportantModifier", "maybePostfixModifierPosition", "fullPrefix", "parseClassNameOriginal", "createSortModifiers", "orderSensitiveModifiers", "modifier", "sortedModifiers", "unsortedModifiers", "createConfigUtils", "SPLIT_CLASSES_REGEX", "mergeClassList", "classList", "configUtils", "getClassGroupId", "getConflictingClassGroupIds", "sortModifiers", "classGroupsInConflict", "classNames", "result", "originalClassName", "isExternal", "variantModifier", "modifierId", "classId", "conflictGroups", "i", "group", "twJoin", "argument", "resolvedValue", "string", "toValue", "mix", "k", "createTailwindMerge", "createConfigFirst", "createConfigRest", "cacheGet", "cacheSet", "functionToCall", "initTailwindMerge", "previousConfig", "createConfigCurrent", "tailwindMerge", "cachedResult", "fromTheme", "themeGetter", "arbitraryValueRegex", "arbitraryVariableRegex", "fractionRegex", "tshirtUnitRegex", "lengthUnitRegex", "colorFunctionRegex", "shadowRegex", "imageRegex", "isFraction", "isNumber", "isInteger", "isPercent", "isTshirtSize", "isAny", "is<PERSON>engthOnly", "isNever", "is<PERSON><PERSON>ow", "isImage", "isAnyNonArbitrary", "isArbitraryValue", "isArbitraryVariable", "isArbitrarySize", "getIsArbitraryValue", "isLabelSize", "isArbitraryLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isArbitraryNumber", "isLabelNumber", "isArbitraryPosition", "isLabelPosition", "isArbitraryImage", "isLabelImage", "isArbitraryShadow", "isLabel<PERSON><PERSON>ow", "isArbitraryVariableLength", "getIsArbitraryVariable", "isArbitraryVariableFamilyName", "isLabelFamilyName", "isArbitraryVariablePosition", "isArbitraryVariableSize", "isArbitraryVariableImage", "isArbitraryVariableShadow", "test<PERSON><PERSON><PERSON>", "testValue", "shouldMatchNoLabel", "label", "getDefaultConfig", "themeColor", "themeFont", "themeText", "themeFontWeight", "themeTracking", "themeLeading", "themeBreakpoint", "themeContainer", "themeSpacing", "themeRadius", "themeShadow", "themeInsetShadow", "themeTextShadow", "themeDropShadow", "themeBlur", "themePerspective", "themeAspect", "themeEase", "themeAnimate", "scaleBreak", "scalePosition", "scalePositionWithArbitrary", "scaleOverflow", "scaleOverscroll", "scaleUnambiguousSpacing", "scaleInset", "scaleGridTemplateColsRows", "scaleGridColRowStartAndEnd", "scaleGridColRowStartOrEnd", "scaleGridAutoColsRows", "scaleAlignPrimaryAxis", "scaleAlignSecondaryAxis", "scaleMargin", "scaleSizing", "scaleColor", "scaleBgPosition", "scaleBgRepeat", "scaleBgSize", "scaleGradientStopPosition", "scaleRadius", "scaleBorderWidth", "scaleLineStyle", "scaleBlendMode", "scaleMaskImagePosition", "scaleBlur", "scaleRotate", "scaleScale", "scaleSkew", "scaleTranslate", "twMerge", "cn", "inputs", "buttonVariants", "<PERSON><PERSON>", "React.forwardRef", "size", "<PERSON><PERSON><PERSON><PERSON>", "ref", "Comp", "Slot", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "Input", "type", "NAME", "Label", "forwardedRef", "Primitive", "event", "Root", "labelVariants", "LabelPrimitive.Root", "warn", "i18n", "code", "msg", "rest", "args", "_b", "isString", "_d", "_c", "alreadyWarned", "warnOnce", "loadedClb", "cb", "initialized", "loadNamespaces", "ns", "loadLanguages", "lng", "hasLoadedNamespace", "options", "i18nInstance", "loadNotPending", "obj", "isObject", "matchHtmlEntity", "htmlEntities", "unescapeHtmlEntity", "unescape", "text", "defaultOptions", "setDefaults", "getDefaults", "setI18n", "instance", "getI18n", "initReactI18next", "I18nContext", "createContext", "ReportNamespaces", "namespaces", "usePrevious", "ignore", "useRef", "useEffect", "alwaysNewT", "language", "namespace", "keyPrefix", "useMemoizedT", "useCallback", "useTranslation", "i18nFromProps", "i18nFromContext", "defaultNSFromContext", "useContext", "notReadyT", "optsOrDefaultValue", "retNotReady", "i18nOptions", "useSuspense", "ready", "memoGetT", "getT", "getNewT", "setT", "useState", "joinedNS", "previousJoinedNS", "isMounted", "bindI18n", "bindI18nStore", "boundReset", "ret", "resolve", "toKebabCase", "toCamelCase", "match", "p1", "p2", "toPascalCase", "camelCase", "mergeClasses", "classes", "array", "hasA11yProp", "prop", "defaultAttributes", "Icon", "forwardRef", "color", "strokeWidth", "absoluteStrokeWidth", "children", "iconNode", "createElement", "tag", "attrs", "createLucideIcon", "iconName", "Component", "__iconNode", "ArrowLeft", "BookOpen", "Clock", "<PERSON><PERSON><PERSON>", "House", "Mail", "<PERSON><PERSON>", "Search", "Star", "TrendingUp", "Twitter", "User", "createStoreImpl", "createState", "state", "listeners", "setState", "partial", "replace", "nextState", "previousState", "listener", "getState", "api", "initialState", "createStore", "identity", "arg", "useStore", "selector", "slice", "React", "createImpl", "useBoundStore", "create", "createJSONStorage", "getStorage", "storage", "name", "parse", "str2", "str", "newValue", "toThenable", "fn", "input", "onFulfilled", "_onRejected", "_onFulfilled", "onRejected", "persistImpl", "baseOptions", "set", "get", "persistedState", "currentState", "hasHydrated", "hydrationListeners", "finishHydrationListeners", "setItem", "savedSetState", "config<PERSON><PERSON><PERSON>", "stateFromStorage", "hydrate", "_a2", "postRehydrationCallback", "deserializedStorageValue", "migration", "migrationResult", "migrated", "migratedState", "newOptions", "persist", "useAuthStore", "credentials", "mockUser", "data", "user", "updatedUser", "Header", "isAuthenticated", "logout", "jsxs", "Link", "Fragment", "Footer", "MainLayout", "showHeader", "showFooter", "Outlet", "AuthLayout", "requireAuth", "redirectTo", "location", "useLocation", "Navigate", "ReaderLayout", "AdminLayout", "HomePage", "_", "LoginPage", "navigate", "useNavigate", "login", "isLoading", "formData", "setFormData", "errors", "setErrors", "handleChange", "prev", "validateForm", "newErrors", "handleSubmit", "BrowsePage", "MangaDetailPage", "ReaderPage", "RegisterPage", "BookmarksPage", "HistoryPage", "ProfilePage", "NotFoundPage", "Home", "router", "createBrowserRouter", "AppRouter", "RouterProvider", "defer", "res", "rej", "promise", "reject", "makeString", "object", "copy", "a", "s", "lastOfPathSeparatorRegExp", "<PERSON><PERSON><PERSON>", "canNotTraverseDeeper", "getLastOfPath", "Empty", "stack", "stackIndex", "set<PERSON>ath", "p", "last", "push<PERSON><PERSON>", "concat", "<PERSON><PERSON><PERSON>", "getPathWithDefaults", "defaultData", "deepExtend", "target", "source", "overwrite", "regexEscape", "_entityMap", "escape", "RegExpCache", "capacity", "pattern", "regExpFromCache", "regExpNew", "chars", "looksLikeObjectPathRegExpCache", "looksLikeObjectPath", "nsSeparator", "keySeparator", "possibleChars", "c", "matched", "ki", "deepFind", "tokens", "current", "next", "nextPath", "j", "getCleanedCode", "consoleLogger", "<PERSON><PERSON>", "concreteLogger", "lvl", "debugOnly", "moduleName", "baseLogger", "EventEmitter", "events", "numListeners", "observer", "numTimesAdded", "ResourceStore", "ignoreJSONStructure", "resources", "deep", "pack", "v", "postProcessor", "module", "processors", "translator", "processor", "checkedLoadedFor", "shouldHandleAsObject", "Translator", "services", "opt", "resolved", "wouldCheckForNsInKey", "seemsNaturalLanguage", "parts", "keys", "last<PERSON>ey", "returnDetails", "appendNamespaceToCIMode", "resUsed<PERSON><PERSON>", "resExactUsedKey", "noObject", "joinArrays", "handleAsObjectInI18nFormat", "needsPluralHandling", "hasDefaultValue", "defaultValueSuffix", "defaultValueSuffixOrdinalFallback", "needsZeroSuffixLookup", "defaultValue", "resForObjHndl", "handleAsObject", "resType", "resTypeIsArray", "newKeyToUse", "<PERSON><PERSON><PERSON>", "usedDefault", "usedKey", "resForMissing", "updateMissing", "fk", "lngs", "fallbackLngs", "send", "l", "specificDefaultValue", "defaultForMissing", "suffixes", "suffix", "skipOnVariables", "nestBef", "nb", "na", "nestAft", "postProcess", "postProcessorNames", "found", "exactUsed<PERSON>ey", "usedLng", "usedNS", "extracted", "needsContextHandling", "codes", "finalKeys", "pluralSuffix", "zeroSuffix", "ordinalPrefix", "<PERSON><PERSON>ey", "<PERSON><PERSON><PERSON>", "optionsKeys", "useOptionsReplaceForData", "option", "LanguageUtil", "formattedCode", "cleanedLng", "lngScOnly", "lngOnly", "supportedLng", "fallbacks", "fallbackCode", "fallbackCodes", "addCode", "fc", "suffixesOrder", "dummyRule", "count", "PluralResolver", "languageUtils", "cleanedCode", "cache<PERSON>ey", "rule", "lngPart", "pluralCategory1", "pluralCategory2", "pluralCategory", "deepFindWithDefaults", "regexSafe", "val", "Interpolator", "escape$1", "escapeValue", "useRawValueToEscape", "prefixEscaped", "suffixEscaped", "formatSeparator", "unescapeSuffix", "unescapePrefix", "nestingPrefix", "nestingPrefixEscaped", "nestingSuffix", "nestingSuffixEscaped", "nestingOptionsSeparator", "maxReplaces", "alwaysFormat", "getOrResetRegExp", "existingRegExp", "replaces", "handleFormat", "missingInterpolationHandler", "todo", "matchedVar", "temp", "safeValue", "clonedOptions", "handleHasOptions", "inheritedOptions", "sep", "optionsString", "matchedSingleQuotes", "matchedDoubleQuotes", "formatters", "doReduce", "elem", "parseFormatStr", "formatStr", "formatName", "formatOptions", "optStr", "<PERSON><PERSON><PERSON>", "createCachedFormatter", "optForCache", "frm", "createNonCachedFormatter", "<PERSON><PERSON><PERSON>", "cf", "formatter", "format", "formats", "lastIndex", "mem", "formatted", "valOptions", "removePending", "q", "Connector", "backend", "store", "languages", "callback", "toLoad", "pending", "toLoadLanguages", "toLoadNamespaces", "hasAllNamespaces", "err", "loaded", "loadedKeys", "fcName", "tried", "wait", "resolver", "fallback<PERSON><PERSON><PERSON>", "isUpdate", "clb", "_e", "opts", "transformOptions", "noop", "bindMemberFunctions", "inst", "I18n", "defOpts", "createClassOnDemand", "ClassOrObject", "lu", "deferred", "load", "finish", "usedCallback", "append", "li", "lngInLngs", "setLngProps", "done", "setLng", "fl", "fixedT", "<PERSON><PERSON><PERSON>", "fallbackLng", "lastLng", "loadState", "preResult", "preloaded", "newLngs", "rtlLngs", "forkResourceStore", "mergedOptions", "clone", "clonedData", "for<PERSON>ach", "defaults", "hasXSS", "fieldContentRegExp", "serializeCookie", "maxAge", "cookie", "minutes", "domain", "cookieOptions", "nameEQ", "ca", "cookie$1", "_ref", "lookup<PERSON><PERSON><PERSON>", "_ref2", "cookieMinutes", "cookieDomain", "querystring", "lookupQuerystring", "search", "params", "pos", "hasLocalStorageSupport", "localStorageAvailable", "<PERSON><PERSON><PERSON>", "localStorage", "lookupLocalStorage", "hasSessionStorageSupport", "sessionStorageAvailable", "sessionStorage", "lookupSessionStorage", "navigator$1", "userLanguage", "htmlTag", "internalHtmlTag", "lookupFromPathIndex", "subdomain", "lookupFromSubdomainIndex", "internalLookupFromSubdomainIndex", "canCookies", "order", "Browser", "detector", "detectionOrder", "detected", "detectorName", "lookup", "d", "caches", "cacheName", "enCommon", "enManga", "en<PERSON><PERSON><PERSON>", "vi<PERSON><PERSON><PERSON>", "viManga", "vi<PERSON><PERSON><PERSON>", "LanguageDetector", "App", "QueryClientProvider", "createRoot", "StrictMode"], "mappings": "8+BAEA,IAAIA,EAAIC,GAAmB,EAEzB,OAAAC,cAAqBF,EAAE,WACvBE,eAAsBF,EAAE,2BCHb,MAAAG,GAAc,IAAIC,GAAY,CACzC,eAAgB,CACd,QAAS,CACP,UAAW,EAAI,GAAK,IACpB,OAAQ,GAAK,GAAK,IAClB,MAAO,CAACC,EAAcC,KAEhBA,GAAA,YAAAA,EAAO,SAAU,MAAOA,GAAA,YAAAA,EAAO,QAAS,IACnC,GAEFD,EAAe,EAExB,qBAAsB,EACxB,EACA,UAAW,CACT,MAAO,EAAA,CACT,CAEJ,CAAC,ECpBD,SAASE,GAAEC,EAAE,CAAC,IAAIC,EAAEC,EAAEC,EAAE,GAAG,GAAa,OAAOH,GAAjB,UAA8B,OAAOA,GAAjB,SAAmBG,GAAGH,UAAoB,OAAOA,GAAjB,SAAmB,GAAG,MAAM,QAAQA,CAAC,EAAE,CAAC,IAAII,EAAEJ,EAAE,OAAO,IAAIC,EAAE,EAAEA,EAAEG,EAAEH,IAAID,EAAEC,CAAC,IAAIC,EAAEH,GAAEC,EAAEC,CAAC,CAAC,KAAKE,IAAIA,GAAG,KAAKA,GAAGD,EAAE,KAAM,KAAIA,KAAKF,EAAEA,EAAEE,CAAC,IAAIC,IAAIA,GAAG,KAAKA,GAAGD,GAAG,OAAOC,CAAC,CAAQ,SAASE,IAAM,CAAC,QAAQL,EAAEC,EAAEC,EAAE,EAAEC,EAAE,GAAGC,EAAE,UAAU,OAAOF,EAAEE,EAAEF,KAAKF,EAAE,UAAUE,CAAC,KAAKD,EAAEF,GAAEC,CAAC,KAAKG,IAAIA,GAAG,KAAKA,GAAGF,GAAG,OAAOE,CAAC,CCe/W,MAAMG,GAAiBC,GAAQ,OAAOA,GAAU,UAAY,GAAGA,CAAK,GAAKA,IAAU,EAAI,IAAMA,EAChFC,GAAKH,GACLI,GAAM,CAACC,EAAMC,IAAUC,GAAQ,CACpC,IAAIC,EACJ,IAAKF,GAAW,KAA4B,OAASA,EAAO,WAAa,KAAM,OAAOH,GAAGE,EAAME,GAAU,KAA2B,OAASA,EAAM,MAAOA,GAAU,KAA2B,OAASA,EAAM,SAAS,EACvN,KAAM,CAAE,SAAAE,EAAU,gBAAAC,CAAe,EAAKJ,EAChCK,EAAuB,OAAO,KAAKF,CAAQ,EAAE,IAAKG,GAAU,CAC9D,MAAMC,EAAcN,GAAU,KAA2B,OAASA,EAAMK,CAAO,EACzEE,EAAqBJ,GAAoB,KAAqC,OAASA,EAAgBE,CAAO,EACpH,GAAIC,IAAgB,KAAM,OAAO,KACjC,MAAME,EAAad,GAAcY,CAAW,GAAKZ,GAAca,CAAkB,EACjF,OAAOL,EAASG,CAAO,EAAEG,CAAU,CAC/C,CAAS,EACKC,EAAwBT,GAAS,OAAO,QAAQA,CAAK,EAAE,OAAO,CAACU,EAAKC,IAAQ,CAC9E,GAAI,CAACC,EAAKjB,CAAK,EAAIgB,EACnB,OAAIhB,IAAU,SAGde,EAAIE,CAAG,EAAIjB,GACJe,CACV,EAAE,EAAE,EACCG,EAA+Bd,GAAW,OAAsCE,EAA2BF,EAAO,oBAAsB,MAAQE,IAA6B,OAAvG,OAAyHA,EAAyB,OAAO,CAACS,EAAKC,IAAQ,CAC/O,GAAI,CAAE,MAAOG,EAAS,UAAWC,EAAa,GAAGC,CAAsB,EAAKL,EAC5E,OAAO,OAAO,QAAQK,CAAsB,EAAE,MAAOL,GAAQ,CACzD,GAAI,CAACC,EAAKjB,CAAK,EAAIgB,EACnB,OAAO,MAAM,QAAQhB,CAAK,EAAIA,EAAM,SAAS,CACzC,GAAGQ,EACH,GAAGM,CACvB,EAAkBG,CAAG,CAAC,EAAK,CACP,GAAGT,EACH,GAAGM,CACvB,EAAmBG,CAAG,IAAMjB,CAC5B,CAAa,EAAI,CACD,GAAGe,EACHI,EACAC,CAChB,EAAgBL,CACP,EAAE,EAAE,EACL,OAAOd,GAAGE,EAAMM,EAAsBS,EAA8Bb,GAAU,KAA2B,OAASA,EAAM,MAAOA,GAAU,KAA2B,OAASA,EAAM,SAAS,CAC/L,ECtDCiB,GAAuB,IACvBC,GAAwBnB,GAAU,CACtC,MAAMoB,EAAWC,GAAerB,CAAM,EAChC,CACJ,uBAAAsB,EACA,+BAAAC,CACJ,EAAMvB,EAgBJ,MAAO,CACL,gBAhBsBwB,GAAa,CACnC,MAAMC,EAAaD,EAAU,MAAMN,EAAoB,EAEvD,OAAIO,EAAW,CAAC,IAAM,IAAMA,EAAW,SAAW,GAChDA,EAAW,MAAO,EAEbC,GAAkBD,EAAYL,CAAQ,GAAKO,GAA+BH,CAAS,CAC3F,EAUC,4BATkC,CAACI,EAAcC,IAAuB,CACxE,MAAMC,EAAYR,EAAuBM,CAAY,GAAK,CAAE,EAC5D,OAAIC,GAAsBN,EAA+BK,CAAY,EAC5D,CAAC,GAAGE,EAAW,GAAGP,EAA+BK,CAAY,CAAC,EAEhEE,CACR,CAIA,CACH,EACMJ,GAAoB,CAACD,EAAYM,IAAoB,OACzD,GAAIN,EAAW,SAAW,EACxB,OAAOM,EAAgB,aAEzB,MAAMC,EAAmBP,EAAW,CAAC,EAC/BQ,EAAsBF,EAAgB,SAAS,IAAIC,CAAgB,EACnEE,EAA8BD,EAAsBP,GAAkBD,EAAW,MAAM,CAAC,EAAGQ,CAAmB,EAAI,OACxH,GAAIC,EACF,OAAOA,EAET,GAAIH,EAAgB,WAAW,SAAW,EACxC,OAEF,MAAMI,EAAYV,EAAW,KAAKP,EAAoB,EACtD,OAAOkB,EAAAL,EAAgB,WAAW,KAAK,CAAC,CACtC,UAAAM,CACJ,IAAQA,EAAUF,CAAS,CAAC,IAFnB,YAAAC,EAEsB,YAC/B,EACME,GAAyB,aACzBX,GAAiCH,GAAa,CAClD,GAAIc,GAAuB,KAAKd,CAAS,EAAG,CAC1C,MAAMe,EAA6BD,GAAuB,KAAKd,CAAS,EAAE,CAAC,EACrEgB,EAAWD,GAAA,YAAAA,EAA4B,UAAU,EAAGA,EAA2B,QAAQ,GAAG,GAChG,GAAIC,EAEF,MAAO,cAAgBA,CAE7B,CACA,EAIMnB,GAAiBrB,GAAU,CAC/B,KAAM,CACJ,MAAAyC,EACA,YAAAC,CACJ,EAAM1C,EACEoB,EAAW,CACf,SAAU,IAAI,IACd,WAAY,CAAA,CACb,EACD,UAAWQ,KAAgBc,EACzBC,GAA0BD,EAAYd,CAAY,EAAGR,EAAUQ,EAAca,CAAK,EAEpF,OAAOrB,CACT,EACMuB,GAA4B,CAACC,EAAYb,EAAiBH,EAAca,IAAU,CACtFG,EAAW,QAAQC,GAAmB,CACpC,GAAI,OAAOA,GAAoB,SAAU,CACvC,MAAMC,EAAwBD,IAAoB,GAAKd,EAAkBgB,GAAQhB,EAAiBc,CAAe,EACjHC,EAAsB,aAAelB,EACrC,MACN,CACI,GAAI,OAAOiB,GAAoB,WAAY,CACzC,GAAIG,GAAcH,CAAe,EAAG,CAClCF,GAA0BE,EAAgBJ,CAAK,EAAGV,EAAiBH,EAAca,CAAK,EACtF,MACR,CACMV,EAAgB,WAAW,KAAK,CAC9B,UAAWc,EACX,aAAAjB,CACR,CAAO,EACD,MACN,CACI,OAAO,QAAQiB,CAAe,EAAE,QAAQ,CAAC,CAAChC,EAAK+B,CAAU,IAAM,CAC7DD,GAA0BC,EAAYG,GAAQhB,EAAiBlB,CAAG,EAAGe,EAAca,CAAK,CAC9F,CAAK,CACL,CAAG,CACH,EACMM,GAAU,CAAChB,EAAiBkB,IAAS,CACzC,IAAIC,EAAyBnB,EAC7B,OAAAkB,EAAK,MAAM/B,EAAoB,EAAE,QAAQiC,GAAY,CAC9CD,EAAuB,SAAS,IAAIC,CAAQ,GAC/CD,EAAuB,SAAS,IAAIC,EAAU,CAC5C,SAAU,IAAI,IACd,WAAY,CAAA,CACpB,CAAO,EAEHD,EAAyBA,EAAuB,SAAS,IAAIC,CAAQ,CACzE,CAAG,EACMD,CACT,EACMF,GAAgBI,GAAQA,EAAK,cAG7BC,GAAiBC,GAAgB,CACrC,GAAIA,EAAe,EACjB,MAAO,CACL,IAAK,IAAA,GACL,IAAK,IAAM,CAAA,CACZ,EAEH,IAAIC,EAAY,EACZC,EAAQ,IAAI,IACZC,EAAgB,IAAI,IACxB,MAAMC,EAAS,CAAC7C,EAAKjB,IAAU,CAC7B4D,EAAM,IAAI3C,EAAKjB,CAAK,EACpB2D,IACIA,EAAYD,IACdC,EAAY,EACZE,EAAgBD,EAChBA,EAAQ,IAAI,IAEf,EACD,MAAO,CACL,IAAI3C,EAAK,CACP,IAAIjB,EAAQ4D,EAAM,IAAI3C,CAAG,EACzB,GAAIjB,IAAU,OACZ,OAAOA,EAET,IAAKA,EAAQ6D,EAAc,IAAI5C,CAAG,KAAO,OACvC,OAAA6C,EAAO7C,EAAKjB,CAAK,EACVA,CAEV,EACD,IAAIiB,EAAKjB,EAAO,CACV4D,EAAM,IAAI3C,CAAG,EACf2C,EAAM,IAAI3C,EAAKjB,CAAK,EAEpB8D,EAAO7C,EAAKjB,CAAK,CAEzB,CACG,CACH,EACM+D,GAAqB,IACrBC,GAAqB,IACrBC,GAA4BD,GAAmB,OAC/CE,GAAuB9D,GAAU,CACrC,KAAM,CACJ,OAAA+D,EACA,2BAAAC,CACJ,EAAMhE,EAOJ,IAAIiE,EAAiBzC,GAAa,CAChC,MAAM0C,EAAY,CAAE,EACpB,IAAIC,EAAe,EACfC,EAAa,EACbC,EAAgB,EAChBC,EACJ,QAASC,EAAQ,EAAGA,EAAQ/C,EAAU,OAAQ+C,IAAS,CACrD,IAAIC,EAAmBhD,EAAU+C,CAAK,EACtC,GAAIJ,IAAiB,GAAKC,IAAe,EAAG,CAC1C,GAAII,IAAqBZ,GAAoB,CAC3CM,EAAU,KAAK1C,EAAU,MAAM6C,EAAeE,CAAK,CAAC,EACpDF,EAAgBE,EAAQV,GACxB,QACV,CACQ,GAAIW,IAAqB,IAAK,CAC5BF,EAA0BC,EAC1B,QACV,CACA,CACUC,IAAqB,IACvBL,IACSK,IAAqB,IAC9BL,IACSK,IAAqB,IAC9BJ,IACSI,IAAqB,KAC9BJ,GAER,CACI,MAAMK,EAAqCP,EAAU,SAAW,EAAI1C,EAAYA,EAAU,UAAU6C,CAAa,EAC3GK,EAAgBC,GAAuBF,CAAkC,EACzEG,EAAuBF,IAAkBD,EACzCI,EAA+BP,GAA2BA,EAA0BD,EAAgBC,EAA0BD,EAAgB,OACpJ,MAAO,CACL,UAAAH,EACA,qBAAAU,EACA,cAAAF,EACA,6BAAAG,CACD,CACF,EACD,GAAId,EAAQ,CACV,MAAMe,EAAaf,EAASH,GACtBmB,EAAyBd,EAC/BA,EAAiBzC,GAAaA,EAAU,WAAWsD,CAAU,EAAIC,EAAuBvD,EAAU,UAAUsD,EAAW,MAAM,CAAC,EAAI,CAChI,WAAY,GACZ,UAAW,CAAE,EACb,qBAAsB,GACtB,cAAetD,EACf,6BAA8B,MAC/B,CACL,CACE,GAAIwC,EAA4B,CAC9B,MAAMe,EAAyBd,EAC/BA,EAAiBzC,GAAawC,EAA2B,CACvD,UAAAxC,EACA,eAAgBuD,CACtB,CAAK,CACL,CACE,OAAOd,CACT,EACMU,GAAyBD,GACzBA,EAAc,SAASf,EAAkB,EACpCe,EAAc,UAAU,EAAGA,EAAc,OAAS,CAAC,EAMxDA,EAAc,WAAWf,EAAkB,EACtCe,EAAc,UAAU,CAAC,EAE3BA,EAQHM,GAAsBhF,GAAU,CACpC,MAAMiF,EAA0B,OAAO,YAAYjF,EAAO,wBAAwB,IAAIkF,GAAY,CAACA,EAAU,EAAI,CAAC,CAAC,EAmBnH,OAlBsBhB,GAAa,CACjC,GAAIA,EAAU,QAAU,EACtB,OAAOA,EAET,MAAMiB,EAAkB,CAAE,EAC1B,IAAIC,EAAoB,CAAE,EAC1B,OAAAlB,EAAU,QAAQgB,GAAY,CACAA,EAAS,CAAC,IAAM,KAAOD,EAAwBC,CAAQ,GAEjFC,EAAgB,KAAK,GAAGC,EAAkB,KAAI,EAAIF,CAAQ,EAC1DE,EAAoB,CAAE,GAEtBA,EAAkB,KAAKF,CAAQ,CAEvC,CAAK,EACDC,EAAgB,KAAK,GAAGC,EAAkB,KAAI,CAAE,EACzCD,CACR,CAEH,EACME,GAAoBrF,IAAW,CACnC,MAAOqD,GAAerD,EAAO,SAAS,EACtC,eAAgB8D,GAAqB9D,CAAM,EAC3C,cAAegF,GAAoBhF,CAAM,EACzC,GAAGmB,GAAsBnB,CAAM,CACjC,GACMsF,GAAsB,MACtBC,GAAiB,CAACC,EAAWC,IAAgB,CACjD,KAAM,CACJ,eAAAxB,EACA,gBAAAyB,EACA,4BAAAC,EACA,cAAAC,CACJ,EAAMH,EAQEI,EAAwB,CAAE,EAC1BC,EAAaN,EAAU,KAAI,EAAG,MAAMF,EAAmB,EAC7D,IAAIS,EAAS,GACb,QAASxB,EAAQuB,EAAW,OAAS,EAAGvB,GAAS,EAAGA,GAAS,EAAG,CAC9D,MAAMyB,EAAoBF,EAAWvB,CAAK,EACpC,CACJ,WAAA0B,EACA,UAAA/B,EACA,qBAAAU,EACA,cAAAF,EACA,6BAAAG,CACN,EAAQZ,EAAe+B,CAAiB,EACpC,GAAIC,EAAY,CACdF,EAASC,GAAqBD,EAAO,OAAS,EAAI,IAAMA,EAASA,GACjE,QACN,CACI,IAAIlE,EAAqB,CAAC,CAACgD,EACvBjD,EAAe8D,EAAgB7D,EAAqB6C,EAAc,UAAU,EAAGG,CAA4B,EAAIH,CAAa,EAChI,GAAI,CAAC9C,EAAc,CACjB,GAAI,CAACC,EAAoB,CAEvBkE,EAASC,GAAqBD,EAAO,OAAS,EAAI,IAAMA,EAASA,GACjE,QACR,CAEM,GADAnE,EAAe8D,EAAgBhB,CAAa,EACxC,CAAC9C,EAAc,CAEjBmE,EAASC,GAAqBD,EAAO,OAAS,EAAI,IAAMA,EAASA,GACjE,QACR,CACMlE,EAAqB,EAC3B,CACI,MAAMqE,EAAkBN,EAAc1B,CAAS,EAAE,KAAK,GAAG,EACnDiC,EAAavB,EAAuBsB,EAAkBvC,GAAqBuC,EAC3EE,EAAUD,EAAavE,EAC7B,GAAIiE,EAAsB,SAASO,CAAO,EAExC,SAEFP,EAAsB,KAAKO,CAAO,EAClC,MAAMC,EAAiBV,EAA4B/D,EAAcC,CAAkB,EACnF,QAASyE,EAAI,EAAGA,EAAID,EAAe,OAAQ,EAAEC,EAAG,CAC9C,MAAMC,EAAQF,EAAeC,CAAC,EAC9BT,EAAsB,KAAKM,EAAaI,CAAK,CACnD,CAEIR,EAASC,GAAqBD,EAAO,OAAS,EAAI,IAAMA,EAASA,EACrE,CACE,OAAOA,CACT,EAWA,SAASS,IAAS,CAChB,IAAIjC,EAAQ,EACRkC,EACAC,EACAC,EAAS,GACb,KAAOpC,EAAQ,UAAU,SACnBkC,EAAW,UAAUlC,GAAO,KAC1BmC,EAAgBE,GAAQH,CAAQ,KAClCE,IAAWA,GAAU,KACrBA,GAAUD,GAIhB,OAAOC,CACT,CACA,MAAMC,GAAUC,GAAO,CACrB,GAAI,OAAOA,GAAQ,SACjB,OAAOA,EAET,IAAIH,EACAC,EAAS,GACb,QAASG,EAAI,EAAGA,EAAID,EAAI,OAAQC,IAC1BD,EAAIC,CAAC,IACHJ,EAAgBE,GAAQC,EAAIC,CAAC,CAAC,KAChCH,IAAWA,GAAU,KACrBA,GAAUD,GAIhB,OAAOC,CACT,EACA,SAASI,GAAoBC,KAAsBC,EAAkB,CACnE,IAAIxB,EACAyB,EACAC,EACAC,EAAiBC,EACrB,SAASA,EAAkB7B,EAAW,CACpC,MAAMxF,EAASiH,EAAiB,OAAO,CAACK,EAAgBC,IAAwBA,EAAoBD,CAAc,EAAGN,GAAmB,EACxI,OAAAvB,EAAcJ,GAAkBrF,CAAM,EACtCkH,EAAWzB,EAAY,MAAM,IAC7B0B,EAAW1B,EAAY,MAAM,IAC7B2B,EAAiBI,EACVA,EAAchC,CAAS,CAClC,CACE,SAASgC,EAAchC,EAAW,CAChC,MAAMiC,EAAeP,EAAS1B,CAAS,EACvC,GAAIiC,EACF,OAAOA,EAET,MAAM1B,EAASR,GAAeC,EAAWC,CAAW,EACpD,OAAA0B,EAAS3B,EAAWO,CAAM,EACnBA,CACX,CACE,OAAO,UAA6B,CAClC,OAAOqB,EAAeZ,GAAO,MAAM,KAAM,SAAS,CAAC,CACpD,CACH,CACA,MAAMkB,EAAY7G,GAAO,CACvB,MAAM8G,EAAclF,GAASA,EAAM5B,CAAG,GAAK,CAAE,EAC7C,OAAA8G,EAAY,cAAgB,GACrBA,CACT,EACMC,GAAsB,8BACtBC,GAAyB,8BACzBC,GAAgB,aAChBC,GAAkB,mCAClBC,GAAkB,4HAClBC,GAAqB,2CAErBC,GAAc,kEACdC,GAAa,+FACbC,GAAaxI,GAASkI,GAAc,KAAKlI,CAAK,EAC9CyI,EAAWzI,GAAS,CAAC,CAACA,GAAS,CAAC,OAAO,MAAM,OAAOA,CAAK,CAAC,EAC1D0I,GAAY1I,GAAS,CAAC,CAACA,GAAS,OAAO,UAAU,OAAOA,CAAK,CAAC,EAC9D2I,GAAY3I,GAASA,EAAM,SAAS,GAAG,GAAKyI,EAASzI,EAAM,MAAM,EAAG,EAAE,CAAC,EACvE4I,EAAe5I,GAASmI,GAAgB,KAAKnI,CAAK,EAClD6I,GAAQ,IAAM,GACdC,GAAe9I,GAIrBoI,GAAgB,KAAKpI,CAAK,GAAK,CAACqI,GAAmB,KAAKrI,CAAK,EACvD+I,GAAU,IAAM,GAChBC,GAAWhJ,GAASsI,GAAY,KAAKtI,CAAK,EAC1CiJ,GAAUjJ,GAASuI,GAAW,KAAKvI,CAAK,EACxCkJ,GAAoBlJ,GAAS,CAACmJ,EAAiBnJ,CAAK,GAAK,CAACoJ,EAAoBpJ,CAAK,EACnFqJ,GAAkBrJ,GAASsJ,GAAoBtJ,EAAOuJ,GAAaR,EAAO,EAC1EI,EAAmBnJ,GAASgI,GAAoB,KAAKhI,CAAK,EAC1DwJ,GAAoBxJ,GAASsJ,GAAoBtJ,EAAOyJ,GAAeX,EAAY,EACnFY,GAAoB1J,GAASsJ,GAAoBtJ,EAAO2J,GAAelB,CAAQ,EAC/EmB,GAAsB5J,GAASsJ,GAAoBtJ,EAAO6J,GAAiBd,EAAO,EAClFe,GAAmB9J,GAASsJ,GAAoBtJ,EAAO+J,GAAcd,EAAO,EAC5Ee,GAAoBhK,GAASsJ,GAAoBtJ,EAAOiK,GAAejB,EAAQ,EAC/EI,EAAsBpJ,GAASiI,GAAuB,KAAKjI,CAAK,EAChEkK,GAA4BlK,GAASmK,GAAuBnK,EAAOyJ,EAAa,EAChFW,GAAgCpK,GAASmK,GAAuBnK,EAAOqK,EAAiB,EACxFC,GAA8BtK,GAASmK,GAAuBnK,EAAO6J,EAAe,EACpFU,GAA0BvK,GAASmK,GAAuBnK,EAAOuJ,EAAW,EAC5EiB,GAA2BxK,GAASmK,GAAuBnK,EAAO+J,EAAY,EAC9EU,GAA4BzK,GAASmK,GAAuBnK,EAAOiK,GAAe,EAAI,EAEtFX,GAAsB,CAACtJ,EAAO0K,EAAWC,IAAc,CAC3D,MAAMxE,EAAS6B,GAAoB,KAAKhI,CAAK,EAC7C,OAAImG,EACEA,EAAO,CAAC,EACHuE,EAAUvE,EAAO,CAAC,CAAC,EAErBwE,EAAUxE,EAAO,CAAC,CAAC,EAErB,EACT,EACMgE,GAAyB,CAACnK,EAAO0K,EAAWE,EAAqB,KAAU,CAC/E,MAAMzE,EAAS8B,GAAuB,KAAKjI,CAAK,EAChD,OAAImG,EACEA,EAAO,CAAC,EACHuE,EAAUvE,EAAO,CAAC,CAAC,EAErByE,EAEF,EACT,EAEMf,GAAkBgB,GAASA,IAAU,YAAcA,IAAU,aAC7Dd,GAAec,GAASA,IAAU,SAAWA,IAAU,MACvDtB,GAAcsB,GAASA,IAAU,UAAYA,IAAU,QAAUA,IAAU,UAC3EpB,GAAgBoB,GAASA,IAAU,SACnClB,GAAgBkB,GAASA,IAAU,SACnCR,GAAoBQ,GAASA,IAAU,cACvCZ,GAAgBY,GAASA,IAAU,SA2BnCC,GAAmB,IAAM,CAM7B,MAAMC,EAAajD,EAAU,OAAO,EAC9BkD,EAAYlD,EAAU,MAAM,EAC5BmD,EAAYnD,EAAU,MAAM,EAC5BoD,EAAkBpD,EAAU,aAAa,EACzCqD,EAAgBrD,EAAU,UAAU,EACpCsD,EAAetD,EAAU,SAAS,EAClCuD,EAAkBvD,EAAU,YAAY,EACxCwD,EAAiBxD,EAAU,WAAW,EACtCyD,EAAezD,EAAU,SAAS,EAClC0D,EAAc1D,EAAU,QAAQ,EAChC2D,EAAc3D,EAAU,QAAQ,EAChC4D,EAAmB5D,EAAU,cAAc,EAC3C6D,EAAkB7D,EAAU,aAAa,EACzC8D,EAAkB9D,EAAU,aAAa,EACzC+D,EAAY/D,EAAU,MAAM,EAC5BgE,EAAmBhE,EAAU,aAAa,EAC1CiE,EAAcjE,EAAU,QAAQ,EAChCkE,EAAYlE,EAAU,MAAM,EAC5BmE,EAAenE,EAAU,SAAS,EAQlCoE,EAAa,IAAM,CAAC,OAAQ,QAAS,MAAO,aAAc,OAAQ,OAAQ,QAAS,QAAQ,EAC3FC,EAAgB,IAAM,CAAC,SAAU,MAAO,SAAU,OAAQ,QAAS,WAEzE,WAAY,YAEZ,YAAa,eAEb,eAAgB,cAEhB,aAAa,EACPC,EAA6B,IAAM,CAAC,GAAGD,EAAa,EAAI/C,EAAqBD,CAAgB,EAC7FkD,EAAgB,IAAM,CAAC,OAAQ,SAAU,OAAQ,UAAW,QAAQ,EACpEC,EAAkB,IAAM,CAAC,OAAQ,UAAW,MAAM,EAClDC,EAA0B,IAAM,CAACnD,EAAqBD,EAAkBoC,CAAY,EACpFiB,EAAa,IAAM,CAAChE,GAAY,OAAQ,OAAQ,GAAG+D,GAAyB,EAC5EE,EAA4B,IAAM,CAAC/D,GAAW,OAAQ,UAAWU,EAAqBD,CAAgB,EACtGuD,EAA6B,IAAM,CAAC,OAAQ,CAChD,KAAM,CAAC,OAAQhE,GAAWU,EAAqBD,CAAgB,CACnE,EAAKT,GAAWU,EAAqBD,CAAgB,EAC7CwD,GAA4B,IAAM,CAACjE,GAAW,OAAQU,EAAqBD,CAAgB,EAC3FyD,EAAwB,IAAM,CAAC,OAAQ,MAAO,MAAO,KAAMxD,EAAqBD,CAAgB,EAChG0D,EAAwB,IAAM,CAAC,QAAS,MAAO,SAAU,UAAW,SAAU,SAAU,UAAW,WAAY,cAAe,UAAU,EACxIC,EAA0B,IAAM,CAAC,QAAS,MAAO,SAAU,UAAW,cAAe,UAAU,EAC/FC,EAAc,IAAM,CAAC,OAAQ,GAAGR,EAAuB,CAAE,EACzDS,EAAc,IAAM,CAACxE,GAAY,OAAQ,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,GAAG+D,GAAyB,EAC5IU,EAAa,IAAM,CAAClC,EAAY3B,EAAqBD,CAAgB,EACrE+D,GAAkB,IAAM,CAAC,GAAGf,EAAa,EAAI7B,GAA6BV,GAAqB,CACnG,SAAU,CAACR,EAAqBD,CAAgB,CACpD,CAAG,EACKgE,GAAgB,IAAM,CAAC,YAAa,CACxC,OAAQ,CAAC,GAAI,IAAK,IAAK,QAAS,OAAO,CAC3C,CAAG,EACKC,EAAc,IAAM,CAAC,OAAQ,QAAS,UAAW7C,GAAyBlB,GAAiB,CAC/F,KAAM,CAACD,EAAqBD,CAAgB,CAChD,CAAG,EACKkE,EAA4B,IAAM,CAAC1E,GAAWuB,GAA2BV,EAAiB,EAC1F8D,EAAc,IAAM,CAE1B,GAAI,OAAQ,OAAQ9B,EAAapC,EAAqBD,CAAgB,EAChEoE,EAAmB,IAAM,CAAC,GAAI9E,EAAUyB,GAA2BV,EAAiB,EACpFgE,GAAiB,IAAM,CAAC,QAAS,SAAU,SAAU,QAAQ,EAC7DC,GAAiB,IAAM,CAAC,SAAU,WAAY,SAAU,UAAW,SAAU,UAAW,cAAe,aAAc,aAAc,aAAc,aAAc,YAAa,MAAO,aAAc,QAAS,YAAY,EACtNC,EAAyB,IAAM,CAACjF,EAAUE,GAAW2B,GAA6BV,EAAmB,EACrG+D,GAAY,IAAM,CAExB,GAAI,OAAQ9B,EAAWzC,EAAqBD,CAAgB,EACtDyE,GAAc,IAAM,CAAC,OAAQnF,EAAUW,EAAqBD,CAAgB,EAC5E0E,GAAa,IAAM,CAAC,OAAQpF,EAAUW,EAAqBD,CAAgB,EAC3E2E,GAAY,IAAM,CAACrF,EAAUW,EAAqBD,CAAgB,EAClE4E,GAAiB,IAAM,CAACvF,GAAY,OAAQ,GAAG+D,EAAuB,CAAE,EAC9E,MAAO,CACL,UAAW,IACX,MAAO,CACL,QAAS,CAAC,OAAQ,OAAQ,QAAS,QAAQ,EAC3C,OAAQ,CAAC,OAAO,EAChB,KAAM,CAAC3D,CAAY,EACnB,WAAY,CAACA,CAAY,EACzB,MAAO,CAACC,EAAK,EACb,UAAW,CAACD,CAAY,EACxB,cAAe,CAACA,CAAY,EAC5B,KAAM,CAAC,KAAM,MAAO,QAAQ,EAC5B,KAAM,CAACM,EAAiB,EACxB,cAAe,CAAC,OAAQ,aAAc,QAAS,SAAU,SAAU,WAAY,OAAQ,YAAa,OAAO,EAC3G,eAAgB,CAACN,CAAY,EAC7B,QAAS,CAAC,OAAQ,QAAS,OAAQ,SAAU,UAAW,OAAO,EAC/D,YAAa,CAAC,WAAY,OAAQ,SAAU,WAAY,UAAW,MAAM,EACzE,OAAQ,CAACA,CAAY,EACrB,OAAQ,CAACA,CAAY,EACrB,QAAS,CAAC,KAAMH,CAAQ,EACxB,KAAM,CAACG,CAAY,EACnB,cAAe,CAACA,CAAY,EAC5B,SAAU,CAAC,UAAW,QAAS,SAAU,OAAQ,QAAS,QAAQ,CACnE,EACD,YAAa,CAQX,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,SAAUJ,GAAYW,EAAkBC,EAAqB2C,CAAW,CACjG,CAAO,EAMD,UAAW,CAAC,WAAW,EAKvB,QAAS,CAAC,CACR,QAAS,CAACtD,EAAUU,EAAkBC,EAAqBkC,CAAc,CACjF,CAAO,EAKD,cAAe,CAAC,CACd,cAAeY,EAAU,CACjC,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgBA,EAAU,CAClC,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,OAAQ,QAAS,aAAc,cAAc,CACtE,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkB,CAAC,QAAS,OAAO,CAC3C,CAAO,EAKD,IAAK,CAAC,CACJ,IAAK,CAAC,SAAU,SAAS,CACjC,CAAO,EAKD,QAAS,CAAC,QAAS,eAAgB,SAAU,OAAQ,cAAe,QAAS,eAAgB,gBAAiB,aAAc,eAAgB,qBAAsB,qBAAsB,qBAAsB,kBAAmB,YAAa,YAAa,OAAQ,cAAe,WAAY,YAAa,QAAQ,EAKnT,GAAI,CAAC,UAAW,aAAa,EAK7B,MAAO,CAAC,CACN,MAAO,CAAC,QAAS,OAAQ,OAAQ,QAAS,KAAK,CACvD,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAAC,OAAQ,QAAS,OAAQ,OAAQ,QAAS,KAAK,CAC/D,CAAO,EAKD,UAAW,CAAC,UAAW,gBAAgB,EAKvC,aAAc,CAAC,CACb,OAAQ,CAAC,UAAW,QAAS,OAAQ,OAAQ,YAAY,CACjE,CAAO,EAKD,kBAAmB,CAAC,CAClB,OAAQE,EAA0B,CAC1C,CAAO,EAKD,SAAU,CAAC,CACT,SAAUC,EAAa,CAC/B,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAa,CACnC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAa,CACnC,CAAO,EAKD,WAAY,CAAC,CACX,WAAYC,EAAe,CACnC,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgBA,EAAe,CACvC,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgBA,EAAe,CACvC,CAAO,EAKD,SAAU,CAAC,SAAU,QAAS,WAAY,WAAY,QAAQ,EAK9D,MAAO,CAAC,CACN,MAAOE,EAAU,CACzB,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,EAAU,CAC7B,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,EAAU,CAC7B,CAAO,EAKD,MAAO,CAAC,CACN,MAAOA,EAAU,CACzB,CAAO,EAKD,IAAK,CAAC,CACJ,IAAKA,EAAU,CACvB,CAAO,EAKD,IAAK,CAAC,CACJ,IAAKA,EAAU,CACvB,CAAO,EAKD,MAAO,CAAC,CACN,MAAOA,EAAU,CACzB,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQA,EAAU,CAC1B,CAAO,EAKD,KAAM,CAAC,CACL,KAAMA,EAAU,CACxB,CAAO,EAKD,WAAY,CAAC,UAAW,YAAa,UAAU,EAK/C,EAAG,CAAC,CACF,EAAG,CAAC9D,GAAW,OAAQU,EAAqBD,CAAgB,CACpE,CAAO,EAQD,MAAO,CAAC,CACN,MAAO,CAACX,GAAY,OAAQ,OAAQ8C,EAAgB,GAAGiB,EAAyB,CAAA,CACxF,CAAO,EAKD,iBAAkB,CAAC,CACjB,KAAM,CAAC,MAAO,cAAe,MAAO,aAAa,CACzD,CAAO,EAKD,YAAa,CAAC,CACZ,KAAM,CAAC,SAAU,OAAQ,cAAc,CAC/C,CAAO,EAKD,KAAM,CAAC,CACL,KAAM,CAAC9D,EAAUD,GAAY,OAAQ,UAAW,OAAQW,CAAgB,CAChF,CAAO,EAKD,KAAM,CAAC,CACL,KAAM,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CAClE,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CACpE,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAACT,GAAW,QAAS,OAAQ,OAAQU,EAAqBD,CAAgB,CACzF,CAAO,EAKD,YAAa,CAAC,CACZ,YAAasD,EAAyB,CAC9C,CAAO,EAKD,gBAAiB,CAAC,CAChB,IAAKC,EAA0B,CACvC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaC,GAAyB,CAC9C,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAAyB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaF,EAAyB,CAC9C,CAAO,EAKD,gBAAiB,CAAC,CAChB,IAAKC,EAA0B,CACvC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaC,GAAyB,CAC9C,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAAyB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,MAAO,MAAO,QAAS,YAAa,WAAW,CACrE,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaC,EAAqB,CAC1C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAqB,CAC1C,CAAO,EAKD,IAAK,CAAC,CACJ,IAAKL,EAAuB,CACpC,CAAO,EAKD,QAAS,CAAC,CACR,QAASA,EAAuB,CACxC,CAAO,EAKD,QAAS,CAAC,CACR,QAASA,EAAuB,CACxC,CAAO,EAKD,kBAAmB,CAAC,CAClB,QAAS,CAAC,GAAGM,EAAqB,EAAI,QAAQ,CACtD,CAAO,EAKD,gBAAiB,CAAC,CAChB,gBAAiB,CAAC,GAAGC,EAAuB,EAAI,QAAQ,CAChE,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,OAAQ,GAAGA,EAAyB,CAAA,CAC7D,CAAO,EAKD,gBAAiB,CAAC,CAChB,QAAS,CAAC,SAAU,GAAGD,EAAuB,CAAA,CACtD,CAAO,EAKD,cAAe,CAAC,CACd,MAAO,CAAC,GAAGC,IAA2B,CACpC,SAAU,CAAC,GAAI,MAAM,CACtB,CAAA,CACT,CAAO,EAKD,aAAc,CAAC,CACb,KAAM,CAAC,OAAQ,GAAGA,IAA2B,CAC3C,SAAU,CAAC,GAAI,MAAM,CACtB,CAAA,CACT,CAAO,EAKD,gBAAiB,CAAC,CAChB,gBAAiBD,EAAqB,CAC9C,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAAC,GAAGC,EAAuB,EAAI,UAAU,CAChE,CAAO,EAKD,aAAc,CAAC,CACb,aAAc,CAAC,OAAQ,GAAGA,EAAyB,CAAA,CAC3D,CAAO,EAMD,EAAG,CAAC,CACF,EAAGP,EAAuB,CAClC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,EAAG,CAAC,CACF,EAAGQ,EAAW,CACtB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,UAAW,CAAC,CACV,UAAWR,EAAuB,CAC1C,CAAO,EAKD,kBAAmB,CAAC,iBAAiB,EAKrC,UAAW,CAAC,CACV,UAAWA,EAAuB,CAC1C,CAAO,EAKD,kBAAmB,CAAC,iBAAiB,EAQrC,KAAM,CAAC,CACL,KAAMS,EAAW,CACzB,CAAO,EAKD,EAAG,CAAC,CACF,EAAG,CAAC1B,EAAgB,SAAU,GAAG0B,EAAa,CAAA,CACtD,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC1B,EAAgB,SAC1B,OAAQ,GAAG0B,EAAa,CAAA,CAChC,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC1B,EAAgB,SAAU,OACpC,QACA,CACE,OAAQ,CAACD,CAAe,CACzB,EAAE,GAAG2B,EAAa,CAAA,CAC3B,CAAO,EAKD,EAAG,CAAC,CACF,EAAG,CAAC,SAAU,KAAM,GAAGA,EAAa,CAAA,CAC5C,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,SAAU,KAAM,OAAQ,GAAGA,EAAa,CAAA,CAC1D,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,SAAU,KAAM,GAAGA,EAAa,CAAA,CAClD,CAAO,EAQD,YAAa,CAAC,CACZ,KAAM,CAAC,OAAQ/B,EAAWf,GAA2BV,EAAiB,CAC9E,CAAO,EAKD,iBAAkB,CAAC,cAAe,sBAAsB,EAKxD,aAAc,CAAC,SAAU,YAAY,EAKrC,cAAe,CAAC,CACd,KAAM,CAAC0B,EAAiB9B,EAAqBM,EAAiB,CACtE,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,kBAAmB,kBAAmB,YAAa,iBAAkB,SAAU,gBAAiB,WAAY,iBAAkB,iBAAkBf,GAAWQ,CAAgB,CACpM,CAAO,EAKD,cAAe,CAAC,CACd,KAAM,CAACiB,GAA+BjB,EAAkB6B,CAAS,CACzE,CAAO,EAKD,aAAc,CAAC,aAAa,EAK5B,cAAe,CAAC,SAAS,EAKzB,mBAAoB,CAAC,cAAc,EAKnC,aAAc,CAAC,cAAe,eAAe,EAK7C,cAAe,CAAC,oBAAqB,cAAc,EAKnD,eAAgB,CAAC,qBAAsB,mBAAmB,EAK1D,SAAU,CAAC,CACT,SAAU,CAACG,EAAe/B,EAAqBD,CAAgB,CACvE,CAAO,EAKD,aAAc,CAAC,CACb,aAAc,CAACV,EAAU,OAAQW,EAAqBM,EAAiB,CAC/E,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CACT0B,EAAc,GAAGmB,EAAyB,CAAA,CAClD,CAAO,EAKD,aAAc,CAAC,CACb,aAAc,CAAC,OAAQnD,EAAqBD,CAAgB,CACpE,CAAO,EAKD,sBAAuB,CAAC,CACtB,KAAM,CAAC,SAAU,SAAS,CAClC,CAAO,EAKD,kBAAmB,CAAC,CAClB,KAAM,CAAC,OAAQ,UAAW,OAAQC,EAAqBD,CAAgB,CAC/E,CAAO,EAKD,iBAAkB,CAAC,CACjB,KAAM,CAAC,OAAQ,SAAU,QAAS,UAAW,QAAS,KAAK,CACnE,CAAO,EAMD,oBAAqB,CAAC,CACpB,YAAa8D,EAAU,CAC/B,CAAO,EAKD,aAAc,CAAC,CACb,KAAMA,EAAU,CACxB,CAAO,EAKD,kBAAmB,CAAC,YAAa,WAAY,eAAgB,cAAc,EAK3E,wBAAyB,CAAC,CACxB,WAAY,CAAC,GAAGO,GAAc,EAAI,MAAM,CAChD,CAAO,EAKD,4BAA6B,CAAC,CAC5B,WAAY,CAAC/E,EAAU,YAAa,OAAQW,EAAqBI,EAAiB,CAC1F,CAAO,EAKD,wBAAyB,CAAC,CACxB,WAAYyD,EAAU,CAC9B,CAAO,EAKD,mBAAoB,CAAC,CACnB,mBAAoB,CAACxE,EAAU,OAAQW,EAAqBD,CAAgB,CACpF,CAAO,EAKD,iBAAkB,CAAC,YAAa,YAAa,aAAc,aAAa,EAKxE,gBAAiB,CAAC,WAAY,gBAAiB,WAAW,EAK1D,YAAa,CAAC,CACZ,KAAM,CAAC,OAAQ,SAAU,UAAW,QAAQ,CACpD,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQoD,EAAuB,CACvC,CAAO,EAKD,iBAAkB,CAAC,CACjB,MAAO,CAAC,WAAY,MAAO,SAAU,SAAU,WAAY,cAAe,MAAO,QAASnD,EAAqBD,CAAgB,CACvI,CAAO,EAKD,WAAY,CAAC,CACX,WAAY,CAAC,SAAU,SAAU,MAAO,WAAY,WAAY,cAAc,CACtF,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAAC,SAAU,QAAS,MAAO,MAAM,CAChD,CAAO,EAKD,KAAM,CAAC,CACL,KAAM,CAAC,aAAc,WAAY,QAAQ,CACjD,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,OAAQ,SAAU,MAAM,CAC1C,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,OAAQC,EAAqBD,CAAgB,CAC/D,CAAO,EAQD,gBAAiB,CAAC,CAChB,GAAI,CAAC,QAAS,QAAS,QAAQ,CACvC,CAAO,EAKD,UAAW,CAAC,CACV,UAAW,CAAC,SAAU,UAAW,UAAW,MAAM,CAC1D,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,SAAU,UAAW,SAAS,CACpD,CAAO,EAKD,cAAe,CAAC,CACd,GAAI+D,GAAe,CAC3B,CAAO,EAKD,YAAa,CAAC,CACZ,GAAIC,GAAa,CACzB,CAAO,EAKD,UAAW,CAAC,CACV,GAAIC,EAAW,CACvB,CAAO,EAKD,WAAY,CAAC,CACX,GAAI,CAAC,OAAQ,CACX,OAAQ,CAAC,CACP,GAAI,CAAC,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,IAAI,CAC3D,EAAa1E,GAAWU,EAAqBD,CAAgB,EACnD,OAAQ,CAAC,GAAIC,EAAqBD,CAAgB,EAClD,MAAO,CAACT,GAAWU,EAAqBD,CAAgB,CACzD,EAAEqB,GAA0BV,EAAgB,CACrD,CAAO,EAKD,WAAY,CAAC,CACX,GAAImD,EAAU,CACtB,CAAO,EAKD,oBAAqB,CAAC,CACpB,KAAMI,EAAyB,CACvC,CAAO,EAKD,mBAAoB,CAAC,CACnB,IAAKA,EAAyB,CACtC,CAAO,EAKD,kBAAmB,CAAC,CAClB,GAAIA,EAAyB,CACrC,CAAO,EAKD,gBAAiB,CAAC,CAChB,KAAMJ,EAAU,CACxB,CAAO,EAKD,eAAgB,CAAC,CACf,IAAKA,EAAU,CACvB,CAAO,EAKD,cAAe,CAAC,CACd,GAAIA,EAAU,CACtB,CAAO,EAQD,QAAS,CAAC,CACR,QAASK,EAAW,CAC5B,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAW,CAChC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAW,CAChC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAW,CAChC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAW,CAChC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAW,CAChC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAW,CAChC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,WAAY,CAAC,CACX,OAAQC,EAAgB,CAChC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,EAAgB,CACpC,CAAO,EAKD,mBAAoB,CAAC,kBAAkB,EAKvC,WAAY,CAAC,CACX,WAAYA,EAAgB,CACpC,CAAO,EAKD,mBAAoB,CAAC,kBAAkB,EAKvC,eAAgB,CAAC,CACf,OAAQ,CAAC,GAAGC,GAAgB,EAAE,SAAU,MAAM,CACtD,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQ,CAAC,GAAGA,GAAgB,EAAE,SAAU,MAAM,CACtD,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQP,EAAU,CAC1B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQA,EAAU,CAC1B,CAAO,EAKD,gBAAiB,CAAC,CAChB,QAAS,CAAC,GAAGO,GAAgB,EAAE,OAAQ,QAAQ,CACvD,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkB,CAAC/E,EAAUW,EAAqBD,CAAgB,CAC1E,CAAO,EAKD,YAAa,CAAC,CACZ,QAAS,CAAC,GAAIV,EAAUyB,GAA2BV,EAAiB,CAC5E,CAAO,EAKD,gBAAiB,CAAC,CAChB,QAASyD,EAAU,CAC3B,CAAO,EAQD,OAAQ,CAAC,CACP,OAAQ,CAER,GAAI,OAAQxB,EAAahB,GAA2BT,EAAiB,CAC7E,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQiD,EAAU,CAC1B,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,OAAQvB,EAAkBjB,GAA2BT,EAAiB,CAC/F,CAAO,EAKD,qBAAsB,CAAC,CACrB,eAAgBiD,EAAU,CAClC,CAAO,EAKD,SAAU,CAAC,CACT,KAAMM,EAAgB,CAC9B,CAAO,EAOD,eAAgB,CAAC,YAAY,EAK7B,aAAc,CAAC,CACb,KAAMN,EAAU,CACxB,CAAO,EAOD,gBAAiB,CAAC,CAChB,cAAe,CAACxE,EAAUe,EAAiB,CACnD,CAAO,EAOD,oBAAqB,CAAC,CACpB,cAAeyD,EAAU,CACjC,CAAO,EAKD,eAAgB,CAAC,CACf,aAAcM,EAAgB,CACtC,CAAO,EAKD,mBAAoB,CAAC,CACnB,aAAcN,EAAU,CAChC,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAAC,OAAQtB,EAAiBlB,GAA2BT,EAAiB,CAC7F,CAAO,EAKD,oBAAqB,CAAC,CACpB,cAAeiD,EAAU,CACjC,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAACxE,EAAUW,EAAqBD,CAAgB,CACjE,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,GAAGsE,GAAgB,EAAE,cAAe,cAAc,CACxE,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,GAAc,CAClC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,SAAU,UAAW,UAAW,OAAQ,SAAU,MAAM,CACvE,EAAE,cAAc,EAKjB,iBAAkB,CAAC,CACjB,KAAM,CAAC,MAAO,WAAY,YAAa,SAAS,CACxD,CAAO,EAKD,wBAAyB,CAAC,CACxB,cAAe,CAAChF,CAAQ,CAChC,CAAO,EACD,6BAA8B,CAAC,CAC7B,mBAAoBiF,EAAsB,CAClD,CAAO,EACD,2BAA4B,CAAC,CAC3B,iBAAkBA,EAAsB,CAChD,CAAO,EACD,+BAAgC,CAAC,CAC/B,mBAAoBT,EAAU,CACtC,CAAO,EACD,6BAA8B,CAAC,CAC7B,iBAAkBA,EAAU,CACpC,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAsB,CAC7C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAsB,CAC3C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAU,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAU,CAC/B,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAsB,CAC7C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAsB,CAC3C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAU,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAU,CAC/B,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAsB,CAC7C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAsB,CAC3C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAU,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAU,CAC/B,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAsB,CAC7C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAsB,CAC3C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAU,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAU,CAC/B,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAsB,CAC7C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAsB,CAC3C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAU,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAU,CAC/B,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAsB,CAC7C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAsB,CAC3C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAU,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAU,CAC/B,CAAO,EACD,oBAAqB,CAAC,CACpB,cAAe,CAAC7D,EAAqBD,CAAgB,CAC7D,CAAO,EACD,6BAA8B,CAAC,CAC7B,mBAAoBuE,EAAsB,CAClD,CAAO,EACD,2BAA4B,CAAC,CAC3B,iBAAkBA,EAAsB,CAChD,CAAO,EACD,+BAAgC,CAAC,CAC/B,mBAAoBT,EAAU,CACtC,CAAO,EACD,6BAA8B,CAAC,CAC7B,iBAAkBA,EAAU,CACpC,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAe,CAAC,SAAU,SAAS,CAC3C,CAAO,EACD,yBAA0B,CAAC,CACzB,cAAe,CAAC,CACd,QAAS,CAAC,OAAQ,QAAQ,EAC1B,SAAU,CAAC,OAAQ,QAAQ,CAC5B,CAAA,CACT,CAAO,EACD,wBAAyB,CAAC,CACxB,iBAAkBd,EAAa,CACvC,CAAO,EACD,uBAAwB,CAAC,CACvB,aAAc,CAAC1D,CAAQ,CAC/B,CAAO,EACD,4BAA6B,CAAC,CAC5B,kBAAmBiF,EAAsB,CACjD,CAAO,EACD,0BAA2B,CAAC,CAC1B,gBAAiBA,EAAsB,CAC/C,CAAO,EACD,8BAA+B,CAAC,CAC9B,kBAAmBT,EAAU,CACrC,CAAO,EACD,4BAA6B,CAAC,CAC5B,gBAAiBA,EAAU,CACnC,CAAO,EAKD,YAAa,CAAC,CACZ,KAAM,CAAC,QAAS,YAAa,OAAO,CAC5C,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAAC,SAAU,UAAW,UAAW,OAAQ,SAAU,MAAM,CAChF,CAAO,EAKD,gBAAiB,CAAC,CAChB,KAAMC,GAAe,CAC7B,CAAO,EAKD,cAAe,CAAC,CACd,KAAMC,GAAa,CAC3B,CAAO,EAKD,YAAa,CAAC,CACZ,KAAMC,EAAW,CACzB,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,QAAS,WAAW,CAC1C,CAAO,EAKD,aAAc,CAAC,CACb,KAAM,CAAC,OAAQhE,EAAqBD,CAAgB,CAC5D,CAAO,EAQD,OAAQ,CAAC,CACP,OAAQ,CAER,GAAI,OAAQC,EAAqBD,CAAgB,CACzD,CAAO,EAKD,KAAM,CAAC,CACL,KAAMwE,GAAS,CACvB,CAAO,EAKD,WAAY,CAAC,CACX,WAAY,CAAClF,EAAUW,EAAqBD,CAAgB,CACpE,CAAO,EAKD,SAAU,CAAC,CACT,SAAU,CAACV,EAAUW,EAAqBD,CAAgB,CAClE,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAEf,GAAI,OAAQyC,EAAiBnB,GAA2BT,EAAiB,CACjF,CAAO,EAKD,oBAAqB,CAAC,CACpB,cAAeiD,EAAU,CACjC,CAAO,EAKD,UAAW,CAAC,CACV,UAAW,CAAC,GAAIxE,EAAUW,EAAqBD,CAAgB,CACvE,CAAO,EAKD,aAAc,CAAC,CACb,aAAc,CAACV,EAAUW,EAAqBD,CAAgB,CACtE,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CACpE,CAAO,EAKD,SAAU,CAAC,CACT,SAAU,CAACV,EAAUW,EAAqBD,CAAgB,CAClE,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CACnE,CAAO,EAKD,kBAAmB,CAAC,CAClB,kBAAmB,CAEnB,GAAI,OAAQC,EAAqBD,CAAgB,CACzD,CAAO,EAKD,gBAAiB,CAAC,CAChB,gBAAiBwE,GAAS,CAClC,CAAO,EAKD,sBAAuB,CAAC,CACtB,sBAAuB,CAAClF,EAAUW,EAAqBD,CAAgB,CAC/E,CAAO,EAKD,oBAAqB,CAAC,CACpB,oBAAqB,CAACV,EAAUW,EAAqBD,CAAgB,CAC7E,CAAO,EAKD,qBAAsB,CAAC,CACrB,qBAAsB,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CAClF,CAAO,EAKD,sBAAuB,CAAC,CACtB,sBAAuB,CAACV,EAAUW,EAAqBD,CAAgB,CAC/E,CAAO,EAKD,kBAAmB,CAAC,CAClB,kBAAmB,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CAC/E,CAAO,EAKD,mBAAoB,CAAC,CACnB,mBAAoB,CAACV,EAAUW,EAAqBD,CAAgB,CAC5E,CAAO,EAKD,oBAAqB,CAAC,CACpB,oBAAqB,CAACV,EAAUW,EAAqBD,CAAgB,CAC7E,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkB,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CAC9E,CAAO,EAQD,kBAAmB,CAAC,CAClB,OAAQ,CAAC,WAAY,UAAU,CACvC,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkBoD,EAAuB,CACjD,CAAO,EAKD,mBAAoB,CAAC,CACnB,mBAAoBA,EAAuB,CACnD,CAAO,EAKD,mBAAoB,CAAC,CACnB,mBAAoBA,EAAuB,CACnD,CAAO,EAKD,eAAgB,CAAC,CACf,MAAO,CAAC,OAAQ,OAAO,CAC/B,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,MAAO,QAAQ,CACjC,CAAO,EAQD,WAAY,CAAC,CACX,WAAY,CAAC,GAAI,MAAO,SAAU,UAAW,SAAU,YAAa,OAAQnD,EAAqBD,CAAgB,CACzH,CAAO,EAKD,sBAAuB,CAAC,CACtB,WAAY,CAAC,SAAU,UAAU,CACzC,CAAO,EAKD,SAAU,CAAC,CACT,SAAU,CAACV,EAAU,UAAWW,EAAqBD,CAAgB,CAC7E,CAAO,EAKD,KAAM,CAAC,CACL,KAAM,CAAC,SAAU,UAAW6C,EAAW5C,EAAqBD,CAAgB,CACpF,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAACV,EAAUW,EAAqBD,CAAgB,CAC/D,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,OAAQ8C,EAAc7C,EAAqBD,CAAgB,CAC7E,CAAO,EAQD,SAAU,CAAC,CACT,SAAU,CAAC,SAAU,SAAS,CACtC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC2C,EAAkB1C,EAAqBD,CAAgB,CAC7E,CAAO,EAKD,qBAAsB,CAAC,CACrB,qBAAsBiD,EAA0B,CACxD,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQwB,GAAW,CAC3B,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,GAAW,CAC/B,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,GAAW,CAC/B,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,GAAW,CAC/B,CAAO,EAKD,MAAO,CAAC,CACN,MAAOC,GAAU,CACzB,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAAU,CAC7B,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAAU,CAC7B,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAAU,CAC7B,CAAO,EAKD,WAAY,CAAC,UAAU,EAKvB,KAAM,CAAC,CACL,KAAMC,GAAS,CACvB,CAAO,EAKD,SAAU,CAAC,CACT,SAAUA,GAAS,CAC3B,CAAO,EAKD,SAAU,CAAC,CACT,SAAUA,GAAS,CAC3B,CAAO,EAKD,UAAW,CAAC,CACV,UAAW,CAAC1E,EAAqBD,EAAkB,GAAI,OAAQ,MAAO,KAAK,CACnF,CAAO,EAKD,mBAAoB,CAAC,CACnB,OAAQiD,EAA0B,CAC1C,CAAO,EAKD,kBAAmB,CAAC,CAClB,UAAW,CAAC,KAAM,MAAM,CAChC,CAAO,EAKD,UAAW,CAAC,CACV,UAAW2B,GAAc,CACjC,CAAO,EAKD,cAAe,CAAC,CACd,cAAeA,GAAc,CACrC,CAAO,EAKD,cAAe,CAAC,CACd,cAAeA,GAAc,CACrC,CAAO,EAKD,cAAe,CAAC,CACd,cAAeA,GAAc,CACrC,CAAO,EAKD,iBAAkB,CAAC,gBAAgB,EAQnC,OAAQ,CAAC,CACP,OAAQd,EAAU,CAC1B,CAAO,EAKD,WAAY,CAAC,CACX,WAAY,CAAC,OAAQ,MAAM,CACnC,CAAO,EAKD,cAAe,CAAC,CACd,MAAOA,EAAU,CACzB,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQ,CAAC,SAAU,OAAQ,QAAS,aAAc,YAAa,YAAY,CACnF,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,UAAW,UAAW,OAAQ,OAAQ,OAAQ,OAAQ,cAAe,OAAQ,eAAgB,WAAY,OAAQ,YAAa,gBAAiB,QAAS,OAAQ,UAAW,OAAQ,WAAY,aAAc,aAAc,aAAc,WAAY,WAAY,WAAY,WAAY,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,cAAe,cAAe,UAAW,WAAY7D,EAAqBD,CAAgB,CAC1d,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,QAAS,SAAS,CAC3C,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkB,CAAC,OAAQ,MAAM,CACzC,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,GAAI,IAAK,GAAG,CACrC,CAAO,EAKD,kBAAmB,CAAC,CAClB,OAAQ,CAAC,OAAQ,QAAQ,CACjC,CAAO,EAKD,WAAY,CAAC,CACX,WAAYoD,EAAuB,CAC3C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,EAAuB,CAC3C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,aAAc,CAAC,CACb,KAAM,CAAC,QAAS,MAAO,SAAU,YAAY,CACrD,CAAO,EAKD,YAAa,CAAC,CACZ,KAAM,CAAC,SAAU,QAAQ,CACjC,CAAO,EAKD,YAAa,CAAC,CACZ,KAAM,CAAC,OAAQ,IAAK,IAAK,MAAM,CACvC,CAAO,EAKD,kBAAmB,CAAC,CAClB,KAAM,CAAC,YAAa,WAAW,CACvC,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAAC,OAAQ,OAAQ,cAAc,CAC9C,CAAO,EAKD,UAAW,CAAC,CACV,YAAa,CAAC,IAAK,OAAQ,OAAO,CAC1C,CAAO,EAKD,UAAW,CAAC,CACV,YAAa,CAAC,IAAK,KAAM,MAAM,CACvC,CAAO,EAKD,WAAY,CAAC,kBAAkB,EAK/B,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,OAAQ,MAAO,MAAM,CAC9C,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAAC,OAAQ,SAAU,WAAY,YAAanD,EAAqBD,CAAgB,CACxG,CAAO,EAQD,KAAM,CAAC,CACL,KAAM,CAAC,OAAQ,GAAG8D,EAAY,CAAA,CACtC,CAAO,EAKD,WAAY,CAAC,CACX,OAAQ,CAACxE,EAAUyB,GAA2BV,GAAmBE,EAAiB,CAC1F,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,GAAGuD,EAAY,CAAA,CACxC,CAAO,EAQD,sBAAuB,CAAC,CACtB,sBAAuB,CAAC,OAAQ,MAAM,CACvC,CAAA,CACF,EACD,uBAAwB,CACtB,SAAU,CAAC,aAAc,YAAY,EACrC,WAAY,CAAC,eAAgB,cAAc,EAC3C,MAAO,CAAC,UAAW,UAAW,QAAS,MAAO,MAAO,QAAS,SAAU,MAAM,EAC9E,UAAW,CAAC,QAAS,MAAM,EAC3B,UAAW,CAAC,MAAO,QAAQ,EAC3B,KAAM,CAAC,QAAS,OAAQ,QAAQ,EAChC,IAAK,CAAC,QAAS,OAAO,EACtB,EAAG,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EAClD,GAAI,CAAC,KAAM,IAAI,EACf,GAAI,CAAC,KAAM,IAAI,EACf,EAAG,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EAClD,GAAI,CAAC,KAAM,IAAI,EACf,GAAI,CAAC,KAAM,IAAI,EACf,KAAM,CAAC,IAAK,GAAG,EACf,YAAa,CAAC,SAAS,EACvB,aAAc,CAAC,cAAe,mBAAoB,aAAc,cAAe,cAAc,EAC7F,cAAe,CAAC,YAAY,EAC5B,mBAAoB,CAAC,YAAY,EACjC,aAAc,CAAC,YAAY,EAC3B,cAAe,CAAC,YAAY,EAC5B,eAAgB,CAAC,YAAY,EAC7B,aAAc,CAAC,UAAW,UAAU,EACpC,QAAS,CAAC,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,YAAY,EACtM,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,iBAAkB,CAAC,mBAAoB,kBAAkB,EACzD,WAAY,CAAC,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,YAAY,EAC3H,aAAc,CAAC,aAAc,YAAY,EACzC,aAAc,CAAC,aAAc,YAAY,EACzC,eAAgB,CAAC,iBAAkB,iBAAkB,iBAAkB,iBAAkB,iBAAkB,iBAAkB,iBAAkB,gBAAgB,EAC/J,iBAAkB,CAAC,iBAAkB,gBAAgB,EACrD,iBAAkB,CAAC,iBAAkB,gBAAgB,EACrD,UAAW,CAAC,cAAe,cAAe,gBAAgB,EAC1D,iBAAkB,CAAC,YAAa,cAAe,cAAe,aAAa,EAC3E,WAAY,CAAC,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,WAAW,EACnH,YAAa,CAAC,YAAa,WAAW,EACtC,YAAa,CAAC,YAAa,WAAW,EACtC,WAAY,CAAC,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,WAAW,EACnH,YAAa,CAAC,YAAa,WAAW,EACtC,YAAa,CAAC,YAAa,WAAW,EACtC,MAAO,CAAC,UAAW,UAAW,UAAU,EACxC,UAAW,CAAC,OAAO,EACnB,UAAW,CAAC,OAAO,EACnB,WAAY,CAAC,OAAO,CACrB,EACD,+BAAgC,CAC9B,YAAa,CAAC,SAAS,CACxB,EACD,wBAAyB,CAAC,IAAK,KAAM,QAAS,WAAY,SAAU,kBAAmB,OAAQ,eAAgB,aAAc,SAAU,cAAe,WAAW,CAClK,CACH,EAsDMe,GAAuB7G,GAAoB2D,EAAgB,ECr9F1D,SAASmD,MAAMC,EAAsB,CACnC,OAAAF,GAAQlO,GAAKoO,CAAM,CAAC,CAC7B,CCCA,MAAMC,GAAiBjO,GACrB,yRACA,CACE,SAAU,CACR,QAAS,CACP,QAAS,yDACT,YACE,qEACF,QACE,iFACF,UACE,+DACF,MAAO,+CACP,KAAM,iDACR,EACA,KAAM,CACJ,QAAS,iBACT,GAAI,sBACJ,GAAI,uBACJ,KAAM,WAAA,CAEV,EACA,gBAAiB,CACf,QAAS,UACT,KAAM,SAAA,CACR,CAEJ,EAQMkO,EAASC,EAAM,WACnB,CAAC,CAAE,UAAAzM,EAAW,QAAAlB,EAAS,KAAA4N,EAAM,QAAAC,EAAU,GAAO,GAAGlO,CAAM,EAAGmO,IAAQ,CAC1D,MAAAC,EAAOF,EAAUG,GAAO,SAE5B,OAAAC,EAAA,IAACF,EAAA,CACC,UAAWR,GAAGE,GAAe,CAAE,QAAAzN,EAAS,KAAA4N,EAAM,UAAA1M,CAAA,CAAW,CAAC,EAC1D,IAAA4M,EACC,GAAGnO,CAAA,CACN,CAAA,CAGN,EACA+N,EAAO,YAAc,SCjDrB,MAAMQ,EAAOP,EAGX,WAAA,CAAC,CAAE,UAAAzM,EAAW,GAAGvB,CAAM,EAAGmO,IAC1BG,EAAA,IAAC,MAAA,CACC,IAAAH,EACA,UAAWP,GACT,2DACArM,CACF,EACC,GAAGvB,CAAA,CACN,CACD,EACDuO,EAAK,YAAc,OAEnB,MAAMC,GAAaR,EAGjB,WAAA,CAAC,CAAE,UAAAzM,EAAW,GAAGvB,CAAS,EAAAmO,UACzB,MAAI,CAAA,IAAAA,EAAU,UAAWP,GAAG,gCAAiCrM,CAAS,EAAI,GAAGvB,EAAO,CACtF,EACDwO,GAAW,YAAc,aAEzB,MAAMC,GAAYT,EAGhB,WAAA,CAAC,CAAE,UAAAzM,EAAW,GAAGvB,CAAM,EAAGmO,IAC1BG,EAAA,IAAC,KAAA,CACC,IAAAH,EACA,UAAWP,GACT,qDACArM,CACF,EACC,GAAGvB,CAAA,CACN,CACD,EACDyO,GAAU,YAAc,YAExB,MAAMC,GAAkBV,EAGtB,WAAA,CAAC,CAAE,UAAAzM,EAAW,GAAGvB,CAAM,EAAGmO,IAC1BG,EAAA,IAAC,IAAA,CACC,IAAAH,EACA,UAAWP,GAAG,gCAAiCrM,CAAS,EACvD,GAAGvB,CAAA,CACN,CACD,EACD0O,GAAgB,YAAc,kBAE9B,MAAMC,EAAcX,EAGlB,WAAA,CAAC,CAAE,UAAAzM,EAAW,GAAGvB,CAAS,EAAAmO,UACzB,MAAI,CAAA,IAAAA,EAAU,UAAWP,GAAG,WAAYrM,CAAS,EAAI,GAAGvB,EAAO,CACjE,EACD2O,EAAY,YAAc,cAE1B,MAAMC,GAAaZ,EAGjB,WAAA,CAAC,CAAE,UAAAzM,EAAW,GAAGvB,CAAS,EAAAmO,UACzB,MAAI,CAAA,IAAAA,EAAU,UAAWP,GAAG,6BAA8BrM,CAAS,EAAI,GAAGvB,EAAO,CACnF,EACD4O,GAAW,YAAc,aC7DzB,MAAMC,GAAQb,EAAM,WAClB,CAAC,CAAE,UAAAzM,EAAW,KAAAuN,EAAM,GAAG9O,CAAA,EAASmO,IAE5BG,EAAA,IAAC,QAAA,CACC,KAAAQ,EACA,UAAWlB,GACT,+VACArM,CACF,EACA,IAAA4M,EACC,GAAGnO,CAAA,CACN,CAGN,EACA6O,GAAM,YAAc,QChBpB,IAAIE,GAAO,QACPC,GAAQhB,EAAgB,WAAC,CAAChO,EAAOiP,IACZX,EAAG,IACxBY,GAAU,MACV,CACE,GAAGlP,EACH,IAAKiP,EACL,YAAcE,GAAU,OACPA,EAAM,OACV,QAAQ,iCAAiC,KACpDhN,EAAAnC,EAAM,cAAN,MAAAmC,EAAA,KAAAnC,EAAoBmP,GAChB,CAACA,EAAM,kBAAoBA,EAAM,OAAS,GAAGA,EAAM,eAAgB,EAC/E,CACA,CACG,CACF,EACDH,GAAM,YAAcD,GACpB,IAAIK,GAAOJ,GCjBX,MAAMK,GAAgBxP,GACpB,4FACF,EAEMmP,GAAQhB,EAIZ,WAAA,CAAC,CAAE,UAAAzM,EAAW,GAAGvB,CAAM,EAAGmO,IAC1BG,EAAA,IAACgB,GAAA,CACC,IAAAnB,EACA,UAAWP,GAAGyB,GAAc,EAAG9N,CAAS,EACvC,GAAGvB,CAAA,CACN,CACD,EACDgP,GAAM,YAAcM,GAAoB,YCrBjC,MAAMC,GAAO,CAACC,EAAMC,EAAMC,EAAKC,IAAS,aAC7C,MAAMC,EAAO,CAACF,EAAK,CACjB,KAAAD,EACA,GAAIE,GAAQ,CAAE,CAClB,CAAG,EACD,IAAIE,GAAA1N,EAAAqN,GAAA,YAAAA,EAAM,WAAN,YAAArN,EAAgB,SAAhB,MAAA0N,EAAwB,QAC1B,OAAOL,EAAK,SAAS,OAAO,QAAQI,EAAM,OAAQ,kBAAmB,EAAI,EAEvEE,GAASF,EAAK,CAAC,CAAC,IAAGA,EAAK,CAAC,EAAI,mBAAmBA,EAAK,CAAC,CAAC,KACvDG,GAAAC,EAAAR,GAAA,YAAAA,EAAM,WAAN,YAAAQ,EAAgB,SAAhB,MAAAD,EAAwB,KAC1BP,EAAK,SAAS,OAAO,KAAK,GAAGI,CAAI,EACxB,uBAAS,MAClB,QAAQ,KAAK,GAAGA,CAAI,CAExB,EACMK,GAAgB,CAAE,EACXC,GAAW,CAACV,EAAMC,EAAMC,EAAKC,IAAS,CAC7CG,GAASJ,CAAG,GAAKO,GAAcP,CAAG,IAClCI,GAASJ,CAAG,IAAGO,GAAcP,CAAG,EAAI,IAAI,MAC5CH,GAAKC,EAAMC,EAAMC,EAAKC,CAAI,EAC5B,EACMQ,GAAY,CAACX,EAAMY,IAAO,IAAM,CACpC,GAAIZ,EAAK,cACPY,EAAI,MACC,CACL,MAAMC,EAAc,IAAM,CACxB,WAAW,IAAM,CACfb,EAAK,IAAI,cAAea,CAAW,CACpC,EAAE,CAAC,EACJD,EAAI,CACL,EACDZ,EAAK,GAAG,cAAea,CAAW,CACtC,CACA,EACaC,GAAiB,CAACd,EAAMe,EAAIH,IAAO,CAC9CZ,EAAK,eAAee,EAAIJ,GAAUX,EAAMY,CAAE,CAAC,CAC7C,EACaI,GAAgB,CAAChB,EAAMiB,EAAKF,EAAIH,IAAO,CAElD,GADIN,GAASS,CAAE,IAAGA,EAAK,CAACA,CAAE,GACtBf,EAAK,QAAQ,SAAWA,EAAK,QAAQ,QAAQ,QAAQiB,CAAG,EAAI,GAAI,OAAOH,GAAed,EAAMe,EAAIH,CAAE,EACtGG,EAAG,QAAQ,GAAK,CACVf,EAAK,QAAQ,GAAG,QAAQ,CAAC,EAAI,GAAGA,EAAK,QAAQ,GAAG,KAAK,CAAC,CAC9D,CAAG,EACDA,EAAK,cAAciB,EAAKN,GAAUX,EAAMY,CAAE,CAAC,CAC7C,EACaM,GAAqB,CAACH,EAAIf,EAAMmB,EAAU,CAAA,IACjD,CAACnB,EAAK,WAAa,CAACA,EAAK,UAAU,QACrCU,GAASV,EAAM,eAAgB,yCAA0C,CACvE,UAAWA,EAAK,SACtB,CAAK,EACM,IAEFA,EAAK,mBAAmBe,EAAI,CACjC,IAAKI,EAAQ,IACb,SAAU,CAACC,EAAcC,IAAmB,OAC1C,KAAI1O,EAAAwO,EAAQ,WAAR,YAAAxO,EAAkB,QAAQ,qBAAsB,IAAMyO,EAAa,SAAS,iBAAiB,SAAWA,EAAa,sBAAwB,CAACC,EAAeD,EAAa,qBAAsBL,CAAE,EAAG,MAAO,EACtN,CACA,CAAG,EAGUT,GAAWgB,GAAO,OAAOA,GAAQ,SACjCC,GAAWD,GAAO,OAAOA,GAAQ,UAAYA,IAAQ,KC7D5DE,GAAkB,oGAClBC,GAAe,CACnB,QAAS,IACT,QAAS,IACT,OAAQ,IACR,QAAS,IACT,OAAQ,IACR,QAAS,IACT,SAAU,IACV,QAAS,IACT,SAAU,IACV,QAAS,IACT,SAAU,IACV,SAAU,IACV,SAAU,IACV,SAAU,IACV,QAAS,IACT,SAAU,IACV,WAAY,IACZ,UAAW,IACX,SAAU,IACV,QAAS,GACX,EACMC,GAAqBtS,GAAKqS,GAAarS,CAAC,EACjCuS,GAAWC,GAAQA,EAAK,QAAQJ,GAAiBE,EAAkB,ECvBhF,IAAIG,GAAiB,CACnB,SAAU,kBACV,cAAe,GACf,oBAAqB,GACrB,2BAA4B,GAC5B,mBAAoB,GACpB,2BAA4B,CAAC,KAAM,SAAU,IAAK,GAAG,EACrD,YAAa,GACb,SAAAF,EACF,EACO,MAAMG,GAAc,CAACX,EAAU,KAAO,CAC3CU,GAAiB,CACf,GAAGA,GACH,GAAGV,CACJ,CACH,EACaY,GAAc,IAAMF,GCjBjC,IAAIT,GACG,MAAMY,GAAUC,GAAY,CACjCb,GAAea,CACjB,EACaC,GAAU,IAAMd,GCFhBe,GAAmB,CAC9B,KAAM,WACN,KAAKF,EAAU,CACbH,GAAYG,EAAS,QAAQ,KAAK,EAClCD,GAAQC,CAAQ,CACpB,CACA,ECHaG,GAAcC,EAAAA,cAAe,EACnC,MAAMC,EAAiB,CAC5B,aAAc,CACZ,KAAK,eAAiB,CAAE,CAC5B,CACE,kBAAkBC,EAAY,CAC5BA,EAAW,QAAQxB,GAAM,CAClB,KAAK,eAAeA,CAAE,IAAG,KAAK,eAAeA,CAAE,EAAI,GAC9D,CAAK,CACL,CACE,mBAAoB,CAClB,OAAO,OAAO,KAAK,KAAK,cAAc,CAC1C,CACA,CCfA,MAAMyB,GAAc,CAACrS,EAAOsS,IAAW,CACrC,MAAM9D,EAAM+D,EAAAA,OAAQ,EACpBC,OAAAA,EAAAA,UAAU,IAAM,CACdhE,EAAI,QAAiCxO,CACzC,EAAK,CAACA,EAAOsS,CAAM,CAAC,EACX9D,EAAI,OACb,EACMiE,GAAa,CAAC5C,EAAM6C,EAAUC,EAAWC,IAAc/C,EAAK,UAAU6C,EAAUC,EAAWC,CAAS,EACpGC,GAAe,CAAChD,EAAM6C,EAAUC,EAAWC,IAAcE,EAAW,YAACL,GAAW5C,EAAM6C,EAAUC,EAAWC,CAAS,EAAG,CAAC/C,EAAM6C,EAAUC,EAAWC,CAAS,CAAC,EACtJG,GAAiB,CAACnC,EAAIvQ,EAAQ,KAAO,aAChD,KAAM,CACJ,KAAM2S,CACV,EAAM3S,EACE,CACJ,KAAM4S,EACN,UAAWC,CACf,EAAMC,EAAU,WAAClB,EAAW,GAAK,CAAE,EAC3BpC,EAAOmD,GAAiBC,GAAmBlB,GAAS,EAE1D,GADIlC,GAAQ,CAACA,EAAK,mBAAkBA,EAAK,iBAAmB,IAAIsC,IAC5D,CAACtC,EAAM,CACTU,GAASV,EAAM,sBAAuB,wFAAwF,EAC9H,MAAMuD,EAAY,CAAClM,EAAGmM,IAChBlD,GAASkD,CAAkB,EAAUA,EACrCjC,GAASiC,CAAkB,GAAKlD,GAASkD,EAAmB,YAAY,EAAUA,EAAmB,aAClG,MAAM,QAAQnM,CAAC,EAAIA,EAAEA,EAAE,OAAS,CAAC,EAAIA,EAExCoM,EAAc,CAACF,EAAW,CAAA,EAAI,EAAK,EACzC,OAAAE,EAAY,EAAIF,EAChBE,EAAY,KAAO,CAAE,EACrBA,EAAY,MAAQ,GACbA,CACX,EACM9Q,EAAAqN,EAAK,QAAQ,QAAb,MAAArN,EAAoB,MAAM+N,GAASV,EAAM,oBAAqB,qHAAqH,EACvL,MAAM0D,EAAc,CAClB,GAAG3B,GAAa,EAChB,GAAG/B,EAAK,QAAQ,MAChB,GAAGxP,CACJ,EACK,CACJ,YAAAmT,EACA,UAAAZ,CACJ,EAAMW,EACJ,IAAInB,EAAaxB,GAAMsC,KAAwBhD,EAAAL,EAAK,UAAL,YAAAK,EAAc,WAC7DkC,EAAajC,GAASiC,CAAU,EAAI,CAACA,CAAU,EAAIA,GAAc,CAAC,aAAa,GAC/EhC,GAAAC,EAAAR,EAAK,kBAAiB,oBAAtB,MAAAO,EAAA,KAAAC,EAA0C+B,GAC1C,MAAMqB,GAAS5D,EAAK,eAAiBA,EAAK,uBAAyBuC,EAAW,MAAMxS,GAAKmR,GAAmBnR,EAAGiQ,EAAM0D,CAAW,CAAC,EAC3HG,EAAWb,GAAahD,EAAMxP,EAAM,KAAO,KAAMkT,EAAY,SAAW,WAAanB,EAAaA,EAAW,CAAC,EAAGQ,CAAS,EAC1He,EAAO,IAAMD,EACbE,EAAU,IAAMnB,GAAW5C,EAAMxP,EAAM,KAAO,KAAMkT,EAAY,SAAW,WAAanB,EAAaA,EAAW,CAAC,EAAGQ,CAAS,EAC7H,CAAClT,EAAGmU,CAAI,EAAIC,EAAAA,SAASH,CAAI,EAC/B,IAAII,EAAW3B,EAAW,KAAM,EAC5B/R,EAAM,MAAK0T,EAAW,GAAG1T,EAAM,GAAG,GAAG0T,CAAQ,IACjD,MAAMC,EAAmB3B,GAAY0B,CAAQ,EACvCE,EAAY1B,EAAM,OAAC,EAAI,EAC7BC,EAAAA,UAAU,IAAM,CACd,KAAM,CACJ,SAAA0B,EACA,cAAAC,CACN,EAAQZ,EACJU,EAAU,QAAU,GAChB,CAACR,GAAS,CAACD,IACTnT,EAAM,IACRwQ,GAAchB,EAAMxP,EAAM,IAAK+R,EAAY,IAAM,CAC3C6B,EAAU,SAASJ,EAAKD,CAAO,CAC7C,CAAS,EAEDjD,GAAed,EAAMuC,EAAY,IAAM,CACjC6B,EAAU,SAASJ,EAAKD,CAAO,CAC7C,CAAS,GAGDH,GAASO,GAAoBA,IAAqBD,GAAYE,EAAU,SAC1EJ,EAAKD,CAAO,EAEd,MAAMQ,EAAa,IAAM,CACnBH,EAAU,SAASJ,EAAKD,CAAO,CACpC,EACD,OAAIM,IAAUrE,GAAA,MAAAA,EAAM,GAAGqE,EAAUE,IAC7BD,IAAetE,GAAA,MAAAA,EAAM,MAAM,GAAGsE,EAAeC,IAC1C,IAAM,CACXH,EAAU,QAAU,GAChBpE,IAAMqE,GAAA,MAAAA,EAAU,MAAM,KAAK,QAAQzU,GAAKoQ,EAAK,IAAIpQ,EAAG2U,CAAU,IAC9DD,GAAiBtE,GAAMsE,EAAc,MAAM,GAAG,EAAE,QAAQ1U,GAAKoQ,EAAK,MAAM,IAAIpQ,EAAG2U,CAAU,CAAC,CAC/F,CACL,EAAK,CAACvE,EAAMkE,CAAQ,CAAC,EACnBvB,EAAAA,UAAU,IAAM,CACVyB,EAAU,SAAWR,GACvBI,EAAKF,CAAI,CAEZ,EAAE,CAAC9D,EAAM+C,EAAWa,CAAK,CAAC,EAC3B,MAAMY,EAAM,CAAC3U,EAAGmQ,EAAM4D,CAAK,EAK3B,GAJAY,EAAI,EAAI3U,EACR2U,EAAI,KAAOxE,EACXwE,EAAI,MAAQZ,EACRA,GACA,CAACA,GAAS,CAACD,EAAa,OAAOa,EACnC,MAAM,IAAI,QAAQC,GAAW,CACvBjU,EAAM,IACRwQ,GAAchB,EAAMxP,EAAM,IAAK+R,EAAY,IAAMkC,GAAS,EAE1D3D,GAAed,EAAMuC,EAAY,IAAMkC,EAAO,CAAE,CAEtD,CAAG,CACH,EC1GA;AAAA;AAAA;AAAA;AAAA;AAAA,GAOA,MAAMC,GAAexN,GAAWA,EAAO,QAAQ,qBAAsB,OAAO,EAAE,YAAa,EACrFyN,GAAezN,GAAWA,EAAO,QACrC,wBACA,CAAC0N,EAAOC,EAAIC,IAAOA,EAAKA,EAAG,YAAW,EAAKD,EAAG,YAAW,CAC3D,EACME,GAAgB7N,GAAW,CAC/B,MAAM8N,EAAYL,GAAYzN,CAAM,EACpC,OAAO8N,EAAU,OAAO,CAAC,EAAE,YAAW,EAAKA,EAAU,MAAM,CAAC,CAC9D,EACMC,GAAe,IAAIC,IAAYA,EAAQ,OAAO,CAACnT,EAAW+C,EAAOqQ,IAC9D,EAAQpT,GAAcA,EAAU,KAAI,IAAO,IAAMoT,EAAM,QAAQpT,CAAS,IAAM+C,CACtF,EAAE,KAAK,GAAG,EAAE,KAAM,EACbsQ,GAAe5U,GAAU,CAC7B,UAAW6U,KAAQ7U,EACjB,GAAI6U,EAAK,WAAW,OAAO,GAAKA,IAAS,QAAUA,IAAS,QAC1D,MAAO,EAGb,ECzBA;AAAA;AAAA;AAAA;AAAA;AAAA,GAOA,IAAIC,GAAoB,CACtB,MAAO,6BACP,MAAO,GACP,OAAQ,GACR,QAAS,YACT,KAAM,OACN,OAAQ,eACR,YAAa,EACb,cAAe,QACf,eAAgB,OAClB,ECjBA;AAAA;AAAA;AAAA;AAAA;AAAA,GAWA,MAAMC,GAAOC,EAAU,WACrB,CAAC,CACC,MAAAC,EAAQ,eACR,KAAAhH,EAAO,GACP,YAAAiH,EAAc,EACd,oBAAAC,EACA,UAAA5T,EAAY,GACZ,SAAA6T,EACA,SAAAC,EACA,GAAG1F,CACJ,EAAExB,IAAQmH,EAAa,cACtB,MACA,CACE,IAAAnH,EACA,GAAG2G,GACH,MAAO7G,EACP,OAAQA,EACR,OAAQgH,EACR,YAAaE,EAAsB,OAAOD,CAAW,EAAI,GAAK,OAAOjH,CAAI,EAAIiH,EAC7E,UAAWT,GAAa,SAAUlT,CAAS,EAC3C,GAAG,CAAC6T,GAAY,CAACR,GAAYjF,CAAI,GAAK,CAAE,cAAe,MAAQ,EAC/D,GAAGA,CACJ,EACD,CACE,GAAG0F,EAAS,IAAI,CAAC,CAACE,EAAKC,CAAK,IAAMF,EAAa,cAACC,EAAKC,CAAK,CAAC,EAC3D,GAAG,MAAM,QAAQJ,CAAQ,EAAIA,EAAW,CAACA,CAAQ,CACvD,CACA,CACA,ECvCA;AAAA;AAAA;AAAA;AAAA;AAAA,GAWA,MAAMK,EAAmB,CAACC,EAAUL,IAAa,CAC/C,MAAMM,EAAYX,EAAU,WAC1B,CAAC,CAAE,UAAAzT,EAAW,GAAGvB,CAAO,EAAEmO,IAAQmH,EAAa,cAACP,GAAM,CACpD,IAAA5G,EACA,SAAAkH,EACA,UAAWZ,GACT,UAAUP,GAAYK,GAAamB,CAAQ,CAAC,CAAC,GAC7C,UAAUA,CAAQ,GAClBnU,CACD,EACD,GAAGvB,CACJ,CAAA,CACF,EACD,OAAA2V,EAAU,YAAcpB,GAAamB,CAAQ,EACtCC,CACT,EC1BA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMC,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,iBAAkB,IAAK,QAAQ,CAAE,EAC/C,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAU,CAAA,CAC3C,EACMC,GAAYJ,EAAiB,aAAcG,EAAU,ECb3D;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CACE,OACA,CACE,EAAG,qIACH,IAAK,OACX,CACA,CACA,EACME,GAAWL,EAAiB,YAAaG,EAAU,ECnBzD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,KAAM,IAAK,SAAU,EACzD,CAAC,WAAY,CAAE,OAAQ,mBAAoB,IAAK,QAAU,CAAA,CAC5D,EACMG,GAAQN,EAAiB,QAASG,EAAU,ECblD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CACE,OACA,CACE,EAAG,2PACH,IAAK,OACX,CACG,EACD,CAAC,OAAQ,CAAE,EAAG,wBAAyB,IAAK,QAAU,CAAA,CACxD,EACMI,GAASP,EAAiB,SAAUG,EAAU,ECnBpD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,6CAA8C,IAAK,QAAQ,CAAE,EAC3E,CACE,OACA,CACE,EAAG,gHACH,IAAK,QACX,CACA,CACA,EACMK,GAAQR,EAAiB,QAASG,EAAU,ECnBlD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,0CAA2C,IAAK,QAAQ,CAAE,EACxE,CAAC,OAAQ,CAAE,EAAG,IAAK,EAAG,IAAK,MAAO,KAAM,OAAQ,KAAM,GAAI,IAAK,IAAK,QAAU,CAAA,CAChF,EACMM,GAAOT,EAAiB,OAAQG,EAAU,ECbhD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,UAAW,IAAK,QAAU,CAAA,CAC1C,EACMO,GAAOV,EAAiB,OAAQG,EAAU,ECdhD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,mBAAoB,IAAK,QAAQ,CAAE,EACjD,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,IAAK,IAAK,QAAU,CAAA,CAC1D,EACMQ,GAASX,EAAiB,SAAUG,EAAU,ECbpD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CACE,OACA,CACE,EAAG,+WACH,IAAK,QACX,CACA,CACA,EACMS,GAAOZ,EAAiB,OAAQG,EAAU,EClBhD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,YAAa,IAAK,QAAQ,CAAE,EAC1C,CAAC,OAAQ,CAAE,EAAG,yBAA0B,IAAK,QAAU,CAAA,CACzD,EACMU,GAAab,EAAiB,cAAeG,EAAU,ECb7D;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CACE,OACA,CACE,EAAG,0IACH,IAAK,QACX,CACA,CACA,EACMW,GAAUd,EAAiB,UAAWG,EAAU,EClBtD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,4CAA6C,IAAK,QAAQ,CAAE,EAC1E,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,IAAK,EAAG,IAAK,IAAK,QAAU,CAAA,CACzD,EACMY,GAAOf,EAAiB,OAAQG,EAAU,ECb1Ca,GAAmBC,GAAgB,CACvC,IAAIC,EACJ,MAAMC,EAA4B,IAAI,IAChCC,EAAW,CAACC,EAASC,IAAY,CACrC,MAAMC,EAAY,OAAOF,GAAY,WAAaA,EAAQH,CAAK,EAAIG,EACnE,GAAI,CAAC,OAAO,GAAGE,EAAWL,CAAK,EAAG,CAChC,MAAMM,EAAgBN,EACtBA,EAASI,IAA4B,OAAOC,GAAc,UAAYA,IAAc,MAAQA,EAAY,OAAO,OAAO,CAAE,EAAEL,EAAOK,CAAS,EAC1IJ,EAAU,QAASM,GAAaA,EAASP,EAAOM,CAAa,CAAC,CACpE,CACG,EACKE,EAAW,IAAMR,EAMjBS,EAAM,CAAE,SAAAP,EAAU,SAAAM,EAAU,gBALV,IAAME,EAKqB,UAJhCH,IACjBN,EAAU,IAAIM,CAAQ,EACf,IAAMN,EAAU,OAAOM,CAAQ,EAEsB,EACxDG,EAAeV,EAAQD,EAAYG,EAAUM,EAAUC,CAAG,EAChE,OAAOA,CACT,EACME,GAAeZ,GAAgBA,EAAcD,GAAgBC,CAAW,EAAID,GClB5Ec,GAAYC,GAAQA,EAC1B,SAASC,GAASL,EAAKM,EAAWH,GAAU,CAC1C,MAAMI,EAAQC,GAAM,qBAClBR,EAAI,UACJ,IAAMM,EAASN,EAAI,UAAU,EAC7B,IAAMM,EAASN,EAAI,gBAAiB,CAAA,CACrC,EACD,OAAAQ,GAAM,cAAcD,CAAK,EAClBA,CACT,CACA,MAAME,GAAcnB,GAAgB,CAClC,MAAMU,EAAME,GAAYZ,CAAW,EAC7BoB,EAAiBJ,GAAaD,GAASL,EAAKM,CAAQ,EAC1D,cAAO,OAAOI,EAAeV,CAAG,EACzBU,CACT,EACMC,GAAUrB,GAAwDmB,GC8PxE,SAASG,GAAkBC,EAAYtH,EAAS,CAC1C,IAAAuH,EACA,GAAA,CACFA,EAAUD,EAAW,OACX,CACV,MAAA,CAoBK,MAlBgB,CACrB,QAAUE,GAAS,CACb,IAAAhW,EACE,MAAAiW,EAASC,GACTA,IAAS,KACJ,KAEF,KAAK,MAAMA,EAAwB,MAAwB,EAE9DC,GAAOnW,EAAK+V,EAAQ,QAAQC,CAAI,IAAM,KAAOhW,EAAK,KACxD,OAAImW,aAAe,QACVA,EAAI,KAAKF,CAAK,EAEhBA,EAAME,CAAG,CAClB,EACA,QAAS,CAACH,EAAMI,IAAaL,EAAQ,QAAQC,EAAM,KAAK,UAAUI,EAA4B,MAAyB,CAAC,EACxH,WAAaJ,GAASD,EAAQ,WAAWC,CAAI,CAC/C,CAEF,CACA,MAAMK,GAAcC,GAAQC,GAAU,CAChC,GAAA,CACI,MAAA5S,EAAS2S,EAAGC,CAAK,EACvB,OAAI5S,aAAkB,QACbA,EAEF,CACL,KAAK6S,EAAa,CACT,OAAAH,GAAWG,CAAW,EAAE7S,CAAM,CACvC,EACA,MAAM8S,EAAa,CACV,OAAA,IAAA,CAEX,QACOxZ,EAAG,CACH,MAAA,CACL,KAAKyZ,EAAc,CACV,OAAA,IACT,EACA,MAAMC,EAAY,CACT,OAAAN,GAAWM,CAAU,EAAE1Z,CAAC,CAAA,CAEnC,CAAA,CAEJ,EACM2Z,GAAc,CAAChZ,EAAQiZ,IAAgB,CAACC,EAAKC,EAAK9B,IAAQ,CAC9D,IAAIzG,EAAU,CACZ,QAASqH,GAAkB,IAAM,YAAY,EAC7C,WAAarB,GAAUA,EACvB,QAAS,EACT,MAAO,CAACwC,EAAgBC,KAAkB,CACxC,GAAGA,EACH,GAAGD,CAAA,GAEL,GAAGH,CACL,EACIK,EAAc,GACZ,MAAAC,MAAyC,IACzCC,MAA+C,IACrD,IAAIrB,EAAUvH,EAAQ,QACtB,GAAI,CAACuH,EACI,OAAAnY,EACL,IAAI6P,IAAS,CACH,QAAA,KACN,uDAAuDe,EAAQ,IAAI,gDACrE,EACAsI,EAAI,GAAGrJ,CAAI,CACb,EACAsJ,EACA9B,CACF,EAEF,MAAMoC,EAAU,IAAM,CACpB,MAAM7C,EAAQhG,EAAQ,WAAW,CAAE,GAAGuI,IAAO,EACtC,OAAAhB,EAAQ,QAAQvH,EAAQ,KAAM,CACnC,MAAAgG,EACA,QAAShG,EAAQ,OAAA,CAClB,CACH,EACM8I,EAAgBrC,EAAI,SACtBA,EAAA,SAAW,CAACT,EAAOI,IAAY,CACjC0C,EAAc9C,EAAOI,CAAO,EACvByC,EAAQ,CACf,EACA,MAAME,EAAe3Z,EACnB,IAAI6P,IAAS,CACXqJ,EAAI,GAAGrJ,CAAI,EACN4J,EAAQ,CACf,EACAN,EACA9B,CACF,EACAA,EAAI,gBAAkB,IAAMsC,EACxB,IAAAC,EACJ,MAAMC,EAAU,IAAM,CACpB,IAAIzX,EAAI0N,EACR,GAAI,CAACqI,EAAS,OACAmB,EAAA,GACKC,EAAA,QAASlJ,GAAO,CAC7B,IAAAyJ,EACJ,OAAOzJ,GAAIyJ,EAAMX,EAAU,IAAA,KAAOW,EAAMH,CAAY,CAAA,CACrD,EACD,MAAMI,IAA4BjK,EAAKc,EAAQ,qBAAuB,KAAO,OAASd,EAAG,KAAKc,GAAUxO,EAAK+W,EAAI,IAAM,KAAO/W,EAAKuX,CAAY,IAAM,OACrJ,OAAOlB,GAAWN,EAAQ,QAAQ,KAAKA,CAAO,CAAC,EAAEvH,EAAQ,IAAI,EAAE,KAAMoJ,GAA6B,CAChG,GAAIA,EACF,GAAI,OAAOA,EAAyB,SAAY,UAAYA,EAAyB,UAAYpJ,EAAQ,QAAS,CAChH,GAAIA,EAAQ,QAAS,CACnB,MAAMqJ,EAAYrJ,EAAQ,QACxBoJ,EAAyB,MACzBA,EAAyB,OAC3B,EACA,OAAIC,aAAqB,QAChBA,EAAU,KAAMlU,GAAW,CAAC,GAAMA,CAAM,CAAC,EAE3C,CAAC,GAAMkU,CAAS,CAAA,CAEjB,QAAA,MACN,uFACF,CAAA,KAEO,OAAA,CAAC,GAAOD,EAAyB,KAAK,EAG1C,MAAA,CAAC,GAAO,MAAM,CAAA,CACtB,EAAE,KAAME,GAAoB,CACvB,IAAAJ,EACE,KAAA,CAACK,EAAUC,CAAa,EAAIF,EAMlC,GALAN,EAAmBhJ,EAAQ,MACzBwJ,GACCN,EAAMX,MAAU,KAAOW,EAAMH,CAChC,EACAT,EAAIU,EAAkB,EAAI,EACtBO,EACF,OAAOV,EAAQ,CACjB,CACD,EAAE,KAAK,IAAM,CACZM,GAA2B,MAAgBA,EAAwBH,EAAkB,MAAM,EAC3FA,EAAmBT,EAAI,EACTG,EAAA,GACdE,EAAyB,QAASnJ,GAAOA,EAAGuJ,CAAgB,CAAC,CAAA,CAC9D,EAAE,MAAOva,GAAM,CACd0a,GAA2B,MAAgBA,EAAwB,OAAQ1a,CAAC,CAAA,CAC7E,CACH,EACA,OAAAgY,EAAI,QAAU,CACZ,WAAagD,GAAe,CAChBzJ,EAAA,CACR,GAAGA,EACH,GAAGyJ,CACL,EACIA,EAAW,UACblC,EAAUkC,EAAW,QAEzB,EACA,aAAc,IAAM,CAClBlC,GAAW,MAAgBA,EAAQ,WAAWvH,EAAQ,IAAI,CAC5D,EACA,WAAY,IAAMA,EAClB,UAAW,IAAMiJ,EAAQ,EACzB,YAAa,IAAMP,EACnB,UAAYjJ,IACVkJ,EAAmB,IAAIlJ,CAAE,EAClB,IAAM,CACXkJ,EAAmB,OAAOlJ,CAAE,CAC9B,GAEF,kBAAoBA,IAClBmJ,EAAyB,IAAInJ,CAAE,EACxB,IAAM,CACXmJ,EAAyB,OAAOnJ,CAAE,CACpC,EAEJ,EACKO,EAAQ,eACHiJ,EAAA,EAEHD,GAAoBD,CAC7B,EACMW,GAAUtB,GC5bHuB,GAAevC,GAAkB,EAC5CsC,GACE,CAACpB,EAAKC,KAAS,CACb,KAAM,KACN,gBAAiB,GACjB,UAAW,GAEX,MAAO,MAAOqB,GAAkC,CAC1CtB,EAAA,CAAE,UAAW,GAAM,EACnB,GAAA,CAEM,QAAA,IAAI,cAAesB,CAAW,EAGtC,MAAMC,EAAiB,CACrB,GAAI,IACJ,SAAU,WACV,MAAOD,EAAY,MACnB,KAAM,SACN,UAAW,IAAI,KAAK,EAAE,YAAY,EAClC,UAAW,IAAI,KAAK,EAAE,YAAY,CACpC,EAEItB,EAAA,CACF,KAAMuB,EACN,gBAAiB,GACjB,UAAW,EAAA,CACZ,QACMtb,EAAO,CACN,cAAA,MAAM,gBAAiBA,CAAK,EAChC+Z,EAAA,CAAE,UAAW,GAAO,EAClB/Z,CAAA,CAEV,EAEA,OAAQ,IAAM,CACR+Z,EAAA,CACF,KAAM,KACN,gBAAiB,EAAA,CAClB,CACH,EAEA,cAAe,MAAOwB,GAAwB,CACtC,KAAA,CAAE,KAAAC,CAAK,EAAIxB,EAAI,EACrB,GAAKwB,EAED,CAAAzB,EAAA,CAAE,UAAW,GAAM,EACnB,GAAA,CAEF,MAAM0B,EAAc,CAAE,GAAGD,EAAM,GAAGD,CAAK,EACnCxB,EAAA,CACF,KAAM0B,EACN,UAAW,EAAA,CACZ,QACMzb,EAAO,CACN,cAAA,MAAM,yBAA0BA,CAAK,EACzC+Z,EAAA,CAAE,UAAW,GAAO,EAClB/Z,CAAA,EAEV,EAEA,QAAUwb,GAAsB,CAC1BzB,EAAA,CACF,KAAAyB,EACA,gBAAiB,CAAC,CAACA,CAAA,CACpB,CAAA,CACH,GAEF,CACE,KAAM,eACN,WAAa/D,IAAW,CACtB,KAAMA,EAAM,KACZ,gBAAiBA,EAAM,eACzB,EAAA,CACF,CAEJ,EClFaiE,GAAmB,IAAM,CACpC,KAAM,CAAE,EAAAvb,CAAA,EAAMqT,GAAe,QAAQ,EAC/B,CAAE,KAAAgI,EAAM,gBAAAG,EAAiB,OAAAC,CAAA,EAAWR,GAAa,EAEvD,aACG,SAAO,CAAA,UAAU,+GAChB,SAACS,EAAA,KAAA,MAAA,CAAI,UAAU,mCAEb,SAAA,CAAAA,EAAA,KAACC,EAAK,CAAA,GAAG,IAAI,UAAU,8BACrB,SAAA,CAAC1M,EAAAA,IAAAwH,GAAA,CAAS,UAAU,SAAU,CAAA,EAC7BxH,EAAA,IAAA,OAAA,CAAK,UAAU,oBAAoB,SAAU,YAAA,CAAA,CAAA,EAChD,EAGAyM,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAAAzM,EAAA,IAAC0M,EAAA,CACC,GAAG,IACH,UAAU,2DAET,WAAE,iBAAiB,CAAA,CACtB,EACA1M,EAAA,IAAC0M,EAAA,CACC,GAAG,UACH,UAAU,2DAET,WAAE,mBAAmB,CAAA,CACxB,EACCH,GAEGE,EAAA,KAAAE,WAAA,CAAA,SAAA,CAAA3M,EAAA,IAAC0M,EAAA,CACC,GAAG,kBACH,UAAU,2DAET,WAAE,sBAAsB,CAAA,CAC3B,EACA1M,EAAA,IAAC0M,EAAA,CACC,GAAG,gBACH,UAAU,2DAET,WAAE,oBAAoB,CAAA,CACzB,IACEN,GAAA,YAAAA,EAAM,QAAS,UAAWA,GAAA,YAAAA,EAAM,QAAS,cACzCpM,EAAA,IAAC0M,EAAA,CACC,GAAG,SACH,UAAU,2DAET,WAAE,kBAAkB,CAAA,CAAA,CACvB,CAEJ,CAAA,CAAA,EAEJ,QAGC,MAAI,CAAA,UAAU,kCACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,2BACb,SAAA,CAACzM,EAAAA,IAAA8H,GAAA,CAAO,UAAU,uDAAwD,CAAA,QACzEvH,GAAM,CAAA,YAAaxP,EAAE,gBAAgB,EAAG,UAAU,MAAO,CAAA,CAAA,CAAA,CAC5D,CACF,CAAA,EAGA0b,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACZ,SAAA,CACCF,EAAAE,EAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACzM,EAAAA,IAAA0M,EAAA,CAAK,GAAG,gBACP,SAAAD,EAAAA,KAAChN,GAAO,QAAQ,QAAQ,KAAK,KAC3B,SAAA,CAACO,EAAAA,IAAAkI,GAAA,CAAK,UAAU,cAAe,CAAA,EAC9BkE,GAAA,YAAAA,EAAM,QAAA,CAAA,CACT,CACF,CAAA,EACApM,EAAAA,IAACP,EAAO,CAAA,QAAQ,QAAQ,KAAK,KAAK,QAAS+M,EACxC,SAAEzb,EAAA,mBAAmB,CACxB,CAAA,CAAA,CACF,CAAA,EAEA0b,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAzM,EAAA,IAAC0M,EAAK,CAAA,GAAG,cACP,SAAA1M,EAAA,IAACP,EAAO,CAAA,QAAQ,QAAQ,KAAK,KAC1B,SAAA1O,EAAE,kBAAkB,CACvB,CAAA,EACF,EACAiP,EAAA,IAAC0M,EAAK,CAAA,GAAG,iBACP,SAAA1M,EAAAA,IAACP,EAAO,CAAA,KAAK,KAAM,SAAA1O,EAAE,qBAAqB,CAAE,CAAA,CAC9C,CAAA,CAAA,EACF,EAIDiP,EAAA,IAAAP,EAAA,CAAO,QAAQ,QAAQ,KAAK,OAAO,UAAU,YAC5C,SAACO,EAAAA,IAAA6H,GAAA,CAAK,UAAU,SAAA,CAAU,CAC5B,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,ECpGa+E,GAAmB,IAAM,CACpC,KAAM,CAAE,EAAA7b,CAAA,EAAMqT,GAAe,QAAQ,EAErC,aACG,SAAO,CAAA,UAAU,yBAChB,SAACqI,EAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACzM,EAAAA,IAAAwH,GAAA,CAAS,UAAU,SAAU,CAAA,EAC7BxH,EAAA,IAAA,OAAA,CAAK,UAAU,oBAAoB,SAAU,YAAA,CAAA,CAAA,EAChD,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gCAAgC,SAE7C,0EAAA,CAAA,CAAA,EACF,EAGAyM,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACzM,EAAA,IAAA,KAAA,CAAG,UAAU,gBAAgB,SAAc,iBAAA,EAC5CyM,EAAAA,KAAC,KAAG,CAAA,UAAU,oBACZ,SAAA,CAACzM,EAAA,IAAA,KAAA,CACC,SAACA,EAAAA,IAAA,IAAA,CAAE,KAAK,IAAI,UAAU,6DACnB,SAAAjP,EAAE,iBAAiB,CACtB,CAAA,EACF,EACAiP,EAAA,IAAC,KACC,CAAA,SAAAA,EAAAA,IAAC,IAAE,CAAA,KAAK,UAAU,UAAU,6DACzB,SAAAjP,EAAE,mBAAmB,CACxB,CAAA,EACF,EACAiP,EAAAA,IAAC,MACC,SAACA,EAAA,IAAA,IAAA,CAAE,KAAK,SAAS,UAAU,6DAA6D,SAAA,cAAA,CAExF,CACF,CAAA,EACAA,EAAAA,IAAC,MACC,SAACA,EAAAA,IAAA,IAAA,CAAE,KAAK,WAAW,UAAU,6DAA6D,SAAA,SAE1F,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAyM,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACzM,EAAA,IAAA,KAAA,CAAG,UAAU,gBAAgB,SAAM,SAAA,EACpCyM,EAAAA,KAAC,KAAG,CAAA,UAAU,oBACZ,SAAA,CAACzM,EAAAA,IAAA,KAAA,CACC,eAAC,IAAE,CAAA,KAAK,QAAQ,UAAU,6DAA6D,oBAEvF,CACF,CAAA,EACAA,EAAAA,IAAC,MACC,SAACA,EAAA,IAAA,IAAA,CAAE,KAAK,OAAO,UAAU,6DAA6D,SAAA,KAAA,CAEtF,CACF,CAAA,EACAA,EAAAA,IAAC,MACC,SAACA,EAAA,IAAA,IAAA,CAAE,KAAK,WAAW,UAAU,6DAA6D,SAAA,oBAAA,CAE1F,CACF,CAAA,EACAA,EAAAA,IAAC,MACC,SAACA,EAAAA,IAAA,IAAA,CAAE,KAAK,SAAS,UAAU,6DAA6D,SAAA,oBAExF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAyM,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACzM,EAAA,IAAA,KAAA,CAAG,UAAU,gBAAgB,SAAO,UAAA,EACrCyM,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACzM,EAAAA,IAAA,IAAA,CAAE,KAAK,IAAI,UAAU,6DACpB,SAACA,EAAAA,IAAA0H,GAAA,CAAO,UAAU,SAAA,CAAU,CAC9B,CAAA,EACA1H,EAAAA,IAAC,IAAE,CAAA,KAAK,IAAI,UAAU,6DACpB,SAACA,EAAAA,IAAAiI,GAAA,CAAQ,UAAU,SAAA,CAAU,CAC/B,CAAA,EACAjI,EAAAA,IAAC,IAAE,CAAA,KAAK,IAAI,UAAU,6DACpB,SAACA,EAAA,IAAA4H,GAAA,CAAK,UAAU,SAAU,CAAA,CAC5B,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,+DACb,SAAC5H,EAAA,IAAA,IAAA,CAAE,wDAAkD,CAAA,CACvD,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,ECxFa6M,GAAwC,CAAC,CACpD,WAAAC,EAAa,GACb,WAAAC,EAAa,GACb,UAAA9Z,EAAY,EACd,IAEKwZ,EAAA,KAAA,MAAA,CAAI,UAAW,8BAA8BxZ,CAAS,GACpD,SAAA,CAAA6Z,SAAeR,GAAO,EAAA,QACtB,OAAK,CAAA,UAAU,SACd,SAAAtM,MAACgN,IAAO,CAAA,EACV,EACCD,SAAeH,GAAO,CAAA,CAAA,CAAA,EACzB,ECdSK,GAAwC,CAAC,CACpD,YAAAC,EAAc,GACd,WAAAC,EAAa,GACf,IAAM,CACE,KAAA,CAAE,gBAAAZ,CAAgB,EAAIP,GAAa,EACnCoB,EAAWC,GAAY,EAGzB,OAAAH,GAAe,CAACX,EACVvM,EAAAA,IAAAsN,GAAA,CAAS,GAAG,cAAc,QAAO,GAAC,EAK1C,CAACJ,GACDX,IACCa,EAAS,WAAa,eACrBA,EAAS,WAAa,kBAEhBpN,EAAAA,IAAAsN,GAAA,CAAS,GAAIH,EAAY,QAAO,GAAC,QAGnCH,GAAO,EAAA,CACjB,ECzBaO,GAA4C,CAAC,CACxD,UAAAta,EAAY,EACd,IAEI+M,EAAA,IAAC,OAAI,UAAW,8BAA8B/M,CAAS,GAErD,SAAA+M,EAAA,IAACgN,KAAO,CACV,CAAA,ECLSQ,GAA0C,CAAC,CACtD,UAAAva,EAAY,EACd,IAAM,CACJ,KAAM,CAAE,KAAAmZ,EAAM,gBAAAG,CAAgB,EAAIP,GAAa,EAG/C,OAAKO,GAIDH,GAAA,YAAAA,EAAM,QAAS,UAAWA,GAAA,YAAAA,EAAM,QAAS,YACnCpM,EAAAA,IAAAsN,GAAA,CAAS,GAAG,IAAI,QAAO,GAAC,EAI/Bb,EAAA,KAAA,MAAA,CAAI,UAAW,8BAA8BxZ,CAAS,GACrD,SAAA,CAAA+M,EAAA,IAACsM,GAAO,EAAA,EACRG,EAAAA,KAAC,MAAI,CAAA,UAAU,cAEb,SAAA,CAAAzM,EAAAA,IAAC,SAAM,UAAU,yBACf,SAACyM,EAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAACzM,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAW,cAAA,EACtDyM,EAAAA,KAAC,KAAG,CAAA,UAAU,YACZ,SAAA,CAACzM,EAAAA,IAAA,KAAA,CACC,eAAC,IAAE,CAAA,KAAK,mBAAmB,UAAU,oCAAoC,qBAEzE,CACF,CAAA,EACAA,EAAAA,IAAC,MACC,SAACA,EAAA,IAAA,IAAA,CAAE,KAAK,eAAe,UAAU,oCAAoC,SAAA,gBAAA,CAErE,CACF,CAAA,EACAA,EAAAA,IAAC,MACC,SAACA,EAAA,IAAA,IAAA,CAAE,KAAK,eAAe,UAAU,oCAAoC,SAAA,oBAAA,CAErE,CACF,CAAA,EACAA,EAAAA,IAAC,MACC,SAACA,EAAAA,IAAA,IAAA,CAAE,KAAK,kBAAkB,UAAU,oCAAoC,SAAA,mBAExE,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAGC,OAAK,CAAA,UAAU,aACd,SAAAA,EAAA,IAACgN,KAAO,CACV,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EA7CQhN,EAAAA,IAAAsN,GAAA,CAAS,GAAG,SAAS,QAAO,GAAC,CA+CzC,EClDaG,GAAqB,IAI9BhB,EAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAACA,EAAAA,KAAA,UAAA,CAAQ,UAAU,oBACjB,SAAA,CAACzM,EAAA,IAAA,KAAA,CAAG,UAAU,qDAAqD,SAEnE,+BAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,uDAAuD,SAGpE,8HAAA,EACAyM,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACb,SAAA,CAAAzM,EAAAA,IAAC0M,GAAK,GAAG,UACP,SAACD,EAAA,KAAAhN,EAAA,CAAO,KAAK,KACX,SAAA,CAACO,EAAAA,IAAAwH,GAAA,CAAS,UAAU,cAAe,CAAA,EAAE,aAAA,CAAA,CAEvC,CACF,CAAA,EACAxH,EAAA,IAAC0M,EAAK,CAAA,GAAG,YACP,SAAA1M,EAAAA,IAACP,EAAO,CAAA,QAAQ,UAAU,KAAK,KAAK,SAAA,kBAAA,CAEpC,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAgN,EAAAA,KAAC,UAAQ,CAAA,UAAU,QACjB,SAAA,CAACzM,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAEpD,oBAAA,EACAyM,EAAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAACxM,EACC,CAAA,SAAA,CAACwM,EAAAA,KAAAvM,GAAA,CAAW,UAAU,cACpB,SAAA,CAACF,EAAAA,IAAAgI,GAAA,CAAW,UAAU,mCAAoC,CAAA,EACzDhI,EAAA,IAAAG,GAAA,CAAU,UAAU,UAAU,SAAQ,UAAA,CAAA,CAAA,EACzC,QACCE,EACC,CAAA,SAAAL,MAACI,IAAgB,UAAU,cAAc,oEAEzC,CAAA,CACF,CAAA,CAAA,EACF,SAECH,EACC,CAAA,SAAA,CAACwM,EAAAA,KAAAvM,GAAA,CAAW,UAAU,cACpB,SAAA,CAACF,EAAAA,IAAAyH,GAAA,CAAM,UAAU,mCAAoC,CAAA,EACpDzH,EAAA,IAAAG,GAAA,CAAU,UAAU,UAAU,SAAW,aAAA,CAAA,CAAA,EAC5C,QACCE,EACC,CAAA,SAAAL,MAACI,IAAgB,UAAU,cAAc,kEAEzC,CAAA,CACF,CAAA,CAAA,EACF,SAECH,EACC,CAAA,SAAA,CAACwM,EAAAA,KAAAvM,GAAA,CAAW,UAAU,cACpB,SAAA,CAACF,EAAAA,IAAA+H,GAAA,CAAK,UAAU,mCAAoC,CAAA,EACnD/H,EAAA,IAAAG,GAAA,CAAU,UAAU,UAAU,SAAQ,UAAA,CAAA,CAAA,EACzC,QACCE,EACC,CAAA,SAAAL,MAACI,IAAgB,UAAU,cAAc,6DAEzC,CAAA,CACF,CAAA,CAAA,EACF,SAECH,EACC,CAAA,SAAA,CAACwM,EAAAA,KAAAvM,GAAA,CAAW,UAAU,cACpB,SAAA,CAACF,EAAAA,IAAAwH,GAAA,CAAS,UAAU,mCAAoC,CAAA,EACvDxH,EAAA,IAAAG,GAAA,CAAU,UAAU,UAAU,SAAW,aAAA,CAAA,CAAA,EAC5C,QACCE,EACC,CAAA,SAAAL,MAACI,IAAgB,UAAU,cAAc,qEAEzC,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAqM,EAAAA,KAAC,UAAQ,CAAA,UAAU,QACjB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACzM,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAe,kBAAA,EAClDA,EAAAA,IAAC0M,GAAK,GAAG,UACP,eAACjN,EAAO,CAAA,QAAQ,UAAU,SAAA,YAAU,CAAA,CACtC,CAAA,CAAA,EACF,EAECO,EAAA,IAAA,MAAA,CAAI,UAAU,uDAEZ,eAAM,KAAK,CAAE,OAAQ,CAAA,CAAG,EAAE,IAAI,CAAC0N,EAAG1X,IACjCyW,EAAA,KAACxM,EAAA,CAEC,UAAU,oDAEV,SAAA,CAACD,EAAAA,IAAA,MAAA,CAAI,UAAU,uBAAwB,CAAA,EACvCyM,EAAAA,KAACpM,EAAY,CAAA,UAAU,MACrB,SAAA,CAACoM,EAAAA,KAAA,KAAA,CAAG,UAAU,iCAAiC,SAAA,CAAA,cACjCzW,EAAQ,CAAA,EACtB,EACCgK,EAAA,IAAA,IAAA,CAAE,UAAU,gCAAgC,SAAO,UAAA,EACpDyM,EAAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAACzM,EAAAA,IAAA+H,GAAA,CAAK,UAAU,yCAA0C,CAAA,EACzD/H,EAAA,IAAA,OAAA,CAAK,UAAU,eAAe,SAAG,KAAA,CAAA,CAAA,CACpC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,EAbKhK,CAAA,CAeR,CACH,CAAA,CAAA,EACF,EAGAyW,EAAAA,KAAC,UAAQ,CAAA,UAAU,QACjB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACzM,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAiB,oBAAA,EACpDA,EAAAA,IAAC0M,GAAK,GAAG,uBACP,eAACjN,EAAO,CAAA,QAAQ,UAAU,SAAA,YAAU,CAAA,CACtC,CAAA,CAAA,EACF,EAEAO,EAAA,IAAC,MAAI,CAAA,UAAU,uDACZ,SAAA,MAAM,KAAK,CAAE,OAAQ,CAAA,CAAG,EAAE,IAAI,CAAC0N,EAAG1X,IACjCgK,EAAAA,IAACC,EAAiB,CAAA,UAAU,oCAC1B,SAAAD,EAAA,IAACK,EAAY,CAAA,UAAU,MACrB,SAAAoM,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACzM,EAAAA,IAAA,MAAA,CAAI,UAAU,4BAA6B,CAAA,EAC5CyM,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,qBAAqB,SAAA,CAAA,cACrBzW,EAAQ,CAAA,EACtB,EACCgK,EAAA,IAAA,IAAA,CAAE,UAAU,qCAAqC,SAElD,cAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gCAAgC,SAAW,aAAA,CAAA,CAAA,CAC1D,CAAA,CAAA,CAAA,CACF,CACF,CAAA,GAdShK,CAeX,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,ECtJS2X,GAAsB,IAAM,CACvC,KAAM,CAAE,EAAA5c,CAAA,EAAMqT,GAAe,QAAQ,EAC/BwJ,EAAWC,GAAY,EACvB,CAAE,MAAAC,EAAO,UAAAC,CAAU,EAAI/B,GAAa,EAEpC,CAACgC,EAAUC,CAAW,EAAI9I,WAAS,CACvC,MAAO,GACP,SAAU,EAAA,CACX,EACK,CAAC+I,EAAQC,CAAS,EAAIhJ,EAAAA,SAAiC,CAAA,CAAE,EAEzDiJ,EAAgBtd,GAA2C,CAC/D,KAAM,CAAE,KAAA+Y,EAAM,MAAAxY,CAAM,EAAIP,EAAE,OACdmd,EAAAI,IAAS,CAAE,GAAGA,EAAM,CAACxE,CAAI,EAAGxY,GAAQ,EAE5C6c,EAAOrE,CAAI,GACHsE,EAAAE,IAAS,CAAE,GAAGA,EAAM,CAACxE,CAAI,EAAG,IAAK,CAE/C,EAEMyE,EAAe,IAAM,CACzB,MAAMC,EAAoC,CAAC,EAEvC,OAACP,EAAS,MAEF,eAAe,KAAKA,EAAS,KAAK,IAClCO,EAAA,MAAQxd,EAAE,oBAAoB,GAF9Bwd,EAAA,MAAQxd,EAAE,gBAAgB,EAKjCid,EAAS,WACFO,EAAA,SAAWxd,EAAE,gBAAgB,GAGzCod,EAAUI,CAAS,EACZ,OAAO,KAAKA,CAAS,EAAE,SAAW,CAC3C,EAEMC,EAAe,MAAO1d,GAAuB,CAG7C,GAFJA,EAAE,eAAe,EAEb,EAACwd,IAED,GAAA,CACF,MAAMR,EAAME,CAAQ,EACpBJ,EAAS,GAAG,QACLhd,EAAO,CACN,QAAA,MAAM,gBAAiBA,CAAK,EAC1Bud,EAAA,CAAE,QAAS,uDAAwD,CAAA,CAEjF,EAEA,aACG,MAAI,CAAA,UAAU,2EACb,SAAC1B,EAAA,KAAAxM,EAAA,CAAK,UAAU,kBACd,SAAA,CAACwM,EAAAA,KAAAvM,GAAA,CAAW,UAAU,cACpB,SAAA,CAAAF,EAAAA,IAAC,OAAI,UAAU,2BACb,eAACwH,GAAS,CAAA,UAAU,uBAAuB,CAC7C,CAAA,QACCrH,GAAU,CAAA,UAAU,WAAY,SAAApP,EAAE,kBAAkB,EAAE,EACvDiP,EAAAA,IAACI,IAAgB,SAEjB,yCAAA,CAAA,CAAA,EACF,SACCC,EACC,CAAA,SAAA,CAAAoM,EAAA,KAAC,OAAK,CAAA,SAAU+B,EAAc,UAAU,YACrC,SAAA,CAAAN,EAAO,SACLlO,EAAA,IAAA,MAAA,CAAI,UAAU,qEACZ,WAAO,QACV,EAGFyM,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAzM,MAACU,GAAM,CAAA,QAAQ,QAAS,SAAA3P,EAAE,aAAa,EAAE,EACzCiP,EAAA,IAACO,GAAA,CACC,GAAG,QACH,KAAK,QACL,KAAK,QACL,MAAOyN,EAAS,MAChB,SAAUI,EACV,YAAY,iBACZ,UAAWF,EAAO,MAAQ,qBAAuB,EAAA,CACnD,EACCA,EAAO,OACNlO,EAAA,IAAC,KAAE,UAAU,2BAA4B,WAAO,KAAM,CAAA,CAAA,EAE1D,EAEAyM,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAzM,MAACU,GAAM,CAAA,QAAQ,WAAY,SAAA3P,EAAE,gBAAgB,EAAE,EAC/CiP,EAAA,IAACO,GAAA,CACC,GAAG,WACH,KAAK,WACL,KAAK,WACL,MAAOyN,EAAS,SAChB,SAAUI,EACV,UAAWF,EAAO,SAAW,qBAAuB,EAAA,CACtD,EACCA,EAAO,UACNlO,EAAA,IAAC,KAAE,UAAU,2BAA4B,WAAO,QAAS,CAAA,CAAA,EAE7D,EAEAA,EAAA,IAACP,EAAA,CACC,KAAK,SACL,UAAU,SACV,SAAUsO,EAET,SAAAA,EAAY,oBAAsBhd,EAAE,kBAAkB,CAAA,CAAA,CACzD,EACF,QAEC,MAAI,CAAA,UAAU,2BACb,SAAC0b,EAAA,KAAA,IAAA,CAAE,UAAU,wBAAwB,SAAA,CAAA,qBAChB,IACnBzM,MAAC0M,GAAK,GAAG,YAAY,UAAU,+BAC5B,SAAA3b,EAAE,qBAAqB,CAC1B,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EClIa0d,GAAuB,IAAM,CACxC,KAAM,CAAE,EAAA1d,CAAE,EAAIqT,GAAe,CAAC,SAAU,OAAO,CAAC,EAG9C,OAAAqI,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAzM,MAAC,KAAG,CAAA,UAAU,0BAA2B,SAAAjP,EAAE,mBAAmB,EAAE,EAChEiP,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,4CAErC,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECda0O,GAA4B,IAErCjC,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACzM,EAAA,IAAA,KAAA,CAAG,UAAU,0BAA0B,SAAe,kBAAA,EACvDA,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,+CAErC,CAAA,CACF,CAAA,CAAA,EACF,ECTS2O,GAAuB,IAEhClC,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACzM,EAAA,IAAA,KAAA,CAAG,UAAU,0BAA0B,SAAU,aAAA,EAClDA,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,0CAErC,CAAA,CACF,CAAA,CAAA,EACF,ECRS4O,GAAyB,IAAM,CAC1C,KAAM,CAAE,EAAA7d,CAAA,EAAMqT,GAAe,QAAQ,EAGnC,OAAAqI,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAzM,MAAC,KAAG,CAAA,UAAU,0BAA2B,SAAAjP,EAAE,qBAAqB,EAAE,EAClEiP,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,uCAErC,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECba6O,GAA0B,IAAM,CAC3C,KAAM,CAAE,EAAA9d,CAAA,EAAMqT,GAAe,QAAQ,EAGnC,OAAAqI,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAzM,MAAC,KAAG,CAAA,UAAU,0BAA2B,SAAAjP,EAAE,sBAAsB,EAAE,EACnEiP,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,wCAErC,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECba8O,GAAwB,IAAM,CACzC,KAAM,CAAE,EAAA/d,CAAA,EAAMqT,GAAe,QAAQ,EAGnC,OAAAqI,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAzM,MAAC,KAAG,CAAA,UAAU,0BAA2B,SAAAjP,EAAE,oBAAoB,EAAE,EACjEiP,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,uCAErC,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECba+O,GAAwB,IAAM,CACzC,KAAM,CAAE,EAAAhe,CAAA,EAAMqT,GAAe,QAAQ,EAGnC,OAAAqI,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAzM,MAAC,KAAG,CAAA,UAAU,0BAA2B,SAAAjP,EAAE,oBAAoB,EAAE,EACjEiP,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,qCAErC,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECHagP,GAAyB,UAIjC,MAAI,CAAA,UAAU,2EACb,SAACvC,EAAA,KAAAxM,EAAA,CAAK,UAAU,8BACd,SAAA,CAAAwM,OAACvM,GACC,CAAA,SAAA,CAACF,EAAA,IAAAG,GAAA,CAAU,UAAU,gDAAgD,SAErE,MAAA,EACCH,EAAA,IAAAG,GAAA,CAAU,UAAU,WAAW,SAAmB,sBAAA,EACnDH,EAAAA,IAACI,IAAgB,SAEjB,6DAAA,CAAA,CAAA,EACF,QACCC,EAAY,CAAA,UAAU,YACrB,SAACoM,EAAA,KAAA,MAAA,CAAI,UAAU,iDACb,SAAA,CAAAzM,EAAAA,IAAC0M,GAAK,GAAG,IACP,SAACD,EAAA,KAAAhN,EAAA,CAAO,UAAU,mBAChB,SAAA,CAACO,EAAAA,IAAAiP,GAAA,CAAK,UAAU,cAAe,CAAA,EAAE,cAAA,CAAA,CAEnC,CACF,CAAA,EACAxC,EAAA,KAAChN,EAAA,CACC,QAAQ,UACR,QAAS,IAAM,OAAO,QAAQ,KAAK,EACnC,UAAU,mBAEV,SAAA,CAACO,EAAAA,IAAAuH,GAAA,CAAU,UAAU,cAAe,CAAA,EAAE,UAAA,CAAA,CAAA,CAExC,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,ECxBE2H,GAASC,GAAoB,CACjC,CACE,KAAM,IACN,cAAUtC,GAAW,EAAA,EACrB,SAAU,CAER,CACE,MAAO,GACP,cAAUY,GAAS,CAAA,CAAA,CACrB,EACA,CACE,KAAM,SACN,cAAUgB,GAAW,CAAA,CAAA,CACvB,EACA,CACE,KAAM,YACN,cAAUC,GAAgB,CAAA,CAAA,CAC5B,EAGA,CACE,KAAM,OACN,QAAS1O,EAAAA,IAACiN,GAAW,CAAA,YAAa,EAAO,CAAA,EACzC,SAAU,CACR,CACE,KAAM,QACN,cAAUU,GAAU,CAAA,CAAA,CACtB,EACA,CACE,KAAM,WACN,cAAUiB,GAAa,CAAA,CAAA,CAAA,CACzB,CAEJ,EAGA,CACE,KAAM,OACN,QAAS5O,EAAAA,IAACiN,GAAW,CAAA,YAAa,EAAM,CAAA,EACxC,SAAU,CACR,CACE,KAAM,YACN,cAAU4B,GAAc,CAAA,CAAA,CAC1B,EACA,CACE,KAAM,UACN,cAAUC,GAAY,CAAA,CAAA,CACxB,EACA,CACE,KAAM,UACN,cAAUC,GAAY,CAAA,CAAA,CAAA,CACxB,CACF,CACF,CAEJ,EAGA,CACE,KAAM,QACN,cAAUxB,GAAa,EAAA,EACvB,SAAU,CACR,CACE,KAAM,aACN,cAAUoB,GAAW,CAAA,CAAA,CAAA,CACvB,CAEJ,EAGA,CACE,KAAM,SACN,cAAUnB,GAAY,EAAA,EACtB,SAAU,CACR,CACE,MAAO,GACP,QAAUxN,EAAAA,IAAA,MAAA,CAAI,SAAe,iBAAA,CAAA,CAC/B,EACA,CACE,KAAM,QACN,QAAUA,EAAAA,IAAA,MAAA,CAAI,SAAgB,kBAAA,CAAA,CAChC,EACA,CACE,KAAM,QACN,QAAUA,EAAAA,IAAA,MAAA,CAAI,SAAe,iBAAA,CAAA,CAC/B,EACA,CACE,KAAM,WACN,QAAUA,EAAAA,IAAA,MAAA,CAAI,SAAkB,oBAAA,CAAA,CAAA,CAClC,CAEJ,EAGA,CACE,KAAM,IACN,cAAU6M,GAAW,EAAA,EACrB,SAAU,CACR,CACE,KAAM,IACN,cAAUmC,GAAa,CAAA,CAAA,CAAA,CACzB,CACF,CAEJ,CAAC,EAEYI,GAAsB,IAC1BpP,MAACqP,IAAe,OAAAH,GAAgB,EClInC1N,EAAWgB,GAAO,OAAOA,GAAQ,SACjC8M,GAAQ,IAAM,CAClB,IAAIC,EACAC,EACJ,MAAMC,EAAU,IAAI,QAAQ,CAAC9J,EAAS+J,IAAW,CAC/CH,EAAM5J,EACN6J,EAAME,CACV,CAAG,EACD,OAAAD,EAAQ,QAAUF,EAClBE,EAAQ,OAASD,EACVC,CACT,EACME,GAAaC,GACbA,GAAU,KAAa,GACpB,GAAKA,EAERC,GAAO,CAACC,EAAGC,EAAG,IAAM,CACxBD,EAAE,QAAQxf,GAAK,CACTyf,EAAEzf,CAAC,IAAG,EAAEA,CAAC,EAAIyf,EAAEzf,CAAC,EACxB,CAAG,CACH,EACM0f,GAA4B,OAC5BC,GAAW3d,GAAOA,GAAOA,EAAI,QAAQ,KAAK,EAAI,GAAKA,EAAI,QAAQ0d,GAA2B,GAAG,EAAI1d,EACjG4d,GAAuBN,GAAU,CAACA,GAAUpO,EAASoO,CAAM,EAC3DO,GAAgB,CAACP,EAAQlb,EAAM0b,IAAU,CAC7C,MAAMC,EAAS7O,EAAS9M,CAAI,EAAWA,EAAK,MAAM,GAAG,EAArBA,EAChC,IAAI4b,EAAa,EACjB,KAAOA,EAAaD,EAAM,OAAS,GAAG,CACpC,GAAIH,GAAqBN,CAAM,EAAG,MAAO,CAAE,EAC3C,MAAMtd,EAAM2d,GAASI,EAAMC,CAAU,CAAC,EAClC,CAACV,EAAOtd,CAAG,GAAK8d,IAAOR,EAAOtd,CAAG,EAAI,IAAI8d,GACzC,OAAO,UAAU,eAAe,KAAKR,EAAQtd,CAAG,EAClDsd,EAASA,EAAOtd,CAAG,EAEnBsd,EAAS,CAAE,EAEb,EAAEU,CACN,CACE,OAAIJ,GAAqBN,CAAM,EAAU,CAAE,EACpC,CACL,IAAKA,EACL,EAAGK,GAASI,EAAMC,CAAU,CAAC,CAC9B,CACH,EACMC,GAAU,CAACX,EAAQlb,EAAMuV,IAAa,CAC1C,KAAM,CACJ,IAAAzH,EACA,EAAAjK,CACD,EAAG4X,GAAcP,EAAQlb,EAAM,MAAM,EACtC,GAAI8N,IAAQ,QAAa9N,EAAK,SAAW,EAAG,CAC1C8N,EAAIjK,CAAC,EAAI0R,EACT,MACJ,CACE,IAAInZ,EAAI4D,EAAKA,EAAK,OAAS,CAAC,EACxB8b,EAAI9b,EAAK,MAAM,EAAGA,EAAK,OAAS,CAAC,EACjC+b,EAAON,GAAcP,EAAQY,EAAG,MAAM,EAC1C,KAAOC,EAAK,MAAQ,QAAaD,EAAE,QACjC1f,EAAI,GAAG0f,EAAEA,EAAE,OAAS,CAAC,CAAC,IAAI1f,CAAC,GAC3B0f,EAAIA,EAAE,MAAM,EAAGA,EAAE,OAAS,CAAC,EAC3BC,EAAON,GAAcP,EAAQY,EAAG,MAAM,EAClCC,GAAA,MAAAA,EAAM,KAAO,OAAOA,EAAK,IAAI,GAAGA,EAAK,CAAC,IAAI3f,CAAC,EAAE,EAAM,MACrD2f,EAAK,IAAM,QAGfA,EAAK,IAAI,GAAGA,EAAK,CAAC,IAAI3f,CAAC,EAAE,EAAImZ,CAC/B,EACMyG,GAAW,CAACd,EAAQlb,EAAMuV,EAAU0G,IAAW,CACnD,KAAM,CACJ,IAAAnO,EACA,EAAAjK,CACD,EAAG4X,GAAcP,EAAQlb,EAAM,MAAM,EACtC8N,EAAIjK,CAAC,EAAIiK,EAAIjK,CAAC,GAAK,CAAE,EACrBiK,EAAIjK,CAAC,EAAE,KAAK0R,CAAQ,CACtB,EACM2G,GAAU,CAAChB,EAAQlb,IAAS,CAChC,KAAM,CACJ,IAAA8N,EACA,EAAAjK,CACJ,EAAM4X,GAAcP,EAAQlb,CAAI,EAC9B,GAAK8N,GACA,OAAO,UAAU,eAAe,KAAKA,EAAKjK,CAAC,EAChD,OAAOiK,EAAIjK,CAAC,CACd,EACMsY,GAAsB,CAAC1E,EAAM2E,EAAaxe,IAAQ,CACtD,MAAMjB,EAAQuf,GAAQzE,EAAM7Z,CAAG,EAC/B,OAAIjB,IAAU,OACLA,EAEFuf,GAAQE,EAAaxe,CAAG,CACjC,EACMye,GAAa,CAACC,EAAQC,EAAQC,IAAc,CAChD,UAAW3K,KAAQ0K,EACb1K,IAAS,aAAeA,IAAS,gBAC/BA,KAAQyK,EACNxP,EAASwP,EAAOzK,CAAI,CAAC,GAAKyK,EAAOzK,CAAI,YAAa,QAAU/E,EAASyP,EAAO1K,CAAI,CAAC,GAAK0K,EAAO1K,CAAI,YAAa,OAC5G2K,IAAWF,EAAOzK,CAAI,EAAI0K,EAAO1K,CAAI,GAEzCwK,GAAWC,EAAOzK,CAAI,EAAG0K,EAAO1K,CAAI,EAAG2K,CAAS,EAGlDF,EAAOzK,CAAI,EAAI0K,EAAO1K,CAAI,GAIhC,OAAOyK,CACT,EACMG,GAAcnH,GAAOA,EAAI,QAAQ,sCAAuC,MAAM,EACpF,IAAIoH,GAAa,CACf,IAAK,QACL,IAAK,OACL,IAAK,OACL,IAAK,SACL,IAAK,QACL,IAAK,QACP,EACA,MAAMC,GAASlF,GACT3K,EAAS2K,CAAI,EACRA,EAAK,QAAQ,aAAc4D,GAAKqB,GAAWrB,CAAC,CAAC,EAE/C5D,EAET,MAAMmF,EAAY,CAChB,YAAYC,EAAU,CACpB,KAAK,SAAWA,EAChB,KAAK,UAAY,IAAI,IACrB,KAAK,YAAc,CAAE,CACzB,CACE,UAAUC,EAAS,CACjB,MAAMC,EAAkB,KAAK,UAAU,IAAID,CAAO,EAClD,GAAIC,IAAoB,OACtB,OAAOA,EAET,MAAMC,EAAY,IAAI,OAAOF,CAAO,EACpC,OAAI,KAAK,YAAY,SAAW,KAAK,UACnC,KAAK,UAAU,OAAO,KAAK,YAAY,MAAK,CAAE,EAEhD,KAAK,UAAU,IAAIA,EAASE,CAAS,EACrC,KAAK,YAAY,KAAKF,CAAO,EACtBE,CACX,CACA,CACA,MAAMC,GAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,GAAG,EAChCC,GAAiC,IAAIN,GAAY,EAAE,EACnDO,GAAsB,CAACvf,EAAKwf,EAAaC,IAAiB,CAC9DD,EAAcA,GAAe,GAC7BC,EAAeA,GAAgB,GAC/B,MAAMC,EAAgBL,GAAM,OAAOM,GAAKH,EAAY,QAAQG,CAAC,EAAI,GAAKF,EAAa,QAAQE,CAAC,EAAI,CAAC,EACjG,GAAID,EAAc,SAAW,EAAG,MAAO,GACvC,MAAMnhB,EAAI+gB,GAA+B,UAAU,IAAII,EAAc,IAAIC,GAAKA,IAAM,IAAM,MAAQA,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG,EACjH,IAAIC,EAAU,CAACrhB,EAAE,KAAKyB,CAAG,EACzB,GAAI,CAAC4f,EAAS,CACZ,MAAMC,EAAK7f,EAAI,QAAQyf,CAAY,EAC/BI,EAAK,GAAK,CAACthB,EAAE,KAAKyB,EAAI,UAAU,EAAG6f,CAAE,CAAC,IACxCD,EAAU,GAEhB,CACE,OAAOA,CACT,EACME,GAAW,CAAC5P,EAAK9N,EAAMqd,EAAe,MAAQ,CAClD,GAAI,CAACvP,EAAK,OACV,GAAIA,EAAI9N,CAAI,EACV,OAAK,OAAO,UAAU,eAAe,KAAK8N,EAAK9N,CAAI,EAC5C8N,EAAI9N,CAAI,EADuC,OAGxD,MAAM2d,EAAS3d,EAAK,MAAMqd,CAAY,EACtC,IAAIO,EAAU9P,EACd,QAAS,EAAI,EAAG,EAAI6P,EAAO,QAAS,CAClC,GAAI,CAACC,GAAW,OAAOA,GAAY,SACjC,OAEF,IAAIC,EACAC,EAAW,GACf,QAASC,EAAI,EAAGA,EAAIJ,EAAO,OAAQ,EAAEI,EAMnC,GALIA,IAAM,IACRD,GAAYT,GAEdS,GAAYH,EAAOI,CAAC,EACpBF,EAAOD,EAAQE,CAAQ,EACnBD,IAAS,OAAW,CACtB,GAAI,CAAC,SAAU,SAAU,SAAS,EAAE,QAAQ,OAAOA,CAAI,EAAI,IAAME,EAAIJ,EAAO,OAAS,EACnF,SAEF,GAAKI,EAAI,EAAI,EACb,KACR,CAEIH,EAAUC,CACd,CACE,OAAOD,CACT,EACMI,GAAiBvR,GAAQA,GAAA,YAAAA,EAAM,QAAQ,IAAK,KAE5CwR,GAAgB,CACpB,KAAM,SACN,IAAIrR,EAAM,CACR,KAAK,OAAO,MAAOA,CAAI,CACxB,EACD,KAAKA,EAAM,CACT,KAAK,OAAO,OAAQA,CAAI,CACzB,EACD,MAAMA,EAAM,CACV,KAAK,OAAO,QAASA,CAAI,CAC1B,EACD,OAAOd,EAAMc,EAAM,UACjBC,GAAA1N,EAAA,6BAAU2M,KAAV,YAAA3M,EAAiB,QAAjB,MAAA0N,EAAA,KAAA1N,EAAyB,QAASyN,EACtC,CACA,EACA,MAAMsR,EAAO,CACX,YAAYC,EAAgBxQ,EAAU,GAAI,CACxC,KAAK,KAAKwQ,EAAgBxQ,CAAO,CACrC,CACE,KAAKwQ,EAAgBxQ,EAAU,GAAI,CACjC,KAAK,OAASA,EAAQ,QAAU,WAChC,KAAK,OAASwQ,GAAkBF,GAChC,KAAK,QAAUtQ,EACf,KAAK,MAAQA,EAAQ,KACzB,CACE,OAAOf,EAAM,CACX,OAAO,KAAK,QAAQA,EAAM,MAAO,GAAI,EAAI,CAC7C,CACE,QAAQA,EAAM,CACZ,OAAO,KAAK,QAAQA,EAAM,OAAQ,GAAI,EAAI,CAC9C,CACE,SAASA,EAAM,CACb,OAAO,KAAK,QAAQA,EAAM,QAAS,EAAE,CACzC,CACE,aAAaA,EAAM,CACjB,OAAO,KAAK,QAAQA,EAAM,OAAQ,uBAAwB,EAAI,CAClE,CACE,QAAQA,EAAMwR,EAAKtd,EAAQud,EAAW,CACpC,OAAIA,GAAa,CAAC,KAAK,MAAc,MACjCvR,EAASF,EAAK,CAAC,CAAC,IAAGA,EAAK,CAAC,EAAI,GAAG9L,CAAM,GAAG,KAAK,MAAM,IAAI8L,EAAK,CAAC,CAAC,IAC5D,KAAK,OAAOwR,CAAG,EAAExR,CAAI,EAChC,CACE,OAAO0R,EAAY,CACjB,OAAO,IAAIJ,GAAO,KAAK,OAAQ,CAE3B,OAAQ,GAAG,KAAK,MAAM,IAAII,CAAU,IAEtC,GAAG,KAAK,OACd,CAAK,CACL,CACE,MAAM3Q,EAAS,CACb,OAAAA,EAAUA,GAAW,KAAK,QAC1BA,EAAQ,OAASA,EAAQ,QAAU,KAAK,OACjC,IAAIuQ,GAAO,KAAK,OAAQvQ,CAAO,CAC1C,CACA,CACA,IAAI4Q,EAAa,IAAIL,GAErB,MAAMM,EAAa,CACjB,aAAc,CACZ,KAAK,UAAY,CAAE,CACvB,CACE,GAAGC,EAAQvK,EAAU,CACnB,OAAAuK,EAAO,MAAM,GAAG,EAAE,QAAQtS,GAAS,CAC5B,KAAK,UAAUA,CAAK,IAAG,KAAK,UAAUA,CAAK,EAAI,IAAI,KACxD,MAAMuS,EAAe,KAAK,UAAUvS,CAAK,EAAE,IAAI+H,CAAQ,GAAK,EAC5D,KAAK,UAAU/H,CAAK,EAAE,IAAI+H,EAAUwK,EAAe,CAAC,CAC1D,CAAK,EACM,IACX,CACE,IAAIvS,EAAO+H,EAAU,CACnB,GAAK,KAAK,UAAU/H,CAAK,EACzB,IAAI,CAAC+H,EAAU,CACb,OAAO,KAAK,UAAU/H,CAAK,EAC3B,MACN,CACI,KAAK,UAAUA,CAAK,EAAE,OAAO+H,CAAQ,EACzC,CACE,KAAK/H,KAAUS,EAAM,CACf,KAAK,UAAUT,CAAK,GACP,MAAM,KAAK,KAAK,UAAUA,CAAK,EAAE,SAAS,EAClD,QAAQ,CAAC,CAACwS,EAAUC,CAAa,IAAM,CAC5C,QAASvb,EAAI,EAAGA,EAAIub,EAAevb,IACjCsb,EAAS,GAAG/R,CAAI,CAE1B,CAAO,EAEC,KAAK,UAAU,GAAG,GACL,MAAM,KAAK,KAAK,UAAU,GAAG,EAAE,SAAS,EAChD,QAAQ,CAAC,CAAC+R,EAAUC,CAAa,IAAM,CAC5C,QAASvb,EAAI,EAAGA,EAAIub,EAAevb,IACjCsb,EAAS,MAAMA,EAAU,CAACxS,EAAO,GAAGS,CAAI,CAAC,CAEnD,CAAO,CAEP,CACA,CAEA,MAAMiS,WAAsBL,EAAa,CACvC,YAAY/G,EAAM9J,EAAU,CAC1B,GAAI,CAAC,aAAa,EAClB,UAAW,aACf,EAAK,CACD,MAAO,EACP,KAAK,KAAO8J,GAAQ,CAAE,EACtB,KAAK,QAAU9J,EACX,KAAK,QAAQ,eAAiB,SAChC,KAAK,QAAQ,aAAe,KAE1B,KAAK,QAAQ,sBAAwB,SACvC,KAAK,QAAQ,oBAAsB,GAEzC,CACE,cAAcJ,EAAI,CACZ,KAAK,QAAQ,GAAG,QAAQA,CAAE,EAAI,GAChC,KAAK,QAAQ,GAAG,KAAKA,CAAE,CAE7B,CACE,iBAAiBA,EAAI,CACnB,MAAMjM,EAAQ,KAAK,QAAQ,GAAG,QAAQiM,CAAE,EACpCjM,EAAQ,IACV,KAAK,QAAQ,GAAG,OAAOA,EAAO,CAAC,CAErC,CACE,YAAYmM,EAAKF,EAAI3P,EAAK+P,EAAU,CAAA,EAAI,SACtC,MAAM0P,EAAe1P,EAAQ,eAAiB,OAAYA,EAAQ,aAAe,KAAK,QAAQ,aACxFmR,EAAsBnR,EAAQ,sBAAwB,OAAYA,EAAQ,oBAAsB,KAAK,QAAQ,oBACnH,IAAI3N,EACAyN,EAAI,QAAQ,GAAG,EAAI,GACrBzN,EAAOyN,EAAI,MAAM,GAAG,GAEpBzN,EAAO,CAACyN,EAAKF,CAAE,EACX3P,IACE,MAAM,QAAQA,CAAG,EACnBoC,EAAK,KAAK,GAAGpC,CAAG,EACPkP,EAASlP,CAAG,GAAKyf,EAC1Brd,EAAK,KAAK,GAAGpC,EAAI,MAAMyf,CAAY,CAAC,EAEpCrd,EAAK,KAAKpC,CAAG,IAInB,MAAMkF,EAASoZ,GAAQ,KAAK,KAAMlc,CAAI,EAMtC,MALI,CAAC8C,GAAU,CAACyK,GAAM,CAAC3P,GAAO6P,EAAI,QAAQ,GAAG,EAAI,KAC/CA,EAAMzN,EAAK,CAAC,EACZuN,EAAKvN,EAAK,CAAC,EACXpC,EAAMoC,EAAK,MAAM,CAAC,EAAE,KAAK,GAAG,GAE1B8C,GAAU,CAACgc,GAAuB,CAAChS,EAASlP,CAAG,EAAUkF,EACtD4a,IAAS7Q,GAAA1N,EAAA,KAAK,OAAL,YAAAA,EAAYsO,KAAZ,YAAAZ,EAAmBU,GAAK3P,EAAKyf,CAAY,CAC7D,CACE,YAAY5P,EAAKF,EAAI3P,EAAKjB,EAAOgR,EAAU,CACzC,OAAQ,EACZ,EAAK,CACD,MAAM0P,EAAe1P,EAAQ,eAAiB,OAAYA,EAAQ,aAAe,KAAK,QAAQ,aAC9F,IAAI3N,EAAO,CAACyN,EAAKF,CAAE,EACf3P,IAAKoC,EAAOA,EAAK,OAAOqd,EAAezf,EAAI,MAAMyf,CAAY,EAAIzf,CAAG,GACpE6P,EAAI,QAAQ,GAAG,EAAI,KACrBzN,EAAOyN,EAAI,MAAM,GAAG,EACpB9Q,EAAQ4Q,EACRA,EAAKvN,EAAK,CAAC,GAEb,KAAK,cAAcuN,CAAE,EACrBsO,GAAQ,KAAK,KAAM7b,EAAMrD,CAAK,EACzBgR,EAAQ,QAAQ,KAAK,KAAK,QAASF,EAAKF,EAAI3P,EAAKjB,CAAK,CAC/D,CACE,aAAa8Q,EAAKF,EAAIwR,EAAWpR,EAAU,CACzC,OAAQ,EACZ,EAAK,CACD,UAAW/R,KAAKmjB,GACVjS,EAASiS,EAAUnjB,CAAC,CAAC,GAAK,MAAM,QAAQmjB,EAAUnjB,CAAC,CAAC,IAAG,KAAK,YAAY6R,EAAKF,EAAI3R,EAAGmjB,EAAUnjB,CAAC,EAAG,CACpG,OAAQ,EAChB,CAAO,EAEE+R,EAAQ,QAAQ,KAAK,KAAK,QAASF,EAAKF,EAAIwR,CAAS,CAC9D,CACE,kBAAkBtR,EAAKF,EAAIwR,EAAWC,EAAMxC,EAAW7O,EAAU,CAC/D,OAAQ,GACR,SAAU,EACd,EAAK,CACD,IAAI3N,EAAO,CAACyN,EAAKF,CAAE,EACfE,EAAI,QAAQ,GAAG,EAAI,KACrBzN,EAAOyN,EAAI,MAAM,GAAG,EACpBuR,EAAOD,EACPA,EAAYxR,EACZA,EAAKvN,EAAK,CAAC,GAEb,KAAK,cAAcuN,CAAE,EACrB,IAAI0R,EAAO/C,GAAQ,KAAK,KAAMlc,CAAI,GAAK,CAAE,EACpC2N,EAAQ,WAAUoR,EAAY,KAAK,MAAM,KAAK,UAAUA,CAAS,CAAC,GACnEC,EACF3C,GAAW4C,EAAMF,EAAWvC,CAAS,EAErCyC,EAAO,CACL,GAAGA,EACH,GAAGF,CACJ,EAEHlD,GAAQ,KAAK,KAAM7b,EAAMif,CAAI,EACxBtR,EAAQ,QAAQ,KAAK,KAAK,QAASF,EAAKF,EAAIwR,CAAS,CAC9D,CACE,qBAAqBtR,EAAKF,EAAI,CACxB,KAAK,kBAAkBE,EAAKF,CAAE,GAChC,OAAO,KAAK,KAAKE,CAAG,EAAEF,CAAE,EAE1B,KAAK,iBAAiBA,CAAE,EACxB,KAAK,KAAK,UAAWE,EAAKF,CAAE,CAChC,CACE,kBAAkBE,EAAKF,EAAI,CACzB,OAAO,KAAK,YAAYE,EAAKF,CAAE,IAAM,MACzC,CACE,kBAAkBE,EAAKF,EAAI,CACzB,OAAKA,IAAIA,EAAK,KAAK,QAAQ,WACpB,KAAK,YAAYE,EAAKF,CAAE,CACnC,CACE,kBAAkBE,EAAK,CACrB,OAAO,KAAK,KAAKA,CAAG,CACxB,CACE,4BAA4BA,EAAK,CAC/B,MAAMgK,EAAO,KAAK,kBAAkBhK,CAAG,EAEvC,MAAO,CAAC,EADEgK,GAAQ,OAAO,KAAKA,CAAI,GAAK,CAAE,GAC9B,KAAKyH,GAAKzH,EAAKyH,CAAC,GAAK,OAAO,KAAKzH,EAAKyH,CAAC,CAAC,EAAE,OAAS,CAAC,CACnE,CACE,QAAS,CACP,OAAO,KAAK,IAChB,CACA,CAEA,IAAIC,GAAgB,CAClB,WAAY,CAAE,EACd,iBAAiBC,EAAQ,CACvB,KAAK,WAAWA,EAAO,IAAI,EAAIA,CAChC,EACD,OAAOC,EAAY1iB,EAAOiB,EAAK+P,EAAS2R,EAAY,CAClD,OAAAD,EAAW,QAAQE,GAAa,OAC9B5iB,IAAQwC,EAAA,KAAK,WAAWogB,CAAS,IAAzB,YAAApgB,EAA4B,QAAQxC,EAAOiB,EAAK+P,EAAS2R,KAAe3iB,CACtF,CAAK,EACMA,CACX,CACA,EAEA,MAAM6iB,GAAmB,CAAE,EACrBC,GAAuB5E,GAAO,CAAC/N,EAAS+N,CAAG,GAAK,OAAOA,GAAQ,WAAa,OAAOA,GAAQ,SACjG,MAAM6E,WAAmBlB,EAAa,CACpC,YAAYmB,EAAUhS,EAAU,GAAI,CAClC,MAAO,EACPwN,GAAK,CAAC,gBAAiB,gBAAiB,iBAAkB,eAAgB,mBAAoB,aAAc,OAAO,EAAGwE,EAAU,IAAI,EACpI,KAAK,QAAUhS,EACX,KAAK,QAAQ,eAAiB,SAChC,KAAK,QAAQ,aAAe,KAE9B,KAAK,OAAS4Q,EAAW,OAAO,YAAY,CAChD,CACE,eAAe9Q,EAAK,CACdA,IAAK,KAAK,SAAWA,EAC7B,CACE,OAAO7P,EAAKpB,EAAI,CACd,cAAe,CAAA,CACnB,EAAK,CACD,MAAMojB,EAAM,CACV,GAAGpjB,CACJ,EACD,GAAIoB,GAAO,KAAM,MAAO,GACxB,MAAMiiB,EAAW,KAAK,QAAQjiB,EAAKgiB,CAAG,EACtC,OAAOC,GAAA,YAAAA,EAAU,OAAQ,MAC7B,CACE,eAAejiB,EAAKgiB,EAAK,CACvB,IAAIxC,EAAcwC,EAAI,cAAgB,OAAYA,EAAI,YAAc,KAAK,QAAQ,YAC7ExC,IAAgB,SAAWA,EAAc,KAC7C,MAAMC,EAAeuC,EAAI,eAAiB,OAAYA,EAAI,aAAe,KAAK,QAAQ,aACtF,IAAI7Q,EAAa6Q,EAAI,IAAM,KAAK,QAAQ,WAAa,CAAE,EACvD,MAAME,EAAuB1C,GAAexf,EAAI,QAAQwf,CAAW,EAAI,GACjE2C,EAAuB,CAAC,KAAK,QAAQ,yBAA2B,CAACH,EAAI,cAAgB,CAAC,KAAK,QAAQ,wBAA0B,CAACA,EAAI,aAAe,CAACzC,GAAoBvf,EAAKwf,EAAaC,CAAY,EAC1M,GAAIyC,GAAwB,CAACC,EAAsB,CACjD,MAAMnkB,EAAIgC,EAAI,MAAM,KAAK,aAAa,aAAa,EACnD,GAAIhC,GAAKA,EAAE,OAAS,EAClB,MAAO,CACL,IAAAgC,EACA,WAAYkP,EAASiC,CAAU,EAAI,CAACA,CAAU,EAAIA,CACnD,EAEH,MAAMiR,EAAQpiB,EAAI,MAAMwf,CAAW,GAC/BA,IAAgBC,GAAgBD,IAAgBC,GAAgB,KAAK,QAAQ,GAAG,QAAQ2C,EAAM,CAAC,CAAC,EAAI,MAAIjR,EAAaiR,EAAM,MAAO,GACtIpiB,EAAMoiB,EAAM,KAAK3C,CAAY,CACnC,CACI,MAAO,CACL,IAAAzf,EACA,WAAYkP,EAASiC,CAAU,EAAI,CAACA,CAAU,EAAIA,CACnD,CACL,CACE,UAAUkR,EAAMzjB,EAAG0jB,EAAS,CAC1B,IAAIN,EAAM,OAAOpjB,GAAM,SAAW,CAChC,GAAGA,CACT,EAAQA,EAQJ,GAPI,OAAOojB,GAAQ,UAAY,KAAK,QAAQ,mCAC1CA,EAAM,KAAK,QAAQ,iCAAiC,SAAS,GAE3D,OAAO,SAAY,WAAUA,EAAM,CACrC,GAAGA,CACJ,GACIA,IAAKA,EAAM,CAAE,GACdK,GAAQ,KAAM,MAAO,GACpB,MAAM,QAAQA,CAAI,IAAGA,EAAO,CAAC,OAAOA,CAAI,CAAC,GAC9C,MAAME,EAAgBP,EAAI,gBAAkB,OAAYA,EAAI,cAAgB,KAAK,QAAQ,cACnFvC,EAAeuC,EAAI,eAAiB,OAAYA,EAAI,aAAe,KAAK,QAAQ,aAChF,CACJ,IAAAhiB,EACA,WAAAmR,CACN,EAAQ,KAAK,eAAekR,EAAKA,EAAK,OAAS,CAAC,EAAGL,CAAG,EAC5CtQ,EAAYP,EAAWA,EAAW,OAAS,CAAC,EAClD,IAAIqO,EAAcwC,EAAI,cAAgB,OAAYA,EAAI,YAAc,KAAK,QAAQ,YAC7ExC,IAAgB,SAAWA,EAAc,KAC7C,MAAM3P,EAAMmS,EAAI,KAAO,KAAK,SACtBQ,EAA0BR,EAAI,yBAA2B,KAAK,QAAQ,wBAC5E,IAAInS,GAAA,YAAAA,EAAK,iBAAkB,SACzB,OAAI2S,EACED,EACK,CACL,IAAK,GAAG7Q,CAAS,GAAG8N,CAAW,GAAGxf,CAAG,GACrC,QAASA,EACT,aAAcA,EACd,QAAS6P,EACT,OAAQ6B,EACR,WAAY,KAAK,qBAAqBsQ,CAAG,CAC1C,EAEI,GAAGtQ,CAAS,GAAG8N,CAAW,GAAGxf,CAAG,GAErCuiB,EACK,CACL,IAAKviB,EACL,QAASA,EACT,aAAcA,EACd,QAAS6P,EACT,OAAQ6B,EACR,WAAY,KAAK,qBAAqBsQ,CAAG,CAC1C,EAEIhiB,EAET,MAAMiiB,EAAW,KAAK,QAAQI,EAAML,CAAG,EACvC,IAAI/E,EAAMgF,GAAA,YAAAA,EAAU,IACpB,MAAMQ,GAAaR,GAAA,YAAAA,EAAU,UAAWjiB,EAClC0iB,GAAkBT,GAAA,YAAAA,EAAU,eAAgBjiB,EAC5C2iB,EAAW,CAAC,kBAAmB,oBAAqB,iBAAiB,EACrEC,EAAaZ,EAAI,aAAe,OAAYA,EAAI,WAAa,KAAK,QAAQ,WAC1Ea,EAA6B,CAAC,KAAK,YAAc,KAAK,WAAW,eACjEC,EAAsBd,EAAI,QAAU,QAAa,CAAC9S,EAAS8S,EAAI,KAAK,EACpEe,EAAkBjB,GAAW,gBAAgBE,CAAG,EAChDgB,EAAqBF,EAAsB,KAAK,eAAe,UAAUjT,EAAKmS,EAAI,MAAOA,CAAG,EAAI,GAChGiB,EAAoCjB,EAAI,SAAWc,EAAsB,KAAK,eAAe,UAAUjT,EAAKmS,EAAI,MAAO,CAC3H,QAAS,EACV,CAAA,EAAI,GACCkB,EAAwBJ,GAAuB,CAACd,EAAI,SAAWA,EAAI,QAAU,EAC7EmB,EAAeD,GAAyBlB,EAAI,eAAe,KAAK,QAAQ,eAAe,MAAM,GAAKA,EAAI,eAAegB,CAAkB,EAAE,GAAKhB,EAAI,eAAeiB,CAAiC,EAAE,GAAKjB,EAAI,aACnN,IAAIoB,EAAgBnG,EAChB4F,GAA8B,CAAC5F,GAAO8F,IACxCK,EAAgBD,GAElB,MAAME,EAAiBxB,GAAqBuB,CAAa,EACnDE,GAAU,OAAO,UAAU,SAAS,MAAMF,CAAa,EAC7D,GAAIP,GAA8BO,GAAiBC,GAAkBV,EAAS,QAAQW,EAAO,EAAI,GAAK,EAAEpU,EAAS0T,CAAU,GAAK,MAAM,QAAQQ,CAAa,GAAI,CAC7J,GAAI,CAACpB,EAAI,eAAiB,CAAC,KAAK,QAAQ,cAAe,CAChD,KAAK,QAAQ,uBAChB,KAAK,OAAO,KAAK,iEAAiE,EAEpF,MAAMzjB,EAAI,KAAK,QAAQ,sBAAwB,KAAK,QAAQ,sBAAsBkkB,EAAYW,EAAe,CAC3G,GAAGpB,EACH,GAAI7Q,CACd,CAAS,EAAI,QAAQnR,CAAG,KAAK,KAAK,QAAQ,2CAClC,OAAIuiB,GACFN,EAAS,IAAM1jB,EACf0jB,EAAS,WAAa,KAAK,qBAAqBD,CAAG,EAC5CC,GAEF1jB,CACf,CACM,GAAIkhB,EAAc,CAChB,MAAM8D,EAAiB,MAAM,QAAQH,CAAa,EAC5C7F,EAAOgG,EAAiB,CAAA,EAAK,CAAE,EAC/BC,EAAcD,EAAiBb,EAAkBD,EACvD,UAAWzkB,KAAKolB,EACd,GAAI,OAAO,UAAU,eAAe,KAAKA,EAAeplB,CAAC,EAAG,CAC1D,MAAMylB,EAAU,GAAGD,CAAW,GAAG/D,CAAY,GAAGzhB,CAAC,GAC7C+kB,GAAmB,CAAC9F,EACtBM,EAAKvf,CAAC,EAAI,KAAK,UAAUylB,EAAS,CAChC,GAAGzB,EACH,aAAcH,GAAqBsB,CAAY,EAAIA,EAAanlB,CAAC,EAAI,OAEnE,WAAY,GACZ,GAAImT,CAEtB,CAAe,EAEDoM,EAAKvf,CAAC,EAAI,KAAK,UAAUylB,EAAS,CAChC,GAAGzB,EAED,WAAY,GACZ,GAAI7Q,CAEtB,CAAe,EAECoM,EAAKvf,CAAC,IAAMylB,IAASlG,EAAKvf,CAAC,EAAIolB,EAAcplB,CAAC,EAC9D,CAEQif,EAAMM,CACd,CACA,SAAesF,GAA8B3T,EAAS0T,CAAU,GAAK,MAAM,QAAQ3F,CAAG,EAChFA,EAAMA,EAAI,KAAK2F,CAAU,EACrB3F,IAAKA,EAAM,KAAK,kBAAkBA,EAAKoF,EAAML,EAAKM,CAAO,OACxD,CACL,IAAIoB,EAAc,GACdC,EAAU,GACV,CAAC,KAAK,cAAc1G,CAAG,GAAK8F,IAC9BW,EAAc,GACdzG,EAAMkG,GAEH,KAAK,cAAclG,CAAG,IACzB0G,EAAU,GACV1G,EAAMjd,GAGR,MAAM4jB,GADiC5B,EAAI,gCAAkC,KAAK,QAAQ,iCAClC2B,EAAU,OAAY1G,EACxE4G,EAAgBd,GAAmBI,IAAiBlG,GAAO,KAAK,QAAQ,cAC9E,GAAI0G,GAAWD,GAAeG,EAAe,CAE3C,GADA,KAAK,OAAO,IAAIA,EAAgB,YAAc,aAAchU,EAAK6B,EAAW1R,EAAK6jB,EAAgBV,EAAelG,CAAG,EAC/GwC,EAAc,CAChB,MAAMqE,EAAK,KAAK,QAAQ9jB,EAAK,CAC3B,GAAGgiB,EACH,aAAc,EAC1B,CAAW,EACG8B,GAAMA,EAAG,KAAK,KAAK,OAAO,KAAK,iLAAiL,CAC9N,CACQ,IAAIC,EAAO,CAAE,EACb,MAAMC,GAAe,KAAK,cAAc,iBAAiB,KAAK,QAAQ,YAAahC,EAAI,KAAO,KAAK,QAAQ,EAC3G,GAAI,KAAK,QAAQ,gBAAkB,YAAcgC,IAAgBA,GAAa,CAAC,EAC7E,QAASve,EAAI,EAAGA,EAAIue,GAAa,OAAQve,IACvCse,EAAK,KAAKC,GAAave,CAAC,CAAC,OAElB,KAAK,QAAQ,gBAAkB,MACxCse,EAAO,KAAK,cAAc,mBAAmB/B,EAAI,KAAO,KAAK,QAAQ,EAErE+B,EAAK,KAAK/B,EAAI,KAAO,KAAK,QAAQ,EAEpC,MAAMiC,GAAO,CAACC,EAAGje,EAAGke,IAAyB,QAC3C,MAAMC,EAAoBrB,GAAmBoB,IAAyBlH,EAAMkH,EAAuBP,EAC/F,KAAK,QAAQ,kBACf,KAAK,QAAQ,kBAAkBM,EAAGxS,EAAWzL,EAAGme,EAAmBP,EAAe7B,CAAG,GAC5EzgB,GAAA,KAAK,mBAAL,MAAAA,GAAuB,aAChC,KAAK,iBAAiB,YAAY2iB,EAAGxS,EAAWzL,EAAGme,EAAmBP,EAAe7B,CAAG,EAE1F,KAAK,KAAK,aAAckC,EAAGxS,EAAWzL,EAAGgX,CAAG,CAC7C,EACG,KAAK,QAAQ,cACX,KAAK,QAAQ,oBAAsB6F,EACrCiB,EAAK,QAAQtS,GAAY,CACvB,MAAM4S,EAAW,KAAK,eAAe,YAAY5S,EAAUuQ,CAAG,EAC1DkB,GAAyBlB,EAAI,eAAe,KAAK,QAAQ,eAAe,MAAM,GAAKqC,EAAS,QAAQ,GAAG,KAAK,QAAQ,eAAe,MAAM,EAAI,GAC/IA,EAAS,KAAK,GAAG,KAAK,QAAQ,eAAe,MAAM,EAErDA,EAAS,QAAQC,GAAU,CACzBL,GAAK,CAACxS,CAAQ,EAAGzR,EAAMskB,EAAQtC,EAAI,eAAesC,CAAM,EAAE,GAAKnB,CAAY,CAC3F,CAAe,CACf,CAAa,EAEDc,GAAKF,EAAM/jB,EAAKmjB,CAAY,EAGxC,CACMlG,EAAM,KAAK,kBAAkBA,EAAKoF,EAAML,EAAKC,EAAUK,CAAO,EAC1DqB,GAAW1G,IAAQjd,GAAO,KAAK,QAAQ,8BACzCid,EAAM,GAAGvL,CAAS,GAAG8N,CAAW,GAAGxf,CAAG,KAEnC2jB,GAAWD,IAAgB,KAAK,QAAQ,yBAC3CzG,EAAM,KAAK,QAAQ,uBAAuB,KAAK,QAAQ,4BAA8B,GAAGvL,CAAS,GAAG8N,CAAW,GAAGxf,CAAG,GAAKA,EAAK0jB,EAAczG,EAAM,OAAW+E,CAAG,EAEzK,CACI,OAAIO,GACFN,EAAS,IAAMhF,EACfgF,EAAS,WAAa,KAAK,qBAAqBD,CAAG,EAC5CC,GAEFhF,CACX,CACE,kBAAkBA,EAAKjd,EAAKgiB,EAAKC,EAAUK,EAAS,SAClD,IAAI/gB,EAAA,KAAK,aAAL,MAAAA,EAAiB,MACnB0b,EAAM,KAAK,WAAW,MAAMA,EAAK,CAC/B,GAAG,KAAK,QAAQ,cAAc,iBAC9B,GAAG+E,CACJ,EAAEA,EAAI,KAAO,KAAK,UAAYC,EAAS,QAASA,EAAS,OAAQA,EAAS,QAAS,CAClF,SAAAA,CACR,CAAO,UACQ,CAACD,EAAI,kBAAmB,CAC7BA,EAAI,eAAe,KAAK,aAAa,KAAK,CAC5C,GAAGA,EAED,cAAe,CACb,GAAG,KAAK,QAAQ,cAChB,GAAGA,EAAI,aACnB,CAEA,CAAO,EACD,MAAMuC,EAAkBrV,EAAS+N,CAAG,MAAMhO,EAAA+S,GAAA,YAAAA,EAAK,gBAAL,YAAA/S,EAAoB,mBAAoB,OAAY+S,EAAI,cAAc,gBAAkB,KAAK,QAAQ,cAAc,iBAC7J,IAAIwC,EACJ,GAAID,EAAiB,CACnB,MAAME,EAAKxH,EAAI,MAAM,KAAK,aAAa,aAAa,EACpDuH,EAAUC,GAAMA,EAAG,MAC3B,CACM,IAAI5K,EAAOmI,EAAI,SAAW,CAAC9S,EAAS8S,EAAI,OAAO,EAAIA,EAAI,QAAUA,EAMjE,GALI,KAAK,QAAQ,cAAc,mBAAkBnI,EAAO,CACtD,GAAG,KAAK,QAAQ,cAAc,iBAC9B,GAAGA,CACJ,GACDoD,EAAM,KAAK,aAAa,YAAYA,EAAKpD,EAAMmI,EAAI,KAAO,KAAK,UAAYC,EAAS,QAASD,CAAG,EAC5FuC,EAAiB,CACnB,MAAMG,EAAKzH,EAAI,MAAM,KAAK,aAAa,aAAa,EAC9C0H,EAAUD,GAAMA,EAAG,OACrBF,EAAUG,IAAS3C,EAAI,KAAO,GAC1C,CACU,CAACA,EAAI,KAAOC,GAAYA,EAAS,MAAKD,EAAI,IAAM,KAAK,UAAYC,EAAS,SAC1ED,EAAI,OAAS,KAAO/E,EAAM,KAAK,aAAa,KAAKA,EAAK,IAAIjO,KACxDsT,GAAA,YAAAA,EAAU,MAAOtT,EAAK,CAAC,GAAK,CAACgT,EAAI,SACnC,KAAK,OAAO,KAAK,6CAA6ChT,EAAK,CAAC,CAAC,YAAYhP,EAAI,CAAC,CAAC,EAAE,EAClF,MAEF,KAAK,UAAU,GAAGgP,EAAMhP,CAAG,EACjCgiB,CAAG,GACFA,EAAI,eAAe,KAAK,aAAa,MAAO,CACtD,CACI,MAAM4C,EAAc5C,EAAI,aAAe,KAAK,QAAQ,YAC9C6C,EAAqB3V,EAAS0V,CAAW,EAAI,CAACA,CAAW,EAAIA,EACnE,OAAI3H,GAAO,OAAQ4H,GAAA,MAAAA,EAAoB,SAAU7C,EAAI,qBAAuB,KAC1E/E,EAAMsE,GAAc,OAAOsD,EAAoB5H,EAAKjd,EAAK,KAAK,SAAW,KAAK,QAAQ,wBAA0B,CAC9G,aAAc,CACZ,GAAGiiB,EACH,WAAY,KAAK,qBAAqBD,CAAG,CAC1C,EACD,GAAGA,CACX,EAAUA,EAAK,IAAI,GAER/E,CACX,CACE,QAAQoF,EAAML,EAAM,GAAI,CACtB,IAAI8C,EACAnB,EACAoB,EACAC,EACAC,EACJ,OAAI/V,EAASmT,CAAI,IAAGA,EAAO,CAACA,CAAI,GAChCA,EAAK,QAAQpc,GAAK,CAChB,GAAI,KAAK,cAAc6e,CAAK,EAAG,OAC/B,MAAMI,EAAY,KAAK,eAAejf,EAAG+b,CAAG,EACtChiB,EAAMklB,EAAU,IACtBvB,EAAU3jB,EACV,IAAImR,EAAa+T,EAAU,WACvB,KAAK,QAAQ,aAAY/T,EAAaA,EAAW,OAAO,KAAK,QAAQ,UAAU,GACnF,MAAM2R,EAAsBd,EAAI,QAAU,QAAa,CAAC9S,EAAS8S,EAAI,KAAK,EACpEkB,EAAwBJ,GAAuB,CAACd,EAAI,SAAWA,EAAI,QAAU,EAC7EmD,EAAuBnD,EAAI,UAAY,SAAc9S,EAAS8S,EAAI,OAAO,GAAK,OAAOA,EAAI,SAAY,WAAaA,EAAI,UAAY,GAClIoD,EAAQpD,EAAI,KAAOA,EAAI,KAAO,KAAK,cAAc,mBAAmBA,EAAI,KAAO,KAAK,SAAUA,EAAI,WAAW,EACnH7Q,EAAW,QAAQxB,GAAM,SACnB,KAAK,cAAcmV,CAAK,IAC5BG,EAAStV,EACL,CAACiS,GAAiB,GAAGwD,EAAM,CAAC,CAAC,IAAIzV,CAAE,EAAE,KAAKpO,EAAA,KAAK,QAAL,MAAAA,EAAY,qBAAsB,GAAC0N,EAAA,KAAK,QAAL,MAAAA,EAAY,mBAAmBgW,MAC9GrD,GAAiB,GAAGwD,EAAM,CAAC,CAAC,IAAIzV,CAAE,EAAE,EAAI,GACxC,KAAK,OAAO,KAAK,QAAQgU,CAAO,oBAAoByB,EAAM,KAAK,IAAI,CAAC,sCAAsCH,CAAM,uBAAwB,0NAA0N,GAEpWG,EAAM,QAAQvW,GAAQ,OACpB,GAAI,KAAK,cAAciW,CAAK,EAAG,OAC/BE,EAAUnW,EACV,MAAMwW,EAAY,CAACrlB,CAAG,EACtB,IAAIuB,EAAA,KAAK,aAAL,MAAAA,EAAiB,cACnB,KAAK,WAAW,cAAc8jB,EAAWrlB,EAAK6O,EAAMc,EAAIqS,CAAG,MACtD,CACL,IAAIsD,EACAxC,IAAqBwC,EAAe,KAAK,eAAe,UAAUzW,EAAMmT,EAAI,MAAOA,CAAG,GAC1F,MAAMuD,EAAa,GAAG,KAAK,QAAQ,eAAe,OAC5CC,EAAgB,GAAG,KAAK,QAAQ,eAAe,UAAU,KAAK,QAAQ,eAAe,GAU3F,GATI1C,IACFuC,EAAU,KAAKrlB,EAAMslB,CAAY,EAC7BtD,EAAI,SAAWsD,EAAa,QAAQE,CAAa,IAAM,GACzDH,EAAU,KAAKrlB,EAAMslB,EAAa,QAAQE,EAAe,KAAK,QAAQ,eAAe,CAAC,EAEpFtC,GACFmC,EAAU,KAAKrlB,EAAMulB,CAAU,GAG/BJ,EAAsB,CACxB,MAAMM,EAAa,GAAGzlB,CAAG,GAAG,KAAK,QAAQ,gBAAgB,GAAGgiB,EAAI,OAAO,GACvEqD,EAAU,KAAKI,CAAU,EACrB3C,IACFuC,EAAU,KAAKI,EAAaH,CAAY,EACpCtD,EAAI,SAAWsD,EAAa,QAAQE,CAAa,IAAM,GACzDH,EAAU,KAAKI,EAAaH,EAAa,QAAQE,EAAe,KAAK,QAAQ,eAAe,CAAC,EAE3FtC,GACFmC,EAAU,KAAKI,EAAaF,CAAU,EAGxD,CACA,CACU,IAAIG,EACJ,KAAOA,EAAcL,EAAU,OACxB,KAAK,cAAcP,CAAK,IAC3BC,EAAeW,EACfZ,EAAQ,KAAK,YAAYjW,EAAMc,EAAI+V,EAAa1D,CAAG,EAGjE,CAAS,EACT,CAAO,CACP,CAAK,EACM,CACL,IAAK8C,EACL,QAAAnB,EACA,aAAAoB,EACA,QAAAC,EACA,OAAAC,CACD,CACL,CACE,cAAchI,EAAK,CACjB,OAAOA,IAAQ,QAAa,EAAE,CAAC,KAAK,QAAQ,YAAcA,IAAQ,OAAS,EAAE,CAAC,KAAK,QAAQ,mBAAqBA,IAAQ,GAC5H,CACE,YAAYpO,EAAMc,EAAI3P,EAAK+P,EAAU,CAAA,EAAI,OACvC,OAAIxO,EAAA,KAAK,aAAL,MAAAA,EAAiB,YAAoB,KAAK,WAAW,YAAYsN,EAAMc,EAAI3P,EAAK+P,CAAO,EACpF,KAAK,cAAc,YAAYlB,EAAMc,EAAI3P,EAAK+P,CAAO,CAChE,CACE,qBAAqBA,EAAU,GAAI,CACjC,MAAM4V,EAAc,CAAC,eAAgB,UAAW,UAAW,UAAW,MAAO,OAAQ,cAAe,KAAM,eAAgB,cAAe,gBAAiB,gBAAiB,aAAc,cAAe,eAAe,EACjNC,EAA2B7V,EAAQ,SAAW,CAACb,EAASa,EAAQ,OAAO,EAC7E,IAAI8J,EAAO+L,EAA2B7V,EAAQ,QAAUA,EAUxD,GATI6V,GAA4B,OAAO7V,EAAQ,MAAU,MACvD8J,EAAK,MAAQ9J,EAAQ,OAEnB,KAAK,QAAQ,cAAc,mBAC7B8J,EAAO,CACL,GAAG,KAAK,QAAQ,cAAc,iBAC9B,GAAGA,CACJ,GAEC,CAAC+L,EAA0B,CAC7B/L,EAAO,CACL,GAAGA,CACJ,EACD,UAAW7Z,KAAO2lB,EAChB,OAAO9L,EAAK7Z,CAAG,CAEvB,CACI,OAAO6Z,CACX,CACE,OAAO,gBAAgB9J,EAAS,CAC9B,MAAM7M,EAAS,eACf,UAAW2iB,KAAU9V,EACnB,GAAI,OAAO,UAAU,eAAe,KAAKA,EAAS8V,CAAM,GAAK3iB,IAAW2iB,EAAO,UAAU,EAAG3iB,EAAO,MAAM,GAAmB6M,EAAQ8V,CAAM,IAA5B,OAC5G,MAAO,GAGX,MAAO,EACX,CACA,CAEA,MAAMC,EAAa,CACjB,YAAY/V,EAAS,CACnB,KAAK,QAAUA,EACf,KAAK,cAAgB,KAAK,QAAQ,eAAiB,GACnD,KAAK,OAAS4Q,EAAW,OAAO,eAAe,CACnD,CACE,sBAAsB9R,EAAM,CAE1B,GADAA,EAAOuR,GAAevR,CAAI,EACtB,CAACA,GAAQA,EAAK,QAAQ,GAAG,EAAI,EAAG,OAAO,KAC3C,MAAMqP,EAAIrP,EAAK,MAAM,GAAG,EAGxB,OAFIqP,EAAE,SAAW,IACjBA,EAAE,IAAK,EACHA,EAAEA,EAAE,OAAS,CAAC,EAAE,YAAa,IAAK,KAAY,KAC3C,KAAK,mBAAmBA,EAAE,KAAK,GAAG,CAAC,CAC9C,CACE,wBAAwBrP,EAAM,CAE5B,GADAA,EAAOuR,GAAevR,CAAI,EACtB,CAACA,GAAQA,EAAK,QAAQ,GAAG,EAAI,EAAG,OAAOA,EAC3C,MAAMqP,EAAIrP,EAAK,MAAM,GAAG,EACxB,OAAO,KAAK,mBAAmBqP,EAAE,CAAC,CAAC,CACvC,CACE,mBAAmBrP,EAAM,CACvB,GAAIK,EAASL,CAAI,GAAKA,EAAK,QAAQ,GAAG,EAAI,GAAI,CAC5C,IAAIkX,EACJ,GAAI,CACFA,EAAgB,KAAK,oBAAoBlX,CAAI,EAAE,CAAC,CACjD,MAAW,CAAA,CAIZ,OAHIkX,GAAiB,KAAK,QAAQ,eAChCA,EAAgBA,EAAc,YAAa,GAEzCA,IACA,KAAK,QAAQ,aACRlX,EAAK,YAAa,EAEpBA,EACb,CACI,OAAO,KAAK,QAAQ,WAAa,KAAK,QAAQ,aAAeA,EAAK,YAAW,EAAKA,CACtF,CACE,gBAAgBA,EAAM,CACpB,OAAI,KAAK,QAAQ,OAAS,gBAAkB,KAAK,QAAQ,4BACvDA,EAAO,KAAK,wBAAwBA,CAAI,GAEnC,CAAC,KAAK,eAAiB,CAAC,KAAK,cAAc,QAAU,KAAK,cAAc,QAAQA,CAAI,EAAI,EACnG,CACE,sBAAsBuW,EAAO,CAC3B,GAAI,CAACA,EAAO,OAAO,KACnB,IAAIN,EACJ,OAAAM,EAAM,QAAQvW,GAAQ,CACpB,GAAIiW,EAAO,OACX,MAAMkB,EAAa,KAAK,mBAAmBnX,CAAI,GAC3C,CAAC,KAAK,QAAQ,eAAiB,KAAK,gBAAgBmX,CAAU,KAAGlB,EAAQkB,EACnF,CAAK,EACG,CAAClB,GAAS,KAAK,QAAQ,eACzBM,EAAM,QAAQvW,GAAQ,CACpB,GAAIiW,EAAO,OACX,MAAMmB,EAAY,KAAK,sBAAsBpX,CAAI,EACjD,GAAI,KAAK,gBAAgBoX,CAAS,EAAG,OAAOnB,EAAQmB,EACpD,MAAMC,EAAU,KAAK,wBAAwBrX,CAAI,EACjD,GAAI,KAAK,gBAAgBqX,CAAO,EAAG,OAAOpB,EAAQoB,EAClDpB,EAAQ,KAAK,QAAQ,cAAc,KAAKqB,GAAgB,CACtD,GAAIA,IAAiBD,EAAS,OAAOC,EACrC,GAAI,EAAAA,EAAa,QAAQ,GAAG,EAAI,GAAKD,EAAQ,QAAQ,GAAG,EAAI,KACxDC,EAAa,QAAQ,GAAG,EAAI,GAAKD,EAAQ,QAAQ,GAAG,EAAI,GAAKC,EAAa,UAAU,EAAGA,EAAa,QAAQ,GAAG,CAAC,IAAMD,GACtHC,EAAa,QAAQD,CAAO,IAAM,GAAKA,EAAQ,OAAS,GAAG,OAAOC,CAChF,CAAS,CACT,CAAO,EAEErB,IAAOA,EAAQ,KAAK,iBAAiB,KAAK,QAAQ,WAAW,EAAE,CAAC,GAC9DA,CACX,CACE,iBAAiBsB,EAAWvX,EAAM,CAChC,GAAI,CAACuX,EAAW,MAAO,CAAE,EAGzB,GAFI,OAAOA,GAAc,aAAYA,EAAYA,EAAUvX,CAAI,GAC3DK,EAASkX,CAAS,IAAGA,EAAY,CAACA,CAAS,GAC3C,MAAM,QAAQA,CAAS,EAAG,OAAOA,EACrC,GAAI,CAACvX,EAAM,OAAOuX,EAAU,SAAW,CAAE,EACzC,IAAItB,EAAQsB,EAAUvX,CAAI,EAC1B,OAAKiW,IAAOA,EAAQsB,EAAU,KAAK,sBAAsBvX,CAAI,CAAC,GACzDiW,IAAOA,EAAQsB,EAAU,KAAK,mBAAmBvX,CAAI,CAAC,GACtDiW,IAAOA,EAAQsB,EAAU,KAAK,wBAAwBvX,CAAI,CAAC,GAC3DiW,IAAOA,EAAQsB,EAAU,SACvBtB,GAAS,CAAE,CACtB,CACE,mBAAmBjW,EAAMwX,EAAc,CACrC,MAAMC,EAAgB,KAAK,kBAAkBD,IAAiB,GAAQ,GAAKA,IAAiB,KAAK,QAAQ,aAAe,CAAA,EAAIxX,CAAI,EAC1HuW,EAAQ,CAAE,EACVmB,EAAU5G,GAAK,CACdA,IACD,KAAK,gBAAgBA,CAAC,EACxByF,EAAM,KAAKzF,CAAC,EAEZ,KAAK,OAAO,KAAK,uDAAuDA,CAAC,EAAE,EAE9E,EACD,OAAIzQ,EAASL,CAAI,IAAMA,EAAK,QAAQ,GAAG,EAAI,IAAMA,EAAK,QAAQ,GAAG,EAAI,KAC/D,KAAK,QAAQ,OAAS,gBAAgB0X,EAAQ,KAAK,mBAAmB1X,CAAI,CAAC,EAC3E,KAAK,QAAQ,OAAS,gBAAkB,KAAK,QAAQ,OAAS,eAAe0X,EAAQ,KAAK,sBAAsB1X,CAAI,CAAC,EACrH,KAAK,QAAQ,OAAS,eAAe0X,EAAQ,KAAK,wBAAwB1X,CAAI,CAAC,GAC1EK,EAASL,CAAI,GACtB0X,EAAQ,KAAK,mBAAmB1X,CAAI,CAAC,EAEvCyX,EAAc,QAAQE,GAAM,CACtBpB,EAAM,QAAQoB,CAAE,EAAI,GAAGD,EAAQ,KAAK,mBAAmBC,CAAE,CAAC,CACpE,CAAK,EACMpB,CACX,CACA,CAEA,MAAMqB,GAAgB,CACpB,KAAM,EACN,IAAK,EACL,IAAK,EACL,IAAK,EACL,KAAM,EACN,MAAO,CACT,EACMC,GAAY,CAChB,OAAQC,GAASA,IAAU,EAAI,MAAQ,QACvC,gBAAiB,KAAO,CACtB,iBAAkB,CAAC,MAAO,OAAO,CAClC,EACH,EACA,MAAMC,EAAe,CACnB,YAAYC,EAAe9W,EAAU,GAAI,CACvC,KAAK,cAAgB8W,EACrB,KAAK,QAAU9W,EACf,KAAK,OAAS4Q,EAAW,OAAO,gBAAgB,EAChD,KAAK,iBAAmB,CAAE,CAC9B,CACE,QAAQ9Q,EAAKK,EAAK,CAChB,KAAK,MAAML,CAAG,EAAIK,CACtB,CACE,YAAa,CACX,KAAK,iBAAmB,CAAE,CAC9B,CACE,QAAQrB,EAAMkB,EAAU,GAAI,CAC1B,MAAM+W,EAAc1G,GAAevR,IAAS,MAAQ,KAAOA,CAAI,EACzDX,EAAO6B,EAAQ,QAAU,UAAY,WACrCgX,EAAW,KAAK,UAAU,CAC9B,YAAAD,EACA,KAAA5Y,CACN,CAAK,EACD,GAAI6Y,KAAY,KAAK,iBACnB,OAAO,KAAK,iBAAiBA,CAAQ,EAEvC,IAAIC,EACJ,GAAI,CACFA,EAAO,IAAI,KAAK,YAAYF,EAAa,CACvC,KAAA5Y,CACR,CAAO,CACF,MAAa,CACZ,GAAI,CAAC,KACH,YAAK,OAAO,MAAM,+CAA+C,EAC1DwY,GAET,GAAI,CAAC7X,EAAK,MAAM,KAAK,EAAG,OAAO6X,GAC/B,MAAMO,EAAU,KAAK,cAAc,wBAAwBpY,CAAI,EAC/DmY,EAAO,KAAK,QAAQC,EAASlX,CAAO,CAC1C,CACI,YAAK,iBAAiBgX,CAAQ,EAAIC,EAC3BA,CACX,CACE,YAAYnY,EAAMkB,EAAU,GAAI,CAC9B,IAAIiX,EAAO,KAAK,QAAQnY,EAAMkB,CAAO,EACrC,OAAKiX,IAAMA,EAAO,KAAK,QAAQ,MAAOjX,CAAO,IACtCiX,GAAA,YAAAA,EAAM,kBAAkB,iBAAiB,QAAS,CAC7D,CACE,oBAAoBnY,EAAM7O,EAAK+P,EAAU,CAAA,EAAI,CAC3C,OAAO,KAAK,YAAYlB,EAAMkB,CAAO,EAAE,IAAIuU,GAAU,GAAGtkB,CAAG,GAAGskB,CAAM,EAAE,CAC1E,CACE,YAAYzV,EAAMkB,EAAU,GAAI,CAC9B,IAAIiX,EAAO,KAAK,QAAQnY,EAAMkB,CAAO,EAErC,OADKiX,IAAMA,EAAO,KAAK,QAAQ,MAAOjX,CAAO,GACxCiX,EACEA,EAAK,gBAAiB,EAAC,iBAAiB,KAAK,CAACE,EAAiBC,IAAoBV,GAAcS,CAAe,EAAIT,GAAcU,CAAe,CAAC,EAAE,IAAIC,GAAkB,GAAG,KAAK,QAAQ,OAAO,GAAGrX,EAAQ,QAAU,UAAU,KAAK,QAAQ,OAAO,GAAK,EAAE,GAAGqX,CAAc,EAAE,EADnQ,CAAE,CAExB,CACE,UAAUvY,EAAM8X,EAAO5W,EAAU,CAAA,EAAI,CACnC,MAAMiX,EAAO,KAAK,QAAQnY,EAAMkB,CAAO,EACvC,OAAIiX,EACK,GAAG,KAAK,QAAQ,OAAO,GAAGjX,EAAQ,QAAU,UAAU,KAAK,QAAQ,OAAO,GAAK,EAAE,GAAGiX,EAAK,OAAOL,CAAK,CAAC,IAE/G,KAAK,OAAO,KAAK,6BAA6B9X,CAAI,EAAE,EAC7C,KAAK,UAAU,MAAO8X,EAAO5W,CAAO,EAC/C,CACA,CAEA,MAAMsX,GAAuB,CAACxN,EAAM2E,EAAaxe,EAAKyf,EAAe,IAAKyB,EAAsB,KAAS,CACvG,IAAI9e,EAAOmc,GAAoB1E,EAAM2E,EAAaxe,CAAG,EACrD,MAAI,CAACoC,GAAQ8e,GAAuBhS,EAASlP,CAAG,IAC9CoC,EAAO0d,GAASjG,EAAM7Z,EAAKyf,CAAY,EACnCrd,IAAS,SAAWA,EAAO0d,GAAStB,EAAaxe,EAAKyf,CAAY,IAEjErd,CACT,EACMklB,GAAYC,GAAOA,EAAI,QAAQ,MAAO,MAAM,EAClD,MAAMC,EAAa,CACjB,YAAYzX,EAAU,GAAI,OACxB,KAAK,OAAS4Q,EAAW,OAAO,cAAc,EAC9C,KAAK,QAAU5Q,EACf,KAAK,SAASxO,EAAAwO,GAAA,YAAAA,EAAS,gBAAT,YAAAxO,EAAwB,UAAWxC,GAASA,GAC1D,KAAK,KAAKgR,CAAO,CACrB,CACE,KAAKA,EAAU,GAAI,CACZA,EAAQ,gBAAeA,EAAQ,cAAgB,CAClD,YAAa,EACd,GACD,KAAM,CACJ,OAAQ0X,EACR,YAAAC,EACA,oBAAAC,EACA,OAAAzkB,EACA,cAAA0kB,EACA,OAAAtD,EACA,cAAAuD,EACA,gBAAAC,EACA,eAAAC,EACA,eAAAC,EACA,cAAAC,EACA,qBAAAC,EACA,cAAAC,EACA,qBAAAC,EACA,wBAAAC,EACA,YAAAC,EACA,aAAAC,CACD,EAAGxY,EAAQ,cACZ,KAAK,OAAS0X,IAAa,OAAYA,EAAW1I,GAClD,KAAK,YAAc2I,IAAgB,OAAYA,EAAc,GAC7D,KAAK,oBAAsBC,IAAwB,OAAYA,EAAsB,GACrF,KAAK,OAASzkB,EAAS2b,GAAY3b,CAAM,EAAI0kB,GAAiB,KAC9D,KAAK,OAAStD,EAASzF,GAAYyF,CAAM,EAAIuD,GAAiB,KAC9D,KAAK,gBAAkBC,GAAmB,IAC1C,KAAK,eAAiBC,EAAiB,GAAKC,GAAkB,IAC9D,KAAK,eAAiB,KAAK,eAAiB,GAAKD,GAAkB,GACnE,KAAK,cAAgBE,EAAgBpJ,GAAYoJ,CAAa,EAAIC,GAAwBrJ,GAAY,KAAK,EAC3G,KAAK,cAAgBsJ,EAAgBtJ,GAAYsJ,CAAa,EAAIC,GAAwBvJ,GAAY,GAAG,EACzG,KAAK,wBAA0BwJ,GAA2B,IAC1D,KAAK,YAAcC,GAAe,IAClC,KAAK,aAAeC,IAAiB,OAAYA,EAAe,GAChE,KAAK,YAAa,CACtB,CACE,OAAQ,CACF,KAAK,SAAS,KAAK,KAAK,KAAK,OAAO,CAC5C,CACE,aAAc,CACZ,MAAMC,EAAmB,CAACC,EAAgBvJ,KACpCuJ,GAAA,YAAAA,EAAgB,UAAWvJ,GAC7BuJ,EAAe,UAAY,EACpBA,GAEF,IAAI,OAAOvJ,EAAS,GAAG,EAEhC,KAAK,OAASsJ,EAAiB,KAAK,OAAQ,GAAG,KAAK,MAAM,QAAQ,KAAK,MAAM,EAAE,EAC/E,KAAK,eAAiBA,EAAiB,KAAK,eAAgB,GAAG,KAAK,MAAM,GAAG,KAAK,cAAc,QAAQ,KAAK,cAAc,GAAG,KAAK,MAAM,EAAE,EAC3I,KAAK,cAAgBA,EAAiB,KAAK,cAAe,GAAG,KAAK,aAAa,QAAQ,KAAK,aAAa,EAAE,CAC/G,CACE,YAAY9Q,EAAKmC,EAAMhK,EAAKE,EAAS,OACnC,IAAIyD,EACAzU,EACA2pB,EACJ,MAAMlK,EAAc,KAAK,SAAW,KAAK,QAAQ,eAAiB,KAAK,QAAQ,cAAc,kBAAoB,CAAE,EAC7GmK,EAAe3oB,GAAO,CAC1B,GAAIA,EAAI,QAAQ,KAAK,eAAe,EAAI,EAAG,CACzC,MAAMoC,EAAOilB,GAAqBxN,EAAM2E,EAAaxe,EAAK,KAAK,QAAQ,aAAc,KAAK,QAAQ,mBAAmB,EACrH,OAAO,KAAK,aAAe,KAAK,OAAOoC,EAAM,OAAWyN,EAAK,CAC3D,GAAGE,EACH,GAAG8J,EACH,iBAAkB7Z,CACnB,CAAA,EAAIoC,CACb,CACM,MAAM,EAAIpC,EAAI,MAAM,KAAK,eAAe,EAClC,EAAI,EAAE,MAAK,EAAG,KAAM,EACpBtB,EAAI,EAAE,KAAK,KAAK,eAAe,EAAE,KAAM,EAC7C,OAAO,KAAK,OAAO2oB,GAAqBxN,EAAM2E,EAAa,EAAG,KAAK,QAAQ,aAAc,KAAK,QAAQ,mBAAmB,EAAG9f,EAAGmR,EAAK,CAClI,GAAGE,EACH,GAAG8J,EACH,iBAAkB,CAC1B,CAAO,CACF,EACD,KAAK,YAAa,EAClB,MAAM+O,GAA8B7Y,GAAA,YAAAA,EAAS,8BAA+B,KAAK,QAAQ,4BACnFwU,IAAkBhjB,EAAAwO,GAAA,YAAAA,EAAS,gBAAT,YAAAxO,EAAwB,mBAAoB,OAAYwO,EAAQ,cAAc,gBAAkB,KAAK,QAAQ,cAAc,gBAQnJ,MAPc,CAAC,CACb,MAAO,KAAK,eACZ,UAAWwX,GAAOD,GAAUC,CAAG,CACrC,EAAO,CACD,MAAO,KAAK,OACZ,UAAWA,GAAO,KAAK,YAAcD,GAAU,KAAK,OAAOC,CAAG,CAAC,EAAID,GAAUC,CAAG,CACtF,CAAK,EACK,QAAQsB,GAAQ,CAEpB,IADAH,EAAW,EACJlV,EAAQqV,EAAK,MAAM,KAAKnR,CAAG,GAAG,CACnC,MAAMoR,EAAatV,EAAM,CAAC,EAAE,KAAM,EAElC,GADAzU,EAAQ4pB,EAAaG,CAAU,EAC3B/pB,IAAU,OACZ,GAAI,OAAO6pB,GAAgC,WAAY,CACrD,MAAMG,EAAOH,EAA4BlR,EAAKlE,EAAOzD,CAAO,EAC5DhR,EAAQmQ,EAAS6Z,CAAI,EAAIA,EAAO,EAC5C,SAAqBhZ,GAAW,OAAO,UAAU,eAAe,KAAKA,EAAS+Y,CAAU,EAC5E/pB,EAAQ,WACCwlB,EAAiB,CAC1BxlB,EAAQyU,EAAM,CAAC,EACf,QACZ,MACY,KAAK,OAAO,KAAK,8BAA8BsV,CAAU,sBAAsBpR,CAAG,EAAE,EACpF3Y,EAAQ,OAED,CAACmQ,EAASnQ,CAAK,GAAK,CAAC,KAAK,sBACnCA,EAAQse,GAAWte,CAAK,GAE1B,MAAMiqB,EAAYH,EAAK,UAAU9pB,CAAK,EAStC,GARA2Y,EAAMA,EAAI,QAAQlE,EAAM,CAAC,EAAGwV,CAAS,EACjCzE,GACFsE,EAAK,MAAM,WAAa9pB,EAAM,OAC9B8pB,EAAK,MAAM,WAAarV,EAAM,CAAC,EAAE,QAEjCqV,EAAK,MAAM,UAAY,EAEzBH,IACIA,GAAY,KAAK,YACnB,KAEV,CACA,CAAK,EACMhR,CACX,CACE,KAAKA,EAAK8O,EAAIzW,EAAU,CAAA,EAAI,CAC1B,IAAIyD,EACAzU,EACAkqB,EACJ,MAAMC,EAAmB,CAAClpB,EAAKmpB,IAAqB,CAClD,MAAMC,EAAM,KAAK,wBACjB,GAAIppB,EAAI,QAAQopB,CAAG,EAAI,EAAG,OAAOppB,EACjC,MAAM2f,EAAI3f,EAAI,MAAM,IAAI,OAAO,GAAGopB,CAAG,OAAO,CAAC,EAC7C,IAAIC,EAAgB,IAAI1J,EAAE,CAAC,CAAC,GAC5B3f,EAAM2f,EAAE,CAAC,EACT0J,EAAgB,KAAK,YAAYA,EAAeJ,CAAa,EAC7D,MAAMK,EAAsBD,EAAc,MAAM,IAAI,EAC9CE,EAAsBF,EAAc,MAAM,IAAI,KAC/CC,GAAA,YAAAA,EAAqB,SAAU,GAAK,IAAM,GAAK,CAACC,GAAuBA,EAAoB,OAAS,IAAM,KAC7GF,EAAgBA,EAAc,QAAQ,KAAM,GAAG,GAEjD,GAAI,CACFJ,EAAgB,KAAK,MAAMI,CAAa,EACpCF,IAAkBF,EAAgB,CACpC,GAAGE,EACH,GAAGF,CACJ,EACF,OAAQzqB,EAAG,CACV,YAAK,OAAO,KAAK,oDAAoDwB,CAAG,GAAIxB,CAAC,EACtE,GAAGwB,CAAG,GAAGopB,CAAG,GAAGC,CAAa,EAC3C,CACM,OAAIJ,EAAc,cAAgBA,EAAc,aAAa,QAAQ,KAAK,MAAM,EAAI,IAAI,OAAOA,EAAc,aACtGjpB,CACR,EACD,KAAOwT,EAAQ,KAAK,cAAc,KAAKkE,CAAG,GAAG,CAC3C,IAAI8R,EAAa,CAAE,EACnBP,EAAgB,CACd,GAAGlZ,CACJ,EACDkZ,EAAgBA,EAAc,SAAW,CAAC/Z,EAAS+Z,EAAc,OAAO,EAAIA,EAAc,QAAUA,EACpGA,EAAc,mBAAqB,GACnC,OAAOA,EAAc,aACrB,IAAIQ,EAAW,GACf,GAAIjW,EAAM,CAAC,EAAE,QAAQ,KAAK,eAAe,IAAM,IAAM,CAAC,OAAO,KAAKA,EAAM,CAAC,CAAC,EAAG,CAC3E,MAAMjV,EAAIiV,EAAM,CAAC,EAAE,MAAM,KAAK,eAAe,EAAE,IAAIkW,GAAQA,EAAK,KAAI,CAAE,EACtElW,EAAM,CAAC,EAAIjV,EAAE,MAAO,EACpBirB,EAAajrB,EACbkrB,EAAW,EACnB,CAEM,GADA1qB,EAAQynB,EAAG0C,EAAiB,KAAK,KAAM1V,EAAM,CAAC,EAAE,KAAI,EAAIyV,CAAa,EAAGA,CAAa,EACjFlqB,GAASyU,EAAM,CAAC,IAAMkE,GAAO,CAACxI,EAASnQ,CAAK,EAAG,OAAOA,EACrDmQ,EAASnQ,CAAK,IAAGA,EAAQse,GAAWte,CAAK,GACzCA,IACH,KAAK,OAAO,KAAK,qBAAqByU,EAAM,CAAC,CAAC,gBAAgBkE,CAAG,EAAE,EACnE3Y,EAAQ,IAEN0qB,IACF1qB,EAAQyqB,EAAW,OAAO,CAAClI,EAAG5iB,IAAM,KAAK,OAAO4iB,EAAG5iB,EAAGqR,EAAQ,IAAK,CACjE,GAAGA,EACH,iBAAkByD,EAAM,CAAC,EAAE,KAAI,CACzC,CAAS,EAAGzU,EAAM,MAAM,GAElB2Y,EAAMA,EAAI,QAAQlE,EAAM,CAAC,EAAGzU,CAAK,EACjC,KAAK,OAAO,UAAY,CAC9B,CACI,OAAO2Y,CACX,CACA,CAEA,MAAMiS,GAAiBC,GAAa,CAClC,IAAIC,EAAaD,EAAU,YAAW,EAAG,KAAM,EAC/C,MAAME,EAAgB,CAAE,EACxB,GAAIF,EAAU,QAAQ,GAAG,EAAI,GAAI,CAC/B,MAAM1L,EAAI0L,EAAU,MAAM,GAAG,EAC7BC,EAAa3L,EAAE,CAAC,EAAE,YAAW,EAAG,KAAM,EACtC,MAAM6L,EAAS7L,EAAE,CAAC,EAAE,UAAU,EAAGA,EAAE,CAAC,EAAE,OAAS,CAAC,EAC5C2L,IAAe,YAAcE,EAAO,QAAQ,GAAG,EAAI,EAChDD,EAAc,WAAUA,EAAc,SAAWC,EAAO,KAAM,GAC1DF,IAAe,gBAAkBE,EAAO,QAAQ,GAAG,EAAI,EAC3DD,EAAc,QAAOA,EAAc,MAAQC,EAAO,KAAM,GAEhDA,EAAO,MAAM,GAAG,EACxB,QAAQ/H,GAAO,CAClB,GAAIA,EAAK,CACP,KAAM,CAAChiB,EAAK,GAAG+O,CAAI,EAAIiT,EAAI,MAAM,GAAG,EAC9BuF,EAAMxY,EAAK,KAAK,GAAG,EAAE,OAAO,QAAQ,WAAY,EAAE,EAClDib,EAAahqB,EAAI,KAAM,EACxB8pB,EAAcE,CAAU,IAAGF,EAAcE,CAAU,EAAIzC,GACxDA,IAAQ,UAASuC,EAAcE,CAAU,EAAI,IAC7CzC,IAAQ,SAAQuC,EAAcE,CAAU,EAAI,IAC3C,MAAMzC,CAAG,IAAGuC,EAAcE,CAAU,EAAI,SAASzC,EAAK,EAAE,EACvE,CACA,CAAO,CAEP,CACE,MAAO,CACL,WAAAsC,EACA,cAAAC,CACD,CACH,EACMG,GAAwBpS,GAAM,CAClC,MAAMlV,EAAQ,CAAE,EAChB,MAAO,CAAC2e,EAAG4C,EAAGtlB,IAAM,CAClB,IAAIsrB,EAActrB,EACdA,GAAKA,EAAE,kBAAoBA,EAAE,cAAgBA,EAAE,aAAaA,EAAE,gBAAgB,GAAKA,EAAEA,EAAE,gBAAgB,IACzGsrB,EAAc,CACZ,GAAGA,EACH,CAACtrB,EAAE,gBAAgB,EAAG,MACvB,GAEH,MAAMoB,EAAMkkB,EAAI,KAAK,UAAUgG,CAAW,EAC1C,IAAIC,EAAMxnB,EAAM3C,CAAG,EACnB,OAAKmqB,IACHA,EAAMtS,EAAGuI,GAAe8D,CAAC,EAAGtlB,CAAC,EAC7B+D,EAAM3C,CAAG,EAAImqB,GAERA,EAAI7I,CAAC,CACb,CACH,EACM8I,GAA2BvS,GAAM,CAACyJ,EAAG4C,EAAGtlB,IAAMiZ,EAAGuI,GAAe8D,CAAC,EAAGtlB,CAAC,EAAE0iB,CAAC,EAC9E,MAAM+I,EAAU,CACd,YAAYta,EAAU,GAAI,CACxB,KAAK,OAAS4Q,EAAW,OAAO,WAAW,EAC3C,KAAK,QAAU5Q,EACf,KAAK,KAAKA,CAAO,CACrB,CACE,KAAKgS,EAAUhS,EAAU,CACvB,cAAe,CAAA,CACnB,EAAK,CACD,KAAK,gBAAkBA,EAAQ,cAAc,iBAAmB,IAChE,MAAMua,EAAKva,EAAQ,oBAAsBka,GAAwBG,GACjE,KAAK,QAAU,CACb,OAAQE,EAAG,CAACza,EAAKmS,IAAQ,CACvB,MAAMuI,EAAY,IAAI,KAAK,aAAa1a,EAAK,CAC3C,GAAGmS,CACb,CAAS,EACD,OAAOuF,GAAOgD,EAAU,OAAOhD,CAAG,CAC1C,CAAO,EACD,SAAU+C,EAAG,CAACza,EAAKmS,IAAQ,CACzB,MAAMuI,EAAY,IAAI,KAAK,aAAa1a,EAAK,CAC3C,GAAGmS,EACH,MAAO,UACjB,CAAS,EACD,OAAOuF,GAAOgD,EAAU,OAAOhD,CAAG,CAC1C,CAAO,EACD,SAAU+C,EAAG,CAACza,EAAKmS,IAAQ,CACzB,MAAMuI,EAAY,IAAI,KAAK,eAAe1a,EAAK,CAC7C,GAAGmS,CACb,CAAS,EACD,OAAOuF,GAAOgD,EAAU,OAAOhD,CAAG,CAC1C,CAAO,EACD,aAAc+C,EAAG,CAACza,EAAKmS,IAAQ,CAC7B,MAAMuI,EAAY,IAAI,KAAK,mBAAmB1a,EAAK,CACjD,GAAGmS,CACb,CAAS,EACD,OAAOuF,GAAOgD,EAAU,OAAOhD,EAAKvF,EAAI,OAAS,KAAK,CAC9D,CAAO,EACD,KAAMsI,EAAG,CAACza,EAAKmS,IAAQ,CACrB,MAAMuI,EAAY,IAAI,KAAK,WAAW1a,EAAK,CACzC,GAAGmS,CACb,CAAS,EACD,OAAOuF,GAAOgD,EAAU,OAAOhD,CAAG,CACnC,CAAA,CACF,CACL,CACE,IAAIhQ,EAAMiP,EAAI,CACZ,KAAK,QAAQjP,EAAK,YAAW,EAAG,KAAM,CAAA,EAAIiP,CAC9C,CACE,UAAUjP,EAAMiP,EAAI,CAClB,KAAK,QAAQjP,EAAK,YAAW,EAAG,MAAM,EAAI0S,GAAsBzD,CAAE,CACtE,CACE,OAAOznB,EAAOyrB,EAAQ3a,EAAKE,EAAU,CAAA,EAAI,CACvC,MAAM0a,EAAUD,EAAO,MAAM,KAAK,eAAe,EACjD,GAAIC,EAAQ,OAAS,GAAKA,EAAQ,CAAC,EAAE,QAAQ,GAAG,EAAI,GAAKA,EAAQ,CAAC,EAAE,QAAQ,GAAG,EAAI,GAAKA,EAAQ,KAAK/rB,GAAKA,EAAE,QAAQ,GAAG,EAAI,EAAE,EAAG,CAC9H,MAAMgsB,EAAYD,EAAQ,UAAU/rB,GAAKA,EAAE,QAAQ,GAAG,EAAI,EAAE,EAC5D+rB,EAAQ,CAAC,EAAI,CAACA,EAAQ,CAAC,EAAG,GAAGA,EAAQ,OAAO,EAAGC,CAAS,CAAC,EAAE,KAAK,KAAK,eAAe,CAC1F,CAyBI,OAxBeD,EAAQ,OAAO,CAACE,EAAKjsB,IAAM,OACxC,KAAM,CACJ,WAAAmrB,EACA,cAAAC,CACR,EAAUH,GAAejrB,CAAC,EACpB,GAAI,KAAK,QAAQmrB,CAAU,EAAG,CAC5B,IAAIe,EAAYD,EAChB,GAAI,CACF,MAAME,IAAatpB,EAAAwO,GAAA,YAAAA,EAAS,eAAT,YAAAxO,EAAwBwO,EAAQ,oBAAqB,CAAE,EACpEmU,EAAI2G,EAAW,QAAUA,EAAW,KAAO9a,EAAQ,QAAUA,EAAQ,KAAOF,EAClF+a,EAAY,KAAK,QAAQf,CAAU,EAAEc,EAAKzG,EAAG,CAC3C,GAAG4F,EACH,GAAG/Z,EACH,GAAG8a,CACf,CAAW,CACF,OAAQvsB,EAAO,CACd,KAAK,OAAO,KAAKA,CAAK,CAChC,CACQ,OAAOssB,CACf,MACQ,KAAK,OAAO,KAAK,oCAAoCf,CAAU,EAAE,EAEnE,OAAOc,CACR,EAAE5rB,CAAK,CAEZ,CACA,CAEA,MAAM+rB,GAAgB,CAACC,EAAGxT,IAAS,CAC7BwT,EAAE,QAAQxT,CAAI,IAAM,SACtB,OAAOwT,EAAE,QAAQxT,CAAI,EACrBwT,EAAE,eAEN,EACA,MAAMC,WAAkBpK,EAAa,CACnC,YAAYqK,EAASC,EAAOnJ,EAAUhS,EAAU,CAAA,EAAI,SAClD,MAAO,EACP,KAAK,QAAUkb,EACf,KAAK,MAAQC,EACb,KAAK,SAAWnJ,EAChB,KAAK,cAAgBA,EAAS,cAC9B,KAAK,QAAUhS,EACf,KAAK,OAAS4Q,EAAW,OAAO,kBAAkB,EAClD,KAAK,aAAe,CAAE,EACtB,KAAK,iBAAmB5Q,EAAQ,kBAAoB,GACpD,KAAK,aAAe,EACpB,KAAK,WAAaA,EAAQ,YAAc,EAAIA,EAAQ,WAAa,EACjE,KAAK,aAAeA,EAAQ,cAAgB,EAAIA,EAAQ,aAAe,IACvE,KAAK,MAAQ,CAAE,EACf,KAAK,MAAQ,CAAE,GACfd,GAAA1N,EAAA,KAAK,UAAL,YAAAA,EAAc,OAAd,MAAA0N,EAAA,KAAA1N,EAAqBwgB,EAAUhS,EAAQ,QAASA,EACpD,CACE,UAAUob,EAAWha,EAAYpB,EAASqb,EAAU,CAClD,MAAMC,EAAS,CAAE,EACXC,EAAU,CAAE,EACZC,EAAkB,CAAE,EACpBC,EAAmB,CAAE,EAC3B,OAAAL,EAAU,QAAQtb,GAAO,CACvB,IAAI4b,EAAmB,GACvBta,EAAW,QAAQxB,GAAM,CACvB,MAAM4H,EAAO,GAAG1H,CAAG,IAAIF,CAAE,GACrB,CAACI,EAAQ,QAAU,KAAK,MAAM,kBAAkBF,EAAKF,CAAE,EACzD,KAAK,MAAM4H,CAAI,EAAI,EACV,KAAK,MAAMA,CAAI,EAAI,IAAc,KAAK,MAAMA,CAAI,IAAM,EAC3D+T,EAAQ/T,CAAI,IAAM,SAAW+T,EAAQ/T,CAAI,EAAI,KAEjD,KAAK,MAAMA,CAAI,EAAI,EACnBkU,EAAmB,GACfH,EAAQ/T,CAAI,IAAM,SAAW+T,EAAQ/T,CAAI,EAAI,IAC7C8T,EAAO9T,CAAI,IAAM,SAAW8T,EAAO9T,CAAI,EAAI,IAC3CiU,EAAiB7b,CAAE,IAAM,SAAW6b,EAAiB7b,CAAE,EAAI,KAEzE,CAAO,EACI8b,IAAkBF,EAAgB1b,CAAG,EAAI,GACpD,CAAK,GACG,OAAO,KAAKwb,CAAM,EAAE,QAAU,OAAO,KAAKC,CAAO,EAAE,SACrD,KAAK,MAAM,KAAK,CACd,QAAAA,EACA,aAAc,OAAO,KAAKA,CAAO,EAAE,OACnC,OAAQ,CAAE,EACV,OAAQ,CAAE,EACV,SAAAF,CACR,CAAO,EAEI,CACL,OAAQ,OAAO,KAAKC,CAAM,EAC1B,QAAS,OAAO,KAAKC,CAAO,EAC5B,gBAAiB,OAAO,KAAKC,CAAe,EAC5C,iBAAkB,OAAO,KAAKC,CAAgB,CAC/C,CACL,CACE,OAAOjU,EAAMmU,EAAK7R,EAAM,CACtB,MAAM4D,EAAIlG,EAAK,MAAM,GAAG,EAClB1H,EAAM4N,EAAE,CAAC,EACT9N,EAAK8N,EAAE,CAAC,EACViO,GAAK,KAAK,KAAK,gBAAiB7b,EAAKF,EAAI+b,CAAG,EAC5C,CAACA,GAAO7R,GACV,KAAK,MAAM,kBAAkBhK,EAAKF,EAAIkK,EAAM,OAAW,OAAW,CAChE,SAAU,EAClB,CAAO,EAEH,KAAK,MAAMtC,CAAI,EAAImU,EAAM,GAAK,EAC1BA,GAAO7R,IAAM,KAAK,MAAMtC,CAAI,EAAI,GACpC,MAAMoU,EAAS,CAAE,EACjB,KAAK,MAAM,QAAQZ,GAAK,CACtB3M,GAAS2M,EAAE,OAAQ,CAAClb,CAAG,EAAGF,CAAE,EAC5Bmb,GAAcC,EAAGxT,CAAI,EACjBmU,GAAKX,EAAE,OAAO,KAAKW,CAAG,EACtBX,EAAE,eAAiB,GAAK,CAACA,EAAE,OAC7B,OAAO,KAAKA,EAAE,MAAM,EAAE,QAAQ7G,GAAK,CAC5ByH,EAAOzH,CAAC,IAAGyH,EAAOzH,CAAC,EAAI,CAAE,GAC9B,MAAM0H,EAAab,EAAE,OAAO7G,CAAC,EACzB0H,EAAW,QACbA,EAAW,QAAQjtB,GAAK,CAClBgtB,EAAOzH,CAAC,EAAEvlB,CAAC,IAAM,SAAWgtB,EAAOzH,CAAC,EAAEvlB,CAAC,EAAI,GAC7D,CAAa,CAEb,CAAS,EACDosB,EAAE,KAAO,GACLA,EAAE,OAAO,OACXA,EAAE,SAASA,EAAE,MAAM,EAEnBA,EAAE,SAAU,EAGtB,CAAK,EACD,KAAK,KAAK,SAAUY,CAAM,EAC1B,KAAK,MAAQ,KAAK,MAAM,OAAOZ,GAAK,CAACA,EAAE,IAAI,CAC/C,CACE,KAAKlb,EAAKF,EAAIkc,EAAQC,EAAQ,EAAGC,EAAO,KAAK,aAAcX,EAAU,CACnE,GAAI,CAACvb,EAAI,OAAQ,OAAOub,EAAS,KAAM,CAAA,CAAE,EACzC,GAAI,KAAK,cAAgB,KAAK,iBAAkB,CAC9C,KAAK,aAAa,KAAK,CACrB,IAAAvb,EACA,GAAAF,EACA,OAAAkc,EACA,MAAAC,EACA,KAAAC,EACA,SAAAX,CACR,CAAO,EACD,MACN,CACI,KAAK,eACL,MAAMY,EAAW,CAACN,EAAK7R,IAAS,CAE9B,GADA,KAAK,eACD,KAAK,aAAa,OAAS,EAAG,CAChC,MAAMoG,EAAO,KAAK,aAAa,MAAO,EACtC,KAAK,KAAKA,EAAK,IAAKA,EAAK,GAAIA,EAAK,OAAQA,EAAK,MAAOA,EAAK,KAAMA,EAAK,QAAQ,CACtF,CACM,GAAIyL,GAAO7R,GAAQiS,EAAQ,KAAK,WAAY,CAC1C,WAAW,IAAM,CACf,KAAK,KAAK,KAAK,KAAMjc,EAAKF,EAAIkc,EAAQC,EAAQ,EAAGC,EAAO,EAAGX,CAAQ,CACpE,EAAEW,CAAI,EACP,MACR,CACMX,EAASM,EAAK7R,CAAI,CACnB,EACK2M,EAAK,KAAK,QAAQqF,CAAM,EAAE,KAAK,KAAK,OAAO,EACjD,GAAIrF,EAAG,SAAW,EAAG,CACnB,GAAI,CACF,MAAMjoB,EAAIioB,EAAG3W,EAAKF,CAAE,EAChBpR,GAAK,OAAOA,EAAE,MAAS,WACzBA,EAAE,KAAKsb,GAAQmS,EAAS,KAAMnS,CAAI,CAAC,EAAE,MAAMmS,CAAQ,EAEnDA,EAAS,KAAMztB,CAAC,CAEnB,OAAQmtB,EAAK,CACZM,EAASN,CAAG,CACpB,CACM,MACN,CACI,OAAOlF,EAAG3W,EAAKF,EAAIqc,CAAQ,CAC/B,CACE,eAAeb,EAAWha,EAAYpB,EAAU,CAAA,EAAIqb,EAAU,CAC5D,GAAI,CAAC,KAAK,QACR,YAAK,OAAO,KAAK,gEAAgE,EAC1EA,GAAYA,EAAU,EAE3Blc,EAASic,CAAS,IAAGA,EAAY,KAAK,cAAc,mBAAmBA,CAAS,GAChFjc,EAASiC,CAAU,IAAGA,EAAa,CAACA,CAAU,GAClD,MAAMka,EAAS,KAAK,UAAUF,EAAWha,EAAYpB,EAASqb,CAAQ,EACtE,GAAI,CAACC,EAAO,OAAO,OACjB,OAAKA,EAAO,QAAQ,QAAQD,EAAU,EAC/B,KAETC,EAAO,OAAO,QAAQ9T,GAAQ,CAC5B,KAAK,QAAQA,CAAI,CACvB,CAAK,CACL,CACE,KAAK4T,EAAWha,EAAYia,EAAU,CACpC,KAAK,eAAeD,EAAWha,EAAY,CAAA,EAAIia,CAAQ,CAC3D,CACE,OAAOD,EAAWha,EAAYia,EAAU,CACtC,KAAK,eAAeD,EAAWha,EAAY,CACzC,OAAQ,EACT,EAAEia,CAAQ,CACf,CACE,QAAQ7T,EAAMrU,EAAS,GAAI,CACzB,MAAMua,EAAIlG,EAAK,MAAM,GAAG,EAClB1H,EAAM4N,EAAE,CAAC,EACT9N,EAAK8N,EAAE,CAAC,EACd,KAAK,KAAK5N,EAAKF,EAAI,OAAQ,OAAW,OAAW,CAAC+b,EAAK7R,IAAS,CAC1D6R,GAAK,KAAK,OAAO,KAAK,GAAGxoB,CAAM,qBAAqByM,CAAE,iBAAiBE,CAAG,UAAW6b,CAAG,EACxF,CAACA,GAAO7R,GAAM,KAAK,OAAO,IAAI,GAAG3W,CAAM,oBAAoByM,CAAE,iBAAiBE,CAAG,GAAIgK,CAAI,EAC7F,KAAK,OAAOtC,EAAMmU,EAAK7R,CAAI,CACjC,CAAK,CACL,CACE,YAAYsR,EAAWzZ,EAAW1R,EAAKisB,EAAeC,EAAUnc,EAAU,CAAE,EAAEoc,EAAM,IAAM,GAAI,eAC5F,IAAIld,GAAA1N,EAAA,KAAK,WAAL,YAAAA,EAAe,QAAf,MAAA0N,EAAsB,oBAAsB,GAACE,GAAAC,EAAA,KAAK,WAAL,YAAAA,EAAe,QAAf,MAAAD,EAAsB,mBAAmBuC,IAAY,CACpG,KAAK,OAAO,KAAK,qBAAqB1R,CAAG,uBAAuB0R,CAAS,uBAAwB,0NAA0N,EAC3T,MACN,CACI,GAAI,EAAqB1R,GAAQ,MAAQA,IAAQ,IACjD,KAAIosB,EAAA,KAAK,UAAL,MAAAA,EAAc,OAAQ,CACxB,MAAMC,EAAO,CACX,GAAGtc,EACH,SAAAmc,CACD,EACK1F,EAAK,KAAK,QAAQ,OAAO,KAAK,KAAK,OAAO,EAChD,GAAIA,EAAG,OAAS,EACd,GAAI,CACF,IAAIjoB,EACAioB,EAAG,SAAW,EAChBjoB,EAAIioB,EAAG2E,EAAWzZ,EAAW1R,EAAKisB,EAAeI,CAAI,EAErD9tB,EAAIioB,EAAG2E,EAAWzZ,EAAW1R,EAAKisB,CAAa,EAE7C1tB,GAAK,OAAOA,EAAE,MAAS,WACzBA,EAAE,KAAKsb,GAAQsS,EAAI,KAAMtS,CAAI,CAAC,EAAE,MAAMsS,CAAG,EAEzCA,EAAI,KAAM5tB,CAAC,CAEd,OAAQmtB,EAAK,CACZS,EAAIT,CAAG,CACjB,MAEQlF,EAAG2E,EAAWzZ,EAAW1R,EAAKisB,EAAeE,EAAKE,CAAI,CAE9D,CACQ,CAAClB,GAAa,CAACA,EAAU,CAAC,GAC9B,KAAK,MAAM,YAAYA,EAAU,CAAC,EAAGzZ,EAAW1R,EAAKisB,CAAa,EACtE,CACA,CAEA,MAAM3T,GAAM,KAAO,CACjB,MAAO,GACP,UAAW,GACX,GAAI,CAAC,aAAa,EAClB,UAAW,CAAC,aAAa,EACzB,YAAa,CAAC,KAAK,EACnB,WAAY,GACZ,cAAe,GACf,yBAA0B,GAC1B,KAAM,MACN,QAAS,GACT,qBAAsB,GACtB,aAAc,IACd,YAAa,IACb,gBAAiB,IACjB,iBAAkB,IAClB,wBAAyB,GACzB,YAAa,GACb,cAAe,GACf,cAAe,WACf,mBAAoB,GACpB,kBAAmB,GACnB,4BAA6B,GAC7B,YAAa,GACb,wBAAyB,GACzB,WAAY,GACZ,kBAAmB,GACnB,cAAe,GACf,WAAY,GACZ,sBAAuB,GACvB,uBAAwB,GACxB,4BAA6B,GAC7B,wBAAyB,GACzB,iCAAkCtJ,GAAQ,CACxC,IAAIoE,EAAM,CAAE,EAIZ,GAHI,OAAOpE,EAAK,CAAC,GAAM,WAAUoE,EAAMpE,EAAK,CAAC,GACzCE,EAASF,EAAK,CAAC,CAAC,IAAGoE,EAAI,aAAepE,EAAK,CAAC,GAC5CE,EAASF,EAAK,CAAC,CAAC,IAAGoE,EAAI,aAAepE,EAAK,CAAC,GAC5C,OAAOA,EAAK,CAAC,GAAM,UAAY,OAAOA,EAAK,CAAC,GAAM,SAAU,CAC9D,MAAMe,EAAUf,EAAK,CAAC,GAAKA,EAAK,CAAC,EACjC,OAAO,KAAKe,CAAO,EAAE,QAAQ/P,GAAO,CAClCoT,EAAIpT,CAAG,EAAI+P,EAAQ/P,CAAG,CAC9B,CAAO,CACP,CACI,OAAOoT,CACR,EACD,cAAe,CACb,YAAa,GACb,OAAQrU,GAASA,EACjB,OAAQ,KACR,OAAQ,KACR,gBAAiB,IACjB,eAAgB,IAChB,cAAe,MACf,cAAe,IACf,wBAAyB,IACzB,YAAa,IACb,gBAAiB,EAClB,EACD,oBAAqB,EACvB,GACMutB,GAAmBvc,GAAW,SAClC,OAAIb,EAASa,EAAQ,EAAE,IAAGA,EAAQ,GAAK,CAACA,EAAQ,EAAE,GAC9Cb,EAASa,EAAQ,WAAW,IAAGA,EAAQ,YAAc,CAACA,EAAQ,WAAW,GACzEb,EAASa,EAAQ,UAAU,IAAGA,EAAQ,WAAa,CAACA,EAAQ,UAAU,KACtEd,GAAA1N,EAAAwO,EAAQ,gBAAR,YAAAxO,EAAuB,UAAvB,YAAA0N,EAAA,KAAA1N,EAAiC,WAAY,IAC/CwO,EAAQ,cAAgBA,EAAQ,cAAc,OAAO,CAAC,QAAQ,CAAC,GAE7D,OAAOA,EAAQ,eAAkB,YAAWA,EAAQ,UAAYA,EAAQ,eACrEA,CACT,EAEMwc,GAAO,IAAM,CAAE,EACfC,GAAsBC,GAAQ,CACrB,OAAO,oBAAoB,OAAO,eAAeA,CAAI,CAAC,EAC9D,QAAQ9B,GAAO,CACd,OAAO8B,EAAK9B,CAAG,GAAM,aACvB8B,EAAK9B,CAAG,EAAI8B,EAAK9B,CAAG,EAAE,KAAK8B,CAAI,EAErC,CAAG,CACH,EACA,MAAMC,WAAa9L,EAAa,CAC9B,YAAY7Q,EAAU,CAAE,EAAEqb,EAAU,CASlC,GARA,MAAO,EACP,KAAK,QAAUkB,GAAiBvc,CAAO,EACvC,KAAK,SAAW,CAAE,EAClB,KAAK,OAAS4Q,EACd,KAAK,QAAU,CACb,SAAU,CAAA,CACX,EACD6L,GAAoB,IAAI,EACpBpB,GAAY,CAAC,KAAK,eAAiB,CAACrb,EAAQ,QAAS,CACvD,GAAI,CAAC,KAAK,QAAQ,UAChB,YAAK,KAAKA,EAASqb,CAAQ,EACpB,KAET,WAAW,IAAM,CACf,KAAK,KAAKrb,EAASqb,CAAQ,CAC5B,EAAE,CAAC,CACV,CACA,CACE,KAAKrb,EAAU,CAAE,EAAEqb,EAAU,CAC3B,KAAK,eAAiB,GAClB,OAAOrb,GAAY,aACrBqb,EAAWrb,EACXA,EAAU,CAAE,GAEVA,EAAQ,WAAa,MAAQA,EAAQ,KACnCb,EAASa,EAAQ,EAAE,EACrBA,EAAQ,UAAYA,EAAQ,GACnBA,EAAQ,GAAG,QAAQ,aAAa,EAAI,IAC7CA,EAAQ,UAAYA,EAAQ,GAAG,CAAC,IAGpC,MAAM4c,EAAUrU,GAAK,EACrB,KAAK,QAAU,CACb,GAAGqU,EACH,GAAG,KAAK,QACR,GAAGL,GAAiBvc,CAAO,CAC5B,EACD,KAAK,QAAQ,cAAgB,CAC3B,GAAG4c,EAAQ,cACX,GAAG,KAAK,QAAQ,aACjB,EACG5c,EAAQ,eAAiB,SAC3B,KAAK,QAAQ,wBAA0BA,EAAQ,cAE7CA,EAAQ,cAAgB,SAC1B,KAAK,QAAQ,uBAAyBA,EAAQ,aAEhD,MAAM6c,EAAsBC,GACrBA,EACD,OAAOA,GAAkB,WAAmB,IAAIA,EAC7CA,EAFoB,KAI7B,GAAI,CAAC,KAAK,QAAQ,QAAS,CACrB,KAAK,QAAQ,OACflM,EAAW,KAAKiM,EAAoB,KAAK,QAAQ,MAAM,EAAG,KAAK,OAAO,EAEtEjM,EAAW,KAAK,KAAM,KAAK,OAAO,EAEpC,IAAI4J,EACA,KAAK,QAAQ,UACfA,EAAY,KAAK,QAAQ,UAEzBA,EAAYF,GAEd,MAAMyC,EAAK,IAAIhH,GAAa,KAAK,OAAO,EACxC,KAAK,MAAQ,IAAI7E,GAAc,KAAK,QAAQ,UAAW,KAAK,OAAO,EACnE,MAAMxD,EAAI,KAAK,SACfA,EAAE,OAASkD,EACXlD,EAAE,cAAgB,KAAK,MACvBA,EAAE,cAAgBqP,EAClBrP,EAAE,eAAiB,IAAImJ,GAAekG,EAAI,CACxC,QAAS,KAAK,QAAQ,gBACtB,qBAAsB,KAAK,QAAQ,oBAC3C,CAAO,EACGvC,IAAc,CAAC,KAAK,QAAQ,cAAc,QAAU,KAAK,QAAQ,cAAc,SAAWoC,EAAQ,cAAc,UAClHlP,EAAE,UAAYmP,EAAoBrC,CAAS,EAC3C9M,EAAE,UAAU,KAAKA,EAAG,KAAK,OAAO,EAChC,KAAK,QAAQ,cAAc,OAASA,EAAE,UAAU,OAAO,KAAKA,EAAE,SAAS,GAEzEA,EAAE,aAAe,IAAI+J,GAAa,KAAK,OAAO,EAC9C/J,EAAE,MAAQ,CACR,mBAAoB,KAAK,mBAAmB,KAAK,IAAI,CACtD,EACDA,EAAE,iBAAmB,IAAIuN,GAAU4B,EAAoB,KAAK,QAAQ,OAAO,EAAGnP,EAAE,cAAeA,EAAG,KAAK,OAAO,EAC9GA,EAAE,iBAAiB,GAAG,IAAK,CAAClP,KAAUS,IAAS,CAC7C,KAAK,KAAKT,EAAO,GAAGS,CAAI,CAChC,CAAO,EACG,KAAK,QAAQ,mBACfyO,EAAE,iBAAmBmP,EAAoB,KAAK,QAAQ,gBAAgB,EAClEnP,EAAE,iBAAiB,MAAMA,EAAE,iBAAiB,KAAKA,EAAG,KAAK,QAAQ,UAAW,KAAK,OAAO,GAE1F,KAAK,QAAQ,aACfA,EAAE,WAAamP,EAAoB,KAAK,QAAQ,UAAU,EACtDnP,EAAE,WAAW,MAAMA,EAAE,WAAW,KAAK,IAAI,GAE/C,KAAK,WAAa,IAAIqE,GAAW,KAAK,SAAU,KAAK,OAAO,EAC5D,KAAK,WAAW,GAAG,IAAK,CAACvT,KAAUS,IAAS,CAC1C,KAAK,KAAKT,EAAO,GAAGS,CAAI,CAChC,CAAO,EACD,KAAK,QAAQ,SAAS,QAAQhR,GAAK,CAC7BA,EAAE,MAAMA,EAAE,KAAK,IAAI,CAC/B,CAAO,CACP,CAGI,GAFA,KAAK,OAAS,KAAK,QAAQ,cAAc,OACpCotB,IAAUA,EAAWmB,IACtB,KAAK,QAAQ,aAAe,CAAC,KAAK,SAAS,kBAAoB,CAAC,KAAK,QAAQ,IAAK,CACpF,MAAMnH,EAAQ,KAAK,SAAS,cAAc,iBAAiB,KAAK,QAAQ,WAAW,EAC/EA,EAAM,OAAS,GAAKA,EAAM,CAAC,IAAM,QAAO,KAAK,QAAQ,IAAMA,EAAM,CAAC,EAC5E,CACQ,CAAC,KAAK,SAAS,kBAAoB,CAAC,KAAK,QAAQ,KACnD,KAAK,OAAO,KAAK,yDAAyD,EAE3D,CAAC,cAAe,oBAAqB,oBAAqB,mBAAmB,EACrF,QAAQyG,GAAU,CACzB,KAAKA,CAAM,EAAI,IAAI7c,IAAS,KAAK,MAAM6c,CAAM,EAAE,GAAG7c,CAAI,CAC5D,CAAK,EACuB,CAAC,cAAe,eAAgB,oBAAqB,sBAAsB,EACnF,QAAQ6c,GAAU,CAChC,KAAKA,CAAM,EAAI,IAAI7c,KACjB,KAAK,MAAM6c,CAAM,EAAE,GAAG7c,CAAI,EACnB,KAEf,CAAK,EACD,MAAM+d,EAAW/P,GAAO,EAClBgQ,EAAO,IAAM,CACjB,MAAMC,EAAS,CAACvB,EAAKjtB,IAAM,CACzB,KAAK,eAAiB,GAClB,KAAK,eAAiB,CAAC,KAAK,sBAAsB,KAAK,OAAO,KAAK,uEAAuE,EAC9I,KAAK,cAAgB,GAChB,KAAK,QAAQ,SAAS,KAAK,OAAO,IAAI,cAAe,KAAK,OAAO,EACtE,KAAK,KAAK,cAAe,KAAK,OAAO,EACrCsuB,EAAS,QAAQtuB,CAAC,EAClB2sB,EAASM,EAAKjtB,CAAC,CAChB,EACD,GAAI,KAAK,WAAa,CAAC,KAAK,cAAe,OAAOwuB,EAAO,KAAM,KAAK,EAAE,KAAK,IAAI,CAAC,EAChF,KAAK,eAAe,KAAK,QAAQ,IAAKA,CAAM,CAC7C,EACD,OAAI,KAAK,QAAQ,WAAa,CAAC,KAAK,QAAQ,UAC1CD,EAAM,EAEN,WAAWA,EAAM,CAAC,EAEbD,CACX,CACE,cAActb,EAAU2Z,EAAWmB,GAAM,SACvC,IAAIW,EAAe9B,EACnB,MAAMpG,EAAU9V,EAASuC,CAAQ,EAAIA,EAAW,KAAK,SAErD,GADI,OAAOA,GAAa,aAAYyb,EAAezb,GAC/C,CAAC,KAAK,QAAQ,WAAa,KAAK,QAAQ,wBAAyB,CACnE,IAAIuT,GAAA,YAAAA,EAAS,iBAAkB,WAAa,CAAC,KAAK,QAAQ,SAAW,KAAK,QAAQ,QAAQ,SAAW,GAAI,OAAOkI,EAAc,EAC9H,MAAM7B,EAAS,CAAE,EACX8B,EAAStd,GAAO,CAEpB,GADI,CAACA,GACDA,IAAQ,SAAU,OACT,KAAK,SAAS,cAAc,mBAAmBA,CAAG,EAC1D,QAAQqU,GAAK,CACZA,IAAM,UACNmH,EAAO,QAAQnH,CAAC,EAAI,GAAGmH,EAAO,KAAKnH,CAAC,CAClD,CAAS,CACF,EACIc,EAIHmI,EAAOnI,CAAO,EAHI,KAAK,SAAS,cAAc,iBAAiB,KAAK,QAAQ,WAAW,EAC7E,QAAQd,GAAKiJ,EAAOjJ,CAAC,CAAC,GAIlCjV,GAAA1N,EAAA,KAAK,QAAQ,UAAb,YAAAA,EAAsB,UAAtB,MAAA0N,EAAA,KAAA1N,EAAgC2iB,GAAKiJ,EAAOjJ,CAAC,GAC7C,KAAK,SAAS,iBAAiB,KAAKmH,EAAQ,KAAK,QAAQ,GAAI7sB,GAAK,CAC5D,CAACA,GAAK,CAAC,KAAK,kBAAoB,KAAK,UAAU,KAAK,oBAAoB,KAAK,QAAQ,EACzF0uB,EAAa1uB,CAAC,CACtB,CAAO,CACP,MACM0uB,EAAa,IAAI,CAEvB,CACE,gBAAgBnJ,EAAMpU,EAAIyb,EAAU,CAClC,MAAM2B,EAAW/P,GAAO,EACxB,OAAI,OAAO+G,GAAS,aAClBqH,EAAWrH,EACXA,EAAO,QAEL,OAAOpU,GAAO,aAChByb,EAAWzb,EACXA,EAAK,QAEFoU,IAAMA,EAAO,KAAK,WAClBpU,IAAIA,EAAK,KAAK,QAAQ,IACtByb,IAAUA,EAAWmB,IAC1B,KAAK,SAAS,iBAAiB,OAAOxI,EAAMpU,EAAI+b,GAAO,CACrDqB,EAAS,QAAS,EAClB3B,EAASM,CAAG,CAClB,CAAK,EACMqB,CACX,CACE,IAAIvL,EAAQ,CACV,GAAI,CAACA,EAAQ,MAAM,IAAI,MAAM,+FAA+F,EAC5H,GAAI,CAACA,EAAO,KAAM,MAAM,IAAI,MAAM,0FAA0F,EAC5H,OAAIA,EAAO,OAAS,YAClB,KAAK,QAAQ,QAAUA,IAErBA,EAAO,OAAS,UAAYA,EAAO,KAAOA,EAAO,MAAQA,EAAO,SAClE,KAAK,QAAQ,OAASA,GAEpBA,EAAO,OAAS,qBAClB,KAAK,QAAQ,iBAAmBA,GAE9BA,EAAO,OAAS,eAClB,KAAK,QAAQ,WAAaA,GAExBA,EAAO,OAAS,iBAClBD,GAAc,iBAAiBC,CAAM,EAEnCA,EAAO,OAAS,cAClB,KAAK,QAAQ,UAAYA,GAEvBA,EAAO,OAAS,YAClB,KAAK,QAAQ,SAAS,KAAKA,CAAM,EAE5B,IACX,CACE,oBAAoB0C,EAAG,CACrB,GAAI,GAACA,GAAK,CAAC,KAAK,YACZ,GAAC,SAAU,KAAK,EAAE,QAAQA,CAAC,EAAI,IACnC,SAASkJ,EAAK,EAAGA,EAAK,KAAK,UAAU,OAAQA,IAAM,CACjD,MAAMC,EAAY,KAAK,UAAUD,CAAE,EACnC,GAAI,GAAC,SAAU,KAAK,EAAE,QAAQC,CAAS,EAAI,KACvC,KAAK,MAAM,4BAA4BA,CAAS,EAAG,CACrD,KAAK,iBAAmBA,EACxB,KACR,CACA,CACQ,CAAC,KAAK,kBAAoB,KAAK,UAAU,QAAQnJ,CAAC,EAAI,GAAK,KAAK,MAAM,4BAA4BA,CAAC,IACrG,KAAK,iBAAmBA,EACxB,KAAK,UAAU,QAAQA,CAAC,GAE9B,CACE,eAAerU,EAAKub,EAAU,CAC5B,KAAK,qBAAuBvb,EAC5B,MAAMkd,EAAW/P,GAAO,EACxB,KAAK,KAAK,mBAAoBnN,CAAG,EACjC,MAAMyd,EAAc,GAAK,CACvB,KAAK,SAAW,EAChB,KAAK,UAAY,KAAK,SAAS,cAAc,mBAAmB,CAAC,EACjE,KAAK,iBAAmB,OACxB,KAAK,oBAAoB,CAAC,CAC3B,EACKC,EAAO,CAAC7B,EAAKxH,IAAM,CACnBA,EACE,KAAK,uBAAyBrU,IAChCyd,EAAYpJ,CAAC,EACb,KAAK,WAAW,eAAeA,CAAC,EAChC,KAAK,qBAAuB,OAC5B,KAAK,KAAK,kBAAmBA,CAAC,EAC9B,KAAK,OAAO,IAAI,kBAAmBA,CAAC,GAGtC,KAAK,qBAAuB,OAE9B6I,EAAS,QAAQ,IAAI/d,IAAS,KAAK,EAAE,GAAGA,CAAI,CAAC,EACzCoc,GAAUA,EAASM,EAAK,IAAI1c,IAAS,KAAK,EAAE,GAAGA,CAAI,CAAC,CACzD,EACKwe,EAASzJ,GAAQ,SACjB,CAAClU,GAAO,CAACkU,GAAQ,KAAK,SAAS,mBAAkBA,EAAO,CAAE,GAC9D,MAAM0J,EAAKve,EAAS6U,CAAI,EAAIA,EAAOA,GAAQA,EAAK,CAAC,EAC3CG,EAAI,KAAK,MAAM,4BAA4BuJ,CAAE,EAAIA,EAAK,KAAK,SAAS,cAAc,sBAAsBve,EAAS6U,CAAI,EAAI,CAACA,CAAI,EAAIA,CAAI,EACxIG,IACG,KAAK,UACRoJ,EAAYpJ,CAAC,EAEV,KAAK,WAAW,UAAU,KAAK,WAAW,eAAeA,CAAC,GAC/DjV,GAAA1N,EAAA,KAAK,SAAS,mBAAd,YAAAA,EAAgC,oBAAhC,MAAA0N,EAAA,KAAA1N,EAAoD2iB,IAEtD,KAAK,cAAcA,EAAGwH,GAAO,CAC3B6B,EAAK7B,EAAKxH,CAAC,CACnB,CAAO,CACF,EACD,MAAI,CAACrU,GAAO,KAAK,SAAS,kBAAoB,CAAC,KAAK,SAAS,iBAAiB,MAC5E2d,EAAO,KAAK,SAAS,iBAAiB,OAAM,CAAE,EACrC,CAAC3d,GAAO,KAAK,SAAS,kBAAoB,KAAK,SAAS,iBAAiB,MAC9E,KAAK,SAAS,iBAAiB,OAAO,SAAW,EACnD,KAAK,SAAS,iBAAiB,OAAM,EAAG,KAAK2d,CAAM,EAEnD,KAAK,SAAS,iBAAiB,OAAOA,CAAM,EAG9CA,EAAO3d,CAAG,EAELkd,CACX,CACE,UAAUld,EAAKF,EAAIgC,EAAW,CAC5B,MAAM+b,EAAS,CAAC1tB,EAAKqsB,KAAStd,IAAS,CACrC,IAAInQ,EACA,OAAOytB,GAAS,SAClBztB,EAAI,KAAK,QAAQ,iCAAiC,CAACoB,EAAKqsB,CAAI,EAAE,OAAOtd,CAAI,CAAC,EAE1EnQ,EAAI,CACF,GAAGytB,CACJ,EAEHztB,EAAE,IAAMA,EAAE,KAAO8uB,EAAO,IACxB9uB,EAAE,KAAOA,EAAE,MAAQ8uB,EAAO,KAC1B9uB,EAAE,GAAKA,EAAE,IAAM8uB,EAAO,GAClB9uB,EAAE,YAAc,KAAIA,EAAE,UAAYA,EAAE,WAAa+S,GAAa+b,EAAO,WACzE,MAAMjO,EAAe,KAAK,QAAQ,cAAgB,IAClD,IAAIkO,EACJ,OAAI/uB,EAAE,WAAa,MAAM,QAAQoB,CAAG,EAClC2tB,EAAY3tB,EAAI,IAAIiG,GAAK,GAAGrH,EAAE,SAAS,GAAG6gB,CAAY,GAAGxZ,CAAC,EAAE,EAE5D0nB,EAAY/uB,EAAE,UAAY,GAAGA,EAAE,SAAS,GAAG6gB,CAAY,GAAGzf,CAAG,GAAKA,EAE7D,KAAK,EAAE2tB,EAAW/uB,CAAC,CAC3B,EACD,OAAIsQ,EAASW,CAAG,EACd6d,EAAO,IAAM7d,EAEb6d,EAAO,KAAO7d,EAEhB6d,EAAO,GAAK/d,EACZ+d,EAAO,UAAY/b,EACZ+b,CACX,CACE,KAAK1e,EAAM,OACT,OAAOzN,EAAA,KAAK,aAAL,YAAAA,EAAiB,UAAU,GAAGyN,EACzC,CACE,UAAUA,EAAM,OACd,OAAOzN,EAAA,KAAK,aAAL,YAAAA,EAAiB,OAAO,GAAGyN,EACtC,CACE,oBAAoBW,EAAI,CACtB,KAAK,QAAQ,UAAYA,CAC7B,CACE,mBAAmBA,EAAII,EAAU,GAAI,CACnC,GAAI,CAAC,KAAK,cACR,YAAK,OAAO,KAAK,kDAAmD,KAAK,SAAS,EAC3E,GAET,GAAI,CAAC,KAAK,WAAa,CAAC,KAAK,UAAU,OACrC,YAAK,OAAO,KAAK,6DAA8D,KAAK,SAAS,EACtF,GAET,MAAMF,EAAME,EAAQ,KAAO,KAAK,kBAAoB,KAAK,UAAU,CAAC,EAC9D6d,EAAc,KAAK,QAAU,KAAK,QAAQ,YAAc,GACxDC,EAAU,KAAK,UAAU,KAAK,UAAU,OAAS,CAAC,EACxD,GAAIhe,EAAI,gBAAkB,SAAU,MAAO,GAC3C,MAAMI,EAAiB,CAAC,EAAGtR,IAAM,CAC/B,MAAMmvB,EAAY,KAAK,SAAS,iBAAiB,MAAM,GAAG,CAAC,IAAInvB,CAAC,EAAE,EAClE,OAAOmvB,IAAc,IAAMA,IAAc,GAAKA,IAAc,CAC7D,EACD,GAAI/d,EAAQ,SAAU,CACpB,MAAMge,EAAYhe,EAAQ,SAAS,KAAME,CAAc,EACvD,GAAI8d,IAAc,OAAW,OAAOA,CAC1C,CAGI,MAFI,QAAK,kBAAkBle,EAAKF,CAAE,GAC9B,CAAC,KAAK,SAAS,iBAAiB,SAAW,KAAK,QAAQ,WAAa,CAAC,KAAK,QAAQ,yBACnFM,EAAeJ,EAAKF,CAAE,IAAM,CAACie,GAAe3d,EAAe4d,EAASle,CAAE,GAE9E,CACE,eAAeA,EAAIyb,EAAU,CAC3B,MAAM2B,EAAW/P,GAAO,EACxB,OAAK,KAAK,QAAQ,IAId9N,EAASS,CAAE,IAAGA,EAAK,CAACA,CAAE,GAC1BA,EAAG,QAAQ,GAAK,CACV,KAAK,QAAQ,GAAG,QAAQ,CAAC,EAAI,GAAG,KAAK,QAAQ,GAAG,KAAK,CAAC,CAChE,CAAK,EACD,KAAK,cAAc+b,GAAO,CACxBqB,EAAS,QAAS,EACd3B,GAAUA,EAASM,CAAG,CAChC,CAAK,EACMqB,IAXD3B,GAAUA,EAAU,EACjB,QAAQ,QAAS,EAW9B,CACE,cAAcrH,EAAMqH,EAAU,CAC5B,MAAM2B,EAAW/P,GAAO,EACpB9N,EAAS6U,CAAI,IAAGA,EAAO,CAACA,CAAI,GAChC,MAAMiK,EAAY,KAAK,QAAQ,SAAW,CAAE,EACtCC,EAAUlK,EAAK,OAAOlU,GAAOme,EAAU,QAAQne,CAAG,EAAI,GAAK,KAAK,SAAS,cAAc,gBAAgBA,CAAG,CAAC,EACjH,OAAKoe,EAAQ,QAIb,KAAK,QAAQ,QAAUD,EAAU,OAAOC,CAAO,EAC/C,KAAK,cAAcvC,GAAO,CACxBqB,EAAS,QAAS,EACd3B,GAAUA,EAASM,CAAG,CAChC,CAAK,EACMqB,IARD3B,GAAUA,EAAU,EACjB,QAAQ,QAAS,EAQ9B,CACE,IAAIvb,EAAK,SAEP,GADKA,IAAKA,EAAM,KAAK,qBAAqBtO,EAAA,KAAK,YAAL,YAAAA,EAAgB,QAAS,EAAI,KAAK,UAAU,CAAC,EAAI,KAAK,WAC5F,CAACsO,EAAK,MAAO,MACjB,MAAMqe,EAAU,CAAC,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,MAAO,MAAO,MAAO,MAAO,KAAM,MAAO,KAAK,EACjbrH,IAAgB5X,EAAA,KAAK,WAAL,YAAAA,EAAe,gBAAiB,IAAI6W,GAAaxN,IAAK,EAC5E,OAAO4V,EAAQ,QAAQrH,EAAc,wBAAwBhX,CAAG,CAAC,EAAI,IAAMA,EAAI,YAAa,EAAC,QAAQ,OAAO,EAAI,EAAI,MAAQ,KAChI,CACE,OAAO,eAAeE,EAAU,CAAE,EAAEqb,EAAU,CAC5C,OAAO,IAAIsB,GAAK3c,EAASqb,CAAQ,CACrC,CACE,cAAcrb,EAAU,GAAIqb,EAAWmB,GAAM,CAC3C,MAAM4B,EAAoBpe,EAAQ,kBAC9Boe,GAAmB,OAAOpe,EAAQ,kBACtC,MAAMqe,EAAgB,CACpB,GAAG,KAAK,QACR,GAAGre,EAED,QAAS,EAEZ,EACKse,EAAQ,IAAI3B,GAAK0B,CAAa,EAcpC,IAbIre,EAAQ,QAAU,QAAaA,EAAQ,SAAW,UACpDse,EAAM,OAASA,EAAM,OAAO,MAAMte,CAAO,GAErB,CAAC,QAAS,WAAY,UAAU,EACxC,QAAQ/R,GAAK,CACzBqwB,EAAMrwB,CAAC,EAAI,KAAKA,CAAC,CACvB,CAAK,EACDqwB,EAAM,SAAW,CACf,GAAG,KAAK,QACT,EACDA,EAAM,SAAS,MAAQ,CACrB,mBAAoBA,EAAM,mBAAmB,KAAKA,CAAK,CACxD,EACGF,EAAmB,CACrB,MAAMG,EAAa,OAAO,KAAK,KAAK,MAAM,IAAI,EAAE,OAAO,CAACvS,EAAMmI,KAC5DnI,EAAKmI,CAAC,EAAI,CACR,GAAG,KAAK,MAAM,KAAKA,CAAC,CACrB,EACDnI,EAAKmI,CAAC,EAAI,OAAO,KAAKnI,EAAKmI,CAAC,CAAC,EAAE,OAAO,CAACpkB,EAAKnB,KAC1CmB,EAAInB,CAAC,EAAI,CACP,GAAGod,EAAKmI,CAAC,EAAEvlB,CAAC,CACb,EACMmB,GACNic,EAAKmI,CAAC,CAAC,EACHnI,GACN,EAAE,EACLsS,EAAM,MAAQ,IAAIpN,GAAcqN,EAAYF,CAAa,EACzDC,EAAM,SAAS,cAAgBA,EAAM,KAC3C,CACI,OAAAA,EAAM,WAAa,IAAIvM,GAAWuM,EAAM,SAAUD,CAAa,EAC/DC,EAAM,WAAW,GAAG,IAAK,CAAC9f,KAAUS,IAAS,CAC3Cqf,EAAM,KAAK9f,EAAO,GAAGS,CAAI,CAC/B,CAAK,EACDqf,EAAM,KAAKD,EAAehD,CAAQ,EAClCiD,EAAM,WAAW,QAAUD,EAC3BC,EAAM,WAAW,iBAAiB,SAAS,MAAQ,CACjD,mBAAoBA,EAAM,mBAAmB,KAAKA,CAAK,CACxD,EACMA,CACX,CACE,QAAS,CACP,MAAO,CACL,QAAS,KAAK,QACd,MAAO,KAAK,MACZ,SAAU,KAAK,SACf,UAAW,KAAK,UAChB,iBAAkB,KAAK,gBACxB,CACL,CACA,CACA,MAAMxd,EAAW6b,GAAK,eAAgB,EACtC7b,EAAS,eAAiB6b,GAAK,eAER7b,EAAS,eACpBA,EAAS,IACRA,EAAS,KACAA,EAAS,cACPA,EAAS,gBACrBA,EAAS,IACEA,EAAS,eACdA,EAAS,UACjBA,EAAS,EACJA,EAAS,OACIA,EAAS,oBACVA,EAAS,mBACbA,EAAS,eACVA,EAAS,cCvmE/B,KAAM,CACJ,MAAAkG,GACA,QAAAwX,EACF,EAAI,CAAE,EACN,SAASC,GAASte,EAAK,CACrB,OAAAqe,GAAQ,KAAKxX,GAAM,KAAK,UAAW,CAAC,EAAG4H,GAAU,CAC/C,GAAIA,EACF,UAAW1K,KAAQ0K,EACbzO,EAAI+D,CAAI,IAAM,SAAW/D,EAAI+D,CAAI,EAAI0K,EAAO1K,CAAI,EAG5D,CAAG,EACM/D,CACT,CACA,SAASue,GAAO3W,EAAO,CACrB,OAAI,OAAOA,GAAU,SAAiB,GAGlB,CAAC,kBAAmB,uBAAwB,uBAAwB,2BAA4B,kBAAmB,gBAAiB,mBAAoB,aAAc,cAAe,oBAAqB,wBAAyB,oBAAqB,YAAY,EACrQ,KAAKoH,GAAWA,EAAQ,KAAKpH,CAAK,CAAC,CACxD,CAGA,MAAM4W,GAAqB,wCACrBC,GAAkB,SAAUpX,EAAMgQ,EAAK,CAI3C,MAAMvF,EAHQ,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAChF,KAAM,GACP,EAEKjjB,EAAQ,mBAAmBwoB,CAAG,EACpC,IAAI7P,EAAM,GAAGH,CAAI,IAAIxY,CAAK,GAC1B,GAAIijB,EAAI,OAAS,EAAG,CAClB,MAAM4M,EAAS5M,EAAI,OAAS,EAC5B,GAAI,OAAO,MAAM4M,CAAM,EAAG,MAAM,IAAI,MAAM,2BAA2B,EACrElX,GAAO,aAAa,KAAK,MAAMkX,CAAM,CAAC,EAC1C,CACE,GAAI5M,EAAI,OAAQ,CACd,GAAI,CAAC0M,GAAmB,KAAK1M,EAAI,MAAM,EACrC,MAAM,IAAI,UAAU,0BAA0B,EAEhDtK,GAAO,YAAYsK,EAAI,MAAM,EACjC,CACE,GAAIA,EAAI,KAAM,CACZ,GAAI,CAAC0M,GAAmB,KAAK1M,EAAI,IAAI,EACnC,MAAM,IAAI,UAAU,wBAAwB,EAE9CtK,GAAO,UAAUsK,EAAI,IAAI,EAC7B,CACE,GAAIA,EAAI,QAAS,CACf,GAAI,OAAOA,EAAI,QAAQ,aAAgB,WACrC,MAAM,IAAI,UAAU,2BAA2B,EAEjDtK,GAAO,aAAasK,EAAI,QAAQ,YAAa,CAAA,EACjD,CAGE,GAFIA,EAAI,WAAUtK,GAAO,cACrBsK,EAAI,SAAQtK,GAAO,YACnBsK,EAAI,SAEN,OADiB,OAAOA,EAAI,UAAa,SAAWA,EAAI,SAAS,cAAgBA,EAAI,SACrE,CACd,IAAK,GACHtK,GAAO,oBACP,MACF,IAAK,MACHA,GAAO,iBACP,MACF,IAAK,SACHA,GAAO,oBACP,MACF,IAAK,OACHA,GAAO,kBACP,MACF,QACE,MAAM,IAAI,UAAU,4BAA4B,CACxD,CAEE,OAAIsK,EAAI,cAAatK,GAAO,iBACrBA,CACT,EACMmX,GAAS,CACb,OAAOtX,EAAMxY,EAAO+vB,EAASC,EAAQ,CACnC,IAAIC,EAAgB,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CACtF,KAAM,IACN,SAAU,QACX,EACGF,IACFE,EAAc,QAAU,IAAI,KAC5BA,EAAc,QAAQ,QAAQA,EAAc,QAAQ,UAAYF,EAAU,GAAK,GAAI,GAEjFC,IAAQC,EAAc,OAASD,GACnC,SAAS,OAASJ,GAAgBpX,EAAM,mBAAmBxY,CAAK,EAAGiwB,CAAa,CACjF,EACD,KAAKzX,EAAM,CACT,MAAM0X,EAAS,GAAG1X,CAAI,IAChB2X,EAAK,SAAS,OAAO,MAAM,GAAG,EACpC,QAASzpB,EAAI,EAAGA,EAAIypB,EAAG,OAAQzpB,IAAK,CAClC,IAAIka,EAAIuP,EAAGzpB,CAAC,EACZ,KAAOka,EAAE,OAAO,CAAC,IAAM,KAAKA,EAAIA,EAAE,UAAU,EAAGA,EAAE,MAAM,EACvD,GAAIA,EAAE,QAAQsP,CAAM,IAAM,EAAG,OAAOtP,EAAE,UAAUsP,EAAO,OAAQtP,EAAE,MAAM,CAC7E,CACI,OAAO,IACR,EACD,OAAOpI,EAAM,CACX,KAAK,OAAOA,EAAM,GAAI,EAAE,CAC5B,CACA,EACA,IAAI4X,GAAW,CACb,KAAM,SAEN,OAAOC,EAAM,CACX,GAAI,CACF,aAAAC,CACN,EAAQD,EACJ,GAAIC,GAAgB,OAAO,SAAa,IACtC,OAAOR,GAAO,KAAKQ,CAAY,GAAK,MAGvC,EAED,kBAAkBxf,EAAKyf,EAAO,CAC5B,GAAI,CACF,aAAAD,EACA,cAAAE,EACA,aAAAC,EACA,cAAAR,CACN,EAAQM,EACAD,GAAgB,OAAO,SAAa,KACtCR,GAAO,OAAOQ,EAAcxf,EAAK0f,EAAeC,EAAcR,CAAa,CAEjF,CACA,EAEIS,GAAc,CAChB,KAAM,cAEN,OAAOL,EAAM,OACX,GAAI,CACF,kBAAAM,CACN,EAAQN,EACAtK,EACJ,GAAI,OAAO,OAAW,IAAa,CACjC,GAAI,CACF,OAAA6K,CACD,EAAG,OAAO,SACP,CAAC,OAAO,SAAS,UAAUpuB,EAAA,OAAO,SAAS,OAAhB,YAAAA,EAAsB,QAAQ,MAAO,KAClEouB,EAAS,OAAO,SAAS,KAAK,UAAU,OAAO,SAAS,KAAK,QAAQ,GAAG,CAAC,GAG3E,MAAMC,EADQD,EAAO,UAAU,CAAC,EACX,MAAM,GAAG,EAC9B,QAASlqB,EAAI,EAAGA,EAAImqB,EAAO,OAAQnqB,IAAK,CACtC,MAAMoqB,EAAMD,EAAOnqB,CAAC,EAAE,QAAQ,GAAG,EAC7BoqB,EAAM,GACID,EAAOnqB,CAAC,EAAE,UAAU,EAAGoqB,CAAG,IAC1BH,IACV5K,EAAQ8K,EAAOnqB,CAAC,EAAE,UAAUoqB,EAAM,CAAC,EAG/C,CACA,CACI,OAAO/K,CACX,CACA,EAEA,IAAIgL,GAAyB,KAC7B,MAAMC,GAAwB,IAAM,CAClC,GAAID,KAA2B,KAAM,OAAOA,GAC5C,GAAI,CAEF,GADAA,GAAyB,OAAO,OAAW,KAAe,OAAO,eAAiB,KAC9E,CAACA,GACH,MAAO,GAET,MAAME,EAAU,wBAChB,OAAO,aAAa,QAAQA,EAAS,KAAK,EAC1C,OAAO,aAAa,WAAWA,CAAO,CACvC,MAAW,CACVF,GAAyB,EAC7B,CACE,OAAOA,EACT,EACA,IAAIG,GAAe,CACjB,KAAM,eAEN,OAAOb,EAAM,CACX,GAAI,CACF,mBAAAc,CACN,EAAQd,EACJ,GAAIc,GAAsBH,KACxB,OAAO,OAAO,aAAa,QAAQG,CAAkB,GAAK,MAG7D,EAED,kBAAkBrgB,EAAKyf,EAAO,CAC5B,GAAI,CACF,mBAAAY,CACN,EAAQZ,EACAY,GAAsBH,MACxB,OAAO,aAAa,QAAQG,EAAoBrgB,CAAG,CAEzD,CACA,EAEA,IAAIsgB,GAA2B,KAC/B,MAAMC,GAA0B,IAAM,CACpC,GAAID,KAA6B,KAAM,OAAOA,GAC9C,GAAI,CAEF,GADAA,GAA2B,OAAO,OAAW,KAAe,OAAO,iBAAmB,KAClF,CAACA,GACH,MAAO,GAET,MAAMH,EAAU,wBAChB,OAAO,eAAe,QAAQA,EAAS,KAAK,EAC5C,OAAO,eAAe,WAAWA,CAAO,CACzC,MAAW,CACVG,GAA2B,EAC/B,CACE,OAAOA,EACT,EACA,IAAIE,GAAiB,CACnB,KAAM,iBACN,OAAOjB,EAAM,CACX,GAAI,CACF,qBAAAkB,CACN,EAAQlB,EACJ,GAAIkB,GAAwBF,KAC1B,OAAO,OAAO,eAAe,QAAQE,CAAoB,GAAK,MAGjE,EACD,kBAAkBzgB,EAAKyf,EAAO,CAC5B,GAAI,CACF,qBAAAgB,CACN,EAAQhB,EACAgB,GAAwBF,MAC1B,OAAO,eAAe,QAAQE,EAAsBzgB,CAAG,CAE7D,CACA,EAEI0gB,GAAc,CAChB,KAAM,YACN,OAAOxgB,EAAS,CACd,MAAM+U,EAAQ,CAAE,EAChB,GAAI,OAAO,UAAc,IAAa,CACpC,KAAM,CACJ,UAAAqG,EACA,aAAAqF,EACA,SAAA/e,CACR,EAAU,UACJ,GAAI0Z,EAEF,QAAS,EAAI,EAAG,EAAIA,EAAU,OAAQ,IACpCrG,EAAM,KAAKqG,EAAU,CAAC,CAAC,EAGvBqF,GACF1L,EAAM,KAAK0L,CAAY,EAErB/e,GACFqT,EAAM,KAAKrT,CAAQ,CAE3B,CACI,OAAOqT,EAAM,OAAS,EAAIA,EAAQ,MACtC,CACA,EAEI2L,GAAU,CACZ,KAAM,UAEN,OAAOrB,EAAM,CACX,GAAI,CACF,QAAAqB,CACN,EAAQrB,EACAtK,EACJ,MAAM4L,EAAkBD,IAAY,OAAO,SAAa,IAAc,SAAS,gBAAkB,MACjG,OAAIC,GAAmB,OAAOA,EAAgB,cAAiB,aAC7D5L,EAAQ4L,EAAgB,aAAa,MAAM,GAEtC5L,CACX,CACA,EAEI1iB,GAAO,CACT,KAAM,OAEN,OAAOgtB,EAAM,OACX,GAAI,CACF,oBAAAuB,CACN,EAAQvB,EACJ,GAAI,OAAO,OAAW,IAAa,OACnC,MAAM3d,EAAW,OAAO,SAAS,SAAS,MAAM,iBAAiB,EACjE,OAAK,MAAM,QAAQA,CAAQ,GAEpBlQ,EAAAkQ,EADO,OAAOkf,GAAwB,SAAWA,EAAsB,CACzD,IAAd,YAAApvB,EAAiB,QAAQ,IAAK,IAFP,MAGlC,CACA,EAEIqvB,GAAY,CACd,KAAM,YACN,OAAOxB,EAAM,SACX,GAAI,CACF,yBAAAyB,CACN,EAAQzB,EAEJ,MAAM0B,EAAmC,OAAOD,GAA6B,SAAWA,EAA2B,EAAI,EAIjHpf,EAAW,OAAO,OAAW,OAAexC,GAAA1N,EAAA,OAAO,WAAP,YAAAA,EAAiB,WAAjB,YAAA0N,EAA2B,MAAM,2DAGnF,GAAKwC,EAEL,OAAOA,EAASqf,CAAgC,CACpD,CACA,EAGA,IAAIC,GAAa,GACjB,GAAI,CAEF,SAAS,OACTA,GAAa,EAEf,MAAY,CAAA,CACZ,MAAMC,GAAQ,CAAC,cAAe,SAAU,eAAgB,iBAAkB,YAAa,SAAS,EAC3FD,IAAYC,GAAM,OAAO,EAAG,CAAC,EAClC,MAAMrgB,GAAc,KAAO,CACzB,MAAAqgB,GACA,kBAAmB,MACnB,aAAc,UACd,mBAAoB,aACpB,qBAAsB,aAEtB,OAAQ,CAAC,cAAc,EACvB,gBAAiB,CAAC,QAAQ,EAI1B,wBAAyB9M,GAAKA,CAChC,GACA,MAAM+M,EAAQ,CACZ,YAAYlP,EAAU,CACpB,IAAIhS,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EACpF,KAAK,KAAO,mBACZ,KAAK,UAAY,CAAE,EACnB,KAAK,KAAKgS,EAAUhS,CAAO,CAC/B,CACE,MAAO,CACL,IAAIgS,EAAW,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CACjF,cAAe,CAAA,CAChB,EACGhS,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EAChFuC,EAAc,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EACxF,KAAK,SAAWyP,EAChB,KAAK,QAAUyM,GAASze,EAAS,KAAK,SAAW,CAAA,EAAIY,IAAa,EAC9D,OAAO,KAAK,QAAQ,yBAA4B,UAAY,KAAK,QAAQ,wBAAwB,QAAQ,OAAO,EAAI,KACtH,KAAK,QAAQ,wBAA0BuT,GAAKA,EAAE,QAAQ,IAAK,GAAG,GAI5D,KAAK,QAAQ,qBAAoB,KAAK,QAAQ,oBAAsB,KAAK,QAAQ,oBACrF,KAAK,YAAc5R,EACnB,KAAK,YAAY6c,EAAQ,EACzB,KAAK,YAAYM,EAAW,EAC5B,KAAK,YAAYQ,EAAY,EAC7B,KAAK,YAAYI,EAAc,EAC/B,KAAK,YAAYE,EAAW,EAC5B,KAAK,YAAYE,EAAO,EACxB,KAAK,YAAYruB,EAAI,EACrB,KAAK,YAAYwuB,EAAS,CAC9B,CACE,YAAYM,EAAU,CACpB,YAAK,UAAUA,EAAS,IAAI,EAAIA,EACzB,IACX,CACE,QAAS,CACP,IAAIC,EAAiB,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,KAAK,QAAQ,MAClGC,EAAW,CAAE,EASjB,OARAD,EAAe,QAAQE,GAAgB,CACrC,GAAI,KAAK,UAAUA,CAAY,EAAG,CAChC,IAAIC,EAAS,KAAK,UAAUD,CAAY,EAAE,OAAO,KAAK,OAAO,EACzDC,GAAU,OAAOA,GAAW,WAAUA,EAAS,CAACA,CAAM,GACtDA,IAAQF,EAAWA,EAAS,OAAOE,CAAM,EACrD,CACA,CAAK,EACDF,EAAWA,EAAS,OAAOG,GAAwBA,GAAM,MAAQ,CAAC9C,GAAO8C,CAAC,CAAC,EAAE,IAAIA,GAAK,KAAK,QAAQ,wBAAwBA,CAAC,CAAC,EACzH,KAAK,UAAY,KAAK,SAAS,eAAiB,KAAK,SAAS,cAAc,sBAA8BH,EACvGA,EAAS,OAAS,EAAIA,EAAS,CAAC,EAAI,IAC/C,CACE,kBAAkBvhB,EAAK,CACrB,IAAI2hB,EAAS,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,KAAK,QAAQ,OACzFA,IACD,KAAK,QAAQ,iBAAmB,KAAK,QAAQ,gBAAgB,QAAQ3hB,CAAG,EAAI,IAChF2hB,EAAO,QAAQC,GAAa,CACtB,KAAK,UAAUA,CAAS,GAAG,KAAK,UAAUA,CAAS,EAAE,kBAAkB5hB,EAAK,KAAK,OAAO,CAClG,CAAK,EACL,CACA,CACAohB,GAAQ,KAAO,00KClYT9P,GAAY,CAChB,GAAI,CACF,OAAQuQ,GACR,MAAOC,GACP,OAAQC,EACV,EACA,GAAI,CACF,OAAQC,GACR,MAAOC,GACP,OAAQC,EAAA,CAEZ,EAEAnjB,EACG,IAAIojB,EAAgB,EACpB,IAAIjhB,EAAgB,EACpB,KAAK,CACJ,UAAAoQ,GACA,YAAa,KACb,MAAO,GAGP,UAAW,SACX,GAAI,CAAC,SAAU,QAAS,QAAQ,EAEhC,cAAe,CACb,YAAa,EACf,EAEA,UAAW,CACT,MAAO,CAAC,eAAgB,YAAa,SAAS,EAC9C,OAAQ,CAAC,cAAc,CAAA,CAE3B,CAAC,ECvCH,SAAS8Q,IAAM,CAEX,OAAC9X,EAAA,KAAA+X,GAAoB,CAAA,OAAQ/zB,GAC3B,SAAA,CAAAuP,EAAA,IAACoP,GAAU,EAAA,EACV,EAAA,EACH,CAEJ,CCRAqV,GAAA,WAAW,SAAS,eAAe,MAAM,CAAE,EAAE,OAC1CzkB,EAAA,IAAA0kB,EAAA,WAAA,CACC,SAAC1kB,EAAAA,IAAAukB,GAAA,CAAA,CAAI,CACP,CAAA,CACF", "x_google_ignoreList": [0, 2, 3, 4, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 55, 56]}