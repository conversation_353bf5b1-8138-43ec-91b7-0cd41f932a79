import type { 
  Manga, 
  Chapter, 
  User, 
  LoginCredentials, 
  RegisterData,
  MangaFilters,
  PaginatedResponse,
  ApiResponse 
} from '../types';

// Base API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    // Add auth token if available
    const token = localStorage.getItem('auth-token');
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Auth endpoints
  async login(credentials: LoginCredentials): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async register(data: RegisterData): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async logout(): Promise<ApiResponse<null>> {
    return this.request('/auth/logout', {
      method: 'POST',
    });
  }

  // Manga endpoints
  async getMangaList(filters: MangaFilters = {}): Promise<PaginatedResponse<Manga>> {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(key, v.toString()));
        } else {
          params.append(key, value.toString());
        }
      }
    });

    return this.request(`/manga?${params.toString()}`);
  }

  async getMangaById(id: string): Promise<ApiResponse<Manga>> {
    return this.request(`/manga/${id}`);
  }

  async getMangaChapters(mangaId: string): Promise<ApiResponse<Chapter[]>> {
    return this.request(`/manga/${mangaId}/chapters`);
  }

  // Chapter endpoints
  async getChapterById(id: string): Promise<ApiResponse<Chapter>> {
    return this.request(`/chapters/${id}`);
  }

  // User endpoints
  async getUserProfile(): Promise<ApiResponse<User>> {
    return this.request('/user/profile');
  }

  async updateUserProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    return this.request('/user/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Bookmarks
  async getUserBookmarks(): Promise<ApiResponse<Manga[]>> {
    return this.request('/user/bookmarks');
  }

  async addBookmark(mangaId: string): Promise<ApiResponse<null>> {
    return this.request('/user/bookmarks', {
      method: 'POST',
      body: JSON.stringify({ mangaId }),
    });
  }

  async removeBookmark(mangaId: string): Promise<ApiResponse<null>> {
    return this.request(`/user/bookmarks/${mangaId}`, {
      method: 'DELETE',
    });
  }

  // Reading history
  async getReadingHistory(): Promise<ApiResponse<any[]>> {
    return this.request('/user/history');
  }

  async updateReadingProgress(
    mangaId: string, 
    chapterId: string, 
    pageNumber: number
  ): Promise<ApiResponse<null>> {
    return this.request('/user/history', {
      method: 'POST',
      body: JSON.stringify({ mangaId, chapterId, pageNumber }),
    });
  }
}

export const apiClient = new ApiClient(API_BASE_URL);
