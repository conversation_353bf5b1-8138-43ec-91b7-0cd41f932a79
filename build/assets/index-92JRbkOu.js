import{Q as es,j as l,a as ts}from"./query-LIbp8HbF.js";import{r as P,R as Ye,L as _,O as ss,u as rs,c as ns,a as is}from"./router-BHs97Tqp.js";import{a as os}from"./vendor-BtP0CW_r.js";import{S as as,P as ls}from"./ui-QvwcAOJv.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))r(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function t(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(n){if(n.ep)return;n.ep=!0;const i=t(n);fetch(n.href,i)}})();var Le={},Ze;function cs(){if(Ze)return Le;Ze=1;var s=os();return Le.createRoot=s.createRoot,Le.hydrateRoot=s.hydrateRoot,Le}var ds=cs();const us=new es({defaultOptions:{queries:{staleTime:5*60*1e3,gcTime:10*60*1e3,retry:(s,e)=>(e==null?void 0:e.status)>=400&&(e==null?void 0:e.status)<500?!1:s<3,refetchOnWindowFocus:!1},mutations:{retry:!1}}}),hs=(s,e,t,r)=>{var i,o,a,d;const n=[t,{code:e,...r||{}}];if((o=(i=s==null?void 0:s.services)==null?void 0:i.logger)!=null&&o.forward)return s.services.logger.forward(n,"warn","react-i18next::",!0);oe(n[0])&&(n[0]=`react-i18next:: ${n[0]}`),(d=(a=s==null?void 0:s.services)==null?void 0:a.logger)!=null&&d.warn?s.services.logger.warn(...n):console!=null&&console.warn&&console.warn(...n)},et={},Ve=(s,e,t,r)=>{oe(t)&&et[t]||(oe(t)&&(et[t]=new Date),hs(s,e,t,r))},Rt=(s,e)=>()=>{if(s.isInitialized)e();else{const t=()=>{setTimeout(()=>{s.off("initialized",t)},0),e()};s.on("initialized",t)}},Ue=(s,e,t)=>{s.loadNamespaces(e,Rt(s,t))},tt=(s,e,t,r)=>{if(oe(t)&&(t=[t]),s.options.preload&&s.options.preload.indexOf(e)>-1)return Ue(s,t,r);t.forEach(n=>{s.options.ns.indexOf(n)<0&&s.options.ns.push(n)}),s.loadLanguages(e,Rt(s,r))},fs=(s,e,t={})=>!e.languages||!e.languages.length?(Ve(e,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:e.languages}),!0):e.hasLoadedNamespace(s,{lng:t.lng,precheck:(r,n)=>{var i;if(((i=t.bindI18n)==null?void 0:i.indexOf("languageChanging"))>-1&&r.services.backendConnector.backend&&r.isLanguageChangingTo&&!n(r.isLanguageChangingTo,s))return!1}}),oe=s=>typeof s=="string",gs=s=>typeof s=="object"&&s!==null,ms=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,ps={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},xs=s=>ps[s],bs=s=>s.replace(ms,xs);let Be={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:bs};const ys=(s={})=>{Be={...Be,...s}},vs=()=>Be;let Pt;const ws=s=>{Pt=s},ks=()=>Pt,Ss={type:"3rdParty",init(s){ys(s.options.react),ws(s)}},Ns=P.createContext();class js{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(t=>{this.usedNamespaces[t]||(this.usedNamespaces[t]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const Cs=(s,e)=>{const t=P.useRef();return P.useEffect(()=>{t.current=s},[s,e]),t.current},Tt=(s,e,t,r)=>s.getFixedT(e,t,r),Ls=(s,e,t,r)=>P.useCallback(Tt(s,e,t,r),[s,e,t,r]),se=(s,e={})=>{var L,T,U,A;const{i18n:t}=e,{i18n:r,defaultNS:n}=P.useContext(Ns)||{},i=t||r||ks();if(i&&!i.reportNamespaces&&(i.reportNamespaces=new js),!i){Ve(i,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const y=(O,H)=>oe(H)?H:gs(H)&&oe(H.defaultValue)?H.defaultValue:Array.isArray(O)?O[O.length-1]:O,j=[y,{},!1];return j.t=y,j.i18n={},j.ready=!1,j}(L=i.options.react)!=null&&L.wait&&Ve(i,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const o={...vs(),...i.options.react,...e},{useSuspense:a,keyPrefix:d}=o;let c=s||n||((T=i.options)==null?void 0:T.defaultNS);c=oe(c)?[c]:c||["translation"],(A=(U=i.reportNamespaces).addUsedNamespaces)==null||A.call(U,c);const u=(i.isInitialized||i.initializedStoreOnce)&&c.every(y=>fs(y,i,o)),h=Ls(i,e.lng||null,o.nsMode==="fallback"?c:c[0],d),g=()=>h,m=()=>Tt(i,e.lng||null,o.nsMode==="fallback"?c:c[0],d),[f,p]=P.useState(g);let k=c.join();e.lng&&(k=`${e.lng}${k}`);const R=Cs(k),S=P.useRef(!0);P.useEffect(()=>{const{bindI18n:y,bindI18nStore:j}=o;S.current=!0,!u&&!a&&(e.lng?tt(i,e.lng,c,()=>{S.current&&p(m)}):Ue(i,c,()=>{S.current&&p(m)})),u&&R&&R!==k&&S.current&&p(m);const O=()=>{S.current&&p(m)};return y&&(i==null||i.on(y,O)),j&&(i==null||i.store.on(j,O)),()=>{S.current=!1,i&&(y==null||y.split(" ").forEach(H=>i.off(H,O))),j&&i&&j.split(" ").forEach(H=>i.store.off(H,O))}},[i,k]),P.useEffect(()=>{S.current&&u&&p(g)},[i,d,u]);const C=[f,i,u];if(C.t=f,C.i18n=i,C.ready=u,u||!u&&!a)return C;throw new Promise(y=>{e.lng?tt(i,e.lng,c,()=>y()):Ue(i,c,()=>y())})};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Os=s=>s.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Rs=s=>s.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),st=s=>{const e=Rs(s);return e.charAt(0).toUpperCase()+e.slice(1)},$t=(...s)=>s.filter((e,t,r)=>!!e&&e.trim()!==""&&r.indexOf(e)===t).join(" ").trim(),Ps=s=>{for(const e in s)if(e.startsWith("aria-")||e==="role"||e==="title")return!0};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ts={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $s=P.forwardRef(({color:s="currentColor",size:e=24,strokeWidth:t=2,absoluteStrokeWidth:r,className:n="",children:i,iconNode:o,...a},d)=>P.createElement("svg",{ref:d,...Ts,width:e,height:e,stroke:s,strokeWidth:r?Number(t)*24/Number(e):t,className:$t("lucide",n),...!i&&!Ps(a)&&{"aria-hidden":"true"},...a},[...o.map(([c,u])=>P.createElement(c,u)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const J=(s,e)=>{const t=P.forwardRef(({className:r,...n},i)=>P.createElement($s,{ref:i,iconNode:e,className:$t(`lucide-${Os(st(s))}`,`lucide-${s}`,r),...n}));return t.displayName=st(s),t};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Es=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],we=J("book-open",Es);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Is=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],As=J("clock",Is);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fs=[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]],Ms=J("github",Fs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zs=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],Ds=J("mail",zs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vs=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Us=J("menu",Vs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bs=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Hs=J("search",Bs);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ks=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],rt=J("star",Ks);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _s=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],Gs=J("trending-up",_s);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ws=[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]],qs=J("twitter",Ws);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Js=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],Qs=J("user",Js);function Et(s){var e,t,r="";if(typeof s=="string"||typeof s=="number")r+=s;else if(typeof s=="object")if(Array.isArray(s)){var n=s.length;for(e=0;e<n;e++)s[e]&&(t=Et(s[e]))&&(r&&(r+=" "),r+=t)}else for(t in s)s[t]&&(r&&(r+=" "),r+=t);return r}function It(){for(var s,e,t=0,r="",n=arguments.length;t<n;t++)(s=arguments[t])&&(e=Et(s))&&(r&&(r+=" "),r+=e);return r}const nt=s=>typeof s=="boolean"?`${s}`:s===0?"0":s,it=It,At=(s,e)=>t=>{var r;if((e==null?void 0:e.variants)==null)return it(s,t==null?void 0:t.class,t==null?void 0:t.className);const{variants:n,defaultVariants:i}=e,o=Object.keys(n).map(c=>{const u=t==null?void 0:t[c],h=i==null?void 0:i[c];if(u===null)return null;const g=nt(u)||nt(h);return n[c][g]}),a=t&&Object.entries(t).reduce((c,u)=>{let[h,g]=u;return g===void 0||(c[h]=g),c},{}),d=e==null||(r=e.compoundVariants)===null||r===void 0?void 0:r.reduce((c,u)=>{let{class:h,className:g,...m}=u;return Object.entries(m).every(f=>{let[p,k]=f;return Array.isArray(k)?k.includes({...i,...a}[p]):{...i,...a}[p]===k})?[...c,h,g]:c},[]);return it(s,o,d,t==null?void 0:t.class,t==null?void 0:t.className)},Je="-",Xs=s=>{const e=Zs(s),{conflictingClassGroups:t,conflictingClassGroupModifiers:r}=s;return{getClassGroupId:o=>{const a=o.split(Je);return a[0]===""&&a.length!==1&&a.shift(),Ft(a,e)||Ys(o)},getConflictingClassGroupIds:(o,a)=>{const d=t[o]||[];return a&&r[o]?[...d,...r[o]]:d}}},Ft=(s,e)=>{var o;if(s.length===0)return e.classGroupId;const t=s[0],r=e.nextPart.get(t),n=r?Ft(s.slice(1),r):void 0;if(n)return n;if(e.validators.length===0)return;const i=s.join(Je);return(o=e.validators.find(({validator:a})=>a(i)))==null?void 0:o.classGroupId},ot=/^\[(.+)\]$/,Ys=s=>{if(ot.test(s)){const e=ot.exec(s)[1],t=e==null?void 0:e.substring(0,e.indexOf(":"));if(t)return"arbitrary.."+t}},Zs=s=>{const{theme:e,classGroups:t}=s,r={nextPart:new Map,validators:[]};for(const n in t)He(t[n],r,n,e);return r},He=(s,e,t,r)=>{s.forEach(n=>{if(typeof n=="string"){const i=n===""?e:at(e,n);i.classGroupId=t;return}if(typeof n=="function"){if(er(n)){He(n(r),e,t,r);return}e.validators.push({validator:n,classGroupId:t});return}Object.entries(n).forEach(([i,o])=>{He(o,at(e,i),t,r)})})},at=(s,e)=>{let t=s;return e.split(Je).forEach(r=>{t.nextPart.has(r)||t.nextPart.set(r,{nextPart:new Map,validators:[]}),t=t.nextPart.get(r)}),t},er=s=>s.isThemeGetter,tr=s=>{if(s<1)return{get:()=>{},set:()=>{}};let e=0,t=new Map,r=new Map;const n=(i,o)=>{t.set(i,o),e++,e>s&&(e=0,r=t,t=new Map)};return{get(i){let o=t.get(i);if(o!==void 0)return o;if((o=r.get(i))!==void 0)return n(i,o),o},set(i,o){t.has(i)?t.set(i,o):n(i,o)}}},Ke="!",_e=":",sr=_e.length,rr=s=>{const{prefix:e,experimentalParseClassName:t}=s;let r=n=>{const i=[];let o=0,a=0,d=0,c;for(let f=0;f<n.length;f++){let p=n[f];if(o===0&&a===0){if(p===_e){i.push(n.slice(d,f)),d=f+sr;continue}if(p==="/"){c=f;continue}}p==="["?o++:p==="]"?o--:p==="("?a++:p===")"&&a--}const u=i.length===0?n:n.substring(d),h=nr(u),g=h!==u,m=c&&c>d?c-d:void 0;return{modifiers:i,hasImportantModifier:g,baseClassName:h,maybePostfixModifierPosition:m}};if(e){const n=e+_e,i=r;r=o=>o.startsWith(n)?i(o.substring(n.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:o,maybePostfixModifierPosition:void 0}}if(t){const n=r;r=i=>t({className:i,parseClassName:n})}return r},nr=s=>s.endsWith(Ke)?s.substring(0,s.length-1):s.startsWith(Ke)?s.substring(1):s,ir=s=>{const e=Object.fromEntries(s.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const n=[];let i=[];return r.forEach(o=>{o[0]==="["||e[o]?(n.push(...i.sort(),o),i=[]):i.push(o)}),n.push(...i.sort()),n}},or=s=>({cache:tr(s.cacheSize),parseClassName:rr(s),sortModifiers:ir(s),...Xs(s)}),ar=/\s+/,lr=(s,e)=>{const{parseClassName:t,getClassGroupId:r,getConflictingClassGroupIds:n,sortModifiers:i}=e,o=[],a=s.trim().split(ar);let d="";for(let c=a.length-1;c>=0;c-=1){const u=a[c],{isExternal:h,modifiers:g,hasImportantModifier:m,baseClassName:f,maybePostfixModifierPosition:p}=t(u);if(h){d=u+(d.length>0?" "+d:d);continue}let k=!!p,R=r(k?f.substring(0,p):f);if(!R){if(!k){d=u+(d.length>0?" "+d:d);continue}if(R=r(f),!R){d=u+(d.length>0?" "+d:d);continue}k=!1}const S=i(g).join(":"),C=m?S+Ke:S,L=C+R;if(o.includes(L))continue;o.push(L);const T=n(R,k);for(let U=0;U<T.length;++U){const A=T[U];o.push(C+A)}d=u+(d.length>0?" "+d:d)}return d};function cr(){let s=0,e,t,r="";for(;s<arguments.length;)(e=arguments[s++])&&(t=Mt(e))&&(r&&(r+=" "),r+=t);return r}const Mt=s=>{if(typeof s=="string")return s;let e,t="";for(let r=0;r<s.length;r++)s[r]&&(e=Mt(s[r]))&&(t&&(t+=" "),t+=e);return t};function dr(s,...e){let t,r,n,i=o;function o(d){const c=e.reduce((u,h)=>h(u),s());return t=or(c),r=t.cache.get,n=t.cache.set,i=a,a(d)}function a(d){const c=r(d);if(c)return c;const u=lr(d,t);return n(d,u),u}return function(){return i(cr.apply(null,arguments))}}const I=s=>{const e=t=>t[s]||[];return e.isThemeGetter=!0,e},zt=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Dt=/^\((?:(\w[\w-]*):)?(.+)\)$/i,ur=/^\d+\/\d+$/,hr=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,fr=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,gr=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,mr=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,pr=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,le=s=>ur.test(s),N=s=>!!s&&!Number.isNaN(Number(s)),Z=s=>!!s&&Number.isInteger(Number(s)),Me=s=>s.endsWith("%")&&N(s.slice(0,-1)),X=s=>hr.test(s),xr=()=>!0,br=s=>fr.test(s)&&!gr.test(s),Vt=()=>!1,yr=s=>mr.test(s),vr=s=>pr.test(s),wr=s=>!x(s)&&!b(s),kr=s=>me(s,Ht,Vt),x=s=>zt.test(s),ie=s=>me(s,Kt,br),ze=s=>me(s,Lr,N),lt=s=>me(s,Ut,Vt),Sr=s=>me(s,Bt,vr),Oe=s=>me(s,_t,yr),b=s=>Dt.test(s),be=s=>pe(s,Kt),Nr=s=>pe(s,Or),ct=s=>pe(s,Ut),jr=s=>pe(s,Ht),Cr=s=>pe(s,Bt),Re=s=>pe(s,_t,!0),me=(s,e,t)=>{const r=zt.exec(s);return r?r[1]?e(r[1]):t(r[2]):!1},pe=(s,e,t=!1)=>{const r=Dt.exec(s);return r?r[1]?e(r[1]):t:!1},Ut=s=>s==="position"||s==="percentage",Bt=s=>s==="image"||s==="url",Ht=s=>s==="length"||s==="size"||s==="bg-size",Kt=s=>s==="length",Lr=s=>s==="number",Or=s=>s==="family-name",_t=s=>s==="shadow",Rr=()=>{const s=I("color"),e=I("font"),t=I("text"),r=I("font-weight"),n=I("tracking"),i=I("leading"),o=I("breakpoint"),a=I("container"),d=I("spacing"),c=I("radius"),u=I("shadow"),h=I("inset-shadow"),g=I("text-shadow"),m=I("drop-shadow"),f=I("blur"),p=I("perspective"),k=I("aspect"),R=I("ease"),S=I("animate"),C=()=>["auto","avoid","all","avoid-page","page","left","right","column"],L=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],T=()=>[...L(),b,x],U=()=>["auto","hidden","clip","visible","scroll"],A=()=>["auto","contain","none"],y=()=>[b,x,d],j=()=>[le,"full","auto",...y()],O=()=>[Z,"none","subgrid",b,x],H=()=>["auto",{span:["full",Z,b,x]},Z,b,x],ae=()=>[Z,"auto",b,x],K=()=>["auto","min","max","fr",b,x],B=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Q=()=>["start","end","center","stretch","center-safe","end-safe"],E=()=>["auto",...y()],M=()=>[le,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...y()],v=()=>[s,b,x],re=()=>[...L(),ct,lt,{position:[b,x]}],xe=()=>["no-repeat",{repeat:["","x","y","space","round"]}],z=()=>["auto","cover","contain",jr,kr,{size:[b,x]}],G=()=>[Me,be,ie],$=()=>["","none","full",c,b,x],D=()=>["",N,be,ie],ne=()=>["solid","dashed","dotted","double"],Qe=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],F=()=>[N,Me,ct,lt],Xe=()=>["","none",f,b,x],Ne=()=>["none",N,b,x],je=()=>["none",N,b,x],Fe=()=>[N,b,x],Ce=()=>[le,"full",...y()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[X],breakpoint:[X],color:[xr],container:[X],"drop-shadow":[X],ease:["in","out","in-out"],font:[wr],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[X],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[X],shadow:[X],spacing:["px",N],text:[X],"text-shadow":[X],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",le,x,b,k]}],container:["container"],columns:[{columns:[N,x,b,a]}],"break-after":[{"break-after":C()}],"break-before":[{"break-before":C()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:T()}],overflow:[{overflow:U()}],"overflow-x":[{"overflow-x":U()}],"overflow-y":[{"overflow-y":U()}],overscroll:[{overscroll:A()}],"overscroll-x":[{"overscroll-x":A()}],"overscroll-y":[{"overscroll-y":A()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:j()}],"inset-x":[{"inset-x":j()}],"inset-y":[{"inset-y":j()}],start:[{start:j()}],end:[{end:j()}],top:[{top:j()}],right:[{right:j()}],bottom:[{bottom:j()}],left:[{left:j()}],visibility:["visible","invisible","collapse"],z:[{z:[Z,"auto",b,x]}],basis:[{basis:[le,"full","auto",a,...y()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[N,le,"auto","initial","none",x]}],grow:[{grow:["",N,b,x]}],shrink:[{shrink:["",N,b,x]}],order:[{order:[Z,"first","last","none",b,x]}],"grid-cols":[{"grid-cols":O()}],"col-start-end":[{col:H()}],"col-start":[{"col-start":ae()}],"col-end":[{"col-end":ae()}],"grid-rows":[{"grid-rows":O()}],"row-start-end":[{row:H()}],"row-start":[{"row-start":ae()}],"row-end":[{"row-end":ae()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":K()}],"auto-rows":[{"auto-rows":K()}],gap:[{gap:y()}],"gap-x":[{"gap-x":y()}],"gap-y":[{"gap-y":y()}],"justify-content":[{justify:[...B(),"normal"]}],"justify-items":[{"justify-items":[...Q(),"normal"]}],"justify-self":[{"justify-self":["auto",...Q()]}],"align-content":[{content:["normal",...B()]}],"align-items":[{items:[...Q(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Q(),{baseline:["","last"]}]}],"place-content":[{"place-content":B()}],"place-items":[{"place-items":[...Q(),"baseline"]}],"place-self":[{"place-self":["auto",...Q()]}],p:[{p:y()}],px:[{px:y()}],py:[{py:y()}],ps:[{ps:y()}],pe:[{pe:y()}],pt:[{pt:y()}],pr:[{pr:y()}],pb:[{pb:y()}],pl:[{pl:y()}],m:[{m:E()}],mx:[{mx:E()}],my:[{my:E()}],ms:[{ms:E()}],me:[{me:E()}],mt:[{mt:E()}],mr:[{mr:E()}],mb:[{mb:E()}],ml:[{ml:E()}],"space-x":[{"space-x":y()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":y()}],"space-y-reverse":["space-y-reverse"],size:[{size:M()}],w:[{w:[a,"screen",...M()]}],"min-w":[{"min-w":[a,"screen","none",...M()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...M()]}],h:[{h:["screen","lh",...M()]}],"min-h":[{"min-h":["screen","lh","none",...M()]}],"max-h":[{"max-h":["screen","lh",...M()]}],"font-size":[{text:["base",t,be,ie]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,b,ze]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Me,x]}],"font-family":[{font:[Nr,x,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,b,x]}],"line-clamp":[{"line-clamp":[N,"none",b,ze]}],leading:[{leading:[i,...y()]}],"list-image":[{"list-image":["none",b,x]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",b,x]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:v()}],"text-color":[{text:v()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ne(),"wavy"]}],"text-decoration-thickness":[{decoration:[N,"from-font","auto",b,ie]}],"text-decoration-color":[{decoration:v()}],"underline-offset":[{"underline-offset":[N,"auto",b,x]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:y()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",b,x]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",b,x]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:re()}],"bg-repeat":[{bg:xe()}],"bg-size":[{bg:z()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Z,b,x],radial:["",b,x],conic:[Z,b,x]},Cr,Sr]}],"bg-color":[{bg:v()}],"gradient-from-pos":[{from:G()}],"gradient-via-pos":[{via:G()}],"gradient-to-pos":[{to:G()}],"gradient-from":[{from:v()}],"gradient-via":[{via:v()}],"gradient-to":[{to:v()}],rounded:[{rounded:$()}],"rounded-s":[{"rounded-s":$()}],"rounded-e":[{"rounded-e":$()}],"rounded-t":[{"rounded-t":$()}],"rounded-r":[{"rounded-r":$()}],"rounded-b":[{"rounded-b":$()}],"rounded-l":[{"rounded-l":$()}],"rounded-ss":[{"rounded-ss":$()}],"rounded-se":[{"rounded-se":$()}],"rounded-ee":[{"rounded-ee":$()}],"rounded-es":[{"rounded-es":$()}],"rounded-tl":[{"rounded-tl":$()}],"rounded-tr":[{"rounded-tr":$()}],"rounded-br":[{"rounded-br":$()}],"rounded-bl":[{"rounded-bl":$()}],"border-w":[{border:D()}],"border-w-x":[{"border-x":D()}],"border-w-y":[{"border-y":D()}],"border-w-s":[{"border-s":D()}],"border-w-e":[{"border-e":D()}],"border-w-t":[{"border-t":D()}],"border-w-r":[{"border-r":D()}],"border-w-b":[{"border-b":D()}],"border-w-l":[{"border-l":D()}],"divide-x":[{"divide-x":D()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":D()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ne(),"hidden","none"]}],"divide-style":[{divide:[...ne(),"hidden","none"]}],"border-color":[{border:v()}],"border-color-x":[{"border-x":v()}],"border-color-y":[{"border-y":v()}],"border-color-s":[{"border-s":v()}],"border-color-e":[{"border-e":v()}],"border-color-t":[{"border-t":v()}],"border-color-r":[{"border-r":v()}],"border-color-b":[{"border-b":v()}],"border-color-l":[{"border-l":v()}],"divide-color":[{divide:v()}],"outline-style":[{outline:[...ne(),"none","hidden"]}],"outline-offset":[{"outline-offset":[N,b,x]}],"outline-w":[{outline:["",N,be,ie]}],"outline-color":[{outline:v()}],shadow:[{shadow:["","none",u,Re,Oe]}],"shadow-color":[{shadow:v()}],"inset-shadow":[{"inset-shadow":["none",h,Re,Oe]}],"inset-shadow-color":[{"inset-shadow":v()}],"ring-w":[{ring:D()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:v()}],"ring-offset-w":[{"ring-offset":[N,ie]}],"ring-offset-color":[{"ring-offset":v()}],"inset-ring-w":[{"inset-ring":D()}],"inset-ring-color":[{"inset-ring":v()}],"text-shadow":[{"text-shadow":["none",g,Re,Oe]}],"text-shadow-color":[{"text-shadow":v()}],opacity:[{opacity:[N,b,x]}],"mix-blend":[{"mix-blend":[...Qe(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Qe()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[N]}],"mask-image-linear-from-pos":[{"mask-linear-from":F()}],"mask-image-linear-to-pos":[{"mask-linear-to":F()}],"mask-image-linear-from-color":[{"mask-linear-from":v()}],"mask-image-linear-to-color":[{"mask-linear-to":v()}],"mask-image-t-from-pos":[{"mask-t-from":F()}],"mask-image-t-to-pos":[{"mask-t-to":F()}],"mask-image-t-from-color":[{"mask-t-from":v()}],"mask-image-t-to-color":[{"mask-t-to":v()}],"mask-image-r-from-pos":[{"mask-r-from":F()}],"mask-image-r-to-pos":[{"mask-r-to":F()}],"mask-image-r-from-color":[{"mask-r-from":v()}],"mask-image-r-to-color":[{"mask-r-to":v()}],"mask-image-b-from-pos":[{"mask-b-from":F()}],"mask-image-b-to-pos":[{"mask-b-to":F()}],"mask-image-b-from-color":[{"mask-b-from":v()}],"mask-image-b-to-color":[{"mask-b-to":v()}],"mask-image-l-from-pos":[{"mask-l-from":F()}],"mask-image-l-to-pos":[{"mask-l-to":F()}],"mask-image-l-from-color":[{"mask-l-from":v()}],"mask-image-l-to-color":[{"mask-l-to":v()}],"mask-image-x-from-pos":[{"mask-x-from":F()}],"mask-image-x-to-pos":[{"mask-x-to":F()}],"mask-image-x-from-color":[{"mask-x-from":v()}],"mask-image-x-to-color":[{"mask-x-to":v()}],"mask-image-y-from-pos":[{"mask-y-from":F()}],"mask-image-y-to-pos":[{"mask-y-to":F()}],"mask-image-y-from-color":[{"mask-y-from":v()}],"mask-image-y-to-color":[{"mask-y-to":v()}],"mask-image-radial":[{"mask-radial":[b,x]}],"mask-image-radial-from-pos":[{"mask-radial-from":F()}],"mask-image-radial-to-pos":[{"mask-radial-to":F()}],"mask-image-radial-from-color":[{"mask-radial-from":v()}],"mask-image-radial-to-color":[{"mask-radial-to":v()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":L()}],"mask-image-conic-pos":[{"mask-conic":[N]}],"mask-image-conic-from-pos":[{"mask-conic-from":F()}],"mask-image-conic-to-pos":[{"mask-conic-to":F()}],"mask-image-conic-from-color":[{"mask-conic-from":v()}],"mask-image-conic-to-color":[{"mask-conic-to":v()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:re()}],"mask-repeat":[{mask:xe()}],"mask-size":[{mask:z()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",b,x]}],filter:[{filter:["","none",b,x]}],blur:[{blur:Xe()}],brightness:[{brightness:[N,b,x]}],contrast:[{contrast:[N,b,x]}],"drop-shadow":[{"drop-shadow":["","none",m,Re,Oe]}],"drop-shadow-color":[{"drop-shadow":v()}],grayscale:[{grayscale:["",N,b,x]}],"hue-rotate":[{"hue-rotate":[N,b,x]}],invert:[{invert:["",N,b,x]}],saturate:[{saturate:[N,b,x]}],sepia:[{sepia:["",N,b,x]}],"backdrop-filter":[{"backdrop-filter":["","none",b,x]}],"backdrop-blur":[{"backdrop-blur":Xe()}],"backdrop-brightness":[{"backdrop-brightness":[N,b,x]}],"backdrop-contrast":[{"backdrop-contrast":[N,b,x]}],"backdrop-grayscale":[{"backdrop-grayscale":["",N,b,x]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[N,b,x]}],"backdrop-invert":[{"backdrop-invert":["",N,b,x]}],"backdrop-opacity":[{"backdrop-opacity":[N,b,x]}],"backdrop-saturate":[{"backdrop-saturate":[N,b,x]}],"backdrop-sepia":[{"backdrop-sepia":["",N,b,x]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":y()}],"border-spacing-x":[{"border-spacing-x":y()}],"border-spacing-y":[{"border-spacing-y":y()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",b,x]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[N,"initial",b,x]}],ease:[{ease:["linear","initial",R,b,x]}],delay:[{delay:[N,b,x]}],animate:[{animate:["none",S,b,x]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,b,x]}],"perspective-origin":[{"perspective-origin":T()}],rotate:[{rotate:Ne()}],"rotate-x":[{"rotate-x":Ne()}],"rotate-y":[{"rotate-y":Ne()}],"rotate-z":[{"rotate-z":Ne()}],scale:[{scale:je()}],"scale-x":[{"scale-x":je()}],"scale-y":[{"scale-y":je()}],"scale-z":[{"scale-z":je()}],"scale-3d":["scale-3d"],skew:[{skew:Fe()}],"skew-x":[{"skew-x":Fe()}],"skew-y":[{"skew-y":Fe()}],transform:[{transform:[b,x,"","none","gpu","cpu"]}],"transform-origin":[{origin:T()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ce()}],"translate-x":[{"translate-x":Ce()}],"translate-y":[{"translate-y":Ce()}],"translate-z":[{"translate-z":Ce()}],"translate-none":["translate-none"],accent:[{accent:v()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:v()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",b,x]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":y()}],"scroll-mx":[{"scroll-mx":y()}],"scroll-my":[{"scroll-my":y()}],"scroll-ms":[{"scroll-ms":y()}],"scroll-me":[{"scroll-me":y()}],"scroll-mt":[{"scroll-mt":y()}],"scroll-mr":[{"scroll-mr":y()}],"scroll-mb":[{"scroll-mb":y()}],"scroll-ml":[{"scroll-ml":y()}],"scroll-p":[{"scroll-p":y()}],"scroll-px":[{"scroll-px":y()}],"scroll-py":[{"scroll-py":y()}],"scroll-ps":[{"scroll-ps":y()}],"scroll-pe":[{"scroll-pe":y()}],"scroll-pt":[{"scroll-pt":y()}],"scroll-pr":[{"scroll-pr":y()}],"scroll-pb":[{"scroll-pb":y()}],"scroll-pl":[{"scroll-pl":y()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",b,x]}],fill:[{fill:["none",...v()]}],"stroke-w":[{stroke:[N,be,ie,ze]}],stroke:[{stroke:["none",...v()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Pr=dr(Rr);function Y(...s){return Pr(It(s))}const Tr=At("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),W=P.forwardRef(({className:s,variant:e,size:t,asChild:r=!1,...n},i)=>{const o=r?as:"button";return l.jsx(o,{className:Y(Tr({variant:e,size:t,className:s})),ref:i,...n})});W.displayName="Button";const Te=P.forwardRef(({className:s,type:e,...t},r)=>l.jsx("input",{type:e,className:Y("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...t}));Te.displayName="Input";const dt=s=>{let e;const t=new Set,r=(c,u)=>{const h=typeof c=="function"?c(e):c;if(!Object.is(h,e)){const g=e;e=u??(typeof h!="object"||h===null)?h:Object.assign({},e,h),t.forEach(m=>m(e,g))}},n=()=>e,a={setState:r,getState:n,getInitialState:()=>d,subscribe:c=>(t.add(c),()=>t.delete(c))},d=e=s(r,n,a);return a},$r=s=>s?dt(s):dt,Er=s=>s;function Ir(s,e=Er){const t=Ye.useSyncExternalStore(s.subscribe,()=>e(s.getState()),()=>e(s.getInitialState()));return Ye.useDebugValue(t),t}const Ar=s=>{const e=$r(s),t=r=>Ir(e,r);return Object.assign(t,e),t},Fr=s=>Ar;function Mr(s,e){let t;try{t=s()}catch{return}return{getItem:n=>{var i;const o=d=>d===null?null:JSON.parse(d,void 0),a=(i=t.getItem(n))!=null?i:null;return a instanceof Promise?a.then(o):o(a)},setItem:(n,i)=>t.setItem(n,JSON.stringify(i,void 0)),removeItem:n=>t.removeItem(n)}}const Ge=s=>e=>{try{const t=s(e);return t instanceof Promise?t:{then(r){return Ge(r)(t)},catch(r){return this}}}catch(t){return{then(r){return this},catch(r){return Ge(r)(t)}}}},zr=(s,e)=>(t,r,n)=>{let i={storage:Mr(()=>localStorage),partialize:p=>p,version:0,merge:(p,k)=>({...k,...p}),...e},o=!1;const a=new Set,d=new Set;let c=i.storage;if(!c)return s((...p)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),t(...p)},r,n);const u=()=>{const p=i.partialize({...r()});return c.setItem(i.name,{state:p,version:i.version})},h=n.setState;n.setState=(p,k)=>{h(p,k),u()};const g=s((...p)=>{t(...p),u()},r,n);n.getInitialState=()=>g;let m;const f=()=>{var p,k;if(!c)return;o=!1,a.forEach(S=>{var C;return S((C=r())!=null?C:g)});const R=((k=i.onRehydrateStorage)==null?void 0:k.call(i,(p=r())!=null?p:g))||void 0;return Ge(c.getItem.bind(c))(i.name).then(S=>{if(S)if(typeof S.version=="number"&&S.version!==i.version){if(i.migrate){const C=i.migrate(S.state,S.version);return C instanceof Promise?C.then(L=>[!0,L]):[!0,C]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,S.state];return[!1,void 0]}).then(S=>{var C;const[L,T]=S;if(m=i.merge(T,(C=r())!=null?C:g),t(m,!0),L)return u()}).then(()=>{R==null||R(m,void 0),m=r(),o=!0,d.forEach(S=>S(m))}).catch(S=>{R==null||R(void 0,S)})};return n.persist={setOptions:p=>{i={...i,...p},p.storage&&(c=p.storage)},clearStorage:()=>{c==null||c.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>f(),hasHydrated:()=>o,onHydrate:p=>(a.add(p),()=>{a.delete(p)}),onFinishHydration:p=>(d.add(p),()=>{d.delete(p)})},i.skipHydration||f(),m||g},Dr=zr,Gt=Fr()(Dr((s,e)=>({user:null,isAuthenticated:!1,isLoading:!1,login:async t=>{s({isLoading:!0});try{console.log("Login with:",t);const r={id:"1",username:"testuser",email:t.email,role:"reader",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};s({user:r,isAuthenticated:!0,isLoading:!1})}catch(r){throw console.error("Login failed:",r),s({isLoading:!1}),r}},logout:()=>{s({user:null,isAuthenticated:!1})},updateProfile:async t=>{const{user:r}=e();if(r){s({isLoading:!0});try{const n={...r,...t};s({user:n,isLoading:!1})}catch(n){throw console.error("Profile update failed:",n),s({isLoading:!1}),n}}},setUser:t=>{s({user:t,isAuthenticated:!!t})}}),{name:"auth-storage",partialize:s=>({user:s.user,isAuthenticated:s.isAuthenticated})})),Vr=()=>{const{t:s}=se("common"),{user:e,isAuthenticated:t,logout:r}=Gt();return l.jsx("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:l.jsxs("div",{className:"container flex h-16 items-center",children:[l.jsxs(_,{to:"/",className:"flex items-center space-x-2",children:[l.jsx(we,{className:"h-6 w-6"}),l.jsx("span",{className:"font-bold text-xl",children:"BlogTruyen"})]}),l.jsxs("nav",{className:"hidden md:flex items-center space-x-6 ml-8",children:[l.jsx(_,{to:"/",className:"text-sm font-medium transition-colors hover:text-primary",children:s("navigation.home")}),l.jsx(_,{to:"/browse",className:"text-sm font-medium transition-colors hover:text-primary",children:s("navigation.browse")}),t&&l.jsxs(l.Fragment,{children:[l.jsx(_,{to:"/bookmarks",className:"text-sm font-medium transition-colors hover:text-primary",children:s("navigation.bookmarks")}),l.jsx(_,{to:"/history",className:"text-sm font-medium transition-colors hover:text-primary",children:s("navigation.history")})]})]}),l.jsx("div",{className:"flex-1 flex justify-center px-4",children:l.jsxs("div",{className:"relative w-full max-w-sm",children:[l.jsx(Hs,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),l.jsx(Te,{placeholder:s("actions.search"),className:"pl-8"})]})}),l.jsxs("div",{className:"flex items-center space-x-2",children:[t?l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsxs(W,{variant:"ghost",size:"sm",children:[l.jsx(Qs,{className:"h-4 w-4 mr-2"}),e==null?void 0:e.username]}),l.jsx(W,{variant:"ghost",size:"sm",onClick:r,children:s("navigation.logout")})]}):l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(_,{to:"/login",children:l.jsx(W,{variant:"ghost",size:"sm",children:s("navigation.login")})}),l.jsx(_,{to:"/register",children:l.jsx(W,{size:"sm",children:s("navigation.register")})})]}),l.jsx(W,{variant:"ghost",size:"icon",className:"md:hidden",children:l.jsx(Us,{className:"h-4 w-4"})})]})]})})},Ur=()=>{const{t:s}=se("common");return l.jsx("footer",{className:"border-t bg-background",children:l.jsxs("div",{className:"container py-8",children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(we,{className:"h-6 w-6"}),l.jsx("span",{className:"font-bold text-xl",children:"BlogTruyen"})]}),l.jsx("p",{className:"text-sm text-muted-foreground",children:"Nền tảng đọc truyện tranh online hiện đại với trải nghiệm đọc tuyệt vời."})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsx("h3",{className:"font-semibold",children:"Liên kết nhanh"}),l.jsxs("ul",{className:"space-y-2 text-sm",children:[l.jsx("li",{children:l.jsx("a",{href:"/",className:"text-muted-foreground hover:text-primary transition-colors",children:s("navigation.home")})}),l.jsx("li",{children:l.jsx("a",{href:"/browse",className:"text-muted-foreground hover:text-primary transition-colors",children:s("navigation.browse")})}),l.jsx("li",{children:l.jsx("a",{href:"/about",className:"text-muted-foreground hover:text-primary transition-colors",children:"Về chúng tôi"})}),l.jsx("li",{children:l.jsx("a",{href:"/contact",className:"text-muted-foreground hover:text-primary transition-colors",children:"Liên hệ"})})]})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsx("h3",{className:"font-semibold",children:"Hỗ trợ"}),l.jsxs("ul",{className:"space-y-2 text-sm",children:[l.jsx("li",{children:l.jsx("a",{href:"/help",className:"text-muted-foreground hover:text-primary transition-colors",children:"Trợ giúp"})}),l.jsx("li",{children:l.jsx("a",{href:"/faq",className:"text-muted-foreground hover:text-primary transition-colors",children:"FAQ"})}),l.jsx("li",{children:l.jsx("a",{href:"/privacy",className:"text-muted-foreground hover:text-primary transition-colors",children:"Chính sách bảo mật"})}),l.jsx("li",{children:l.jsx("a",{href:"/terms",className:"text-muted-foreground hover:text-primary transition-colors",children:"Điều khoản sử dụng"})})]})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsx("h3",{className:"font-semibold",children:"Kết nối"}),l.jsxs("div",{className:"flex space-x-4",children:[l.jsx("a",{href:"#",className:"text-muted-foreground hover:text-primary transition-colors",children:l.jsx(Ms,{className:"h-5 w-5"})}),l.jsx("a",{href:"#",className:"text-muted-foreground hover:text-primary transition-colors",children:l.jsx(qs,{className:"h-5 w-5"})}),l.jsx("a",{href:"#",className:"text-muted-foreground hover:text-primary transition-colors",children:l.jsx(Ds,{className:"h-5 w-5"})})]})]})]}),l.jsx("div",{className:"border-t mt-8 pt-8 text-center text-sm text-muted-foreground",children:l.jsx("p",{children:"© 2024 BlogTruyen. Tất cả quyền được bảo lưu."})})]})})},Br=()=>l.jsxs("div",{className:"min-h-screen flex flex-col",children:[l.jsx(Vr,{}),l.jsx("main",{className:"flex-1",children:l.jsx(ss,{})}),l.jsx(Ur,{})]}),ee=P.forwardRef(({className:s,...e},t)=>l.jsx("div",{ref:t,className:Y("rounded-lg border bg-card text-card-foreground shadow-sm",s),...e}));ee.displayName="Card";const he=P.forwardRef(({className:s,...e},t)=>l.jsx("div",{ref:t,className:Y("flex flex-col space-y-1.5 p-6",s),...e}));he.displayName="CardHeader";const fe=P.forwardRef(({className:s,...e},t)=>l.jsx("h3",{ref:t,className:Y("text-2xl font-semibold leading-none tracking-tight",s),...e}));fe.displayName="CardTitle";const ge=P.forwardRef(({className:s,...e},t)=>l.jsx("p",{ref:t,className:Y("text-sm text-muted-foreground",s),...e}));ge.displayName="CardDescription";const te=P.forwardRef(({className:s,...e},t)=>l.jsx("div",{ref:t,className:Y("p-6 pt-0",s),...e}));te.displayName="CardContent";const Hr=P.forwardRef(({className:s,...e},t)=>l.jsx("div",{ref:t,className:Y("flex items-center p-6 pt-0",s),...e}));Hr.displayName="CardFooter";const Kr=()=>l.jsxs("div",{className:"container py-8",children:[l.jsxs("section",{className:"text-center py-12",children:[l.jsx("h1",{className:"text-4xl font-bold tracking-tight lg:text-6xl mb-6",children:"Chào mừng đến với BlogTruyen"}),l.jsx("p",{className:"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto",children:"Khám phá thế giới truyện tranh với hàng ngàn bộ manga chất lượng cao. Đọc miễn phí, không quảng cáo, trải nghiệm tuyệt vời."}),l.jsxs("div",{className:"flex gap-4 justify-center",children:[l.jsx(_,{to:"/browse",children:l.jsxs(W,{size:"lg",children:[l.jsx(we,{className:"mr-2 h-4 w-4"}),"Bắt đầu đọc"]})}),l.jsx(_,{to:"/register",children:l.jsx(W,{variant:"outline",size:"lg",children:"Đăng ký miễn phí"})})]})]}),l.jsxs("section",{className:"py-12",children:[l.jsx("h2",{className:"text-3xl font-bold text-center mb-8",children:"Tính năng nổi bật"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[l.jsxs(ee,{children:[l.jsxs(he,{className:"text-center",children:[l.jsx(Gs,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),l.jsx(fe,{className:"text-lg",children:"Xu hướng"})]}),l.jsx(te,{children:l.jsx(ge,{className:"text-center",children:"Theo dõi những bộ truyện hot nhất, được cập nhật liên tục"})})]}),l.jsxs(ee,{children:[l.jsxs(he,{className:"text-center",children:[l.jsx(As,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),l.jsx(fe,{className:"text-lg",children:"Lịch sử đọc"})]}),l.jsx(te,{children:l.jsx(ge,{className:"text-center",children:"Lưu lại tiến trình đọc, tiếp tục từ nơi bạn đã dừng lại"})})]}),l.jsxs(ee,{children:[l.jsxs(he,{className:"text-center",children:[l.jsx(rt,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),l.jsx(fe,{className:"text-lg",children:"Đánh giá"})]}),l.jsx(te,{children:l.jsx(ge,{className:"text-center",children:"Đánh giá và bình luận về những bộ truyện yêu thích"})})]}),l.jsxs(ee,{children:[l.jsxs(he,{className:"text-center",children:[l.jsx(we,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),l.jsx(fe,{className:"text-lg",children:"Đa nền tảng"})]}),l.jsx(te,{children:l.jsx(ge,{className:"text-center",children:"Đọc mọi lúc mọi nơi trên điện thoại, máy tính bảng, laptop"})})]})]})]}),l.jsxs("section",{className:"py-12",children:[l.jsxs("div",{className:"flex justify-between items-center mb-8",children:[l.jsx("h2",{className:"text-3xl font-bold",children:"Truyện phổ biến"}),l.jsx(_,{to:"/browse",children:l.jsx(W,{variant:"outline",children:"Xem tất cả"})})]}),l.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:Array.from({length:6}).map((s,e)=>l.jsxs(ee,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[l.jsx("div",{className:"aspect-[3/4] bg-muted"}),l.jsxs(te,{className:"p-3",children:[l.jsxs("h3",{className:"font-semibold text-sm truncate",children:["Tên truyện ",e+1]}),l.jsx("p",{className:"text-xs text-muted-foreground",children:"Tác giả"}),l.jsxs("div",{className:"flex items-center mt-1",children:[l.jsx(rt,{className:"h-3 w-3 fill-yellow-400 text-yellow-400"}),l.jsx("span",{className:"text-xs ml-1",children:"4.5"})]})]})]},e))})]}),l.jsxs("section",{className:"py-12",children:[l.jsxs("div",{className:"flex justify-between items-center mb-8",children:[l.jsx("h2",{className:"text-3xl font-bold",children:"Cập nhật mới nhất"}),l.jsx(_,{to:"/browse?sort=updated",children:l.jsx(W,{variant:"outline",children:"Xem tất cả"})})]}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((s,e)=>l.jsx(ee,{className:"hover:shadow-lg transition-shadow",children:l.jsx(te,{className:"p-4",children:l.jsxs("div",{className:"flex space-x-4",children:[l.jsx("div",{className:"w-16 h-20 bg-muted rounded"}),l.jsxs("div",{className:"flex-1",children:[l.jsxs("h3",{className:"font-semibold mb-1",children:["Tên truyện ",e+1]}),l.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"Chapter 123"}),l.jsx("p",{className:"text-xs text-muted-foreground",children:"2 giờ trước"})]})]})})},e))})]})]});var _r="Label",Wt=P.forwardRef((s,e)=>l.jsx(ls.label,{...s,ref:e,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||((n=s.onMouseDown)==null||n.call(s,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));Wt.displayName=_r;var qt=Wt;const Gr=At("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),We=P.forwardRef(({className:s,...e},t)=>l.jsx(qt,{ref:t,className:Y(Gr(),s),...e}));We.displayName=qt.displayName;const Wr=()=>{const{t:s}=se("common"),e=rs(),{login:t,isLoading:r}=Gt(),[n,i]=P.useState({email:"",password:""}),[o,a]=P.useState({}),d=h=>{const{name:g,value:m}=h.target;i(f=>({...f,[g]:m})),o[g]&&a(f=>({...f,[g]:""}))},c=()=>{const h={};return n.email?/\S+@\S+\.\S+/.test(n.email)||(h.email=s("forms.invalidEmail")):h.email=s("forms.required"),n.password||(h.password=s("forms.required")),a(h),Object.keys(h).length===0},u=async h=>{if(h.preventDefault(),!!c())try{await t(n),e("/")}catch(g){console.error("Login failed:",g),a({general:"Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin."})}};return l.jsx("div",{className:"container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8",children:l.jsxs(ee,{className:"w-full max-w-md",children:[l.jsxs(he,{className:"text-center",children:[l.jsx("div",{className:"flex justify-center mb-4",children:l.jsx(we,{className:"h-8 w-8 text-primary"})}),l.jsx(fe,{className:"text-2xl",children:s("navigation.login")}),l.jsx(ge,{children:"Đăng nhập để truy cập tài khoản của bạn"})]}),l.jsxs(te,{children:[l.jsxs("form",{onSubmit:u,className:"space-y-4",children:[o.general&&l.jsx("div",{className:"text-sm text-destructive text-center p-2 bg-destructive/10 rounded",children:o.general}),l.jsxs("div",{className:"space-y-2",children:[l.jsx(We,{htmlFor:"email",children:s("forms.email")}),l.jsx(Te,{id:"email",name:"email",type:"email",value:n.email,onChange:d,placeholder:"<EMAIL>",className:o.email?"border-destructive":""}),o.email&&l.jsx("p",{className:"text-sm text-destructive",children:o.email})]}),l.jsxs("div",{className:"space-y-2",children:[l.jsx(We,{htmlFor:"password",children:s("forms.password")}),l.jsx(Te,{id:"password",name:"password",type:"password",value:n.password,onChange:d,className:o.password?"border-destructive":""}),o.password&&l.jsx("p",{className:"text-sm text-destructive",children:o.password})]}),l.jsx(W,{type:"submit",className:"w-full",disabled:r,children:r?"Đang đăng nhập...":s("navigation.login")})]}),l.jsx("div",{className:"mt-6 text-center text-sm",children:l.jsxs("p",{className:"text-muted-foreground",children:["Chưa có tài khoản?"," ",l.jsx(_,{to:"/register",className:"text-primary hover:underline",children:s("navigation.register")})]})})]})]})})},qr=()=>{const{t:s}=se(["common","manga"]);return l.jsxs("div",{className:"container py-8",children:[l.jsx("h1",{className:"text-3xl font-bold mb-8",children:s("navigation.browse")}),l.jsx("div",{className:"text-center py-12",children:l.jsx("p",{className:"text-muted-foreground",children:"Trang duyệt truyện đang được phát triển..."})})]})},Jr=()=>l.jsxs("div",{className:"container py-8",children:[l.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Chi tiết truyện"}),l.jsx("div",{className:"text-center py-12",children:l.jsx("p",{className:"text-muted-foreground",children:"Trang chi tiết truyện đang được phát triển..."})})]}),Qr=()=>l.jsxs("div",{className:"container py-8",children:[l.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Đọc truyện"}),l.jsx("div",{className:"text-center py-12",children:l.jsx("p",{className:"text-muted-foreground",children:"Trang đọc truyện đang được phát triển..."})})]}),Xr=()=>{const{t:s}=se("common");return l.jsxs("div",{className:"container py-8",children:[l.jsx("h1",{className:"text-3xl font-bold mb-8",children:s("navigation.register")}),l.jsx("div",{className:"text-center py-12",children:l.jsx("p",{className:"text-muted-foreground",children:"Trang đăng ký đang được phát triển..."})})]})},Yr=()=>{const{t:s}=se("common");return l.jsxs("div",{className:"container py-8",children:[l.jsx("h1",{className:"text-3xl font-bold mb-8",children:s("navigation.bookmarks")}),l.jsx("div",{className:"text-center py-12",children:l.jsx("p",{className:"text-muted-foreground",children:"Trang đánh dấu đang được phát triển..."})})]})},Zr=()=>{const{t:s}=se("common");return l.jsxs("div",{className:"container py-8",children:[l.jsx("h1",{className:"text-3xl font-bold mb-8",children:s("navigation.history")}),l.jsx("div",{className:"text-center py-12",children:l.jsx("p",{className:"text-muted-foreground",children:"Trang lịch sử đang được phát triển..."})})]})},en=()=>{const{t:s}=se("common");return l.jsxs("div",{className:"container py-8",children:[l.jsx("h1",{className:"text-3xl font-bold mb-8",children:s("navigation.profile")}),l.jsx("div",{className:"text-center py-12",children:l.jsx("p",{className:"text-muted-foreground",children:"Trang hồ sơ đang được phát triển..."})})]})},tn=ns([{path:"/",element:l.jsx(Br,{}),children:[{index:!0,element:l.jsx(Kr,{})},{path:"login",element:l.jsx(Wr,{})},{path:"register",element:l.jsx(Xr,{})},{path:"browse",element:l.jsx(qr,{})},{path:"manga/:id",element:l.jsx(Jr,{})},{path:"read/:chapterId",element:l.jsx(Qr,{})},{path:"bookmarks",element:l.jsx(Yr,{})},{path:"history",element:l.jsx(Zr,{})},{path:"profile",element:l.jsx(en,{})}]}]),sn=()=>l.jsx(is,{router:tn}),w=s=>typeof s=="string",ye=()=>{let s,e;const t=new Promise((r,n)=>{s=r,e=n});return t.resolve=s,t.reject=e,t},ut=s=>s==null?"":""+s,rn=(s,e,t)=>{s.forEach(r=>{e[r]&&(t[r]=e[r])})},nn=/###/g,ht=s=>s&&s.indexOf("###")>-1?s.replace(nn,"."):s,ft=s=>!s||w(s),ve=(s,e,t)=>{const r=w(e)?e.split("."):e;let n=0;for(;n<r.length-1;){if(ft(s))return{};const i=ht(r[n]);!s[i]&&t&&(s[i]=new t),Object.prototype.hasOwnProperty.call(s,i)?s=s[i]:s={},++n}return ft(s)?{}:{obj:s,k:ht(r[n])}},gt=(s,e,t)=>{const{obj:r,k:n}=ve(s,e,Object);if(r!==void 0||e.length===1){r[n]=t;return}let i=e[e.length-1],o=e.slice(0,e.length-1),a=ve(s,o,Object);for(;a.obj===void 0&&o.length;)i=`${o[o.length-1]}.${i}`,o=o.slice(0,o.length-1),a=ve(s,o,Object),a!=null&&a.obj&&typeof a.obj[`${a.k}.${i}`]<"u"&&(a.obj=void 0);a.obj[`${a.k}.${i}`]=t},on=(s,e,t,r)=>{const{obj:n,k:i}=ve(s,e,Object);n[i]=n[i]||[],n[i].push(t)},$e=(s,e)=>{const{obj:t,k:r}=ve(s,e);if(t&&Object.prototype.hasOwnProperty.call(t,r))return t[r]},an=(s,e,t)=>{const r=$e(s,t);return r!==void 0?r:$e(e,t)},Jt=(s,e,t)=>{for(const r in e)r!=="__proto__"&&r!=="constructor"&&(r in s?w(s[r])||s[r]instanceof String||w(e[r])||e[r]instanceof String?t&&(s[r]=e[r]):Jt(s[r],e[r],t):s[r]=e[r]);return s},ce=s=>s.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var ln={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const cn=s=>w(s)?s.replace(/[&<>"'\/]/g,e=>ln[e]):s;class dn{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(t!==void 0)return t;const r=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,r),this.regExpQueue.push(e),r}}const un=[" ",",","?","!",";"],hn=new dn(20),fn=(s,e,t)=>{e=e||"",t=t||"";const r=un.filter(o=>e.indexOf(o)<0&&t.indexOf(o)<0);if(r.length===0)return!0;const n=hn.getRegExp(`(${r.map(o=>o==="?"?"\\?":o).join("|")})`);let i=!n.test(s);if(!i){const o=s.indexOf(t);o>0&&!n.test(s.substring(0,o))&&(i=!0)}return i},qe=(s,e,t=".")=>{if(!s)return;if(s[e])return Object.prototype.hasOwnProperty.call(s,e)?s[e]:void 0;const r=e.split(t);let n=s;for(let i=0;i<r.length;){if(!n||typeof n!="object")return;let o,a="";for(let d=i;d<r.length;++d)if(d!==i&&(a+=t),a+=r[d],o=n[a],o!==void 0){if(["string","number","boolean"].indexOf(typeof o)>-1&&d<r.length-1)continue;i+=d-i+1;break}n=o}return n},ke=s=>s==null?void 0:s.replace("_","-"),gn={type:"logger",log(s){this.output("log",s)},warn(s){this.output("warn",s)},error(s){this.output("error",s)},output(s,e){var t,r;(r=(t=console==null?void 0:console[s])==null?void 0:t.apply)==null||r.call(t,console,e)}};class Ee{constructor(e,t={}){this.init(e,t)}init(e,t={}){this.prefix=t.prefix||"i18next:",this.logger=e||gn,this.options=t,this.debug=t.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,r,n){return n&&!this.debug?null:(w(e[0])&&(e[0]=`${r}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new Ee(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new Ee(this.logger,e)}}var q=new Ee;class Ae{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(r=>{this.observers[r]||(this.observers[r]=new Map);const n=this.observers[r].get(t)||0;this.observers[r].set(t,n+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e,...t){this.observers[e]&&Array.from(this.observers[e].entries()).forEach(([n,i])=>{for(let o=0;o<i;o++)n(...t)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([n,i])=>{for(let o=0;o<i;o++)n.apply(n,[e,...t])})}}class mt extends Ae{constructor(e,t={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,r,n={}){var c,u;const i=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,o=n.ignoreJSONStructure!==void 0?n.ignoreJSONStructure:this.options.ignoreJSONStructure;let a;e.indexOf(".")>-1?a=e.split("."):(a=[e,t],r&&(Array.isArray(r)?a.push(...r):w(r)&&i?a.push(...r.split(i)):a.push(r)));const d=$e(this.data,a);return!d&&!t&&!r&&e.indexOf(".")>-1&&(e=a[0],t=a[1],r=a.slice(2).join(".")),d||!o||!w(r)?d:qe((u=(c=this.data)==null?void 0:c[e])==null?void 0:u[t],r,i)}addResource(e,t,r,n,i={silent:!1}){const o=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator;let a=[e,t];r&&(a=a.concat(o?r.split(o):r)),e.indexOf(".")>-1&&(a=e.split("."),n=t,t=a[1]),this.addNamespaces(t),gt(this.data,a,n),i.silent||this.emit("added",e,t,r,n)}addResources(e,t,r,n={silent:!1}){for(const i in r)(w(r[i])||Array.isArray(r[i]))&&this.addResource(e,t,i,r[i],{silent:!0});n.silent||this.emit("added",e,t,r)}addResourceBundle(e,t,r,n,i,o={silent:!1,skipCopy:!1}){let a=[e,t];e.indexOf(".")>-1&&(a=e.split("."),n=r,r=t,t=a[1]),this.addNamespaces(t);let d=$e(this.data,a)||{};o.skipCopy||(r=JSON.parse(JSON.stringify(r))),n?Jt(d,r,i):d={...d,...r},gt(this.data,a,d),o.silent||this.emit("added",e,t,r)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return this.getResource(e,t)!==void 0}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(n=>t[n]&&Object.keys(t[n]).length>0)}toJSON(){return this.data}}var Qt={processors:{},addPostProcessor(s){this.processors[s.name]=s},handle(s,e,t,r,n){return s.forEach(i=>{var o;e=((o=this.processors[i])==null?void 0:o.process(e,t,r,n))??e}),e}};const pt={},xt=s=>!w(s)&&typeof s!="boolean"&&typeof s!="number";class Ie extends Ae{constructor(e,t={}){super(),rn(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=q.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,t={interpolation:{}}){const r={...t};if(e==null)return!1;const n=this.resolve(e,r);return(n==null?void 0:n.res)!==void 0}extractFromKey(e,t){let r=t.nsSeparator!==void 0?t.nsSeparator:this.options.nsSeparator;r===void 0&&(r=":");const n=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator;let i=t.ns||this.options.defaultNS||[];const o=r&&e.indexOf(r)>-1,a=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!fn(e,r,n);if(o&&!a){const d=e.match(this.interpolator.nestingRegexp);if(d&&d.length>0)return{key:e,namespaces:w(i)?[i]:i};const c=e.split(r);(r!==n||r===n&&this.options.ns.indexOf(c[0])>-1)&&(i=c.shift()),e=c.join(n)}return{key:e,namespaces:w(i)?[i]:i}}translate(e,t,r){let n=typeof t=="object"?{...t}:t;if(typeof n!="object"&&this.options.overloadTranslationOptionHandler&&(n=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(n={...n}),n||(n={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const i=n.returnDetails!==void 0?n.returnDetails:this.options.returnDetails,o=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,{key:a,namespaces:d}=this.extractFromKey(e[e.length-1],n),c=d[d.length-1];let u=n.nsSeparator!==void 0?n.nsSeparator:this.options.nsSeparator;u===void 0&&(u=":");const h=n.lng||this.language,g=n.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((h==null?void 0:h.toLowerCase())==="cimode")return g?i?{res:`${c}${u}${a}`,usedKey:a,exactUsedKey:a,usedLng:h,usedNS:c,usedParams:this.getUsedParamsDetails(n)}:`${c}${u}${a}`:i?{res:a,usedKey:a,exactUsedKey:a,usedLng:h,usedNS:c,usedParams:this.getUsedParamsDetails(n)}:a;const m=this.resolve(e,n);let f=m==null?void 0:m.res;const p=(m==null?void 0:m.usedKey)||a,k=(m==null?void 0:m.exactUsedKey)||a,R=["[object Number]","[object Function]","[object RegExp]"],S=n.joinArrays!==void 0?n.joinArrays:this.options.joinArrays,C=!this.i18nFormat||this.i18nFormat.handleAsObject,L=n.count!==void 0&&!w(n.count),T=Ie.hasDefaultValue(n),U=L?this.pluralResolver.getSuffix(h,n.count,n):"",A=n.ordinal&&L?this.pluralResolver.getSuffix(h,n.count,{ordinal:!1}):"",y=L&&!n.ordinal&&n.count===0,j=y&&n[`defaultValue${this.options.pluralSeparator}zero`]||n[`defaultValue${U}`]||n[`defaultValue${A}`]||n.defaultValue;let O=f;C&&!f&&T&&(O=j);const H=xt(O),ae=Object.prototype.toString.apply(O);if(C&&O&&H&&R.indexOf(ae)<0&&!(w(S)&&Array.isArray(O))){if(!n.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const K=this.options.returnedObjectHandler?this.options.returnedObjectHandler(p,O,{...n,ns:d}):`key '${a} (${this.language})' returned an object instead of string.`;return i?(m.res=K,m.usedParams=this.getUsedParamsDetails(n),m):K}if(o){const K=Array.isArray(O),B=K?[]:{},Q=K?k:p;for(const E in O)if(Object.prototype.hasOwnProperty.call(O,E)){const M=`${Q}${o}${E}`;T&&!f?B[E]=this.translate(M,{...n,defaultValue:xt(j)?j[E]:void 0,joinArrays:!1,ns:d}):B[E]=this.translate(M,{...n,joinArrays:!1,ns:d}),B[E]===M&&(B[E]=O[E])}f=B}}else if(C&&w(S)&&Array.isArray(f))f=f.join(S),f&&(f=this.extendTranslation(f,e,n,r));else{let K=!1,B=!1;!this.isValidLookup(f)&&T&&(K=!0,f=j),this.isValidLookup(f)||(B=!0,f=a);const E=(n.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&B?void 0:f,M=T&&j!==f&&this.options.updateMissing;if(B||K||M){if(this.logger.log(M?"updateKey":"missingKey",h,c,a,M?j:f),o){const z=this.resolve(a,{...n,keySeparator:!1});z&&z.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let v=[];const re=this.languageUtils.getFallbackCodes(this.options.fallbackLng,n.lng||this.language);if(this.options.saveMissingTo==="fallback"&&re&&re[0])for(let z=0;z<re.length;z++)v.push(re[z]);else this.options.saveMissingTo==="all"?v=this.languageUtils.toResolveHierarchy(n.lng||this.language):v.push(n.lng||this.language);const xe=(z,G,$)=>{var ne;const D=T&&$!==f?$:E;this.options.missingKeyHandler?this.options.missingKeyHandler(z,c,G,D,M,n):(ne=this.backendConnector)!=null&&ne.saveMissing&&this.backendConnector.saveMissing(z,c,G,D,M,n),this.emit("missingKey",z,c,G,f)};this.options.saveMissing&&(this.options.saveMissingPlurals&&L?v.forEach(z=>{const G=this.pluralResolver.getSuffixes(z,n);y&&n[`defaultValue${this.options.pluralSeparator}zero`]&&G.indexOf(`${this.options.pluralSeparator}zero`)<0&&G.push(`${this.options.pluralSeparator}zero`),G.forEach($=>{xe([z],a+$,n[`defaultValue${$}`]||j)})}):xe(v,a,j))}f=this.extendTranslation(f,e,n,m,r),B&&f===a&&this.options.appendNamespaceToMissingKey&&(f=`${c}${u}${a}`),(B||K)&&this.options.parseMissingKeyHandler&&(f=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${c}${u}${a}`:a,K?f:void 0,n))}return i?(m.res=f,m.usedParams=this.getUsedParamsDetails(n),m):f}extendTranslation(e,t,r,n,i){var d,c;if((d=this.i18nFormat)!=null&&d.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||n.usedLng,n.usedNS,n.usedKey,{resolved:n});else if(!r.skipInterpolation){r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});const u=w(e)&&(((c=r==null?void 0:r.interpolation)==null?void 0:c.skipOnVariables)!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let h;if(u){const m=e.match(this.interpolator.nestingRegexp);h=m&&m.length}let g=r.replace&&!w(r.replace)?r.replace:r;if(this.options.interpolation.defaultVariables&&(g={...this.options.interpolation.defaultVariables,...g}),e=this.interpolator.interpolate(e,g,r.lng||this.language||n.usedLng,r),u){const m=e.match(this.interpolator.nestingRegexp),f=m&&m.length;h<f&&(r.nest=!1)}!r.lng&&n&&n.res&&(r.lng=this.language||n.usedLng),r.nest!==!1&&(e=this.interpolator.nest(e,(...m)=>(i==null?void 0:i[0])===m[0]&&!r.context?(this.logger.warn(`It seems you are nesting recursively key: ${m[0]} in key: ${t[0]}`),null):this.translate(...m,t),r)),r.interpolation&&this.interpolator.reset()}const o=r.postProcess||this.options.postProcess,a=w(o)?[o]:o;return e!=null&&(a!=null&&a.length)&&r.applyPostProcessor!==!1&&(e=Qt.handle(a,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...n,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),e}resolve(e,t={}){let r,n,i,o,a;return w(e)&&(e=[e]),e.forEach(d=>{if(this.isValidLookup(r))return;const c=this.extractFromKey(d,t),u=c.key;n=u;let h=c.namespaces;this.options.fallbackNS&&(h=h.concat(this.options.fallbackNS));const g=t.count!==void 0&&!w(t.count),m=g&&!t.ordinal&&t.count===0,f=t.context!==void 0&&(w(t.context)||typeof t.context=="number")&&t.context!=="",p=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);h.forEach(k=>{var R,S;this.isValidLookup(r)||(a=k,!pt[`${p[0]}-${k}`]&&((R=this.utils)!=null&&R.hasLoadedNamespace)&&!((S=this.utils)!=null&&S.hasLoadedNamespace(a))&&(pt[`${p[0]}-${k}`]=!0,this.logger.warn(`key "${n}" for languages "${p.join(", ")}" won't get resolved as namespace "${a}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),p.forEach(C=>{var U;if(this.isValidLookup(r))return;o=C;const L=[u];if((U=this.i18nFormat)!=null&&U.addLookupKeys)this.i18nFormat.addLookupKeys(L,u,C,k,t);else{let A;g&&(A=this.pluralResolver.getSuffix(C,t.count,t));const y=`${this.options.pluralSeparator}zero`,j=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(g&&(L.push(u+A),t.ordinal&&A.indexOf(j)===0&&L.push(u+A.replace(j,this.options.pluralSeparator)),m&&L.push(u+y)),f){const O=`${u}${this.options.contextSeparator}${t.context}`;L.push(O),g&&(L.push(O+A),t.ordinal&&A.indexOf(j)===0&&L.push(O+A.replace(j,this.options.pluralSeparator)),m&&L.push(O+y))}}let T;for(;T=L.pop();)this.isValidLookup(r)||(i=T,r=this.getResource(C,k,T,t))}))})}),{res:r,usedKey:n,exactUsedKey:i,usedLng:o,usedNS:a}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,t,r,n={}){var i;return(i=this.i18nFormat)!=null&&i.getResource?this.i18nFormat.getResource(e,t,r,n):this.resourceStore.getResource(e,t,r,n)}getUsedParamsDetails(e={}){const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],r=e.replace&&!w(e.replace);let n=r?e.replace:e;if(r&&typeof e.count<"u"&&(n.count=e.count),this.options.interpolation.defaultVariables&&(n={...this.options.interpolation.defaultVariables,...n}),!r){n={...n};for(const i of t)delete n[i]}return n}static hasDefaultValue(e){const t="defaultValue";for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t===r.substring(0,t.length)&&e[r]!==void 0)return!0;return!1}}class bt{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=q.create("languageUtils")}getScriptPartFromCode(e){if(e=ke(e),!e||e.indexOf("-")<0)return null;const t=e.split("-");return t.length===2||(t.pop(),t[t.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(e=ke(e),!e||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(w(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch{}return t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(r=>{if(t)return;const n=this.formatLanguageCode(r);(!this.options.supportedLngs||this.isSupportedCode(n))&&(t=n)}),!t&&this.options.supportedLngs&&e.forEach(r=>{if(t)return;const n=this.getScriptPartFromCode(r);if(this.isSupportedCode(n))return t=n;const i=this.getLanguagePartFromCode(r);if(this.isSupportedCode(i))return t=i;t=this.options.supportedLngs.find(o=>{if(o===i)return o;if(!(o.indexOf("-")<0&&i.indexOf("-")<0)&&(o.indexOf("-")>0&&i.indexOf("-")<0&&o.substring(0,o.indexOf("-"))===i||o.indexOf(i)===0&&i.length>1))return o})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if(typeof e=="function"&&(e=e(t)),w(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let r=e[t];return r||(r=e[this.getScriptPartFromCode(t)]),r||(r=e[this.formatLanguageCode(t)]),r||(r=e[this.getLanguagePartFromCode(t)]),r||(r=e.default),r||[]}toResolveHierarchy(e,t){const r=this.getFallbackCodes((t===!1?[]:t)||this.options.fallbackLng||[],e),n=[],i=o=>{o&&(this.isSupportedCode(o)?n.push(o):this.logger.warn(`rejecting language code not found in supportedLngs: ${o}`))};return w(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&i(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&i(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&i(this.getLanguagePartFromCode(e))):w(e)&&i(this.formatLanguageCode(e)),r.forEach(o=>{n.indexOf(o)<0&&i(this.formatLanguageCode(o))}),n}}const yt={zero:0,one:1,two:2,few:3,many:4,other:5},vt={select:s=>s===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class mn{constructor(e,t={}){this.languageUtils=e,this.options=t,this.logger=q.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e,t={}){const r=ke(e==="dev"?"en":e),n=t.ordinal?"ordinal":"cardinal",i=JSON.stringify({cleanedCode:r,type:n});if(i in this.pluralRulesCache)return this.pluralRulesCache[i];let o;try{o=new Intl.PluralRules(r,{type:n})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),vt;if(!e.match(/-|_/))return vt;const d=this.languageUtils.getLanguagePartFromCode(e);o=this.getRule(d,t)}return this.pluralRulesCache[i]=o,o}needsPlural(e,t={}){let r=this.getRule(e,t);return r||(r=this.getRule("dev",t)),(r==null?void 0:r.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(e,t,r={}){return this.getSuffixes(e,r).map(n=>`${t}${n}`)}getSuffixes(e,t={}){let r=this.getRule(e,t);return r||(r=this.getRule("dev",t)),r?r.resolvedOptions().pluralCategories.sort((n,i)=>yt[n]-yt[i]).map(n=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${n}`):[]}getSuffix(e,t,r={}){const n=this.getRule(e,r);return n?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${n.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,r))}}const wt=(s,e,t,r=".",n=!0)=>{let i=an(s,e,t);return!i&&n&&w(t)&&(i=qe(s,t,r),i===void 0&&(i=qe(e,t,r))),i},De=s=>s.replace(/\$/g,"$$$$");class pn{constructor(e={}){var t;this.logger=q.create("interpolator"),this.options=e,this.format=((t=e==null?void 0:e.interpolation)==null?void 0:t.format)||(r=>r),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:r,useRawValueToEscape:n,prefix:i,prefixEscaped:o,suffix:a,suffixEscaped:d,formatSeparator:c,unescapeSuffix:u,unescapePrefix:h,nestingPrefix:g,nestingPrefixEscaped:m,nestingSuffix:f,nestingSuffixEscaped:p,nestingOptionsSeparator:k,maxReplaces:R,alwaysFormat:S}=e.interpolation;this.escape=t!==void 0?t:cn,this.escapeValue=r!==void 0?r:!0,this.useRawValueToEscape=n!==void 0?n:!1,this.prefix=i?ce(i):o||"{{",this.suffix=a?ce(a):d||"}}",this.formatSeparator=c||",",this.unescapePrefix=u?"":h||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=g?ce(g):m||ce("$t("),this.nestingSuffix=f?ce(f):p||ce(")"),this.nestingOptionsSeparator=k||",",this.maxReplaces=R||1e3,this.alwaysFormat=S!==void 0?S:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(t,r)=>(t==null?void 0:t.source)===r?(t.lastIndex=0,t):new RegExp(r,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,r,n){var m;let i,o,a;const d=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},c=f=>{if(f.indexOf(this.formatSeparator)<0){const S=wt(t,d,f,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(S,void 0,r,{...n,...t,interpolationkey:f}):S}const p=f.split(this.formatSeparator),k=p.shift().trim(),R=p.join(this.formatSeparator).trim();return this.format(wt(t,d,k,this.options.keySeparator,this.options.ignoreJSONStructure),R,r,{...n,...t,interpolationkey:k})};this.resetRegExp();const u=(n==null?void 0:n.missingInterpolationHandler)||this.options.missingInterpolationHandler,h=((m=n==null?void 0:n.interpolation)==null?void 0:m.skipOnVariables)!==void 0?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:f=>De(f)},{regex:this.regexp,safeValue:f=>this.escapeValue?De(this.escape(f)):De(f)}].forEach(f=>{for(a=0;i=f.regex.exec(e);){const p=i[1].trim();if(o=c(p),o===void 0)if(typeof u=="function"){const R=u(e,i,n);o=w(R)?R:""}else if(n&&Object.prototype.hasOwnProperty.call(n,p))o="";else if(h){o=i[0];continue}else this.logger.warn(`missed to pass in variable ${p} for interpolating ${e}`),o="";else!w(o)&&!this.useRawValueToEscape&&(o=ut(o));const k=f.safeValue(o);if(e=e.replace(i[0],k),h?(f.regex.lastIndex+=o.length,f.regex.lastIndex-=i[0].length):f.regex.lastIndex=0,a++,a>=this.maxReplaces)break}}),e}nest(e,t,r={}){let n,i,o;const a=(d,c)=>{const u=this.nestingOptionsSeparator;if(d.indexOf(u)<0)return d;const h=d.split(new RegExp(`${u}[ ]*{`));let g=`{${h[1]}`;d=h[0],g=this.interpolate(g,o);const m=g.match(/'/g),f=g.match(/"/g);(((m==null?void 0:m.length)??0)%2===0&&!f||f.length%2!==0)&&(g=g.replace(/'/g,'"'));try{o=JSON.parse(g),c&&(o={...c,...o})}catch(p){return this.logger.warn(`failed parsing options string in nesting for key ${d}`,p),`${d}${u}${g}`}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,d};for(;n=this.nestingRegexp.exec(e);){let d=[];o={...r},o=o.replace&&!w(o.replace)?o.replace:o,o.applyPostProcessor=!1,delete o.defaultValue;let c=!1;if(n[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(n[1])){const u=n[1].split(this.formatSeparator).map(h=>h.trim());n[1]=u.shift(),d=u,c=!0}if(i=t(a.call(this,n[1].trim(),o),o),i&&n[0]===e&&!w(i))return i;w(i)||(i=ut(i)),i||(this.logger.warn(`missed to resolve ${n[1]} for nesting ${e}`),i=""),c&&(i=d.reduce((u,h)=>this.format(u,h,r.lng,{...r,interpolationkey:n[1].trim()}),i.trim())),e=e.replace(n[0],i),this.regexp.lastIndex=0}return e}}const xn=s=>{let e=s.toLowerCase().trim();const t={};if(s.indexOf("(")>-1){const r=s.split("(");e=r[0].toLowerCase().trim();const n=r[1].substring(0,r[1].length-1);e==="currency"&&n.indexOf(":")<0?t.currency||(t.currency=n.trim()):e==="relativetime"&&n.indexOf(":")<0?t.range||(t.range=n.trim()):n.split(";").forEach(o=>{if(o){const[a,...d]=o.split(":"),c=d.join(":").trim().replace(/^'+|'+$/g,""),u=a.trim();t[u]||(t[u]=c),c==="false"&&(t[u]=!1),c==="true"&&(t[u]=!0),isNaN(c)||(t[u]=parseInt(c,10))}})}return{formatName:e,formatOptions:t}},kt=s=>{const e={};return(t,r,n)=>{let i=n;n&&n.interpolationkey&&n.formatParams&&n.formatParams[n.interpolationkey]&&n[n.interpolationkey]&&(i={...i,[n.interpolationkey]:void 0});const o=r+JSON.stringify(i);let a=e[o];return a||(a=s(ke(r),n),e[o]=a),a(t)}},bn=s=>(e,t,r)=>s(ke(t),r)(e);class yn{constructor(e={}){this.logger=q.create("formatter"),this.options=e,this.init(e)}init(e,t={interpolation:{}}){this.formatSeparator=t.interpolation.formatSeparator||",";const r=t.cacheInBuiltFormats?kt:bn;this.formats={number:r((n,i)=>{const o=new Intl.NumberFormat(n,{...i});return a=>o.format(a)}),currency:r((n,i)=>{const o=new Intl.NumberFormat(n,{...i,style:"currency"});return a=>o.format(a)}),datetime:r((n,i)=>{const o=new Intl.DateTimeFormat(n,{...i});return a=>o.format(a)}),relativetime:r((n,i)=>{const o=new Intl.RelativeTimeFormat(n,{...i});return a=>o.format(a,i.range||"day")}),list:r((n,i)=>{const o=new Intl.ListFormat(n,{...i});return a=>o.format(a)})}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=kt(t)}format(e,t,r,n={}){const i=t.split(this.formatSeparator);if(i.length>1&&i[0].indexOf("(")>1&&i[0].indexOf(")")<0&&i.find(a=>a.indexOf(")")>-1)){const a=i.findIndex(d=>d.indexOf(")")>-1);i[0]=[i[0],...i.splice(1,a)].join(this.formatSeparator)}return i.reduce((a,d)=>{var h;const{formatName:c,formatOptions:u}=xn(d);if(this.formats[c]){let g=a;try{const m=((h=n==null?void 0:n.formatParams)==null?void 0:h[n.interpolationkey])||{},f=m.locale||m.lng||n.locale||n.lng||r;g=this.formats[c](a,f,{...u,...n,...m})}catch(m){this.logger.warn(m)}return g}else this.logger.warn(`there was no format function for ${c}`);return a},e)}}const vn=(s,e)=>{s.pending[e]!==void 0&&(delete s.pending[e],s.pendingCount--)};class wn extends Ae{constructor(e,t,r,n={}){var i,o;super(),this.backend=e,this.store=t,this.services=r,this.languageUtils=r.languageUtils,this.options=n,this.logger=q.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=n.maxParallelReads||10,this.readingCalls=0,this.maxRetries=n.maxRetries>=0?n.maxRetries:5,this.retryTimeout=n.retryTimeout>=1?n.retryTimeout:350,this.state={},this.queue=[],(o=(i=this.backend)==null?void 0:i.init)==null||o.call(i,r,n.backend,n)}queueLoad(e,t,r,n){const i={},o={},a={},d={};return e.forEach(c=>{let u=!0;t.forEach(h=>{const g=`${c}|${h}`;!r.reload&&this.store.hasResourceBundle(c,h)?this.state[g]=2:this.state[g]<0||(this.state[g]===1?o[g]===void 0&&(o[g]=!0):(this.state[g]=1,u=!1,o[g]===void 0&&(o[g]=!0),i[g]===void 0&&(i[g]=!0),d[h]===void 0&&(d[h]=!0)))}),u||(a[c]=!0)}),(Object.keys(i).length||Object.keys(o).length)&&this.queue.push({pending:o,pendingCount:Object.keys(o).length,loaded:{},errors:[],callback:n}),{toLoad:Object.keys(i),pending:Object.keys(o),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(d)}}loaded(e,t,r){const n=e.split("|"),i=n[0],o=n[1];t&&this.emit("failedLoading",i,o,t),!t&&r&&this.store.addResourceBundle(i,o,r,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&r&&(this.state[e]=0);const a={};this.queue.forEach(d=>{on(d.loaded,[i],o),vn(d,e),t&&d.errors.push(t),d.pendingCount===0&&!d.done&&(Object.keys(d.loaded).forEach(c=>{a[c]||(a[c]={});const u=d.loaded[c];u.length&&u.forEach(h=>{a[c][h]===void 0&&(a[c][h]=!0)})}),d.done=!0,d.errors.length?d.callback(d.errors):d.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(d=>!d.done)}read(e,t,r,n=0,i=this.retryTimeout,o){if(!e.length)return o(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:r,tried:n,wait:i,callback:o});return}this.readingCalls++;const a=(c,u)=>{if(this.readingCalls--,this.waitingReads.length>0){const h=this.waitingReads.shift();this.read(h.lng,h.ns,h.fcName,h.tried,h.wait,h.callback)}if(c&&u&&n<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,r,n+1,i*2,o)},i);return}o(c,u)},d=this.backend[r].bind(this.backend);if(d.length===2){try{const c=d(e,t);c&&typeof c.then=="function"?c.then(u=>a(null,u)).catch(a):a(null,c)}catch(c){a(c)}return}return d(e,t,a)}prepareLoading(e,t,r={},n){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),n&&n();w(e)&&(e=this.languageUtils.toResolveHierarchy(e)),w(t)&&(t=[t]);const i=this.queueLoad(e,t,r,n);if(!i.toLoad.length)return i.pending.length||n(),null;i.toLoad.forEach(o=>{this.loadOne(o)})}load(e,t,r){this.prepareLoading(e,t,{},r)}reload(e,t,r){this.prepareLoading(e,t,{reload:!0},r)}loadOne(e,t=""){const r=e.split("|"),n=r[0],i=r[1];this.read(n,i,"read",void 0,void 0,(o,a)=>{o&&this.logger.warn(`${t}loading namespace ${i} for language ${n} failed`,o),!o&&a&&this.logger.log(`${t}loaded namespace ${i} for language ${n}`,a),this.loaded(e,o,a)})}saveMissing(e,t,r,n,i,o={},a=()=>{}){var d,c,u,h,g;if((c=(d=this.services)==null?void 0:d.utils)!=null&&c.hasLoadedNamespace&&!((h=(u=this.services)==null?void 0:u.utils)!=null&&h.hasLoadedNamespace(t))){this.logger.warn(`did not save key "${r}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(r==null||r==="")){if((g=this.backend)!=null&&g.create){const m={...o,isUpdate:i},f=this.backend.create.bind(this.backend);if(f.length<6)try{let p;f.length===5?p=f(e,t,r,n,m):p=f(e,t,r,n),p&&typeof p.then=="function"?p.then(k=>a(null,k)).catch(a):a(null,p)}catch(p){a(p)}else f(e,t,r,n,a,m)}!e||!e[0]||this.store.addResource(e[0],t,r,n)}}}const St=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:s=>{let e={};if(typeof s[1]=="object"&&(e=s[1]),w(s[1])&&(e.defaultValue=s[1]),w(s[2])&&(e.tDescription=s[2]),typeof s[2]=="object"||typeof s[3]=="object"){const t=s[3]||s[2];Object.keys(t).forEach(r=>{e[r]=t[r]})}return e},interpolation:{escapeValue:!0,format:s=>s,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),Nt=s=>{var e,t;return w(s.ns)&&(s.ns=[s.ns]),w(s.fallbackLng)&&(s.fallbackLng=[s.fallbackLng]),w(s.fallbackNS)&&(s.fallbackNS=[s.fallbackNS]),((t=(e=s.supportedLngs)==null?void 0:e.indexOf)==null?void 0:t.call(e,"cimode"))<0&&(s.supportedLngs=s.supportedLngs.concat(["cimode"])),typeof s.initImmediate=="boolean"&&(s.initAsync=s.initImmediate),s},Pe=()=>{},kn=s=>{Object.getOwnPropertyNames(Object.getPrototypeOf(s)).forEach(t=>{typeof s[t]=="function"&&(s[t]=s[t].bind(s))})};class Se extends Ae{constructor(e={},t){if(super(),this.options=Nt(e),this.services={},this.logger=q,this.modules={external:[]},kn(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(e={},t){this.isInitializing=!0,typeof e=="function"&&(t=e,e={}),e.defaultNS==null&&e.ns&&(w(e.ns)?e.defaultNS=e.ns:e.ns.indexOf("translation")<0&&(e.defaultNS=e.ns[0]));const r=St();this.options={...r,...this.options,...Nt(e)},this.options.interpolation={...r.interpolation,...this.options.interpolation},e.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=e.keySeparator),e.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=e.nsSeparator);const n=c=>c?typeof c=="function"?new c:c:null;if(!this.options.isClone){this.modules.logger?q.init(n(this.modules.logger),this.options):q.init(null,this.options);let c;this.modules.formatter?c=this.modules.formatter:c=yn;const u=new bt(this.options);this.store=new mt(this.options.resources,this.options);const h=this.services;h.logger=q,h.resourceStore=this.store,h.languageUtils=u,h.pluralResolver=new mn(u,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),c&&(!this.options.interpolation.format||this.options.interpolation.format===r.interpolation.format)&&(h.formatter=n(c),h.formatter.init(h,this.options),this.options.interpolation.format=h.formatter.format.bind(h.formatter)),h.interpolator=new pn(this.options),h.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},h.backendConnector=new wn(n(this.modules.backend),h.resourceStore,h,this.options),h.backendConnector.on("*",(g,...m)=>{this.emit(g,...m)}),this.modules.languageDetector&&(h.languageDetector=n(this.modules.languageDetector),h.languageDetector.init&&h.languageDetector.init(h,this.options.detection,this.options)),this.modules.i18nFormat&&(h.i18nFormat=n(this.modules.i18nFormat),h.i18nFormat.init&&h.i18nFormat.init(this)),this.translator=new Ie(this.services,this.options),this.translator.on("*",(g,...m)=>{this.emit(g,...m)}),this.modules.external.forEach(g=>{g.init&&g.init(this)})}if(this.format=this.options.interpolation.format,t||(t=Pe),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const c=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);c.length>0&&c[0]!=="dev"&&(this.options.lng=c[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(c=>{this[c]=(...u)=>this.store[c](...u)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(c=>{this[c]=(...u)=>(this.store[c](...u),this)});const a=ye(),d=()=>{const c=(u,h)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(h),t(u,h)};if(this.languages&&!this.isInitialized)return c(null,this.t.bind(this));this.changeLanguage(this.options.lng,c)};return this.options.resources||!this.options.initAsync?d():setTimeout(d,0),a}loadResources(e,t=Pe){var i,o;let r=t;const n=w(e)?e:this.language;if(typeof e=="function"&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if((n==null?void 0:n.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return r();const a=[],d=c=>{if(!c||c==="cimode")return;this.services.languageUtils.toResolveHierarchy(c).forEach(h=>{h!=="cimode"&&a.indexOf(h)<0&&a.push(h)})};n?d(n):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(u=>d(u)),(o=(i=this.options.preload)==null?void 0:i.forEach)==null||o.call(i,c=>d(c)),this.services.backendConnector.load(a,this.options.ns,c=>{!c&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),r(c)})}else r(null)}reloadResources(e,t,r){const n=ye();return typeof e=="function"&&(r=e,e=void 0),typeof t=="function"&&(r=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),r||(r=Pe),this.services.backendConnector.reload(e,t,i=>{n.resolve(),r(i)}),n}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&Qt.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1)){for(let t=0;t<this.languages.length;t++){const r=this.languages[t];if(!(["cimode","dev"].indexOf(r)>-1)&&this.store.hasLanguageSomeTranslations(r)){this.resolvedLanguage=r;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,t){this.isLanguageChangingTo=e;const r=ye();this.emit("languageChanging",e);const n=a=>{this.language=a,this.languages=this.services.languageUtils.toResolveHierarchy(a),this.resolvedLanguage=void 0,this.setResolvedLanguage(a)},i=(a,d)=>{d?this.isLanguageChangingTo===e&&(n(d),this.translator.changeLanguage(d),this.isLanguageChangingTo=void 0,this.emit("languageChanged",d),this.logger.log("languageChanged",d)):this.isLanguageChangingTo=void 0,r.resolve((...c)=>this.t(...c)),t&&t(a,(...c)=>this.t(...c))},o=a=>{var u,h;!e&&!a&&this.services.languageDetector&&(a=[]);const d=w(a)?a:a&&a[0],c=this.store.hasLanguageSomeTranslations(d)?d:this.services.languageUtils.getBestMatchFromCodes(w(a)?[a]:a);c&&(this.language||n(c),this.translator.language||this.translator.changeLanguage(c),(h=(u=this.services.languageDetector)==null?void 0:u.cacheUserLanguage)==null||h.call(u,c)),this.loadResources(c,g=>{i(g,c)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?o(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(o):this.services.languageDetector.detect(o):o(e),r}getFixedT(e,t,r){const n=(i,o,...a)=>{let d;typeof o!="object"?d=this.options.overloadTranslationOptionHandler([i,o].concat(a)):d={...o},d.lng=d.lng||n.lng,d.lngs=d.lngs||n.lngs,d.ns=d.ns||n.ns,d.keyPrefix!==""&&(d.keyPrefix=d.keyPrefix||r||n.keyPrefix);const c=this.options.keySeparator||".";let u;return d.keyPrefix&&Array.isArray(i)?u=i.map(h=>`${d.keyPrefix}${c}${h}`):u=d.keyPrefix?`${d.keyPrefix}${c}${i}`:i,this.t(u,d)};return w(e)?n.lng=e:n.lngs=e,n.ns=t,n.keyPrefix=r,n}t(...e){var t;return(t=this.translator)==null?void 0:t.translate(...e)}exists(...e){var t;return(t=this.translator)==null?void 0:t.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,t={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const r=t.lng||this.resolvedLanguage||this.languages[0],n=this.options?this.options.fallbackLng:!1,i=this.languages[this.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const o=(a,d)=>{const c=this.services.backendConnector.state[`${a}|${d}`];return c===-1||c===0||c===2};if(t.precheck){const a=t.precheck(this,o);if(a!==void 0)return a}return!!(this.hasResourceBundle(r,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||o(r,e)&&(!n||o(i,e)))}loadNamespaces(e,t){const r=ye();return this.options.ns?(w(e)&&(e=[e]),e.forEach(n=>{this.options.ns.indexOf(n)<0&&this.options.ns.push(n)}),this.loadResources(n=>{r.resolve(),t&&t(n)}),r):(t&&t(),Promise.resolve())}loadLanguages(e,t){const r=ye();w(e)&&(e=[e]);const n=this.options.preload||[],i=e.filter(o=>n.indexOf(o)<0&&this.services.languageUtils.isSupportedCode(o));return i.length?(this.options.preload=n.concat(i),this.loadResources(o=>{r.resolve(),t&&t(o)}),r):(t&&t(),Promise.resolve())}dir(e){var n,i;if(e||(e=this.resolvedLanguage||(((n=this.languages)==null?void 0:n.length)>0?this.languages[0]:this.language)),!e)return"rtl";const t=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],r=((i=this.services)==null?void 0:i.languageUtils)||new bt(St());return t.indexOf(r.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(e={},t){return new Se(e,t)}cloneInstance(e={},t=Pe){const r=e.forkResourceStore;r&&delete e.forkResourceStore;const n={...this.options,...e,isClone:!0},i=new Se(n);if((e.debug!==void 0||e.prefix!==void 0)&&(i.logger=i.logger.clone(e)),["store","services","language"].forEach(a=>{i[a]=this[a]}),i.services={...this.services},i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},r){const a=Object.keys(this.store.data).reduce((d,c)=>(d[c]={...this.store.data[c]},d[c]=Object.keys(d[c]).reduce((u,h)=>(u[h]={...d[c][h]},u),d[c]),d),{});i.store=new mt(a,n),i.services.resourceStore=i.store}return i.translator=new Ie(i.services,n),i.translator.on("*",(a,...d)=>{i.emit(a,...d)}),i.init(n,t),i.translator.options=n,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const V=Se.createInstance();V.createInstance=Se.createInstance;V.createInstance;V.dir;V.init;V.loadResources;V.reloadResources;V.use;V.changeLanguage;V.getFixedT;V.t;V.exists;V.setDefaultNamespace;V.hasLoadedNamespace;V.loadNamespaces;V.loadLanguages;const{slice:Sn,forEach:Nn}=[];function jn(s){return Nn.call(Sn.call(arguments,1),e=>{if(e)for(const t in e)s[t]===void 0&&(s[t]=e[t])}),s}function Cn(s){return typeof s!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(t=>t.test(s))}const jt=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,Ln=function(s,e){const r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},n=encodeURIComponent(e);let i=`${s}=${n}`;if(r.maxAge>0){const o=r.maxAge-0;if(Number.isNaN(o))throw new Error("maxAge should be a Number");i+=`; Max-Age=${Math.floor(o)}`}if(r.domain){if(!jt.test(r.domain))throw new TypeError("option domain is invalid");i+=`; Domain=${r.domain}`}if(r.path){if(!jt.test(r.path))throw new TypeError("option path is invalid");i+=`; Path=${r.path}`}if(r.expires){if(typeof r.expires.toUTCString!="function")throw new TypeError("option expires is invalid");i+=`; Expires=${r.expires.toUTCString()}`}if(r.httpOnly&&(i+="; HttpOnly"),r.secure&&(i+="; Secure"),r.sameSite)switch(typeof r.sameSite=="string"?r.sameSite.toLowerCase():r.sameSite){case!0:i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"strict":i+="; SameSite=Strict";break;case"none":i+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return r.partitioned&&(i+="; Partitioned"),i},Ct={create(s,e,t,r){let n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};t&&(n.expires=new Date,n.expires.setTime(n.expires.getTime()+t*60*1e3)),r&&(n.domain=r),document.cookie=Ln(s,encodeURIComponent(e),n)},read(s){const e=`${s}=`,t=document.cookie.split(";");for(let r=0;r<t.length;r++){let n=t[r];for(;n.charAt(0)===" ";)n=n.substring(1,n.length);if(n.indexOf(e)===0)return n.substring(e.length,n.length)}return null},remove(s){this.create(s,"",-1)}};var On={name:"cookie",lookup(s){let{lookupCookie:e}=s;if(e&&typeof document<"u")return Ct.read(e)||void 0},cacheUserLanguage(s,e){let{lookupCookie:t,cookieMinutes:r,cookieDomain:n,cookieOptions:i}=e;t&&typeof document<"u"&&Ct.create(t,s,r,n,i)}},Rn={name:"querystring",lookup(s){var r;let{lookupQuerystring:e}=s,t;if(typeof window<"u"){let{search:n}=window.location;!window.location.search&&((r=window.location.hash)==null?void 0:r.indexOf("?"))>-1&&(n=window.location.hash.substring(window.location.hash.indexOf("?")));const o=n.substring(1).split("&");for(let a=0;a<o.length;a++){const d=o[a].indexOf("=");d>0&&o[a].substring(0,d)===e&&(t=o[a].substring(d+1))}}return t}};let de=null;const Lt=()=>{if(de!==null)return de;try{if(de=typeof window<"u"&&window.localStorage!==null,!de)return!1;const s="i18next.translate.boo";window.localStorage.setItem(s,"foo"),window.localStorage.removeItem(s)}catch{de=!1}return de};var Pn={name:"localStorage",lookup(s){let{lookupLocalStorage:e}=s;if(e&&Lt())return window.localStorage.getItem(e)||void 0},cacheUserLanguage(s,e){let{lookupLocalStorage:t}=e;t&&Lt()&&window.localStorage.setItem(t,s)}};let ue=null;const Ot=()=>{if(ue!==null)return ue;try{if(ue=typeof window<"u"&&window.sessionStorage!==null,!ue)return!1;const s="i18next.translate.boo";window.sessionStorage.setItem(s,"foo"),window.sessionStorage.removeItem(s)}catch{ue=!1}return ue};var Tn={name:"sessionStorage",lookup(s){let{lookupSessionStorage:e}=s;if(e&&Ot())return window.sessionStorage.getItem(e)||void 0},cacheUserLanguage(s,e){let{lookupSessionStorage:t}=e;t&&Ot()&&window.sessionStorage.setItem(t,s)}},$n={name:"navigator",lookup(s){const e=[];if(typeof navigator<"u"){const{languages:t,userLanguage:r,language:n}=navigator;if(t)for(let i=0;i<t.length;i++)e.push(t[i]);r&&e.push(r),n&&e.push(n)}return e.length>0?e:void 0}},En={name:"htmlTag",lookup(s){let{htmlTag:e}=s,t;const r=e||(typeof document<"u"?document.documentElement:null);return r&&typeof r.getAttribute=="function"&&(t=r.getAttribute("lang")),t}},In={name:"path",lookup(s){var n;let{lookupFromPathIndex:e}=s;if(typeof window>"u")return;const t=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(t)?(n=t[typeof e=="number"?e:0])==null?void 0:n.replace("/",""):void 0}},An={name:"subdomain",lookup(s){var n,i;let{lookupFromSubdomainIndex:e}=s;const t=typeof e=="number"?e+1:1,r=typeof window<"u"&&((i=(n=window.location)==null?void 0:n.hostname)==null?void 0:i.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(r)return r[t]}};let Xt=!1;try{document.cookie,Xt=!0}catch{}const Yt=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];Xt||Yt.splice(1,1);const Fn=()=>({order:Yt,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:s=>s});class Zt{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(e,t)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=e,this.options=jn(t,this.options||{},Fn()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=n=>n.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=r,this.addDetector(On),this.addDetector(Rn),this.addDetector(Pn),this.addDetector(Tn),this.addDetector($n),this.addDetector(En),this.addDetector(In),this.addDetector(An)}addDetector(e){return this.detectors[e.name]=e,this}detect(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,t=[];return e.forEach(r=>{if(this.detectors[r]){let n=this.detectors[r].lookup(this.options);n&&typeof n=="string"&&(n=[n]),n&&(t=t.concat(n))}}),t=t.filter(r=>r!=null&&!Cn(r)).map(r=>this.options.convertDetectedLanguage(r)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?t:t.length>0?t[0]:null}cacheUserLanguage(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;t&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||t.forEach(r=>{this.detectors[r]&&this.detectors[r].cacheUserLanguage(e,this.options)}))}}Zt.type="languageDetector";const Mn={home:"Home",browse:"Browse",bookmarks:"Bookmarks",history:"History",profile:"Profile",admin:"Admin",login:"Login",logout:"Logout",register:"Register"},zn={search:"Search",filter:"Filter",sort:"Sort",save:"Save",cancel:"Cancel",delete:"Delete",edit:"Edit",view:"View",back:"Back",next:"Next",previous:"Previous",submit:"Submit",reset:"Reset"},Dn={loading:"Loading...",error:"Error",success:"Success",noData:"No data available",notFound:"Not found"},Vn={email:"Email",password:"Password",confirmPassword:"Confirm Password",username:"Username",required:"This field is required",invalidEmail:"Invalid email format",passwordMismatch:"Passwords do not match",minLength:"Minimum {{count}} characters required"},Un={light:"Light",dark:"Dark",system:"System"},Bn={en:"English",vi:"Tiếng Việt"},Hn={navigation:Mn,actions:zn,status:Dn,forms:Vn,theme:Un,language:Bn},Kn="Manga",_n={author:"Author",artist:"Artist",status:"Status",genres:"Genres",rating:"Rating",chapters:"Chapters",views:"Views",description:"Description",alternativeTitle:"Alternative Title"},Gn={ongoing:"Ongoing",completed:"Completed",hiatus:"Hiatus",cancelled:"Cancelled"},Wn={readNow:"Read Now",addBookmark:"Add to Bookmarks",removeBookmark:"Remove from Bookmarks",continueReading:"Continue Reading",startReading:"Start Reading",rate:"Rate this manga",comment:"Leave a comment"},qn={placeholder:"Search manga...",noResults:"No manga found",filters:{all:"All",genre:"Genre",status:"Status",sortBy:"Sort by",sortOrder:"Order"},sortOptions:{title:"Title",rating:"Rating",views:"Views",updated:"Last Updated",created:"Date Added"}},Jn={title:"Comments",noComments:"No comments yet",writeComment:"Write a comment...",rating:"Your rating"},Qn={title:Kn,details:_n,status:Gn,actions:Wn,search:qn,comments:Jn},Xn="Reader",Yn={previousChapter:"Previous Chapter",nextChapter:"Next Chapter",chapterList:"Chapter List",goToPage:"Go to page",pageOf:"Page {{current}} of {{total}}"},Zn={title:"Reading Settings",readingMode:"Reading Mode",readingDirection:"Reading Direction",pageFit:"Page Fit",brightness:"Brightness",backgroundColor:"Background Color"},ei={single:"Single Page",double:"Double Page",webtoon:"Webtoon"},ti={ltr:"Left to Right",rtl:"Right to Left"},si={width:"Fit Width",height:"Fit Height",auto:"Auto Fit"},ri={fullscreen:"Fullscreen",exitFullscreen:"Exit Fullscreen",settings:"Settings",bookmark:"Bookmark",share:"Share"},ni={title:"Keyboard Shortcuts",nextPage:"Next Page",previousPage:"Previous Page",firstPage:"First Page",lastPage:"Last Page",toggleFullscreen:"Toggle Fullscreen",toggleSettings:"Toggle Settings"},ii={title:Xn,navigation:Yn,settings:Zn,modes:ei,directions:ti,pageFit:si,controls:ri,shortcuts:ni},oi={home:"Trang chủ",browse:"Duyệt",bookmarks:"Đánh dấu",history:"Lịch sử",profile:"Hồ sơ",admin:"Quản trị",login:"Đăng nhập",logout:"Đăng xuất",register:"Đăng ký"},ai={search:"Tìm kiếm",filter:"Lọc",sort:"Sắp xếp",save:"Lưu",cancel:"Hủy",delete:"Xóa",edit:"Sửa",view:"Xem",back:"Quay lại",next:"Tiếp theo",previous:"Trước đó",submit:"Gửi",reset:"Đặt lại"},li={loading:"Đang tải...",error:"Lỗi",success:"Thành công",noData:"Không có dữ liệu",notFound:"Không tìm thấy"},ci={email:"Email",password:"Mật khẩu",confirmPassword:"Xác nhận mật khẩu",username:"Tên người dùng",required:"Trường này là bắt buộc",invalidEmail:"Định dạng email không hợp lệ",passwordMismatch:"Mật khẩu không khớp",minLength:"Tối thiểu {{count}} ký tự"},di={light:"Sáng",dark:"Tối",system:"Hệ thống"},ui={en:"English",vi:"Tiếng Việt"},hi={navigation:oi,actions:ai,status:li,forms:ci,theme:di,language:ui},fi="Truyện tranh",gi={author:"Tác giả",artist:"Họa sĩ",status:"Trạng thái",genres:"Thể loại",rating:"Đánh giá",chapters:"Chương",views:"Lượt xem",description:"Mô tả",alternativeTitle:"Tên khác"},mi={ongoing:"Đang tiến hành",completed:"Hoàn thành",hiatus:"Tạm dừng",cancelled:"Đã hủy"},pi={readNow:"Đọc ngay",addBookmark:"Thêm vào đánh dấu",removeBookmark:"Xóa khỏi đánh dấu",continueReading:"Tiếp tục đọc",startReading:"Bắt đầu đọc",rate:"Đánh giá truyện này",comment:"Để lại bình luận"},xi={placeholder:"Tìm kiếm truyện...",noResults:"Không tìm thấy truyện nào",filters:{all:"Tất cả",genre:"Thể loại",status:"Trạng thái",sortBy:"Sắp xếp theo",sortOrder:"Thứ tự"},sortOptions:{title:"Tiêu đề",rating:"Đánh giá",views:"Lượt xem",updated:"Cập nhật gần nhất",created:"Ngày thêm"}},bi={title:"Bình luận",noComments:"Chưa có bình luận nào",writeComment:"Viết bình luận...",rating:"Đánh giá của bạn"},yi={title:fi,details:gi,status:mi,actions:pi,search:xi,comments:bi},vi="Đọc truyện",wi={previousChapter:"Chương trước",nextChapter:"Chương sau",chapterList:"Danh sách chương",goToPage:"Đến trang",pageOf:"Trang {{current}} / {{total}}"},ki={title:"Cài đặt đọc",readingMode:"Chế độ đọc",readingDirection:"Hướng đọc",pageFit:"Khớp trang",brightness:"Độ sáng",backgroundColor:"Màu nền"},Si={single:"Một trang",double:"Hai trang",webtoon:"Webtoon"},Ni={ltr:"Trái sang phải",rtl:"Phải sang trái"},ji={width:"Khớp chiều rộng",height:"Khớp chiều cao",auto:"Tự động"},Ci={fullscreen:"Toàn màn hình",exitFullscreen:"Thoát toàn màn hình",settings:"Cài đặt",bookmark:"Đánh dấu",share:"Chia sẻ"},Li={title:"Phím tắt",nextPage:"Trang sau",previousPage:"Trang trước",firstPage:"Trang đầu",lastPage:"Trang cuối",toggleFullscreen:"Bật/tắt toàn màn hình",toggleSettings:"Bật/tắt cài đặt"},Oi={title:vi,navigation:wi,settings:ki,modes:Si,directions:Ni,pageFit:ji,controls:Ci,shortcuts:Li},Ri={en:{common:Hn,manga:Qn,reader:ii},vi:{common:hi,manga:yi,reader:Oi}};V.use(Zt).use(Ss).init({resources:Ri,fallbackLng:"en",debug:!1,defaultNS:"common",ns:["common","manga","reader"],interpolation:{escapeValue:!1},detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"]}});function Pi(){return l.jsxs(ts,{client:us,children:[l.jsx(sn,{}),!1]})}ds.createRoot(document.getElementById("root")).render(l.jsx(P.StrictMode,{children:l.jsx(Pi,{})}));
//# sourceMappingURL=index-92JRbkOu.js.map
