// App constants
export const APP_NAME = 'BlogTruyen';
export const APP_VERSION = '1.0.0';

// API endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
  },
  MANGA: {
    LIST: '/manga',
    DETAIL: '/manga/:id',
    CHAPTERS: '/manga/:id/chapters',
  },
  CHAPTERS: {
    DETAIL: '/chapters/:id',
  },
  USER: {
    PROFILE: '/user/profile',
    BOOKMARKS: '/user/bookmarks',
    HISTORY: '/user/history',
  },
} as const;

// Manga statuses
export const MANGA_STATUS = {
  ONGOING: 'ongoing',
  COMPLETED: 'completed',
  HIATUS: 'hiatus',
  CANCELLED: 'cancelled',
} as const;

// User roles
export const USER_ROLES = {
  READER: 'reader',
  MODERATOR: 'moderator',
  ADMIN: 'admin',
} as const;

// Reading modes
export const READING_MODES = {
  SINGLE: 'single',
  DOUBLE: 'double',
  WEBTOON: 'webtoon',
} as const;

// Reading directions
export const READING_DIRECTIONS = {
  LTR: 'ltr',
  RTL: 'rtl',
} as const;

// Page fit options
export const PAGE_FIT_OPTIONS = {
  WIDTH: 'width',
  HEIGHT: 'height',
  AUTO: 'auto',
} as const;

// Sort options
export const SORT_OPTIONS = {
  TITLE: 'title',
  RATING: 'rating',
  VIEWS: 'views',
  UPDATED: 'updated',
  CREATED: 'created',
} as const;

// Sort orders
export const SORT_ORDERS = {
  ASC: 'asc',
  DESC: 'desc',
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
} as const;

// Local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth-token',
  USER_PREFERENCES: 'user-preferences',
  READING_SETTINGS: 'reading-settings',
  THEME: 'theme',
  LANGUAGE: 'language',
} as const;

// Theme options
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const;

// Supported languages
export const LANGUAGES = {
  EN: 'en',
  VI: 'vi',
} as const;

// Image placeholder
export const PLACEHOLDER_IMAGE = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5ObyBJbWFnZTwvdGV4dD48L3N2Zz4=';

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Lỗi kết nối mạng',
  UNAUTHORIZED: 'Bạn cần đăng nhập để thực hiện hành động này',
  FORBIDDEN: 'Bạn không có quyền thực hiện hành động này',
  NOT_FOUND: 'Không tìm thấy tài nguyên',
  SERVER_ERROR: 'Lỗi máy chủ, vui lòng thử lại sau',
  VALIDATION_ERROR: 'Dữ liệu không hợp lệ',
} as const;
