import{Q as is,j as a,a as os}from"./query-CjHflsNX.js";import{r as P,R as tt,L as H,O as Fe,u as as,N as Te,a as ls,c as cs,b as ds}from"./router-BU9vYC2A.js";import{a as us}from"./vendor-BtP0CW_r.js";import{S as hs,P as fs}from"./ui-Dh31QtRt.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))r(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function t(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(n){if(n.ep)return;n.ep=!0;const i=t(n);fetch(n.href,i)}})();var Le={},st;function gs(){if(st)return Le;st=1;var s=us();return Le.createRoot=s.createRoot,Le.hydrateRoot=s.hydrateRoot,Le}var ms=gs();const ps=new is({defaultOptions:{queries:{staleTime:5*60*1e3,gcTime:10*60*1e3,retry:(s,e)=>(e==null?void 0:e.status)>=400&&(e==null?void 0:e.status)<500?!1:s<3,refetchOnWindowFocus:!1},mutations:{retry:!1}}});function Et(s){var e,t,r="";if(typeof s=="string"||typeof s=="number")r+=s;else if(typeof s=="object")if(Array.isArray(s)){var n=s.length;for(e=0;e<n;e++)s[e]&&(t=Et(s[e]))&&(r&&(r+=" "),r+=t)}else for(t in s)s[t]&&(r&&(r+=" "),r+=t);return r}function It(){for(var s,e,t=0,r="",n=arguments.length;t<n;t++)(s=arguments[t])&&(e=Et(s))&&(r&&(r+=" "),r+=e);return r}const rt=s=>typeof s=="boolean"?`${s}`:s===0?"0":s,nt=It,Ft=(s,e)=>t=>{var r;if((e==null?void 0:e.variants)==null)return nt(s,t==null?void 0:t.class,t==null?void 0:t.className);const{variants:n,defaultVariants:i}=e,o=Object.keys(n).map(c=>{const u=t==null?void 0:t[c],h=i==null?void 0:i[c];if(u===null)return null;const g=rt(u)||rt(h);return n[c][g]}),l=t&&Object.entries(t).reduce((c,u)=>{let[h,g]=u;return g===void 0||(c[h]=g),c},{}),d=e==null||(r=e.compoundVariants)===null||r===void 0?void 0:r.reduce((c,u)=>{let{class:h,className:g,...m}=u;return Object.entries(m).every(f=>{let[p,k]=f;return Array.isArray(k)?k.includes({...i,...l}[p]):{...i,...l}[p]===k})?[...c,h,g]:c},[]);return nt(s,o,d,t==null?void 0:t.class,t==null?void 0:t.className)},Ye="-",xs=s=>{const e=ys(s),{conflictingClassGroups:t,conflictingClassGroupModifiers:r}=s;return{getClassGroupId:o=>{const l=o.split(Ye);return l[0]===""&&l.length!==1&&l.shift(),Mt(l,e)||bs(o)},getConflictingClassGroupIds:(o,l)=>{const d=t[o]||[];return l&&r[o]?[...d,...r[o]]:d}}},Mt=(s,e)=>{var o;if(s.length===0)return e.classGroupId;const t=s[0],r=e.nextPart.get(t),n=r?Mt(s.slice(1),r):void 0;if(n)return n;if(e.validators.length===0)return;const i=s.join(Ye);return(o=e.validators.find(({validator:l})=>l(i)))==null?void 0:o.classGroupId},it=/^\[(.+)\]$/,bs=s=>{if(it.test(s)){const e=it.exec(s)[1],t=e==null?void 0:e.substring(0,e.indexOf(":"));if(t)return"arbitrary.."+t}},ys=s=>{const{theme:e,classGroups:t}=s,r={nextPart:new Map,validators:[]};for(const n in t)He(t[n],r,n,e);return r},He=(s,e,t,r)=>{s.forEach(n=>{if(typeof n=="string"){const i=n===""?e:ot(e,n);i.classGroupId=t;return}if(typeof n=="function"){if(vs(n)){He(n(r),e,t,r);return}e.validators.push({validator:n,classGroupId:t});return}Object.entries(n).forEach(([i,o])=>{He(o,ot(e,i),t,r)})})},ot=(s,e)=>{let t=s;return e.split(Ye).forEach(r=>{t.nextPart.has(r)||t.nextPart.set(r,{nextPart:new Map,validators:[]}),t=t.nextPart.get(r)}),t},vs=s=>s.isThemeGetter,ws=s=>{if(s<1)return{get:()=>{},set:()=>{}};let e=0,t=new Map,r=new Map;const n=(i,o)=>{t.set(i,o),e++,e>s&&(e=0,r=t,t=new Map)};return{get(i){let o=t.get(i);if(o!==void 0)return o;if((o=r.get(i))!==void 0)return n(i,o),o},set(i,o){t.has(i)?t.set(i,o):n(i,o)}}},Ke="!",_e=":",ks=_e.length,Ns=s=>{const{prefix:e,experimentalParseClassName:t}=s;let r=n=>{const i=[];let o=0,l=0,d=0,c;for(let f=0;f<n.length;f++){let p=n[f];if(o===0&&l===0){if(p===_e){i.push(n.slice(d,f)),d=f+ks;continue}if(p==="/"){c=f;continue}}p==="["?o++:p==="]"?o--:p==="("?l++:p===")"&&l--}const u=i.length===0?n:n.substring(d),h=js(u),g=h!==u,m=c&&c>d?c-d:void 0;return{modifiers:i,hasImportantModifier:g,baseClassName:h,maybePostfixModifierPosition:m}};if(e){const n=e+_e,i=r;r=o=>o.startsWith(n)?i(o.substring(n.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:o,maybePostfixModifierPosition:void 0}}if(t){const n=r;r=i=>t({className:i,parseClassName:n})}return r},js=s=>s.endsWith(Ke)?s.substring(0,s.length-1):s.startsWith(Ke)?s.substring(1):s,Ss=s=>{const e=Object.fromEntries(s.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const n=[];let i=[];return r.forEach(o=>{o[0]==="["||e[o]?(n.push(...i.sort(),o),i=[]):i.push(o)}),n.push(...i.sort()),n}},Cs=s=>({cache:ws(s.cacheSize),parseClassName:Ns(s),sortModifiers:Ss(s),...xs(s)}),Ls=/\s+/,Os=(s,e)=>{const{parseClassName:t,getClassGroupId:r,getConflictingClassGroupIds:n,sortModifiers:i}=e,o=[],l=s.trim().split(Ls);let d="";for(let c=l.length-1;c>=0;c-=1){const u=l[c],{isExternal:h,modifiers:g,hasImportantModifier:m,baseClassName:f,maybePostfixModifierPosition:p}=t(u);if(h){d=u+(d.length>0?" "+d:d);continue}let k=!!p,R=r(k?f.substring(0,p):f);if(!R){if(!k){d=u+(d.length>0?" "+d:d);continue}if(R=r(f),!R){d=u+(d.length>0?" "+d:d);continue}k=!1}const N=i(g).join(":"),C=m?N+Ke:N,L=C+R;if(o.includes(L))continue;o.push(L);const T=n(R,k);for(let U=0;U<T.length;++U){const I=T[U];o.push(C+I)}d=u+(d.length>0?" "+d:d)}return d};function Rs(){let s=0,e,t,r="";for(;s<arguments.length;)(e=arguments[s++])&&(t=zt(e))&&(r&&(r+=" "),r+=t);return r}const zt=s=>{if(typeof s=="string")return s;let e,t="";for(let r=0;r<s.length;r++)s[r]&&(e=zt(s[r]))&&(t&&(t+=" "),t+=e);return t};function Ps(s,...e){let t,r,n,i=o;function o(d){const c=e.reduce((u,h)=>h(u),s());return t=Cs(c),r=t.cache.get,n=t.cache.set,i=l,l(d)}function l(d){const c=r(d);if(c)return c;const u=Os(d,t);return n(d,u),u}return function(){return i(Rs.apply(null,arguments))}}const E=s=>{const e=t=>t[s]||[];return e.isThemeGetter=!0,e},Dt=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Vt=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Ts=/^\d+\/\d+$/,$s=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,As=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Es=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Is=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Fs=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ue=s=>Ts.test(s),j=s=>!!s&&!Number.isNaN(Number(s)),te=s=>!!s&&Number.isInteger(Number(s)),Ve=s=>s.endsWith("%")&&j(s.slice(0,-1)),X=s=>$s.test(s),Ms=()=>!0,zs=s=>As.test(s)&&!Es.test(s),Ut=()=>!1,Ds=s=>Is.test(s),Vs=s=>Fs.test(s),Us=s=>!x(s)&&!b(s),Bs=s=>me(s,Kt,Ut),x=s=>Dt.test(s),oe=s=>me(s,_t,zs),Ue=s=>me(s,Ws,j),at=s=>me(s,Bt,Ut),Hs=s=>me(s,Ht,Vs),Oe=s=>me(s,Gt,Ds),b=s=>Vt.test(s),be=s=>pe(s,_t),Ks=s=>pe(s,qs),lt=s=>pe(s,Bt),_s=s=>pe(s,Kt),Gs=s=>pe(s,Ht),Re=s=>pe(s,Gt,!0),me=(s,e,t)=>{const r=Dt.exec(s);return r?r[1]?e(r[1]):t(r[2]):!1},pe=(s,e,t=!1)=>{const r=Vt.exec(s);return r?r[1]?e(r[1]):t:!1},Bt=s=>s==="position"||s==="percentage",Ht=s=>s==="image"||s==="url",Kt=s=>s==="length"||s==="size"||s==="bg-size",_t=s=>s==="length",Ws=s=>s==="number",qs=s=>s==="family-name",Gt=s=>s==="shadow",Js=()=>{const s=E("color"),e=E("font"),t=E("text"),r=E("font-weight"),n=E("tracking"),i=E("leading"),o=E("breakpoint"),l=E("container"),d=E("spacing"),c=E("radius"),u=E("shadow"),h=E("inset-shadow"),g=E("text-shadow"),m=E("drop-shadow"),f=E("blur"),p=E("perspective"),k=E("aspect"),R=E("ease"),N=E("animate"),C=()=>["auto","avoid","all","avoid-page","page","left","right","column"],L=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],T=()=>[...L(),b,x],U=()=>["auto","hidden","clip","visible","scroll"],I=()=>["auto","contain","none"],y=()=>[b,x,d],S=()=>[ue,"full","auto",...y()],O=()=>[te,"none","subgrid",b,x],K=()=>["auto",{span:["full",te,b,x]},te,b,x],de=()=>[te,"auto",b,x],_=()=>["auto","min","max","fr",b,x],B=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Q=()=>["start","end","center","stretch","center-safe","end-safe"],A=()=>["auto",...y()],M=()=>[ue,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...y()],v=()=>[s,b,x],ne=()=>[...L(),lt,at,{position:[b,x]}],xe=()=>["no-repeat",{repeat:["","x","y","space","round"]}],z=()=>["auto","cover","contain",_s,Bs,{size:[b,x]}],q=()=>[Ve,be,oe],$=()=>["","none","full",c,b,x],D=()=>["",j,be,oe],ie=()=>["solid","dashed","dotted","double"],Ze=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],F=()=>[j,Ve,lt,at],et=()=>["","none",f,b,x],je=()=>["none",j,b,x],Se=()=>["none",j,b,x],De=()=>[j,b,x],Ce=()=>[ue,"full",...y()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[X],breakpoint:[X],color:[Ms],container:[X],"drop-shadow":[X],ease:["in","out","in-out"],font:[Us],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[X],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[X],shadow:[X],spacing:["px",j],text:[X],"text-shadow":[X],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",ue,x,b,k]}],container:["container"],columns:[{columns:[j,x,b,l]}],"break-after":[{"break-after":C()}],"break-before":[{"break-before":C()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:T()}],overflow:[{overflow:U()}],"overflow-x":[{"overflow-x":U()}],"overflow-y":[{"overflow-y":U()}],overscroll:[{overscroll:I()}],"overscroll-x":[{"overscroll-x":I()}],"overscroll-y":[{"overscroll-y":I()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[te,"auto",b,x]}],basis:[{basis:[ue,"full","auto",l,...y()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[j,ue,"auto","initial","none",x]}],grow:[{grow:["",j,b,x]}],shrink:[{shrink:["",j,b,x]}],order:[{order:[te,"first","last","none",b,x]}],"grid-cols":[{"grid-cols":O()}],"col-start-end":[{col:K()}],"col-start":[{"col-start":de()}],"col-end":[{"col-end":de()}],"grid-rows":[{"grid-rows":O()}],"row-start-end":[{row:K()}],"row-start":[{"row-start":de()}],"row-end":[{"row-end":de()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":_()}],"auto-rows":[{"auto-rows":_()}],gap:[{gap:y()}],"gap-x":[{"gap-x":y()}],"gap-y":[{"gap-y":y()}],"justify-content":[{justify:[...B(),"normal"]}],"justify-items":[{"justify-items":[...Q(),"normal"]}],"justify-self":[{"justify-self":["auto",...Q()]}],"align-content":[{content:["normal",...B()]}],"align-items":[{items:[...Q(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Q(),{baseline:["","last"]}]}],"place-content":[{"place-content":B()}],"place-items":[{"place-items":[...Q(),"baseline"]}],"place-self":[{"place-self":["auto",...Q()]}],p:[{p:y()}],px:[{px:y()}],py:[{py:y()}],ps:[{ps:y()}],pe:[{pe:y()}],pt:[{pt:y()}],pr:[{pr:y()}],pb:[{pb:y()}],pl:[{pl:y()}],m:[{m:A()}],mx:[{mx:A()}],my:[{my:A()}],ms:[{ms:A()}],me:[{me:A()}],mt:[{mt:A()}],mr:[{mr:A()}],mb:[{mb:A()}],ml:[{ml:A()}],"space-x":[{"space-x":y()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":y()}],"space-y-reverse":["space-y-reverse"],size:[{size:M()}],w:[{w:[l,"screen",...M()]}],"min-w":[{"min-w":[l,"screen","none",...M()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[o]},...M()]}],h:[{h:["screen","lh",...M()]}],"min-h":[{"min-h":["screen","lh","none",...M()]}],"max-h":[{"max-h":["screen","lh",...M()]}],"font-size":[{text:["base",t,be,oe]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,b,Ue]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Ve,x]}],"font-family":[{font:[Ks,x,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,b,x]}],"line-clamp":[{"line-clamp":[j,"none",b,Ue]}],leading:[{leading:[i,...y()]}],"list-image":[{"list-image":["none",b,x]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",b,x]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:v()}],"text-color":[{text:v()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ie(),"wavy"]}],"text-decoration-thickness":[{decoration:[j,"from-font","auto",b,oe]}],"text-decoration-color":[{decoration:v()}],"underline-offset":[{"underline-offset":[j,"auto",b,x]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:y()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",b,x]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",b,x]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ne()}],"bg-repeat":[{bg:xe()}],"bg-size":[{bg:z()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},te,b,x],radial:["",b,x],conic:[te,b,x]},Gs,Hs]}],"bg-color":[{bg:v()}],"gradient-from-pos":[{from:q()}],"gradient-via-pos":[{via:q()}],"gradient-to-pos":[{to:q()}],"gradient-from":[{from:v()}],"gradient-via":[{via:v()}],"gradient-to":[{to:v()}],rounded:[{rounded:$()}],"rounded-s":[{"rounded-s":$()}],"rounded-e":[{"rounded-e":$()}],"rounded-t":[{"rounded-t":$()}],"rounded-r":[{"rounded-r":$()}],"rounded-b":[{"rounded-b":$()}],"rounded-l":[{"rounded-l":$()}],"rounded-ss":[{"rounded-ss":$()}],"rounded-se":[{"rounded-se":$()}],"rounded-ee":[{"rounded-ee":$()}],"rounded-es":[{"rounded-es":$()}],"rounded-tl":[{"rounded-tl":$()}],"rounded-tr":[{"rounded-tr":$()}],"rounded-br":[{"rounded-br":$()}],"rounded-bl":[{"rounded-bl":$()}],"border-w":[{border:D()}],"border-w-x":[{"border-x":D()}],"border-w-y":[{"border-y":D()}],"border-w-s":[{"border-s":D()}],"border-w-e":[{"border-e":D()}],"border-w-t":[{"border-t":D()}],"border-w-r":[{"border-r":D()}],"border-w-b":[{"border-b":D()}],"border-w-l":[{"border-l":D()}],"divide-x":[{"divide-x":D()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":D()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ie(),"hidden","none"]}],"divide-style":[{divide:[...ie(),"hidden","none"]}],"border-color":[{border:v()}],"border-color-x":[{"border-x":v()}],"border-color-y":[{"border-y":v()}],"border-color-s":[{"border-s":v()}],"border-color-e":[{"border-e":v()}],"border-color-t":[{"border-t":v()}],"border-color-r":[{"border-r":v()}],"border-color-b":[{"border-b":v()}],"border-color-l":[{"border-l":v()}],"divide-color":[{divide:v()}],"outline-style":[{outline:[...ie(),"none","hidden"]}],"outline-offset":[{"outline-offset":[j,b,x]}],"outline-w":[{outline:["",j,be,oe]}],"outline-color":[{outline:v()}],shadow:[{shadow:["","none",u,Re,Oe]}],"shadow-color":[{shadow:v()}],"inset-shadow":[{"inset-shadow":["none",h,Re,Oe]}],"inset-shadow-color":[{"inset-shadow":v()}],"ring-w":[{ring:D()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:v()}],"ring-offset-w":[{"ring-offset":[j,oe]}],"ring-offset-color":[{"ring-offset":v()}],"inset-ring-w":[{"inset-ring":D()}],"inset-ring-color":[{"inset-ring":v()}],"text-shadow":[{"text-shadow":["none",g,Re,Oe]}],"text-shadow-color":[{"text-shadow":v()}],opacity:[{opacity:[j,b,x]}],"mix-blend":[{"mix-blend":[...Ze(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Ze()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[j]}],"mask-image-linear-from-pos":[{"mask-linear-from":F()}],"mask-image-linear-to-pos":[{"mask-linear-to":F()}],"mask-image-linear-from-color":[{"mask-linear-from":v()}],"mask-image-linear-to-color":[{"mask-linear-to":v()}],"mask-image-t-from-pos":[{"mask-t-from":F()}],"mask-image-t-to-pos":[{"mask-t-to":F()}],"mask-image-t-from-color":[{"mask-t-from":v()}],"mask-image-t-to-color":[{"mask-t-to":v()}],"mask-image-r-from-pos":[{"mask-r-from":F()}],"mask-image-r-to-pos":[{"mask-r-to":F()}],"mask-image-r-from-color":[{"mask-r-from":v()}],"mask-image-r-to-color":[{"mask-r-to":v()}],"mask-image-b-from-pos":[{"mask-b-from":F()}],"mask-image-b-to-pos":[{"mask-b-to":F()}],"mask-image-b-from-color":[{"mask-b-from":v()}],"mask-image-b-to-color":[{"mask-b-to":v()}],"mask-image-l-from-pos":[{"mask-l-from":F()}],"mask-image-l-to-pos":[{"mask-l-to":F()}],"mask-image-l-from-color":[{"mask-l-from":v()}],"mask-image-l-to-color":[{"mask-l-to":v()}],"mask-image-x-from-pos":[{"mask-x-from":F()}],"mask-image-x-to-pos":[{"mask-x-to":F()}],"mask-image-x-from-color":[{"mask-x-from":v()}],"mask-image-x-to-color":[{"mask-x-to":v()}],"mask-image-y-from-pos":[{"mask-y-from":F()}],"mask-image-y-to-pos":[{"mask-y-to":F()}],"mask-image-y-from-color":[{"mask-y-from":v()}],"mask-image-y-to-color":[{"mask-y-to":v()}],"mask-image-radial":[{"mask-radial":[b,x]}],"mask-image-radial-from-pos":[{"mask-radial-from":F()}],"mask-image-radial-to-pos":[{"mask-radial-to":F()}],"mask-image-radial-from-color":[{"mask-radial-from":v()}],"mask-image-radial-to-color":[{"mask-radial-to":v()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":L()}],"mask-image-conic-pos":[{"mask-conic":[j]}],"mask-image-conic-from-pos":[{"mask-conic-from":F()}],"mask-image-conic-to-pos":[{"mask-conic-to":F()}],"mask-image-conic-from-color":[{"mask-conic-from":v()}],"mask-image-conic-to-color":[{"mask-conic-to":v()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ne()}],"mask-repeat":[{mask:xe()}],"mask-size":[{mask:z()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",b,x]}],filter:[{filter:["","none",b,x]}],blur:[{blur:et()}],brightness:[{brightness:[j,b,x]}],contrast:[{contrast:[j,b,x]}],"drop-shadow":[{"drop-shadow":["","none",m,Re,Oe]}],"drop-shadow-color":[{"drop-shadow":v()}],grayscale:[{grayscale:["",j,b,x]}],"hue-rotate":[{"hue-rotate":[j,b,x]}],invert:[{invert:["",j,b,x]}],saturate:[{saturate:[j,b,x]}],sepia:[{sepia:["",j,b,x]}],"backdrop-filter":[{"backdrop-filter":["","none",b,x]}],"backdrop-blur":[{"backdrop-blur":et()}],"backdrop-brightness":[{"backdrop-brightness":[j,b,x]}],"backdrop-contrast":[{"backdrop-contrast":[j,b,x]}],"backdrop-grayscale":[{"backdrop-grayscale":["",j,b,x]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[j,b,x]}],"backdrop-invert":[{"backdrop-invert":["",j,b,x]}],"backdrop-opacity":[{"backdrop-opacity":[j,b,x]}],"backdrop-saturate":[{"backdrop-saturate":[j,b,x]}],"backdrop-sepia":[{"backdrop-sepia":["",j,b,x]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":y()}],"border-spacing-x":[{"border-spacing-x":y()}],"border-spacing-y":[{"border-spacing-y":y()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",b,x]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[j,"initial",b,x]}],ease:[{ease:["linear","initial",R,b,x]}],delay:[{delay:[j,b,x]}],animate:[{animate:["none",N,b,x]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,b,x]}],"perspective-origin":[{"perspective-origin":T()}],rotate:[{rotate:je()}],"rotate-x":[{"rotate-x":je()}],"rotate-y":[{"rotate-y":je()}],"rotate-z":[{"rotate-z":je()}],scale:[{scale:Se()}],"scale-x":[{"scale-x":Se()}],"scale-y":[{"scale-y":Se()}],"scale-z":[{"scale-z":Se()}],"scale-3d":["scale-3d"],skew:[{skew:De()}],"skew-x":[{"skew-x":De()}],"skew-y":[{"skew-y":De()}],transform:[{transform:[b,x,"","none","gpu","cpu"]}],"transform-origin":[{origin:T()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ce()}],"translate-x":[{"translate-x":Ce()}],"translate-y":[{"translate-y":Ce()}],"translate-z":[{"translate-z":Ce()}],"translate-none":["translate-none"],accent:[{accent:v()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:v()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",b,x]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":y()}],"scroll-mx":[{"scroll-mx":y()}],"scroll-my":[{"scroll-my":y()}],"scroll-ms":[{"scroll-ms":y()}],"scroll-me":[{"scroll-me":y()}],"scroll-mt":[{"scroll-mt":y()}],"scroll-mr":[{"scroll-mr":y()}],"scroll-mb":[{"scroll-mb":y()}],"scroll-ml":[{"scroll-ml":y()}],"scroll-p":[{"scroll-p":y()}],"scroll-px":[{"scroll-px":y()}],"scroll-py":[{"scroll-py":y()}],"scroll-ps":[{"scroll-ps":y()}],"scroll-pe":[{"scroll-pe":y()}],"scroll-pt":[{"scroll-pt":y()}],"scroll-pr":[{"scroll-pr":y()}],"scroll-pb":[{"scroll-pb":y()}],"scroll-pl":[{"scroll-pl":y()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",b,x]}],fill:[{fill:["none",...v()]}],"stroke-w":[{stroke:[j,be,oe,Ue]}],stroke:[{stroke:["none",...v()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Qs=Ps(Js);function ee(...s){return Qs(It(s))}const Xs=Ft("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),G=P.forwardRef(({className:s,variant:e,size:t,asChild:r=!1,...n},i)=>{const o=r?hs:"button";return a.jsx(o,{className:ee(Xs({variant:e,size:t,className:s})),ref:i,...n})});G.displayName="Button";const Y=P.forwardRef(({className:s,...e},t)=>a.jsx("div",{ref:t,className:ee("rounded-lg border bg-card text-card-foreground shadow-sm",s),...e}));Y.displayName="Card";const ae=P.forwardRef(({className:s,...e},t)=>a.jsx("div",{ref:t,className:ee("flex flex-col space-y-1.5 p-6",s),...e}));ae.displayName="CardHeader";const se=P.forwardRef(({className:s,...e},t)=>a.jsx("h3",{ref:t,className:ee("text-2xl font-semibold leading-none tracking-tight",s),...e}));se.displayName="CardTitle";const le=P.forwardRef(({className:s,...e},t)=>a.jsx("p",{ref:t,className:ee("text-sm text-muted-foreground",s),...e}));le.displayName="CardDescription";const Z=P.forwardRef(({className:s,...e},t)=>a.jsx("div",{ref:t,className:ee("p-6 pt-0",s),...e}));Z.displayName="CardContent";const Ys=P.forwardRef(({className:s,...e},t)=>a.jsx("div",{ref:t,className:ee("flex items-center p-6 pt-0",s),...e}));Ys.displayName="CardFooter";const $e=P.forwardRef(({className:s,type:e,...t},r)=>a.jsx("input",{type:e,className:ee("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...t}));$e.displayName="Input";var Zs="Label",Wt=P.forwardRef((s,e)=>a.jsx(fs.label,{...s,ref:e,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||((n=s.onMouseDown)==null||n.call(s,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));Wt.displayName=Zs;var qt=Wt;const er=Ft("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Ge=P.forwardRef(({className:s,...e},t)=>a.jsx(qt,{ref:t,className:ee(er(),s),...e}));Ge.displayName=qt.displayName;const tr=(s,e,t,r)=>{var i,o,l,d;const n=[t,{code:e,...r||{}}];if((o=(i=s==null?void 0:s.services)==null?void 0:i.logger)!=null&&o.forward)return s.services.logger.forward(n,"warn","react-i18next::",!0);ce(n[0])&&(n[0]=`react-i18next:: ${n[0]}`),(d=(l=s==null?void 0:s.services)==null?void 0:l.logger)!=null&&d.warn?s.services.logger.warn(...n):console!=null&&console.warn&&console.warn(...n)},ct={},We=(s,e,t,r)=>{ce(t)&&ct[t]||(ce(t)&&(ct[t]=new Date),tr(s,e,t,r))},Jt=(s,e)=>()=>{if(s.isInitialized)e();else{const t=()=>{setTimeout(()=>{s.off("initialized",t)},0),e()};s.on("initialized",t)}},qe=(s,e,t)=>{s.loadNamespaces(e,Jt(s,t))},dt=(s,e,t,r)=>{if(ce(t)&&(t=[t]),s.options.preload&&s.options.preload.indexOf(e)>-1)return qe(s,t,r);t.forEach(n=>{s.options.ns.indexOf(n)<0&&s.options.ns.push(n)}),s.loadLanguages(e,Jt(s,r))},sr=(s,e,t={})=>!e.languages||!e.languages.length?(We(e,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:e.languages}),!0):e.hasLoadedNamespace(s,{lng:t.lng,precheck:(r,n)=>{var i;if(((i=t.bindI18n)==null?void 0:i.indexOf("languageChanging"))>-1&&r.services.backendConnector.backend&&r.isLanguageChangingTo&&!n(r.isLanguageChangingTo,s))return!1}}),ce=s=>typeof s=="string",rr=s=>typeof s=="object"&&s!==null,nr=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,ir={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},or=s=>ir[s],ar=s=>s.replace(nr,or);let Je={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:ar};const lr=(s={})=>{Je={...Je,...s}},cr=()=>Je;let Qt;const dr=s=>{Qt=s},ur=()=>Qt,hr={type:"3rdParty",init(s){lr(s.options.react),dr(s)}},fr=P.createContext();class gr{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(t=>{this.usedNamespaces[t]||(this.usedNamespaces[t]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const mr=(s,e)=>{const t=P.useRef();return P.useEffect(()=>{t.current=s},[s,e]),t.current},Xt=(s,e,t,r)=>s.getFixedT(e,t,r),pr=(s,e,t,r)=>P.useCallback(Xt(s,e,t,r),[s,e,t,r]),re=(s,e={})=>{var L,T,U,I;const{i18n:t}=e,{i18n:r,defaultNS:n}=P.useContext(fr)||{},i=t||r||ur();if(i&&!i.reportNamespaces&&(i.reportNamespaces=new gr),!i){We(i,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const y=(O,K)=>ce(K)?K:rr(K)&&ce(K.defaultValue)?K.defaultValue:Array.isArray(O)?O[O.length-1]:O,S=[y,{},!1];return S.t=y,S.i18n={},S.ready=!1,S}(L=i.options.react)!=null&&L.wait&&We(i,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const o={...cr(),...i.options.react,...e},{useSuspense:l,keyPrefix:d}=o;let c=s||n||((T=i.options)==null?void 0:T.defaultNS);c=ce(c)?[c]:c||["translation"],(I=(U=i.reportNamespaces).addUsedNamespaces)==null||I.call(U,c);const u=(i.isInitialized||i.initializedStoreOnce)&&c.every(y=>sr(y,i,o)),h=pr(i,e.lng||null,o.nsMode==="fallback"?c:c[0],d),g=()=>h,m=()=>Xt(i,e.lng||null,o.nsMode==="fallback"?c:c[0],d),[f,p]=P.useState(g);let k=c.join();e.lng&&(k=`${e.lng}${k}`);const R=mr(k),N=P.useRef(!0);P.useEffect(()=>{const{bindI18n:y,bindI18nStore:S}=o;N.current=!0,!u&&!l&&(e.lng?dt(i,e.lng,c,()=>{N.current&&p(m)}):qe(i,c,()=>{N.current&&p(m)})),u&&R&&R!==k&&N.current&&p(m);const O=()=>{N.current&&p(m)};return y&&(i==null||i.on(y,O)),S&&(i==null||i.store.on(S,O)),()=>{N.current=!1,i&&(y==null||y.split(" ").forEach(K=>i.off(K,O))),S&&i&&S.split(" ").forEach(K=>i.store.off(K,O))}},[i,k]),P.useEffect(()=>{N.current&&u&&p(g)},[i,d,u]);const C=[f,i,u];if(C.t=f,C.i18n=i,C.ready=u,u||!u&&!l)return C;throw new Promise(y=>{e.lng?dt(i,e.lng,c,()=>y()):qe(i,c,()=>y())})};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xr=s=>s.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),br=s=>s.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),ut=s=>{const e=br(s);return e.charAt(0).toUpperCase()+e.slice(1)},Yt=(...s)=>s.filter((e,t,r)=>!!e&&e.trim()!==""&&r.indexOf(e)===t).join(" ").trim(),yr=s=>{for(const e in s)if(e.startsWith("aria-")||e==="role"||e==="title")return!0};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var vr={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wr=P.forwardRef(({color:s="currentColor",size:e=24,strokeWidth:t=2,absoluteStrokeWidth:r,className:n="",children:i,iconNode:o,...l},d)=>P.createElement("svg",{ref:d,...vr,width:e,height:e,stroke:s,strokeWidth:r?Number(t)*24/Number(e):t,className:Yt("lucide",n),...!i&&!yr(l)&&{"aria-hidden":"true"},...l},[...o.map(([c,u])=>P.createElement(c,u)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W=(s,e)=>{const t=P.forwardRef(({className:r,...n},i)=>P.createElement(wr,{ref:i,iconNode:e,className:Yt(`lucide-${xr(ut(s))}`,`lucide-${s}`,r),...n}));return t.displayName=ut(s),t};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kr=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],Nr=W("arrow-left",kr);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jr=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],we=W("book-open",jr);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sr=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],Cr=W("clock",Sr);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lr=[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]],Or=W("github",Lr);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rr=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],Pr=W("house",Rr);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tr=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],$r=W("mail",Tr);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ar=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Er=W("menu",Ar);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ir=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Fr=W("search",Ir);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mr=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],ht=W("star",Mr);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zr=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],Dr=W("trending-up",zr);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vr=[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]],Ur=W("twitter",Vr);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Br=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],Hr=W("user",Br),ft=s=>{let e;const t=new Set,r=(c,u)=>{const h=typeof c=="function"?c(e):c;if(!Object.is(h,e)){const g=e;e=u??(typeof h!="object"||h===null)?h:Object.assign({},e,h),t.forEach(m=>m(e,g))}},n=()=>e,l={setState:r,getState:n,getInitialState:()=>d,subscribe:c=>(t.add(c),()=>t.delete(c))},d=e=s(r,n,l);return l},Kr=s=>s?ft(s):ft,_r=s=>s;function Gr(s,e=_r){const t=tt.useSyncExternalStore(s.subscribe,()=>e(s.getState()),()=>e(s.getInitialState()));return tt.useDebugValue(t),t}const Wr=s=>{const e=Kr(s),t=r=>Gr(e,r);return Object.assign(t,e),t},qr=s=>Wr;function Jr(s,e){let t;try{t=s()}catch{return}return{getItem:n=>{var i;const o=d=>d===null?null:JSON.parse(d,void 0),l=(i=t.getItem(n))!=null?i:null;return l instanceof Promise?l.then(o):o(l)},setItem:(n,i)=>t.setItem(n,JSON.stringify(i,void 0)),removeItem:n=>t.removeItem(n)}}const Qe=s=>e=>{try{const t=s(e);return t instanceof Promise?t:{then(r){return Qe(r)(t)},catch(r){return this}}}catch(t){return{then(r){return this},catch(r){return Qe(r)(t)}}}},Qr=(s,e)=>(t,r,n)=>{let i={storage:Jr(()=>localStorage),partialize:p=>p,version:0,merge:(p,k)=>({...k,...p}),...e},o=!1;const l=new Set,d=new Set;let c=i.storage;if(!c)return s((...p)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),t(...p)},r,n);const u=()=>{const p=i.partialize({...r()});return c.setItem(i.name,{state:p,version:i.version})},h=n.setState;n.setState=(p,k)=>{h(p,k),u()};const g=s((...p)=>{t(...p),u()},r,n);n.getInitialState=()=>g;let m;const f=()=>{var p,k;if(!c)return;o=!1,l.forEach(N=>{var C;return N((C=r())!=null?C:g)});const R=((k=i.onRehydrateStorage)==null?void 0:k.call(i,(p=r())!=null?p:g))||void 0;return Qe(c.getItem.bind(c))(i.name).then(N=>{if(N)if(typeof N.version=="number"&&N.version!==i.version){if(i.migrate){const C=i.migrate(N.state,N.version);return C instanceof Promise?C.then(L=>[!0,L]):[!0,C]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,N.state];return[!1,void 0]}).then(N=>{var C;const[L,T]=N;if(m=i.merge(T,(C=r())!=null?C:g),t(m,!0),L)return u()}).then(()=>{R==null||R(m,void 0),m=r(),o=!0,d.forEach(N=>N(m))}).catch(N=>{R==null||R(void 0,N)})};return n.persist={setOptions:p=>{i={...i,...p},p.storage&&(c=p.storage)},clearStorage:()=>{c==null||c.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>f(),hasHydrated:()=>o,onHydrate:p=>(l.add(p),()=>{l.delete(p)}),onFinishHydration:p=>(d.add(p),()=>{d.delete(p)})},i.skipHydration||f(),m||g},Xr=Qr,Me=qr()(Xr((s,e)=>({user:null,isAuthenticated:!1,isLoading:!1,login:async t=>{s({isLoading:!0});try{console.log("Login with:",t);const r={id:"1",username:"testuser",email:t.email,role:"reader",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};s({user:r,isAuthenticated:!0,isLoading:!1})}catch(r){throw console.error("Login failed:",r),s({isLoading:!1}),r}},logout:()=>{s({user:null,isAuthenticated:!1})},updateProfile:async t=>{const{user:r}=e();if(r){s({isLoading:!0});try{const n={...r,...t};s({user:n,isLoading:!1})}catch(n){throw console.error("Profile update failed:",n),s({isLoading:!1}),n}}},setUser:t=>{s({user:t,isAuthenticated:!!t})}}),{name:"auth-storage",partialize:s=>({user:s.user,isAuthenticated:s.isAuthenticated})})),Zt=()=>{const{t:s}=re("common"),{user:e,isAuthenticated:t,logout:r}=Me();return a.jsx("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:a.jsxs("div",{className:"container flex h-16 items-center",children:[a.jsxs(H,{to:"/",className:"flex items-center space-x-2",children:[a.jsx(we,{className:"h-6 w-6"}),a.jsx("span",{className:"font-bold text-xl",children:"BlogTruyen"})]}),a.jsxs("nav",{className:"hidden md:flex items-center space-x-6 ml-8",children:[a.jsx(H,{to:"/",className:"text-sm font-medium transition-colors hover:text-primary",children:s("navigation.home")}),a.jsx(H,{to:"/browse",className:"text-sm font-medium transition-colors hover:text-primary",children:s("navigation.browse")}),t&&a.jsxs(a.Fragment,{children:[a.jsx(H,{to:"/user/bookmarks",className:"text-sm font-medium transition-colors hover:text-primary",children:s("navigation.bookmarks")}),a.jsx(H,{to:"/user/history",className:"text-sm font-medium transition-colors hover:text-primary",children:s("navigation.history")}),((e==null?void 0:e.role)==="admin"||(e==null?void 0:e.role)==="moderator")&&a.jsx(H,{to:"/admin",className:"text-sm font-medium transition-colors hover:text-primary",children:s("navigation.admin")})]})]}),a.jsx("div",{className:"flex-1 flex justify-center px-4",children:a.jsxs("div",{className:"relative w-full max-w-sm",children:[a.jsx(Fr,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),a.jsx($e,{placeholder:s("actions.search"),className:"pl-8"})]})}),a.jsxs("div",{className:"flex items-center space-x-2",children:[t?a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(H,{to:"/user/profile",children:a.jsxs(G,{variant:"ghost",size:"sm",children:[a.jsx(Hr,{className:"h-4 w-4 mr-2"}),e==null?void 0:e.username]})}),a.jsx(G,{variant:"ghost",size:"sm",onClick:r,children:s("navigation.logout")})]}):a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(H,{to:"/auth/login",children:a.jsx(G,{variant:"ghost",size:"sm",children:s("navigation.login")})}),a.jsx(H,{to:"/auth/register",children:a.jsx(G,{size:"sm",children:s("navigation.register")})})]}),a.jsx(G,{variant:"ghost",size:"icon",className:"md:hidden",children:a.jsx(Er,{className:"h-4 w-4"})})]})]})})},Yr=()=>{const{t:s}=re("common");return a.jsx("footer",{className:"border-t bg-background",children:a.jsxs("div",{className:"container py-8",children:[a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(we,{className:"h-6 w-6"}),a.jsx("span",{className:"font-bold text-xl",children:"BlogTruyen"})]}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Nền tảng đọc truyện tranh online hiện đại với trải nghiệm đọc tuyệt vời."})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"font-semibold",children:"Liên kết nhanh"}),a.jsxs("ul",{className:"space-y-2 text-sm",children:[a.jsx("li",{children:a.jsx("a",{href:"/",className:"text-muted-foreground hover:text-primary transition-colors",children:s("navigation.home")})}),a.jsx("li",{children:a.jsx("a",{href:"/browse",className:"text-muted-foreground hover:text-primary transition-colors",children:s("navigation.browse")})}),a.jsx("li",{children:a.jsx("a",{href:"/about",className:"text-muted-foreground hover:text-primary transition-colors",children:"Về chúng tôi"})}),a.jsx("li",{children:a.jsx("a",{href:"/contact",className:"text-muted-foreground hover:text-primary transition-colors",children:"Liên hệ"})})]})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"font-semibold",children:"Hỗ trợ"}),a.jsxs("ul",{className:"space-y-2 text-sm",children:[a.jsx("li",{children:a.jsx("a",{href:"/help",className:"text-muted-foreground hover:text-primary transition-colors",children:"Trợ giúp"})}),a.jsx("li",{children:a.jsx("a",{href:"/faq",className:"text-muted-foreground hover:text-primary transition-colors",children:"FAQ"})}),a.jsx("li",{children:a.jsx("a",{href:"/privacy",className:"text-muted-foreground hover:text-primary transition-colors",children:"Chính sách bảo mật"})}),a.jsx("li",{children:a.jsx("a",{href:"/terms",className:"text-muted-foreground hover:text-primary transition-colors",children:"Điều khoản sử dụng"})})]})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsx("h3",{className:"font-semibold",children:"Kết nối"}),a.jsxs("div",{className:"flex space-x-4",children:[a.jsx("a",{href:"#",className:"text-muted-foreground hover:text-primary transition-colors",children:a.jsx(Or,{className:"h-5 w-5"})}),a.jsx("a",{href:"#",className:"text-muted-foreground hover:text-primary transition-colors",children:a.jsx(Ur,{className:"h-5 w-5"})}),a.jsx("a",{href:"#",className:"text-muted-foreground hover:text-primary transition-colors",children:a.jsx($r,{className:"h-5 w-5"})})]})]})]}),a.jsx("div",{className:"border-t mt-8 pt-8 text-center text-sm text-muted-foreground",children:a.jsx("p",{children:"© 2024 BlogTruyen. Tất cả quyền được bảo lưu."})})]})})},gt=({showHeader:s=!0,showFooter:e=!0,className:t=""})=>a.jsxs("div",{className:`min-h-screen flex flex-col ${t}`,children:[s&&a.jsx(Zt,{}),a.jsx("main",{className:"flex-1",children:a.jsx(Fe,{})}),e&&a.jsx(Yr,{})]}),mt=({requireAuth:s=!1,redirectTo:e="/"})=>{const{isAuthenticated:t}=Me(),r=as();return s&&!t?a.jsx(Te,{to:"/auth/login",replace:!0}):!s&&t&&(r.pathname==="/auth/login"||r.pathname==="/auth/register")?a.jsx(Te,{to:e,replace:!0}):a.jsx(Fe,{})},Zr=({className:s=""})=>a.jsx("div",{className:`min-h-screen bg-background ${s}`,children:a.jsx(Fe,{})}),en=({className:s=""})=>{const{user:e,isAuthenticated:t}=Me();return t?(e==null?void 0:e.role)!=="admin"&&(e==null?void 0:e.role)!=="moderator"?a.jsx(Te,{to:"/",replace:!0}):a.jsxs("div",{className:`min-h-screen flex flex-col ${s}`,children:[a.jsx(Zt,{}),a.jsxs("div",{className:"flex flex-1",children:[a.jsx("aside",{className:"w-64 bg-muted border-r",children:a.jsxs("nav",{className:"p-4",children:[a.jsx("h2",{className:"font-semibold text-lg mb-4",children:"Admin Panel"}),a.jsxs("ul",{className:"space-y-2",children:[a.jsx("li",{children:a.jsx("a",{href:"/admin/dashboard",className:"block p-2 rounded hover:bg-accent",children:"Dashboard"})}),a.jsx("li",{children:a.jsx("a",{href:"/admin/manga",className:"block p-2 rounded hover:bg-accent",children:"Quản lý truyện"})}),a.jsx("li",{children:a.jsx("a",{href:"/admin/users",className:"block p-2 rounded hover:bg-accent",children:"Quản lý người dùng"})}),a.jsx("li",{children:a.jsx("a",{href:"/admin/comments",className:"block p-2 rounded hover:bg-accent",children:"Quản lý bình luận"})})]})]})}),a.jsx("main",{className:"flex-1 p-6",children:a.jsx(Fe,{})})]})]}):a.jsx(Te,{to:"/login",replace:!0})},tn=()=>a.jsxs("div",{className:"container py-8",children:[a.jsxs("section",{className:"text-center py-12",children:[a.jsx("h1",{className:"text-4xl font-bold tracking-tight lg:text-6xl mb-6",children:"Chào mừng đến với BlogTruyen"}),a.jsx("p",{className:"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto",children:"Khám phá thế giới truyện tranh với hàng ngàn bộ manga chất lượng cao. Đọc miễn phí, không quảng cáo, trải nghiệm tuyệt vời."}),a.jsxs("div",{className:"flex gap-4 justify-center",children:[a.jsx(H,{to:"/browse",children:a.jsxs(G,{size:"lg",children:[a.jsx(we,{className:"mr-2 h-4 w-4"}),"Bắt đầu đọc"]})}),a.jsx(H,{to:"/register",children:a.jsx(G,{variant:"outline",size:"lg",children:"Đăng ký miễn phí"})})]})]}),a.jsxs("section",{className:"py-12",children:[a.jsx("h2",{className:"text-3xl font-bold text-center mb-8",children:"Tính năng nổi bật"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[a.jsxs(Y,{children:[a.jsxs(ae,{className:"text-center",children:[a.jsx(Dr,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),a.jsx(se,{className:"text-lg",children:"Xu hướng"})]}),a.jsx(Z,{children:a.jsx(le,{className:"text-center",children:"Theo dõi những bộ truyện hot nhất, được cập nhật liên tục"})})]}),a.jsxs(Y,{children:[a.jsxs(ae,{className:"text-center",children:[a.jsx(Cr,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),a.jsx(se,{className:"text-lg",children:"Lịch sử đọc"})]}),a.jsx(Z,{children:a.jsx(le,{className:"text-center",children:"Lưu lại tiến trình đọc, tiếp tục từ nơi bạn đã dừng lại"})})]}),a.jsxs(Y,{children:[a.jsxs(ae,{className:"text-center",children:[a.jsx(ht,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),a.jsx(se,{className:"text-lg",children:"Đánh giá"})]}),a.jsx(Z,{children:a.jsx(le,{className:"text-center",children:"Đánh giá và bình luận về những bộ truyện yêu thích"})})]}),a.jsxs(Y,{children:[a.jsxs(ae,{className:"text-center",children:[a.jsx(we,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),a.jsx(se,{className:"text-lg",children:"Đa nền tảng"})]}),a.jsx(Z,{children:a.jsx(le,{className:"text-center",children:"Đọc mọi lúc mọi nơi trên điện thoại, máy tính bảng, laptop"})})]})]})]}),a.jsxs("section",{className:"py-12",children:[a.jsxs("div",{className:"flex justify-between items-center mb-8",children:[a.jsx("h2",{className:"text-3xl font-bold",children:"Truyện phổ biến"}),a.jsx(H,{to:"/browse",children:a.jsx(G,{variant:"outline",children:"Xem tất cả"})})]}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:Array.from({length:6}).map((s,e)=>a.jsxs(Y,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[a.jsx("div",{className:"aspect-[3/4] bg-muted"}),a.jsxs(Z,{className:"p-3",children:[a.jsxs("h3",{className:"font-semibold text-sm truncate",children:["Tên truyện ",e+1]}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"Tác giả"}),a.jsxs("div",{className:"flex items-center mt-1",children:[a.jsx(ht,{className:"h-3 w-3 fill-yellow-400 text-yellow-400"}),a.jsx("span",{className:"text-xs ml-1",children:"4.5"})]})]})]},e))})]}),a.jsxs("section",{className:"py-12",children:[a.jsxs("div",{className:"flex justify-between items-center mb-8",children:[a.jsx("h2",{className:"text-3xl font-bold",children:"Cập nhật mới nhất"}),a.jsx(H,{to:"/browse?sort=updated",children:a.jsx(G,{variant:"outline",children:"Xem tất cả"})})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((s,e)=>a.jsx(Y,{className:"hover:shadow-lg transition-shadow",children:a.jsx(Z,{className:"p-4",children:a.jsxs("div",{className:"flex space-x-4",children:[a.jsx("div",{className:"w-16 h-20 bg-muted rounded"}),a.jsxs("div",{className:"flex-1",children:[a.jsxs("h3",{className:"font-semibold mb-1",children:["Tên truyện ",e+1]}),a.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"Chapter 123"}),a.jsx("p",{className:"text-xs text-muted-foreground",children:"2 giờ trước"})]})]})})},e))})]})]}),sn=()=>{const{t:s}=re("common"),e=ls(),{login:t,isLoading:r}=Me(),[n,i]=P.useState({email:"",password:""}),[o,l]=P.useState({}),d=h=>{const{name:g,value:m}=h.target;i(f=>({...f,[g]:m})),o[g]&&l(f=>({...f,[g]:""}))},c=()=>{const h={};return n.email?/\S+@\S+\.\S+/.test(n.email)||(h.email=s("forms.invalidEmail")):h.email=s("forms.required"),n.password||(h.password=s("forms.required")),l(h),Object.keys(h).length===0},u=async h=>{if(h.preventDefault(),!!c())try{await t(n),e("/")}catch(g){console.error("Login failed:",g),l({general:"Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin."})}};return a.jsx("div",{className:"container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8",children:a.jsxs(Y,{className:"w-full max-w-md",children:[a.jsxs(ae,{className:"text-center",children:[a.jsx("div",{className:"flex justify-center mb-4",children:a.jsx(we,{className:"h-8 w-8 text-primary"})}),a.jsx(se,{className:"text-2xl",children:s("navigation.login")}),a.jsx(le,{children:"Đăng nhập để truy cập tài khoản của bạn"})]}),a.jsxs(Z,{children:[a.jsxs("form",{onSubmit:u,className:"space-y-4",children:[o.general&&a.jsx("div",{className:"text-sm text-destructive text-center p-2 bg-destructive/10 rounded",children:o.general}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(Ge,{htmlFor:"email",children:s("forms.email")}),a.jsx($e,{id:"email",name:"email",type:"email",value:n.email,onChange:d,placeholder:"<EMAIL>",className:o.email?"border-destructive":""}),o.email&&a.jsx("p",{className:"text-sm text-destructive",children:o.email})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(Ge,{htmlFor:"password",children:s("forms.password")}),a.jsx($e,{id:"password",name:"password",type:"password",value:n.password,onChange:d,className:o.password?"border-destructive":""}),o.password&&a.jsx("p",{className:"text-sm text-destructive",children:o.password})]}),a.jsx(G,{type:"submit",className:"w-full",disabled:r,children:r?"Đang đăng nhập...":s("navigation.login")})]}),a.jsx("div",{className:"mt-6 text-center text-sm",children:a.jsxs("p",{className:"text-muted-foreground",children:["Chưa có tài khoản?"," ",a.jsx(H,{to:"/register",className:"text-primary hover:underline",children:s("navigation.register")})]})})]})]})})},rn=()=>{const{t:s}=re(["common","manga"]);return a.jsxs("div",{className:"container py-8",children:[a.jsx("h1",{className:"text-3xl font-bold mb-8",children:s("navigation.browse")}),a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:"text-muted-foreground",children:"Trang duyệt truyện đang được phát triển..."})})]})},nn=()=>a.jsxs("div",{className:"container py-8",children:[a.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Chi tiết truyện"}),a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:"text-muted-foreground",children:"Trang chi tiết truyện đang được phát triển..."})})]}),on=()=>a.jsxs("div",{className:"container py-8",children:[a.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Đọc truyện"}),a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:"text-muted-foreground",children:"Trang đọc truyện đang được phát triển..."})})]}),an=()=>{const{t:s}=re("common");return a.jsxs("div",{className:"container py-8",children:[a.jsx("h1",{className:"text-3xl font-bold mb-8",children:s("navigation.register")}),a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:"text-muted-foreground",children:"Trang đăng ký đang được phát triển..."})})]})},ln=()=>{const{t:s}=re("common");return a.jsxs("div",{className:"container py-8",children:[a.jsx("h1",{className:"text-3xl font-bold mb-8",children:s("navigation.bookmarks")}),a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:"text-muted-foreground",children:"Trang đánh dấu đang được phát triển..."})})]})},cn=()=>{const{t:s}=re("common");return a.jsxs("div",{className:"container py-8",children:[a.jsx("h1",{className:"text-3xl font-bold mb-8",children:s("navigation.history")}),a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:"text-muted-foreground",children:"Trang lịch sử đang được phát triển..."})})]})},dn=()=>{const{t:s}=re("common");return a.jsxs("div",{className:"container py-8",children:[a.jsx("h1",{className:"text-3xl font-bold mb-8",children:s("navigation.profile")}),a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:"text-muted-foreground",children:"Trang hồ sơ đang được phát triển..."})})]})},un=()=>a.jsx("div",{className:"container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8",children:a.jsxs(Y,{className:"w-full max-w-md text-center",children:[a.jsxs(ae,{children:[a.jsx(se,{className:"text-6xl font-bold text-muted-foreground mb-4",children:"404"}),a.jsx(se,{className:"text-2xl",children:"Trang không tồn tại"}),a.jsx(le,{children:"Trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển."})]}),a.jsx(Z,{className:"space-y-4",children:a.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 justify-center",children:[a.jsx(H,{to:"/",children:a.jsxs(G,{className:"w-full sm:w-auto",children:[a.jsx(Pr,{className:"h-4 w-4 mr-2"}),"Về trang chủ"]})}),a.jsxs(G,{variant:"outline",onClick:()=>window.history.back(),className:"w-full sm:w-auto",children:[a.jsx(Nr,{className:"h-4 w-4 mr-2"}),"Quay lại"]})]})})]})}),hn=cs([{path:"/",element:a.jsx(gt,{}),children:[{index:!0,element:a.jsx(tn,{})},{path:"browse",element:a.jsx(rn,{})},{path:"manga/:id",element:a.jsx(nn,{})},{path:"auth",element:a.jsx(mt,{requireAuth:!1}),children:[{path:"login",element:a.jsx(sn,{})},{path:"register",element:a.jsx(an,{})}]},{path:"user",element:a.jsx(mt,{requireAuth:!0}),children:[{path:"bookmarks",element:a.jsx(ln,{})},{path:"history",element:a.jsx(cn,{})},{path:"profile",element:a.jsx(dn,{})}]}]},{path:"/read",element:a.jsx(Zr,{}),children:[{path:":chapterId",element:a.jsx(on,{})}]},{path:"/admin",element:a.jsx(en,{}),children:[{index:!0,element:a.jsx("div",{children:"Admin Dashboard"})},{path:"manga",element:a.jsx("div",{children:"Manga Management"})},{path:"users",element:a.jsx("div",{children:"User Management"})},{path:"comments",element:a.jsx("div",{children:"Comment Management"})}]},{path:"*",element:a.jsx(gt,{}),children:[{path:"*",element:a.jsx(un,{})}]}]),fn=()=>a.jsx(ds,{router:hn}),w=s=>typeof s=="string",ye=()=>{let s,e;const t=new Promise((r,n)=>{s=r,e=n});return t.resolve=s,t.reject=e,t},pt=s=>s==null?"":""+s,gn=(s,e,t)=>{s.forEach(r=>{e[r]&&(t[r]=e[r])})},mn=/###/g,xt=s=>s&&s.indexOf("###")>-1?s.replace(mn,"."):s,bt=s=>!s||w(s),ve=(s,e,t)=>{const r=w(e)?e.split("."):e;let n=0;for(;n<r.length-1;){if(bt(s))return{};const i=xt(r[n]);!s[i]&&t&&(s[i]=new t),Object.prototype.hasOwnProperty.call(s,i)?s=s[i]:s={},++n}return bt(s)?{}:{obj:s,k:xt(r[n])}},yt=(s,e,t)=>{const{obj:r,k:n}=ve(s,e,Object);if(r!==void 0||e.length===1){r[n]=t;return}let i=e[e.length-1],o=e.slice(0,e.length-1),l=ve(s,o,Object);for(;l.obj===void 0&&o.length;)i=`${o[o.length-1]}.${i}`,o=o.slice(0,o.length-1),l=ve(s,o,Object),l!=null&&l.obj&&typeof l.obj[`${l.k}.${i}`]<"u"&&(l.obj=void 0);l.obj[`${l.k}.${i}`]=t},pn=(s,e,t,r)=>{const{obj:n,k:i}=ve(s,e,Object);n[i]=n[i]||[],n[i].push(t)},Ae=(s,e)=>{const{obj:t,k:r}=ve(s,e);if(t&&Object.prototype.hasOwnProperty.call(t,r))return t[r]},xn=(s,e,t)=>{const r=Ae(s,t);return r!==void 0?r:Ae(e,t)},es=(s,e,t)=>{for(const r in e)r!=="__proto__"&&r!=="constructor"&&(r in s?w(s[r])||s[r]instanceof String||w(e[r])||e[r]instanceof String?t&&(s[r]=e[r]):es(s[r],e[r],t):s[r]=e[r]);return s},he=s=>s.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var bn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const yn=s=>w(s)?s.replace(/[&<>"'\/]/g,e=>bn[e]):s;class vn{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(t!==void 0)return t;const r=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,r),this.regExpQueue.push(e),r}}const wn=[" ",",","?","!",";"],kn=new vn(20),Nn=(s,e,t)=>{e=e||"",t=t||"";const r=wn.filter(o=>e.indexOf(o)<0&&t.indexOf(o)<0);if(r.length===0)return!0;const n=kn.getRegExp(`(${r.map(o=>o==="?"?"\\?":o).join("|")})`);let i=!n.test(s);if(!i){const o=s.indexOf(t);o>0&&!n.test(s.substring(0,o))&&(i=!0)}return i},Xe=(s,e,t=".")=>{if(!s)return;if(s[e])return Object.prototype.hasOwnProperty.call(s,e)?s[e]:void 0;const r=e.split(t);let n=s;for(let i=0;i<r.length;){if(!n||typeof n!="object")return;let o,l="";for(let d=i;d<r.length;++d)if(d!==i&&(l+=t),l+=r[d],o=n[l],o!==void 0){if(["string","number","boolean"].indexOf(typeof o)>-1&&d<r.length-1)continue;i+=d-i+1;break}n=o}return n},ke=s=>s==null?void 0:s.replace("_","-"),jn={type:"logger",log(s){this.output("log",s)},warn(s){this.output("warn",s)},error(s){this.output("error",s)},output(s,e){var t,r;(r=(t=console==null?void 0:console[s])==null?void 0:t.apply)==null||r.call(t,console,e)}};class Ee{constructor(e,t={}){this.init(e,t)}init(e,t={}){this.prefix=t.prefix||"i18next:",this.logger=e||jn,this.options=t,this.debug=t.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,r,n){return n&&!this.debug?null:(w(e[0])&&(e[0]=`${r}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new Ee(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new Ee(this.logger,e)}}var J=new Ee;class ze{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(r=>{this.observers[r]||(this.observers[r]=new Map);const n=this.observers[r].get(t)||0;this.observers[r].set(t,n+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e,...t){this.observers[e]&&Array.from(this.observers[e].entries()).forEach(([n,i])=>{for(let o=0;o<i;o++)n(...t)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([n,i])=>{for(let o=0;o<i;o++)n.apply(n,[e,...t])})}}class vt extends ze{constructor(e,t={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,r,n={}){var c,u;const i=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,o=n.ignoreJSONStructure!==void 0?n.ignoreJSONStructure:this.options.ignoreJSONStructure;let l;e.indexOf(".")>-1?l=e.split("."):(l=[e,t],r&&(Array.isArray(r)?l.push(...r):w(r)&&i?l.push(...r.split(i)):l.push(r)));const d=Ae(this.data,l);return!d&&!t&&!r&&e.indexOf(".")>-1&&(e=l[0],t=l[1],r=l.slice(2).join(".")),d||!o||!w(r)?d:Xe((u=(c=this.data)==null?void 0:c[e])==null?void 0:u[t],r,i)}addResource(e,t,r,n,i={silent:!1}){const o=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator;let l=[e,t];r&&(l=l.concat(o?r.split(o):r)),e.indexOf(".")>-1&&(l=e.split("."),n=t,t=l[1]),this.addNamespaces(t),yt(this.data,l,n),i.silent||this.emit("added",e,t,r,n)}addResources(e,t,r,n={silent:!1}){for(const i in r)(w(r[i])||Array.isArray(r[i]))&&this.addResource(e,t,i,r[i],{silent:!0});n.silent||this.emit("added",e,t,r)}addResourceBundle(e,t,r,n,i,o={silent:!1,skipCopy:!1}){let l=[e,t];e.indexOf(".")>-1&&(l=e.split("."),n=r,r=t,t=l[1]),this.addNamespaces(t);let d=Ae(this.data,l)||{};o.skipCopy||(r=JSON.parse(JSON.stringify(r))),n?es(d,r,i):d={...d,...r},yt(this.data,l,d),o.silent||this.emit("added",e,t,r)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return this.getResource(e,t)!==void 0}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(n=>t[n]&&Object.keys(t[n]).length>0)}toJSON(){return this.data}}var ts={processors:{},addPostProcessor(s){this.processors[s.name]=s},handle(s,e,t,r,n){return s.forEach(i=>{var o;e=((o=this.processors[i])==null?void 0:o.process(e,t,r,n))??e}),e}};const wt={},kt=s=>!w(s)&&typeof s!="boolean"&&typeof s!="number";class Ie extends ze{constructor(e,t={}){super(),gn(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=J.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,t={interpolation:{}}){const r={...t};if(e==null)return!1;const n=this.resolve(e,r);return(n==null?void 0:n.res)!==void 0}extractFromKey(e,t){let r=t.nsSeparator!==void 0?t.nsSeparator:this.options.nsSeparator;r===void 0&&(r=":");const n=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator;let i=t.ns||this.options.defaultNS||[];const o=r&&e.indexOf(r)>-1,l=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!Nn(e,r,n);if(o&&!l){const d=e.match(this.interpolator.nestingRegexp);if(d&&d.length>0)return{key:e,namespaces:w(i)?[i]:i};const c=e.split(r);(r!==n||r===n&&this.options.ns.indexOf(c[0])>-1)&&(i=c.shift()),e=c.join(n)}return{key:e,namespaces:w(i)?[i]:i}}translate(e,t,r){let n=typeof t=="object"?{...t}:t;if(typeof n!="object"&&this.options.overloadTranslationOptionHandler&&(n=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(n={...n}),n||(n={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const i=n.returnDetails!==void 0?n.returnDetails:this.options.returnDetails,o=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,{key:l,namespaces:d}=this.extractFromKey(e[e.length-1],n),c=d[d.length-1];let u=n.nsSeparator!==void 0?n.nsSeparator:this.options.nsSeparator;u===void 0&&(u=":");const h=n.lng||this.language,g=n.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((h==null?void 0:h.toLowerCase())==="cimode")return g?i?{res:`${c}${u}${l}`,usedKey:l,exactUsedKey:l,usedLng:h,usedNS:c,usedParams:this.getUsedParamsDetails(n)}:`${c}${u}${l}`:i?{res:l,usedKey:l,exactUsedKey:l,usedLng:h,usedNS:c,usedParams:this.getUsedParamsDetails(n)}:l;const m=this.resolve(e,n);let f=m==null?void 0:m.res;const p=(m==null?void 0:m.usedKey)||l,k=(m==null?void 0:m.exactUsedKey)||l,R=["[object Number]","[object Function]","[object RegExp]"],N=n.joinArrays!==void 0?n.joinArrays:this.options.joinArrays,C=!this.i18nFormat||this.i18nFormat.handleAsObject,L=n.count!==void 0&&!w(n.count),T=Ie.hasDefaultValue(n),U=L?this.pluralResolver.getSuffix(h,n.count,n):"",I=n.ordinal&&L?this.pluralResolver.getSuffix(h,n.count,{ordinal:!1}):"",y=L&&!n.ordinal&&n.count===0,S=y&&n[`defaultValue${this.options.pluralSeparator}zero`]||n[`defaultValue${U}`]||n[`defaultValue${I}`]||n.defaultValue;let O=f;C&&!f&&T&&(O=S);const K=kt(O),de=Object.prototype.toString.apply(O);if(C&&O&&K&&R.indexOf(de)<0&&!(w(N)&&Array.isArray(O))){if(!n.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const _=this.options.returnedObjectHandler?this.options.returnedObjectHandler(p,O,{...n,ns:d}):`key '${l} (${this.language})' returned an object instead of string.`;return i?(m.res=_,m.usedParams=this.getUsedParamsDetails(n),m):_}if(o){const _=Array.isArray(O),B=_?[]:{},Q=_?k:p;for(const A in O)if(Object.prototype.hasOwnProperty.call(O,A)){const M=`${Q}${o}${A}`;T&&!f?B[A]=this.translate(M,{...n,defaultValue:kt(S)?S[A]:void 0,joinArrays:!1,ns:d}):B[A]=this.translate(M,{...n,joinArrays:!1,ns:d}),B[A]===M&&(B[A]=O[A])}f=B}}else if(C&&w(N)&&Array.isArray(f))f=f.join(N),f&&(f=this.extendTranslation(f,e,n,r));else{let _=!1,B=!1;!this.isValidLookup(f)&&T&&(_=!0,f=S),this.isValidLookup(f)||(B=!0,f=l);const A=(n.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&B?void 0:f,M=T&&S!==f&&this.options.updateMissing;if(B||_||M){if(this.logger.log(M?"updateKey":"missingKey",h,c,l,M?S:f),o){const z=this.resolve(l,{...n,keySeparator:!1});z&&z.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let v=[];const ne=this.languageUtils.getFallbackCodes(this.options.fallbackLng,n.lng||this.language);if(this.options.saveMissingTo==="fallback"&&ne&&ne[0])for(let z=0;z<ne.length;z++)v.push(ne[z]);else this.options.saveMissingTo==="all"?v=this.languageUtils.toResolveHierarchy(n.lng||this.language):v.push(n.lng||this.language);const xe=(z,q,$)=>{var ie;const D=T&&$!==f?$:A;this.options.missingKeyHandler?this.options.missingKeyHandler(z,c,q,D,M,n):(ie=this.backendConnector)!=null&&ie.saveMissing&&this.backendConnector.saveMissing(z,c,q,D,M,n),this.emit("missingKey",z,c,q,f)};this.options.saveMissing&&(this.options.saveMissingPlurals&&L?v.forEach(z=>{const q=this.pluralResolver.getSuffixes(z,n);y&&n[`defaultValue${this.options.pluralSeparator}zero`]&&q.indexOf(`${this.options.pluralSeparator}zero`)<0&&q.push(`${this.options.pluralSeparator}zero`),q.forEach($=>{xe([z],l+$,n[`defaultValue${$}`]||S)})}):xe(v,l,S))}f=this.extendTranslation(f,e,n,m,r),B&&f===l&&this.options.appendNamespaceToMissingKey&&(f=`${c}${u}${l}`),(B||_)&&this.options.parseMissingKeyHandler&&(f=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${c}${u}${l}`:l,_?f:void 0,n))}return i?(m.res=f,m.usedParams=this.getUsedParamsDetails(n),m):f}extendTranslation(e,t,r,n,i){var d,c;if((d=this.i18nFormat)!=null&&d.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||n.usedLng,n.usedNS,n.usedKey,{resolved:n});else if(!r.skipInterpolation){r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});const u=w(e)&&(((c=r==null?void 0:r.interpolation)==null?void 0:c.skipOnVariables)!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let h;if(u){const m=e.match(this.interpolator.nestingRegexp);h=m&&m.length}let g=r.replace&&!w(r.replace)?r.replace:r;if(this.options.interpolation.defaultVariables&&(g={...this.options.interpolation.defaultVariables,...g}),e=this.interpolator.interpolate(e,g,r.lng||this.language||n.usedLng,r),u){const m=e.match(this.interpolator.nestingRegexp),f=m&&m.length;h<f&&(r.nest=!1)}!r.lng&&n&&n.res&&(r.lng=this.language||n.usedLng),r.nest!==!1&&(e=this.interpolator.nest(e,(...m)=>(i==null?void 0:i[0])===m[0]&&!r.context?(this.logger.warn(`It seems you are nesting recursively key: ${m[0]} in key: ${t[0]}`),null):this.translate(...m,t),r)),r.interpolation&&this.interpolator.reset()}const o=r.postProcess||this.options.postProcess,l=w(o)?[o]:o;return e!=null&&(l!=null&&l.length)&&r.applyPostProcessor!==!1&&(e=ts.handle(l,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...n,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),e}resolve(e,t={}){let r,n,i,o,l;return w(e)&&(e=[e]),e.forEach(d=>{if(this.isValidLookup(r))return;const c=this.extractFromKey(d,t),u=c.key;n=u;let h=c.namespaces;this.options.fallbackNS&&(h=h.concat(this.options.fallbackNS));const g=t.count!==void 0&&!w(t.count),m=g&&!t.ordinal&&t.count===0,f=t.context!==void 0&&(w(t.context)||typeof t.context=="number")&&t.context!=="",p=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);h.forEach(k=>{var R,N;this.isValidLookup(r)||(l=k,!wt[`${p[0]}-${k}`]&&((R=this.utils)!=null&&R.hasLoadedNamespace)&&!((N=this.utils)!=null&&N.hasLoadedNamespace(l))&&(wt[`${p[0]}-${k}`]=!0,this.logger.warn(`key "${n}" for languages "${p.join(", ")}" won't get resolved as namespace "${l}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),p.forEach(C=>{var U;if(this.isValidLookup(r))return;o=C;const L=[u];if((U=this.i18nFormat)!=null&&U.addLookupKeys)this.i18nFormat.addLookupKeys(L,u,C,k,t);else{let I;g&&(I=this.pluralResolver.getSuffix(C,t.count,t));const y=`${this.options.pluralSeparator}zero`,S=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(g&&(L.push(u+I),t.ordinal&&I.indexOf(S)===0&&L.push(u+I.replace(S,this.options.pluralSeparator)),m&&L.push(u+y)),f){const O=`${u}${this.options.contextSeparator}${t.context}`;L.push(O),g&&(L.push(O+I),t.ordinal&&I.indexOf(S)===0&&L.push(O+I.replace(S,this.options.pluralSeparator)),m&&L.push(O+y))}}let T;for(;T=L.pop();)this.isValidLookup(r)||(i=T,r=this.getResource(C,k,T,t))}))})}),{res:r,usedKey:n,exactUsedKey:i,usedLng:o,usedNS:l}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,t,r,n={}){var i;return(i=this.i18nFormat)!=null&&i.getResource?this.i18nFormat.getResource(e,t,r,n):this.resourceStore.getResource(e,t,r,n)}getUsedParamsDetails(e={}){const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],r=e.replace&&!w(e.replace);let n=r?e.replace:e;if(r&&typeof e.count<"u"&&(n.count=e.count),this.options.interpolation.defaultVariables&&(n={...this.options.interpolation.defaultVariables,...n}),!r){n={...n};for(const i of t)delete n[i]}return n}static hasDefaultValue(e){const t="defaultValue";for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t===r.substring(0,t.length)&&e[r]!==void 0)return!0;return!1}}class Nt{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=J.create("languageUtils")}getScriptPartFromCode(e){if(e=ke(e),!e||e.indexOf("-")<0)return null;const t=e.split("-");return t.length===2||(t.pop(),t[t.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(e=ke(e),!e||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(w(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch{}return t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(r=>{if(t)return;const n=this.formatLanguageCode(r);(!this.options.supportedLngs||this.isSupportedCode(n))&&(t=n)}),!t&&this.options.supportedLngs&&e.forEach(r=>{if(t)return;const n=this.getScriptPartFromCode(r);if(this.isSupportedCode(n))return t=n;const i=this.getLanguagePartFromCode(r);if(this.isSupportedCode(i))return t=i;t=this.options.supportedLngs.find(o=>{if(o===i)return o;if(!(o.indexOf("-")<0&&i.indexOf("-")<0)&&(o.indexOf("-")>0&&i.indexOf("-")<0&&o.substring(0,o.indexOf("-"))===i||o.indexOf(i)===0&&i.length>1))return o})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if(typeof e=="function"&&(e=e(t)),w(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let r=e[t];return r||(r=e[this.getScriptPartFromCode(t)]),r||(r=e[this.formatLanguageCode(t)]),r||(r=e[this.getLanguagePartFromCode(t)]),r||(r=e.default),r||[]}toResolveHierarchy(e,t){const r=this.getFallbackCodes((t===!1?[]:t)||this.options.fallbackLng||[],e),n=[],i=o=>{o&&(this.isSupportedCode(o)?n.push(o):this.logger.warn(`rejecting language code not found in supportedLngs: ${o}`))};return w(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&i(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&i(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&i(this.getLanguagePartFromCode(e))):w(e)&&i(this.formatLanguageCode(e)),r.forEach(o=>{n.indexOf(o)<0&&i(this.formatLanguageCode(o))}),n}}const jt={zero:0,one:1,two:2,few:3,many:4,other:5},St={select:s=>s===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class Sn{constructor(e,t={}){this.languageUtils=e,this.options=t,this.logger=J.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e,t={}){const r=ke(e==="dev"?"en":e),n=t.ordinal?"ordinal":"cardinal",i=JSON.stringify({cleanedCode:r,type:n});if(i in this.pluralRulesCache)return this.pluralRulesCache[i];let o;try{o=new Intl.PluralRules(r,{type:n})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),St;if(!e.match(/-|_/))return St;const d=this.languageUtils.getLanguagePartFromCode(e);o=this.getRule(d,t)}return this.pluralRulesCache[i]=o,o}needsPlural(e,t={}){let r=this.getRule(e,t);return r||(r=this.getRule("dev",t)),(r==null?void 0:r.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(e,t,r={}){return this.getSuffixes(e,r).map(n=>`${t}${n}`)}getSuffixes(e,t={}){let r=this.getRule(e,t);return r||(r=this.getRule("dev",t)),r?r.resolvedOptions().pluralCategories.sort((n,i)=>jt[n]-jt[i]).map(n=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${n}`):[]}getSuffix(e,t,r={}){const n=this.getRule(e,r);return n?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${n.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,r))}}const Ct=(s,e,t,r=".",n=!0)=>{let i=xn(s,e,t);return!i&&n&&w(t)&&(i=Xe(s,t,r),i===void 0&&(i=Xe(e,t,r))),i},Be=s=>s.replace(/\$/g,"$$$$");class Cn{constructor(e={}){var t;this.logger=J.create("interpolator"),this.options=e,this.format=((t=e==null?void 0:e.interpolation)==null?void 0:t.format)||(r=>r),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:r,useRawValueToEscape:n,prefix:i,prefixEscaped:o,suffix:l,suffixEscaped:d,formatSeparator:c,unescapeSuffix:u,unescapePrefix:h,nestingPrefix:g,nestingPrefixEscaped:m,nestingSuffix:f,nestingSuffixEscaped:p,nestingOptionsSeparator:k,maxReplaces:R,alwaysFormat:N}=e.interpolation;this.escape=t!==void 0?t:yn,this.escapeValue=r!==void 0?r:!0,this.useRawValueToEscape=n!==void 0?n:!1,this.prefix=i?he(i):o||"{{",this.suffix=l?he(l):d||"}}",this.formatSeparator=c||",",this.unescapePrefix=u?"":h||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=g?he(g):m||he("$t("),this.nestingSuffix=f?he(f):p||he(")"),this.nestingOptionsSeparator=k||",",this.maxReplaces=R||1e3,this.alwaysFormat=N!==void 0?N:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(t,r)=>(t==null?void 0:t.source)===r?(t.lastIndex=0,t):new RegExp(r,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,r,n){var m;let i,o,l;const d=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},c=f=>{if(f.indexOf(this.formatSeparator)<0){const N=Ct(t,d,f,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(N,void 0,r,{...n,...t,interpolationkey:f}):N}const p=f.split(this.formatSeparator),k=p.shift().trim(),R=p.join(this.formatSeparator).trim();return this.format(Ct(t,d,k,this.options.keySeparator,this.options.ignoreJSONStructure),R,r,{...n,...t,interpolationkey:k})};this.resetRegExp();const u=(n==null?void 0:n.missingInterpolationHandler)||this.options.missingInterpolationHandler,h=((m=n==null?void 0:n.interpolation)==null?void 0:m.skipOnVariables)!==void 0?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:f=>Be(f)},{regex:this.regexp,safeValue:f=>this.escapeValue?Be(this.escape(f)):Be(f)}].forEach(f=>{for(l=0;i=f.regex.exec(e);){const p=i[1].trim();if(o=c(p),o===void 0)if(typeof u=="function"){const R=u(e,i,n);o=w(R)?R:""}else if(n&&Object.prototype.hasOwnProperty.call(n,p))o="";else if(h){o=i[0];continue}else this.logger.warn(`missed to pass in variable ${p} for interpolating ${e}`),o="";else!w(o)&&!this.useRawValueToEscape&&(o=pt(o));const k=f.safeValue(o);if(e=e.replace(i[0],k),h?(f.regex.lastIndex+=o.length,f.regex.lastIndex-=i[0].length):f.regex.lastIndex=0,l++,l>=this.maxReplaces)break}}),e}nest(e,t,r={}){let n,i,o;const l=(d,c)=>{const u=this.nestingOptionsSeparator;if(d.indexOf(u)<0)return d;const h=d.split(new RegExp(`${u}[ ]*{`));let g=`{${h[1]}`;d=h[0],g=this.interpolate(g,o);const m=g.match(/'/g),f=g.match(/"/g);(((m==null?void 0:m.length)??0)%2===0&&!f||f.length%2!==0)&&(g=g.replace(/'/g,'"'));try{o=JSON.parse(g),c&&(o={...c,...o})}catch(p){return this.logger.warn(`failed parsing options string in nesting for key ${d}`,p),`${d}${u}${g}`}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,d};for(;n=this.nestingRegexp.exec(e);){let d=[];o={...r},o=o.replace&&!w(o.replace)?o.replace:o,o.applyPostProcessor=!1,delete o.defaultValue;let c=!1;if(n[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(n[1])){const u=n[1].split(this.formatSeparator).map(h=>h.trim());n[1]=u.shift(),d=u,c=!0}if(i=t(l.call(this,n[1].trim(),o),o),i&&n[0]===e&&!w(i))return i;w(i)||(i=pt(i)),i||(this.logger.warn(`missed to resolve ${n[1]} for nesting ${e}`),i=""),c&&(i=d.reduce((u,h)=>this.format(u,h,r.lng,{...r,interpolationkey:n[1].trim()}),i.trim())),e=e.replace(n[0],i),this.regexp.lastIndex=0}return e}}const Ln=s=>{let e=s.toLowerCase().trim();const t={};if(s.indexOf("(")>-1){const r=s.split("(");e=r[0].toLowerCase().trim();const n=r[1].substring(0,r[1].length-1);e==="currency"&&n.indexOf(":")<0?t.currency||(t.currency=n.trim()):e==="relativetime"&&n.indexOf(":")<0?t.range||(t.range=n.trim()):n.split(";").forEach(o=>{if(o){const[l,...d]=o.split(":"),c=d.join(":").trim().replace(/^'+|'+$/g,""),u=l.trim();t[u]||(t[u]=c),c==="false"&&(t[u]=!1),c==="true"&&(t[u]=!0),isNaN(c)||(t[u]=parseInt(c,10))}})}return{formatName:e,formatOptions:t}},Lt=s=>{const e={};return(t,r,n)=>{let i=n;n&&n.interpolationkey&&n.formatParams&&n.formatParams[n.interpolationkey]&&n[n.interpolationkey]&&(i={...i,[n.interpolationkey]:void 0});const o=r+JSON.stringify(i);let l=e[o];return l||(l=s(ke(r),n),e[o]=l),l(t)}},On=s=>(e,t,r)=>s(ke(t),r)(e);class Rn{constructor(e={}){this.logger=J.create("formatter"),this.options=e,this.init(e)}init(e,t={interpolation:{}}){this.formatSeparator=t.interpolation.formatSeparator||",";const r=t.cacheInBuiltFormats?Lt:On;this.formats={number:r((n,i)=>{const o=new Intl.NumberFormat(n,{...i});return l=>o.format(l)}),currency:r((n,i)=>{const o=new Intl.NumberFormat(n,{...i,style:"currency"});return l=>o.format(l)}),datetime:r((n,i)=>{const o=new Intl.DateTimeFormat(n,{...i});return l=>o.format(l)}),relativetime:r((n,i)=>{const o=new Intl.RelativeTimeFormat(n,{...i});return l=>o.format(l,i.range||"day")}),list:r((n,i)=>{const o=new Intl.ListFormat(n,{...i});return l=>o.format(l)})}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=Lt(t)}format(e,t,r,n={}){const i=t.split(this.formatSeparator);if(i.length>1&&i[0].indexOf("(")>1&&i[0].indexOf(")")<0&&i.find(l=>l.indexOf(")")>-1)){const l=i.findIndex(d=>d.indexOf(")")>-1);i[0]=[i[0],...i.splice(1,l)].join(this.formatSeparator)}return i.reduce((l,d)=>{var h;const{formatName:c,formatOptions:u}=Ln(d);if(this.formats[c]){let g=l;try{const m=((h=n==null?void 0:n.formatParams)==null?void 0:h[n.interpolationkey])||{},f=m.locale||m.lng||n.locale||n.lng||r;g=this.formats[c](l,f,{...u,...n,...m})}catch(m){this.logger.warn(m)}return g}else this.logger.warn(`there was no format function for ${c}`);return l},e)}}const Pn=(s,e)=>{s.pending[e]!==void 0&&(delete s.pending[e],s.pendingCount--)};class Tn extends ze{constructor(e,t,r,n={}){var i,o;super(),this.backend=e,this.store=t,this.services=r,this.languageUtils=r.languageUtils,this.options=n,this.logger=J.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=n.maxParallelReads||10,this.readingCalls=0,this.maxRetries=n.maxRetries>=0?n.maxRetries:5,this.retryTimeout=n.retryTimeout>=1?n.retryTimeout:350,this.state={},this.queue=[],(o=(i=this.backend)==null?void 0:i.init)==null||o.call(i,r,n.backend,n)}queueLoad(e,t,r,n){const i={},o={},l={},d={};return e.forEach(c=>{let u=!0;t.forEach(h=>{const g=`${c}|${h}`;!r.reload&&this.store.hasResourceBundle(c,h)?this.state[g]=2:this.state[g]<0||(this.state[g]===1?o[g]===void 0&&(o[g]=!0):(this.state[g]=1,u=!1,o[g]===void 0&&(o[g]=!0),i[g]===void 0&&(i[g]=!0),d[h]===void 0&&(d[h]=!0)))}),u||(l[c]=!0)}),(Object.keys(i).length||Object.keys(o).length)&&this.queue.push({pending:o,pendingCount:Object.keys(o).length,loaded:{},errors:[],callback:n}),{toLoad:Object.keys(i),pending:Object.keys(o),toLoadLanguages:Object.keys(l),toLoadNamespaces:Object.keys(d)}}loaded(e,t,r){const n=e.split("|"),i=n[0],o=n[1];t&&this.emit("failedLoading",i,o,t),!t&&r&&this.store.addResourceBundle(i,o,r,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&r&&(this.state[e]=0);const l={};this.queue.forEach(d=>{pn(d.loaded,[i],o),Pn(d,e),t&&d.errors.push(t),d.pendingCount===0&&!d.done&&(Object.keys(d.loaded).forEach(c=>{l[c]||(l[c]={});const u=d.loaded[c];u.length&&u.forEach(h=>{l[c][h]===void 0&&(l[c][h]=!0)})}),d.done=!0,d.errors.length?d.callback(d.errors):d.callback())}),this.emit("loaded",l),this.queue=this.queue.filter(d=>!d.done)}read(e,t,r,n=0,i=this.retryTimeout,o){if(!e.length)return o(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:r,tried:n,wait:i,callback:o});return}this.readingCalls++;const l=(c,u)=>{if(this.readingCalls--,this.waitingReads.length>0){const h=this.waitingReads.shift();this.read(h.lng,h.ns,h.fcName,h.tried,h.wait,h.callback)}if(c&&u&&n<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,r,n+1,i*2,o)},i);return}o(c,u)},d=this.backend[r].bind(this.backend);if(d.length===2){try{const c=d(e,t);c&&typeof c.then=="function"?c.then(u=>l(null,u)).catch(l):l(null,c)}catch(c){l(c)}return}return d(e,t,l)}prepareLoading(e,t,r={},n){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),n&&n();w(e)&&(e=this.languageUtils.toResolveHierarchy(e)),w(t)&&(t=[t]);const i=this.queueLoad(e,t,r,n);if(!i.toLoad.length)return i.pending.length||n(),null;i.toLoad.forEach(o=>{this.loadOne(o)})}load(e,t,r){this.prepareLoading(e,t,{},r)}reload(e,t,r){this.prepareLoading(e,t,{reload:!0},r)}loadOne(e,t=""){const r=e.split("|"),n=r[0],i=r[1];this.read(n,i,"read",void 0,void 0,(o,l)=>{o&&this.logger.warn(`${t}loading namespace ${i} for language ${n} failed`,o),!o&&l&&this.logger.log(`${t}loaded namespace ${i} for language ${n}`,l),this.loaded(e,o,l)})}saveMissing(e,t,r,n,i,o={},l=()=>{}){var d,c,u,h,g;if((c=(d=this.services)==null?void 0:d.utils)!=null&&c.hasLoadedNamespace&&!((h=(u=this.services)==null?void 0:u.utils)!=null&&h.hasLoadedNamespace(t))){this.logger.warn(`did not save key "${r}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(r==null||r==="")){if((g=this.backend)!=null&&g.create){const m={...o,isUpdate:i},f=this.backend.create.bind(this.backend);if(f.length<6)try{let p;f.length===5?p=f(e,t,r,n,m):p=f(e,t,r,n),p&&typeof p.then=="function"?p.then(k=>l(null,k)).catch(l):l(null,p)}catch(p){l(p)}else f(e,t,r,n,l,m)}!e||!e[0]||this.store.addResource(e[0],t,r,n)}}}const Ot=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:s=>{let e={};if(typeof s[1]=="object"&&(e=s[1]),w(s[1])&&(e.defaultValue=s[1]),w(s[2])&&(e.tDescription=s[2]),typeof s[2]=="object"||typeof s[3]=="object"){const t=s[3]||s[2];Object.keys(t).forEach(r=>{e[r]=t[r]})}return e},interpolation:{escapeValue:!0,format:s=>s,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),Rt=s=>{var e,t;return w(s.ns)&&(s.ns=[s.ns]),w(s.fallbackLng)&&(s.fallbackLng=[s.fallbackLng]),w(s.fallbackNS)&&(s.fallbackNS=[s.fallbackNS]),((t=(e=s.supportedLngs)==null?void 0:e.indexOf)==null?void 0:t.call(e,"cimode"))<0&&(s.supportedLngs=s.supportedLngs.concat(["cimode"])),typeof s.initImmediate=="boolean"&&(s.initAsync=s.initImmediate),s},Pe=()=>{},$n=s=>{Object.getOwnPropertyNames(Object.getPrototypeOf(s)).forEach(t=>{typeof s[t]=="function"&&(s[t]=s[t].bind(s))})};class Ne extends ze{constructor(e={},t){if(super(),this.options=Rt(e),this.services={},this.logger=J,this.modules={external:[]},$n(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(e={},t){this.isInitializing=!0,typeof e=="function"&&(t=e,e={}),e.defaultNS==null&&e.ns&&(w(e.ns)?e.defaultNS=e.ns:e.ns.indexOf("translation")<0&&(e.defaultNS=e.ns[0]));const r=Ot();this.options={...r,...this.options,...Rt(e)},this.options.interpolation={...r.interpolation,...this.options.interpolation},e.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=e.keySeparator),e.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=e.nsSeparator);const n=c=>c?typeof c=="function"?new c:c:null;if(!this.options.isClone){this.modules.logger?J.init(n(this.modules.logger),this.options):J.init(null,this.options);let c;this.modules.formatter?c=this.modules.formatter:c=Rn;const u=new Nt(this.options);this.store=new vt(this.options.resources,this.options);const h=this.services;h.logger=J,h.resourceStore=this.store,h.languageUtils=u,h.pluralResolver=new Sn(u,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),c&&(!this.options.interpolation.format||this.options.interpolation.format===r.interpolation.format)&&(h.formatter=n(c),h.formatter.init(h,this.options),this.options.interpolation.format=h.formatter.format.bind(h.formatter)),h.interpolator=new Cn(this.options),h.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},h.backendConnector=new Tn(n(this.modules.backend),h.resourceStore,h,this.options),h.backendConnector.on("*",(g,...m)=>{this.emit(g,...m)}),this.modules.languageDetector&&(h.languageDetector=n(this.modules.languageDetector),h.languageDetector.init&&h.languageDetector.init(h,this.options.detection,this.options)),this.modules.i18nFormat&&(h.i18nFormat=n(this.modules.i18nFormat),h.i18nFormat.init&&h.i18nFormat.init(this)),this.translator=new Ie(this.services,this.options),this.translator.on("*",(g,...m)=>{this.emit(g,...m)}),this.modules.external.forEach(g=>{g.init&&g.init(this)})}if(this.format=this.options.interpolation.format,t||(t=Pe),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const c=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);c.length>0&&c[0]!=="dev"&&(this.options.lng=c[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(c=>{this[c]=(...u)=>this.store[c](...u)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(c=>{this[c]=(...u)=>(this.store[c](...u),this)});const l=ye(),d=()=>{const c=(u,h)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),l.resolve(h),t(u,h)};if(this.languages&&!this.isInitialized)return c(null,this.t.bind(this));this.changeLanguage(this.options.lng,c)};return this.options.resources||!this.options.initAsync?d():setTimeout(d,0),l}loadResources(e,t=Pe){var i,o;let r=t;const n=w(e)?e:this.language;if(typeof e=="function"&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if((n==null?void 0:n.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return r();const l=[],d=c=>{if(!c||c==="cimode")return;this.services.languageUtils.toResolveHierarchy(c).forEach(h=>{h!=="cimode"&&l.indexOf(h)<0&&l.push(h)})};n?d(n):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(u=>d(u)),(o=(i=this.options.preload)==null?void 0:i.forEach)==null||o.call(i,c=>d(c)),this.services.backendConnector.load(l,this.options.ns,c=>{!c&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),r(c)})}else r(null)}reloadResources(e,t,r){const n=ye();return typeof e=="function"&&(r=e,e=void 0),typeof t=="function"&&(r=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),r||(r=Pe),this.services.backendConnector.reload(e,t,i=>{n.resolve(),r(i)}),n}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&ts.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1)){for(let t=0;t<this.languages.length;t++){const r=this.languages[t];if(!(["cimode","dev"].indexOf(r)>-1)&&this.store.hasLanguageSomeTranslations(r)){this.resolvedLanguage=r;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,t){this.isLanguageChangingTo=e;const r=ye();this.emit("languageChanging",e);const n=l=>{this.language=l,this.languages=this.services.languageUtils.toResolveHierarchy(l),this.resolvedLanguage=void 0,this.setResolvedLanguage(l)},i=(l,d)=>{d?this.isLanguageChangingTo===e&&(n(d),this.translator.changeLanguage(d),this.isLanguageChangingTo=void 0,this.emit("languageChanged",d),this.logger.log("languageChanged",d)):this.isLanguageChangingTo=void 0,r.resolve((...c)=>this.t(...c)),t&&t(l,(...c)=>this.t(...c))},o=l=>{var u,h;!e&&!l&&this.services.languageDetector&&(l=[]);const d=w(l)?l:l&&l[0],c=this.store.hasLanguageSomeTranslations(d)?d:this.services.languageUtils.getBestMatchFromCodes(w(l)?[l]:l);c&&(this.language||n(c),this.translator.language||this.translator.changeLanguage(c),(h=(u=this.services.languageDetector)==null?void 0:u.cacheUserLanguage)==null||h.call(u,c)),this.loadResources(c,g=>{i(g,c)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?o(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(o):this.services.languageDetector.detect(o):o(e),r}getFixedT(e,t,r){const n=(i,o,...l)=>{let d;typeof o!="object"?d=this.options.overloadTranslationOptionHandler([i,o].concat(l)):d={...o},d.lng=d.lng||n.lng,d.lngs=d.lngs||n.lngs,d.ns=d.ns||n.ns,d.keyPrefix!==""&&(d.keyPrefix=d.keyPrefix||r||n.keyPrefix);const c=this.options.keySeparator||".";let u;return d.keyPrefix&&Array.isArray(i)?u=i.map(h=>`${d.keyPrefix}${c}${h}`):u=d.keyPrefix?`${d.keyPrefix}${c}${i}`:i,this.t(u,d)};return w(e)?n.lng=e:n.lngs=e,n.ns=t,n.keyPrefix=r,n}t(...e){var t;return(t=this.translator)==null?void 0:t.translate(...e)}exists(...e){var t;return(t=this.translator)==null?void 0:t.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,t={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const r=t.lng||this.resolvedLanguage||this.languages[0],n=this.options?this.options.fallbackLng:!1,i=this.languages[this.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const o=(l,d)=>{const c=this.services.backendConnector.state[`${l}|${d}`];return c===-1||c===0||c===2};if(t.precheck){const l=t.precheck(this,o);if(l!==void 0)return l}return!!(this.hasResourceBundle(r,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||o(r,e)&&(!n||o(i,e)))}loadNamespaces(e,t){const r=ye();return this.options.ns?(w(e)&&(e=[e]),e.forEach(n=>{this.options.ns.indexOf(n)<0&&this.options.ns.push(n)}),this.loadResources(n=>{r.resolve(),t&&t(n)}),r):(t&&t(),Promise.resolve())}loadLanguages(e,t){const r=ye();w(e)&&(e=[e]);const n=this.options.preload||[],i=e.filter(o=>n.indexOf(o)<0&&this.services.languageUtils.isSupportedCode(o));return i.length?(this.options.preload=n.concat(i),this.loadResources(o=>{r.resolve(),t&&t(o)}),r):(t&&t(),Promise.resolve())}dir(e){var n,i;if(e||(e=this.resolvedLanguage||(((n=this.languages)==null?void 0:n.length)>0?this.languages[0]:this.language)),!e)return"rtl";const t=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],r=((i=this.services)==null?void 0:i.languageUtils)||new Nt(Ot());return t.indexOf(r.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(e={},t){return new Ne(e,t)}cloneInstance(e={},t=Pe){const r=e.forkResourceStore;r&&delete e.forkResourceStore;const n={...this.options,...e,isClone:!0},i=new Ne(n);if((e.debug!==void 0||e.prefix!==void 0)&&(i.logger=i.logger.clone(e)),["store","services","language"].forEach(l=>{i[l]=this[l]}),i.services={...this.services},i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},r){const l=Object.keys(this.store.data).reduce((d,c)=>(d[c]={...this.store.data[c]},d[c]=Object.keys(d[c]).reduce((u,h)=>(u[h]={...d[c][h]},u),d[c]),d),{});i.store=new vt(l,n),i.services.resourceStore=i.store}return i.translator=new Ie(i.services,n),i.translator.on("*",(l,...d)=>{i.emit(l,...d)}),i.init(n,t),i.translator.options=n,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const V=Ne.createInstance();V.createInstance=Ne.createInstance;V.createInstance;V.dir;V.init;V.loadResources;V.reloadResources;V.use;V.changeLanguage;V.getFixedT;V.t;V.exists;V.setDefaultNamespace;V.hasLoadedNamespace;V.loadNamespaces;V.loadLanguages;const{slice:An,forEach:En}=[];function In(s){return En.call(An.call(arguments,1),e=>{if(e)for(const t in e)s[t]===void 0&&(s[t]=e[t])}),s}function Fn(s){return typeof s!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(t=>t.test(s))}const Pt=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,Mn=function(s,e){const r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},n=encodeURIComponent(e);let i=`${s}=${n}`;if(r.maxAge>0){const o=r.maxAge-0;if(Number.isNaN(o))throw new Error("maxAge should be a Number");i+=`; Max-Age=${Math.floor(o)}`}if(r.domain){if(!Pt.test(r.domain))throw new TypeError("option domain is invalid");i+=`; Domain=${r.domain}`}if(r.path){if(!Pt.test(r.path))throw new TypeError("option path is invalid");i+=`; Path=${r.path}`}if(r.expires){if(typeof r.expires.toUTCString!="function")throw new TypeError("option expires is invalid");i+=`; Expires=${r.expires.toUTCString()}`}if(r.httpOnly&&(i+="; HttpOnly"),r.secure&&(i+="; Secure"),r.sameSite)switch(typeof r.sameSite=="string"?r.sameSite.toLowerCase():r.sameSite){case!0:i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"strict":i+="; SameSite=Strict";break;case"none":i+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return r.partitioned&&(i+="; Partitioned"),i},Tt={create(s,e,t,r){let n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};t&&(n.expires=new Date,n.expires.setTime(n.expires.getTime()+t*60*1e3)),r&&(n.domain=r),document.cookie=Mn(s,encodeURIComponent(e),n)},read(s){const e=`${s}=`,t=document.cookie.split(";");for(let r=0;r<t.length;r++){let n=t[r];for(;n.charAt(0)===" ";)n=n.substring(1,n.length);if(n.indexOf(e)===0)return n.substring(e.length,n.length)}return null},remove(s){this.create(s,"",-1)}};var zn={name:"cookie",lookup(s){let{lookupCookie:e}=s;if(e&&typeof document<"u")return Tt.read(e)||void 0},cacheUserLanguage(s,e){let{lookupCookie:t,cookieMinutes:r,cookieDomain:n,cookieOptions:i}=e;t&&typeof document<"u"&&Tt.create(t,s,r,n,i)}},Dn={name:"querystring",lookup(s){var r;let{lookupQuerystring:e}=s,t;if(typeof window<"u"){let{search:n}=window.location;!window.location.search&&((r=window.location.hash)==null?void 0:r.indexOf("?"))>-1&&(n=window.location.hash.substring(window.location.hash.indexOf("?")));const o=n.substring(1).split("&");for(let l=0;l<o.length;l++){const d=o[l].indexOf("=");d>0&&o[l].substring(0,d)===e&&(t=o[l].substring(d+1))}}return t}};let fe=null;const $t=()=>{if(fe!==null)return fe;try{if(fe=typeof window<"u"&&window.localStorage!==null,!fe)return!1;const s="i18next.translate.boo";window.localStorage.setItem(s,"foo"),window.localStorage.removeItem(s)}catch{fe=!1}return fe};var Vn={name:"localStorage",lookup(s){let{lookupLocalStorage:e}=s;if(e&&$t())return window.localStorage.getItem(e)||void 0},cacheUserLanguage(s,e){let{lookupLocalStorage:t}=e;t&&$t()&&window.localStorage.setItem(t,s)}};let ge=null;const At=()=>{if(ge!==null)return ge;try{if(ge=typeof window<"u"&&window.sessionStorage!==null,!ge)return!1;const s="i18next.translate.boo";window.sessionStorage.setItem(s,"foo"),window.sessionStorage.removeItem(s)}catch{ge=!1}return ge};var Un={name:"sessionStorage",lookup(s){let{lookupSessionStorage:e}=s;if(e&&At())return window.sessionStorage.getItem(e)||void 0},cacheUserLanguage(s,e){let{lookupSessionStorage:t}=e;t&&At()&&window.sessionStorage.setItem(t,s)}},Bn={name:"navigator",lookup(s){const e=[];if(typeof navigator<"u"){const{languages:t,userLanguage:r,language:n}=navigator;if(t)for(let i=0;i<t.length;i++)e.push(t[i]);r&&e.push(r),n&&e.push(n)}return e.length>0?e:void 0}},Hn={name:"htmlTag",lookup(s){let{htmlTag:e}=s,t;const r=e||(typeof document<"u"?document.documentElement:null);return r&&typeof r.getAttribute=="function"&&(t=r.getAttribute("lang")),t}},Kn={name:"path",lookup(s){var n;let{lookupFromPathIndex:e}=s;if(typeof window>"u")return;const t=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(t)?(n=t[typeof e=="number"?e:0])==null?void 0:n.replace("/",""):void 0}},_n={name:"subdomain",lookup(s){var n,i;let{lookupFromSubdomainIndex:e}=s;const t=typeof e=="number"?e+1:1,r=typeof window<"u"&&((i=(n=window.location)==null?void 0:n.hostname)==null?void 0:i.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(r)return r[t]}};let ss=!1;try{document.cookie,ss=!0}catch{}const rs=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];ss||rs.splice(1,1);const Gn=()=>({order:rs,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:s=>s});class ns{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(e,t)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=e,this.options=In(t,this.options||{},Gn()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=n=>n.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=r,this.addDetector(zn),this.addDetector(Dn),this.addDetector(Vn),this.addDetector(Un),this.addDetector(Bn),this.addDetector(Hn),this.addDetector(Kn),this.addDetector(_n)}addDetector(e){return this.detectors[e.name]=e,this}detect(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,t=[];return e.forEach(r=>{if(this.detectors[r]){let n=this.detectors[r].lookup(this.options);n&&typeof n=="string"&&(n=[n]),n&&(t=t.concat(n))}}),t=t.filter(r=>r!=null&&!Fn(r)).map(r=>this.options.convertDetectedLanguage(r)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?t:t.length>0?t[0]:null}cacheUserLanguage(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;t&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||t.forEach(r=>{this.detectors[r]&&this.detectors[r].cacheUserLanguage(e,this.options)}))}}ns.type="languageDetector";const Wn={home:"Home",browse:"Browse",bookmarks:"Bookmarks",history:"History",profile:"Profile",admin:"Admin",login:"Login",logout:"Logout",register:"Register"},qn={search:"Search",filter:"Filter",sort:"Sort",save:"Save",cancel:"Cancel",delete:"Delete",edit:"Edit",view:"View",back:"Back",next:"Next",previous:"Previous",submit:"Submit",reset:"Reset"},Jn={loading:"Loading...",error:"Error",success:"Success",noData:"No data available",notFound:"Not found"},Qn={email:"Email",password:"Password",confirmPassword:"Confirm Password",username:"Username",required:"This field is required",invalidEmail:"Invalid email format",passwordMismatch:"Passwords do not match",minLength:"Minimum {{count}} characters required"},Xn={light:"Light",dark:"Dark",system:"System"},Yn={en:"English",vi:"Tiếng Việt"},Zn={navigation:Wn,actions:qn,status:Jn,forms:Qn,theme:Xn,language:Yn},ei="Manga",ti={author:"Author",artist:"Artist",status:"Status",genres:"Genres",rating:"Rating",chapters:"Chapters",views:"Views",description:"Description",alternativeTitle:"Alternative Title"},si={ongoing:"Ongoing",completed:"Completed",hiatus:"Hiatus",cancelled:"Cancelled"},ri={readNow:"Read Now",addBookmark:"Add to Bookmarks",removeBookmark:"Remove from Bookmarks",continueReading:"Continue Reading",startReading:"Start Reading",rate:"Rate this manga",comment:"Leave a comment"},ni={placeholder:"Search manga...",noResults:"No manga found",filters:{all:"All",genre:"Genre",status:"Status",sortBy:"Sort by",sortOrder:"Order"},sortOptions:{title:"Title",rating:"Rating",views:"Views",updated:"Last Updated",created:"Date Added"}},ii={title:"Comments",noComments:"No comments yet",writeComment:"Write a comment...",rating:"Your rating"},oi={title:ei,details:ti,status:si,actions:ri,search:ni,comments:ii},ai="Reader",li={previousChapter:"Previous Chapter",nextChapter:"Next Chapter",chapterList:"Chapter List",goToPage:"Go to page",pageOf:"Page {{current}} of {{total}}"},ci={title:"Reading Settings",readingMode:"Reading Mode",readingDirection:"Reading Direction",pageFit:"Page Fit",brightness:"Brightness",backgroundColor:"Background Color"},di={single:"Single Page",double:"Double Page",webtoon:"Webtoon"},ui={ltr:"Left to Right",rtl:"Right to Left"},hi={width:"Fit Width",height:"Fit Height",auto:"Auto Fit"},fi={fullscreen:"Fullscreen",exitFullscreen:"Exit Fullscreen",settings:"Settings",bookmark:"Bookmark",share:"Share"},gi={title:"Keyboard Shortcuts",nextPage:"Next Page",previousPage:"Previous Page",firstPage:"First Page",lastPage:"Last Page",toggleFullscreen:"Toggle Fullscreen",toggleSettings:"Toggle Settings"},mi={title:ai,navigation:li,settings:ci,modes:di,directions:ui,pageFit:hi,controls:fi,shortcuts:gi},pi={home:"Trang chủ",browse:"Duyệt",bookmarks:"Đánh dấu",history:"Lịch sử",profile:"Hồ sơ",admin:"Quản trị",login:"Đăng nhập",logout:"Đăng xuất",register:"Đăng ký"},xi={search:"Tìm kiếm",filter:"Lọc",sort:"Sắp xếp",save:"Lưu",cancel:"Hủy",delete:"Xóa",edit:"Sửa",view:"Xem",back:"Quay lại",next:"Tiếp theo",previous:"Trước đó",submit:"Gửi",reset:"Đặt lại"},bi={loading:"Đang tải...",error:"Lỗi",success:"Thành công",noData:"Không có dữ liệu",notFound:"Không tìm thấy"},yi={email:"Email",password:"Mật khẩu",confirmPassword:"Xác nhận mật khẩu",username:"Tên người dùng",required:"Trường này là bắt buộc",invalidEmail:"Định dạng email không hợp lệ",passwordMismatch:"Mật khẩu không khớp",minLength:"Tối thiểu {{count}} ký tự"},vi={light:"Sáng",dark:"Tối",system:"Hệ thống"},wi={en:"English",vi:"Tiếng Việt"},ki={navigation:pi,actions:xi,status:bi,forms:yi,theme:vi,language:wi},Ni="Truyện tranh",ji={author:"Tác giả",artist:"Họa sĩ",status:"Trạng thái",genres:"Thể loại",rating:"Đánh giá",chapters:"Chương",views:"Lượt xem",description:"Mô tả",alternativeTitle:"Tên khác"},Si={ongoing:"Đang tiến hành",completed:"Hoàn thành",hiatus:"Tạm dừng",cancelled:"Đã hủy"},Ci={readNow:"Đọc ngay",addBookmark:"Thêm vào đánh dấu",removeBookmark:"Xóa khỏi đánh dấu",continueReading:"Tiếp tục đọc",startReading:"Bắt đầu đọc",rate:"Đánh giá truyện này",comment:"Để lại bình luận"},Li={placeholder:"Tìm kiếm truyện...",noResults:"Không tìm thấy truyện nào",filters:{all:"Tất cả",genre:"Thể loại",status:"Trạng thái",sortBy:"Sắp xếp theo",sortOrder:"Thứ tự"},sortOptions:{title:"Tiêu đề",rating:"Đánh giá",views:"Lượt xem",updated:"Cập nhật gần nhất",created:"Ngày thêm"}},Oi={title:"Bình luận",noComments:"Chưa có bình luận nào",writeComment:"Viết bình luận...",rating:"Đánh giá của bạn"},Ri={title:Ni,details:ji,status:Si,actions:Ci,search:Li,comments:Oi},Pi="Đọc truyện",Ti={previousChapter:"Chương trước",nextChapter:"Chương sau",chapterList:"Danh sách chương",goToPage:"Đến trang",pageOf:"Trang {{current}} / {{total}}"},$i={title:"Cài đặt đọc",readingMode:"Chế độ đọc",readingDirection:"Hướng đọc",pageFit:"Khớp trang",brightness:"Độ sáng",backgroundColor:"Màu nền"},Ai={single:"Một trang",double:"Hai trang",webtoon:"Webtoon"},Ei={ltr:"Trái sang phải",rtl:"Phải sang trái"},Ii={width:"Khớp chiều rộng",height:"Khớp chiều cao",auto:"Tự động"},Fi={fullscreen:"Toàn màn hình",exitFullscreen:"Thoát toàn màn hình",settings:"Cài đặt",bookmark:"Đánh dấu",share:"Chia sẻ"},Mi={title:"Phím tắt",nextPage:"Trang sau",previousPage:"Trang trước",firstPage:"Trang đầu",lastPage:"Trang cuối",toggleFullscreen:"Bật/tắt toàn màn hình",toggleSettings:"Bật/tắt cài đặt"},zi={title:Pi,navigation:Ti,settings:$i,modes:Ai,directions:Ei,pageFit:Ii,controls:Fi,shortcuts:Mi},Di={en:{common:Zn,manga:oi,reader:mi},vi:{common:ki,manga:Ri,reader:zi}};V.use(ns).use(hr).init({resources:Di,fallbackLng:"en",debug:!1,defaultNS:"common",ns:["common","manga","reader"],interpolation:{escapeValue:!1},detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"]}});function Vi(){return a.jsxs(os,{client:ps,children:[a.jsx(fn,{}),!1]})}ms.createRoot(document.getElementById("root")).render(a.jsx(P.StrictMode,{children:a.jsx(Vi,{})}));
//# sourceMappingURL=index-DchgA8mS.js.map
