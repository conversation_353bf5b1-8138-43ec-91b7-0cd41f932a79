import React from 'react';
import { create<PERSON><PERSON>er<PERSON>outer, RouterProvider } from 'react-router-dom';
import { Layout } from '../components/layout/Layout';
import { HomePage } from '../pages/HomePage';
import { LoginPage } from '../pages/LoginPage';

// Lazy load pages for better performance
const BrowsePage = React.lazy(() => import('../pages/BrowsePage').then(m => ({ default: m.BrowsePage })));
const MangaDetailPage = React.lazy(() => import('../pages/MangaDetailPage').then(m => ({ default: m.MangaDetailPage })));
const ReaderPage = React.lazy(() => import('../pages/ReaderPage').then(m => ({ default: m.ReaderPage })));
const RegisterPage = React.lazy(() => import('../pages/RegisterPage').then(m => ({ default: m.RegisterPage })));
const BookmarksPage = React.lazy(() => import('../pages/BookmarksPage').then(m => ({ default: m.BookmarksPage })));
const HistoryPage = React.lazy(() => import('../pages/HistoryPage').then(m => ({ default: m.HistoryPage })));
const ProfilePage = React.lazy(() => import('../pages/ProfilePage').then(m => ({ default: m.ProfilePage })));

// Loading component
const PageLoader: React.FC = () => (
  <div className="flex items-center justify-center min-h-[50vh]">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
);

// Wrapper for lazy loaded components
const LazyWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <React.Suspense fallback={<PageLoader />}>
    {children}
  </React.Suspense>
);

const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: 'login',
        element: <LoginPage />,
      },
      {
        path: 'register',
        element: (
          <LazyWrapper>
            <RegisterPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'browse',
        element: (
          <LazyWrapper>
            <BrowsePage />
          </LazyWrapper>
        ),
      },
      {
        path: 'manga/:id',
        element: (
          <LazyWrapper>
            <MangaDetailPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'read/:chapterId',
        element: (
          <LazyWrapper>
            <ReaderPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'bookmarks',
        element: (
          <LazyWrapper>
            <BookmarksPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'history',
        element: (
          <LazyWrapper>
            <HistoryPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'profile',
        element: (
          <LazyWrapper>
            <ProfilePage />
          </LazyWrapper>
        ),
      },
    ],
  },
]);

export const AppRouter: React.FC = () => {
  return <RouterProvider router={router} />;
};
