import React from "react";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import {
  MainLayout,
  AuthLayout,
  ReaderLayout,
  AdminLayout,
} from "../components";
import { HomePage } from "../pages/HomePage";
import { LoginPage } from "../pages/LoginPage";

// Import pages directly for now (can be lazy loaded later)
import {
  BrowsePage,
  MangaDetailPage,
  ReaderPage,
  RegisterPage,
  BookmarksPage,
  HistoryPage,
  ProfilePage,
} from "../pages";
import { NotFoundPage } from "../pages/NotFoundPage";
import { ThemePage } from "../pages/ThemePage";

const router = createBrowserRouter([
  {
    path: "/",
    element: <MainLayout />,
    children: [
      // Public routes
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: "browse",
        element: <BrowsePage />,
      },
      {
        path: "manga/:id",
        element: <MangaDetailPage />,
      },
      {
        path: "theme",
        element: <ThemePage />,
      },

      // Auth routes (redirect if already logged in)
      {
        path: "auth",
        element: <AuthLayout requireAuth={false} />,
        children: [
          {
            path: "login",
            element: <LoginPage />,
          },
          {
            path: "register",
            element: <RegisterPage />,
          },
        ],
      },

      // Protected routes (require authentication)
      {
        path: "user",
        element: <AuthLayout requireAuth={true} />,
        children: [
          {
            path: "bookmarks",
            element: <BookmarksPage />,
          },
          {
            path: "history",
            element: <HistoryPage />,
          },
          {
            path: "profile",
            element: <ProfilePage />,
          },
        ],
      },
    ],
  },

  // Reader layout (fullscreen, no header/footer)
  {
    path: "/read",
    element: <ReaderLayout />,
    children: [
      {
        path: ":chapterId",
        element: <ReaderPage />,
      },
    ],
  },

  // Admin layout (protected, with sidebar)
  {
    path: "/admin",
    element: <AdminLayout />,
    children: [
      {
        index: true,
        element: <div>Admin Dashboard</div>,
      },
      {
        path: "manga",
        element: <div>Manga Management</div>,
      },
      {
        path: "users",
        element: <div>User Management</div>,
      },
      {
        path: "comments",
        element: <div>Comment Management</div>,
      },
    ],
  },

  // 404 page
  {
    path: "*",
    element: <MainLayout />,
    children: [
      {
        path: "*",
        element: <NotFoundPage />,
      },
    ],
  },
]);

export const AppRouter: React.FC = () => {
  return <RouterProvider router={router} />;
};
