import React from "react";
// import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { Button } from "../components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import { BrandShowcase } from "../components/common/BrandShowcase";
import { BookOpen, TrendingUp, Clock, Star } from "lucide-react";

export const HomePage: React.FC = () => {
  // const { t } = useTranslation(['common', 'manga']);

  return (
    <div className="container py-8">
      {/* Hero Section */}
      <section className="text-center py-12 relative overflow-hidden">
        <div
          className="absolute inset-0 opacity-10"
          style={{
            background:
              "linear-gradient(135deg, #E40066 0%, #03CEA4 50%, #9747FF 100%)",
          }}
        ></div>
        <div className="relative z-10">
          <h1 className="text-4xl font-bold tracking-tight lg:text-6xl mb-6 bg-gradient-to-r from-brand-primary via-brand-secondary to-brand-accent bg-clip-text text-transparent">
            Chào mừng đến với BlogTruyen
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Khám phá thế giới truyện tranh với hàng ngàn bộ manga chất lượng
            cao. Đọc miễn phí, không quảng cáo, trải nghiệm tuyệt vời.
          </p>
          <div className="flex gap-4 justify-center">
            <Link to="/browse">
              <Button size="lg">
                <BookOpen className="mr-2 h-4 w-4" />
                Bắt đầu đọc
              </Button>
            </Link>
            <Link to="/register">
              <Button variant="outline" size="lg">
                Đăng ký miễn phí
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-12">
        <h2 className="text-3xl font-bold text-center mb-8">
          Tính năng nổi bật
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="text-center">
              <TrendingUp className="h-8 w-8 mx-auto mb-2 text-primary" />
              <CardTitle className="text-lg">Xu hướng</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-center">
                Theo dõi những bộ truyện hot nhất, được cập nhật liên tục
              </CardDescription>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <Clock className="h-8 w-8 mx-auto mb-2 text-primary" />
              <CardTitle className="text-lg">Lịch sử đọc</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-center">
                Lưu lại tiến trình đọc, tiếp tục từ nơi bạn đã dừng lại
              </CardDescription>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <Star className="h-8 w-8 mx-auto mb-2 text-primary" />
              <CardTitle className="text-lg">Đánh giá</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-center">
                Đánh giá và bình luận về những bộ truyện yêu thích
              </CardDescription>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <BookOpen className="h-8 w-8 mx-auto mb-2 text-primary" />
              <CardTitle className="text-lg">Đa nền tảng</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-center">
                Đọc mọi lúc mọi nơi trên điện thoại, máy tính bảng, laptop
              </CardDescription>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Popular Manga Section */}
      <section className="py-12">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold">Truyện phổ biến</h2>
          <Link to="/browse">
            <Button variant="outline">Xem tất cả</Button>
          </Link>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {/* Mock manga cards */}
          {Array.from({ length: 6 }).map((_, index) => (
            <Card
              key={index}
              className="overflow-hidden hover:shadow-lg transition-shadow"
            >
              <div className="aspect-[3/4] bg-muted"></div>
              <CardContent className="p-3">
                <h3 className="font-semibold text-sm truncate">
                  Tên truyện {index + 1}
                </h3>
                <p className="text-xs text-muted-foreground">Tác giả</p>
                <div className="flex items-center mt-1">
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  <span className="text-xs ml-1">4.5</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Latest Updates */}
      <section className="py-12">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold">Cập nhật mới nhất</h2>
          <Link to="/browse?sort=updated">
            <Button variant="outline">Xem tất cả</Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-4">
                <div className="flex space-x-4">
                  <div className="w-16 h-20 bg-muted rounded"></div>
                  <div className="flex-1">
                    <h3 className="font-semibold mb-1">
                      Tên truyện {index + 1}
                    </h3>
                    <p className="text-sm text-muted-foreground mb-2">
                      Chapter 123
                    </p>
                    <p className="text-xs text-muted-foreground">2 giờ trước</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Brand Theme Showcase */}
      <section className="py-12">
        <h2 className="text-3xl font-bold text-center mb-8">Theme Showcase</h2>
        <BrandShowcase />
      </section>
    </div>
  );
};
