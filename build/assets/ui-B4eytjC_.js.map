{"version": 3, "file": "ui-B4eytjC_.js", "sources": ["../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "../../node_modules/@radix-ui/react-slot/dist/index.mjs", "../../node_modules/@radix-ui/react-primitive/dist/index.mjs", "../../node_modules/@radix-ui/primitive/dist/index.mjs", "../../node_modules/@radix-ui/react-context/dist/index.mjs", "../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "../../node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "../../node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "../../node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "../../node_modules/@radix-ui/react-focus-guards/dist/index.mjs", "../../node_modules/@radix-ui/react-focus-scope/dist/index.mjs", "../../node_modules/@radix-ui/react-id/dist/index.mjs", "../../node_modules/@radix-ui/react-portal/dist/index.mjs", "../../node_modules/@radix-ui/react-presence/dist/index.mjs", "../../node_modules/aria-hidden/dist/es2015/index.js", "../../node_modules/tslib/tslib.es6.mjs", "../../node_modules/react-remove-scroll-bar/dist/es2015/constants.js", "../../node_modules/use-callback-ref/dist/es2015/assignRef.js", "../../node_modules/use-callback-ref/dist/es2015/useRef.js", "../../node_modules/use-callback-ref/dist/es2015/useMergeRef.js", "../../node_modules/use-sidecar/dist/es2015/medium.js", "../../node_modules/use-sidecar/dist/es2015/exports.js", "../../node_modules/react-remove-scroll/dist/es2015/medium.js", "../../node_modules/react-remove-scroll/dist/es2015/UI.js", "../../node_modules/get-nonce/dist/es2015/index.js", "../../node_modules/react-style-singleton/dist/es2015/singleton.js", "../../node_modules/react-style-singleton/dist/es2015/hook.js", "../../node_modules/react-style-singleton/dist/es2015/component.js", "../../node_modules/react-remove-scroll-bar/dist/es2015/utils.js", "../../node_modules/react-remove-scroll-bar/dist/es2015/component.js", "../../node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "../../node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "../../node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "../../node_modules/react-remove-scroll/dist/es2015/sidecar.js", "../../node_modules/react-remove-scroll/dist/es2015/Combination.js"], "sourcesContent": ["// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment as Fragment2, jsx } from \"react/jsx-runtime\";\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== React.Fragment) {\n        props2.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props2);\n    }\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ jsx(Fragment2, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return React.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nexport {\n  Slot as Root,\n  Slot,\n  Slottable,\n  createSlot,\n  createSlottable\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport {\n  composeEventHandlers\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/use-controllable-state.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useInsertionEffect = React[\" useInsertionEffect \".trim().toString()] || useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = React.useRef(prop !== void 0);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\nimport * as React2 from \"react\";\nimport { useEffectEvent } from \"@radix-ui/react-use-effect-event\";\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = useEffectEvent(onChangeProp);\n  if (true) {\n    const isControlledRef = React2.useRef(controlledState !== void 0);\n    React2.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = React2.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React2.useRef(uncontrolledState);\n  React2.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = React2.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  React2.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\nexport {\n  useControllableState,\n  useControllableStateReducer\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-callback-ref/src/use-callback-ref.tsx\nimport * as React from \"react\";\nfunction useCallbackRef(callback) {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return React.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\nexport {\n  useCallbackRef\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\nimport * as React from \"react\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n  React.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\nexport {\n  useEscapeKeydown\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/dismissable-layer.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useEscapeKeydown } from \"@radix-ui/react-use-escape-keydown\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = React.createContext({\n  layers: /* @__PURE__ */ new Set(),\n  layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n  branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = \"none\";\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n          pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n          ...props.style\n        },\n        onFocusCapture: composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )\n      }\n    );\n  }\n);\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = React.forwardRef((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n  return /* @__PURE__ */ jsx(Primitive.div, { ...props, ref: composedRefs });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside);\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {\n  });\n  React.useEffect(() => {\n    const handlePointerDown = (event) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        let handleAndDispatchPointerDownOutsideEvent2 = function() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        };\n        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n        const eventDetail = { originalEvent: event };\n        if (event.pointerType === \"touch\") {\n          ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n          ownerDocument.addEventListener(\"click\", handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent2();\n        }\n      } else {\n        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n      ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => isPointerInsideReactTreeRef.current = true\n  };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside);\n  const isFocusInsideReactTreeRef = React.useRef(false);\n  React.useEffect(() => {\n    const handleFocus = (event) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false\n        });\n      }\n    };\n    ownerDocument.addEventListener(\"focusin\", handleFocus);\n    return () => ownerDocument.removeEventListener(\"focusin\", handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n  return {\n    onFocusCapture: () => isFocusInsideReactTreeRef.current = true,\n    onBlurCapture: () => isFocusInsideReactTreeRef.current = false\n  };\n}\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler, { once: true });\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\nexport {\n  Branch,\n  DismissableLayer,\n  DismissableLayerBranch,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// packages/react/focus-guards/src/focus-guards.tsx\nimport * as React from \"react\";\nvar count = 0;\nfunction FocusGuards(props) {\n  useFocusGuards();\n  return props.children;\n}\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n    document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n    count++;\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll(\"[data-radix-focus-guard]\").forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\nfunction createFocusGuard() {\n  const element = document.createElement(\"span\");\n  element.setAttribute(\"data-radix-focus-guard\", \"\");\n  element.tabIndex = 0;\n  element.style.outline = \"none\";\n  element.style.opacity = \"0\";\n  element.style.position = \"fixed\";\n  element.style.pointerEvents = \"none\";\n  return element;\n}\nvar Root = FocusGuards;\nexport {\n  FocusGuards,\n  Root,\n  useFocusGuards\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/focus-scope.tsx\nimport * as React from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { jsx } from \"react/jsx-runtime\";\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = { bubbles: false, cancelable: true };\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = React.forwardRef((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    }\n  }).current;\n  React.useEffect(() => {\n    if (trapped) {\n      let handleFocusIn2 = function(event) {\n        if (focusScope.paused || !container) return;\n        const target = event.target;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }, handleFocusOut2 = function(event) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget;\n        if (relatedTarget === null) return;\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }, handleMutations2 = function(mutations) {\n        const focusedElement = document.activeElement;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      };\n      var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n      document.addEventListener(\"focusin\", handleFocusIn2);\n      document.addEventListener(\"focusout\", handleFocusOut2);\n      const mutationObserver = new MutationObserver(handleMutations2);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n      return () => {\n        document.removeEventListener(\"focusin\", handleFocusIn2);\n        document.removeEventListener(\"focusout\", handleFocusOut2);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n  const handleKeyDown = React.useCallback(\n    (event) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n      const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement;\n      if (isTabKey && focusedElement) {\n        const container2 = event.currentTarget;\n        const [first, last] = getTabbableEdges(container2);\n        const hasTabbableElementsInside = first && last;\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container2) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n  return /* @__PURE__ */ jsx(Primitive.div, { tabIndex: -1, ...scopeProps, ref: composedRefs, onKeyDown: handleKeyDown });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\nfunction getTabbableEdges(container) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last];\n}\nfunction getTabbableCandidates(container) {\n  const nodes = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node) => {\n      const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    }\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode);\n  return nodes;\n}\nfunction findVisible(elements, container) {\n  for (const element of elements) {\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\nfunction isHidden(node, { upTo }) {\n  if (getComputedStyle(node).visibility === \"hidden\") return true;\n  while (node) {\n    if (upTo !== void 0 && node === upTo) return false;\n    if (getComputedStyle(node).display === \"none\") return true;\n    node = node.parentElement;\n  }\n  return false;\n}\nfunction isSelectableInput(element) {\n  return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    element.focus({ preventScroll: true });\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n  let stack = [];\n  return {\n    add(focusScope) {\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n    remove(focusScope) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    }\n  };\n}\nfunction arrayRemove(array, item) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\nfunction removeLinks(items) {\n  return items.filter((item) => item.tagName !== \"A\");\n}\nvar Root = FocusScope;\nexport {\n  FocusScope,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/id/src/id.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useReactId = React[\" useId \".trim().toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = React.useState(useReactId());\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\nexport {\n  useId\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/portal.tsx\nimport * as React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PORTAL_NAME = \"Portal\";\nvar Portal = React.forwardRef((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || mounted && globalThis?.document?.body;\n  return container ? ReactDOM.createPortal(/* @__PURE__ */ jsx(Primitive.div, { ...portalProps, ref: forwardedRef }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\nexport {\n  Portal,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/presence.tsx\nimport * as React2 from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\n\n// src/use-state-machine.tsx\nimport * as React from \"react\";\nfunction useStateMachine(initialState, machine) {\n  return React.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// src/presence.tsx\nvar Presence = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n  const child = typeof children === \"function\" ? children({ present: presence.isPresent }) : React2.Children.only(children);\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === \"function\";\n  return forceMount || presence.isPresent ? React2.cloneElement(child, { ref }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n  const [node, setNode] = React2.useState();\n  const stylesRef = React2.useRef(null);\n  const prevPresentRef = React2.useRef(present);\n  const prevAnimationNameRef = React2.useRef(\"none\");\n  const initialState = present ? \"mounted\" : \"unmounted\";\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: \"unmounted\",\n      ANIMATION_OUT: \"unmountSuspended\"\n    },\n    unmountSuspended: {\n      MOUNT: \"mounted\",\n      ANIMATION_END: \"unmounted\"\n    },\n    unmounted: {\n      MOUNT: \"mounted\"\n    }\n  });\n  React2.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n  }, [state]);\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n      if (present) {\n        send(\"MOUNT\");\n      } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n        send(\"UNMOUNT\");\n      } else {\n        const isAnimating = prevAnimationName !== currentAnimationName;\n        if (wasPresent && isAnimating) {\n          send(\"ANIMATION_OUT\");\n        } else {\n          send(\"UNMOUNT\");\n        }\n      }\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      const handleAnimationEnd = (event) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          send(\"ANIMATION_END\");\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = \"forwards\";\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === \"forwards\") {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event) => {\n        if (event.target === node) {\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener(\"animationstart\", handleAnimationStart);\n      node.addEventListener(\"animationcancel\", handleAnimationEnd);\n      node.addEventListener(\"animationend\", handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener(\"animationstart\", handleAnimationStart);\n        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n        node.removeEventListener(\"animationend\", handleAnimationEnd);\n      };\n    } else {\n      send(\"ANIMATION_END\");\n    }\n  }, [node, send]);\n  return {\n    isPresent: [\"mounted\", \"unmountSuspended\"].includes(state),\n    ref: React2.useCallback((node2) => {\n      stylesRef.current = node2 ? getComputedStyle(node2) : null;\n      setNode(node2);\n    }, [])\n  };\n}\nfunction getAnimationName(styles) {\n  return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Presence;\nexport {\n  Presence,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "var getDefaultParent = function (originalTarget) {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n    return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n    return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n    return targets\n        .map(function (target) {\n        if (parent.contains(target)) {\n            return target;\n        }\n        var correctedTarget = unwrapHost(target);\n        if (correctedTarget && parent.contains(correctedTarget)) {\n            return correctedTarget;\n        }\n        console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n        return null;\n    })\n        .filter(function (x) { return Boolean(x); });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n    var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    if (!markerMap[markerName]) {\n        markerMap[markerName] = new WeakMap();\n    }\n    var markerCounter = markerMap[markerName];\n    var hiddenNodes = [];\n    var elementsToKeep = new Set();\n    var elementsToStop = new Set(targets);\n    var keep = function (el) {\n        if (!el || elementsToKeep.has(el)) {\n            return;\n        }\n        elementsToKeep.add(el);\n        keep(el.parentNode);\n    };\n    targets.forEach(keep);\n    var deep = function (parent) {\n        if (!parent || elementsToStop.has(parent)) {\n            return;\n        }\n        Array.prototype.forEach.call(parent.children, function (node) {\n            if (elementsToKeep.has(node)) {\n                deep(node);\n            }\n            else {\n                try {\n                    var attr = node.getAttribute(controlAttribute);\n                    var alreadyHidden = attr !== null && attr !== 'false';\n                    var counterValue = (counterMap.get(node) || 0) + 1;\n                    var markerValue = (markerCounter.get(node) || 0) + 1;\n                    counterMap.set(node, counterValue);\n                    markerCounter.set(node, markerValue);\n                    hiddenNodes.push(node);\n                    if (counterValue === 1 && alreadyHidden) {\n                        uncontrolledNodes.set(node, true);\n                    }\n                    if (markerValue === 1) {\n                        node.setAttribute(markerName, 'true');\n                    }\n                    if (!alreadyHidden) {\n                        node.setAttribute(controlAttribute, 'true');\n                    }\n                }\n                catch (e) {\n                    console.error('aria-hidden: cannot operate on ', node, e);\n                }\n            }\n        });\n    };\n    deep(parentNode);\n    elementsToKeep.clear();\n    lockCount++;\n    return function () {\n        hiddenNodes.forEach(function (node) {\n            var counterValue = counterMap.get(node) - 1;\n            var markerValue = markerCounter.get(node) - 1;\n            counterMap.set(node, counterValue);\n            markerCounter.set(node, markerValue);\n            if (!counterValue) {\n                if (!uncontrolledNodes.has(node)) {\n                    node.removeAttribute(controlAttribute);\n                }\n                uncontrolledNodes.delete(node);\n            }\n            if (!markerValue) {\n                node.removeAttribute(markerName);\n            }\n        });\n        lockCount--;\n        if (!lockCount) {\n            // clear\n            counterMap = new WeakMap();\n            counterMap = new WeakMap();\n            uncontrolledNodes = new WeakMap();\n            markerMap = {};\n        }\n    };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var hideOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-aria-hidden'; }\n    var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    // we should not hide aria-live elements - https://github.com/theKashey/aria-hidden/issues/10\n    // and script elements, as they have no impact on accessibility.\n    targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live], script')));\n    return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var inertOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-inert-ed'; }\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nexport var supportsInert = function () {\n    return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var suppressOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-suppressed'; }\n    return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "export var zeroRightClassName = 'right-scroll-bar-position';\nexport var fullWidthClassName = 'width-before-scroll-bar';\nexport var noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nexport var removedBarSizeVariable = '--removed-body-scroll-bar-size';\n", "/**\n * Assigns a value for a given ref, no matter of the ref format\n * @param {RefObject} ref - a callback function or ref object\n * @param value - a new value\n *\n * @see https://github.com/theKashey/use-callback-ref#assignref\n * @example\n * const refObject = useRef();\n * const refFn = (ref) => {....}\n *\n * assignRef(refObject, \"refValue\");\n * assignRef(refFn, \"refValue\");\n */\nexport function assignRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    }\n    else if (ref) {\n        ref.current = value;\n    }\n    return ref;\n}\n", "import { useState } from 'react';\n/**\n * creates a MutableRef with ref change callback\n * @param initialValue - initial ref value\n * @param {Function} callback - a callback to run when value changes\n *\n * @example\n * const ref = useCallbackRef(0, (newValue, oldValue) => console.log(oldValue, '->', newValue);\n * ref.current = 1;\n * // prints 0 -> 1\n *\n * @see https://reactjs.org/docs/hooks-reference.html#useref\n * @see https://github.com/theKashey/use-callback-ref#usecallbackref---to-replace-reactuseref\n * @returns {MutableRefObject}\n */\nexport function useCallbackRef(initialValue, callback) {\n    var ref = useState(function () { return ({\n        // value\n        value: initialValue,\n        // last callback\n        callback: callback,\n        // \"memoized\" public interface\n        facade: {\n            get current() {\n                return ref.value;\n            },\n            set current(value) {\n                var last = ref.value;\n                if (last !== value) {\n                    ref.value = value;\n                    ref.callback(value, last);\n                }\n            },\n        },\n    }); })[0];\n    // update callback\n    ref.callback = callback;\n    return ref.facade;\n}\n", "import * as React from 'react';\nimport { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nvar currentValues = new WeakMap();\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n    var callbackRef = useCallbackRef(defaultValue || null, function (newValue) {\n        return refs.forEach(function (ref) { return assignRef(ref, newValue); });\n    });\n    // handle refs changes - added or removed\n    useIsomorphicLayoutEffect(function () {\n        var oldValue = currentValues.get(callbackRef);\n        if (oldValue) {\n            var prevRefs_1 = new Set(oldValue);\n            var nextRefs_1 = new Set(refs);\n            var current_1 = callbackRef.current;\n            prevRefs_1.forEach(function (ref) {\n                if (!nextRefs_1.has(ref)) {\n                    assignRef(ref, null);\n                }\n            });\n            nextRefs_1.forEach(function (ref) {\n                if (!prevRefs_1.has(ref)) {\n                    assignRef(ref, current_1);\n                }\n            });\n        }\n        currentValues.set(callbackRef, refs);\n    }, [refs]);\n    return callbackRef;\n}\n", "import { __assign } from \"tslib\";\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nexport function createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = __assign({ async: true, ssr: false }, options);\n    return medium;\n}\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = __rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, __assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nexport function exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\n", "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noRelative = props.noRelative, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, gapMode = props.gapMode, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noRelative\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noRelative: noRelative, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref, gapMode: gapMode })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n", "var currentNonce;\nexport var setNonce = function (nonce) {\n    currentNonce = nonce;\n};\nexport var getNonce = function () {\n    if (currentNonce) {\n        return currentNonce;\n    }\n    if (typeof __webpack_nonce__ !== 'undefined') {\n        return __webpack_nonce__;\n    }\n    return undefined;\n};\n", "import { getNonce } from 'get-nonce';\nfunction makeStyleTag() {\n    if (!document)\n        return null;\n    var tag = document.createElement('style');\n    tag.type = 'text/css';\n    var nonce = getNonce();\n    if (nonce) {\n        tag.setAttribute('nonce', nonce);\n    }\n    return tag;\n}\nfunction injectStyles(tag, css) {\n    // @ts-ignore\n    if (tag.styleSheet) {\n        // @ts-ignore\n        tag.styleSheet.cssText = css;\n    }\n    else {\n        tag.appendChild(document.createTextNode(css));\n    }\n}\nfunction insertStyleTag(tag) {\n    var head = document.head || document.getElementsByTagName('head')[0];\n    head.appendChild(tag);\n}\nexport var stylesheetSingleton = function () {\n    var counter = 0;\n    var stylesheet = null;\n    return {\n        add: function (style) {\n            if (counter == 0) {\n                if ((stylesheet = makeStyleTag())) {\n                    injectStyles(stylesheet, style);\n                    insertStyleTag(stylesheet);\n                }\n            }\n            counter++;\n        },\n        remove: function () {\n            counter--;\n            if (!counter && stylesheet) {\n                stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n                stylesheet = null;\n            }\n        },\n    };\n};\n", "import * as React from 'react';\nimport { stylesheetSingleton } from './singleton';\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nexport var styleHookSingleton = function () {\n    var sheet = stylesheetSingleton();\n    return function (styles, isDynamic) {\n        React.useEffect(function () {\n            sheet.add(styles);\n            return function () {\n                sheet.remove();\n            };\n        }, [styles && isDynamic]);\n    };\n};\n", "import { styleHook<PERSON>ingleton } from './hook';\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nexport var styleSingleton = function () {\n    var useStyle = styleHookSingleton();\n    var Sheet = function (_a) {\n        var styles = _a.styles, dynamic = _a.dynamic;\n        useStyle(styles, dynamic);\n        return null;\n    };\n    return Sheet;\n};\n", "export var zeroGap = {\n    left: 0,\n    top: 0,\n    right: 0,\n    gap: 0,\n};\nvar parse = function (x) { return parseInt(x || '', 10) || 0; };\nvar getOffset = function (gapMode) {\n    var cs = window.getComputedStyle(document.body);\n    var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n    var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n    var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n    return [parse(left), parse(top), parse(right)];\n};\nexport var getGapWidth = function (gapMode) {\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    if (typeof window === 'undefined') {\n        return zeroGap;\n    }\n    var offsets = getOffset(gapMode);\n    var documentWidth = document.documentElement.clientWidth;\n    var windowWidth = window.innerWidth;\n    return {\n        left: offsets[0],\n        top: offsets[1],\n        right: offsets[2],\n        gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0]),\n    };\n};\n", "import * as React from 'react';\nimport { styleSingleton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\nexport var lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n    var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n    return isFinite(counter) ? counter : 0;\n};\nexport var useLockAttribute = function () {\n    React.useEffect(function () {\n        document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n        return function () {\n            var newCounter = getCurrentUseCounter() - 1;\n            if (newCounter <= 0) {\n                document.body.removeAttribute(lockAttribute);\n            }\n            else {\n                document.body.setAttribute(lockAttribute, newCounter.toString());\n            }\n        };\n    }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (_a) {\n    var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? 'margin' : _b;\n    useLockAttribute();\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = React.useMemo(function () { return getGapWidth(gapMode); }, [gapMode]);\n    return React.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n", "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n", "var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    if (!(node instanceof Element)) {\n        return false;\n    }\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var ownerDocument = node.ownerDocument;\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), scrollHeight = _a[1], clientHeight = _a[2];\n            if (scrollHeight > clientHeight) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== ownerDocument.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        if (!target) {\n            break;\n        }\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        var parent_1 = target.parentNode;\n        // we will \"bubble\" from ShadowDom in case we are, or just to the parent in normal case\n        // this is the same logic used in focus-lock\n        target = (parent_1 && parent_1.nodeType === Node.DOCUMENT_FRAGMENT_NODE ? parent_1.host : parent_1);\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    // handle epsilon around 0 (non standard zoom levels)\n    if (isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScroll) < 1) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScrollTop) < 1) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n", "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(styleSingleton)[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if (('touches' in event && event.touches.length === 2) || (event.type === 'wheel' && event.ctrlKey)) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should, shadowParent: getOutermostShadowParent(target) };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { noRelative: props.noRelative, gapMode: props.gapMode }) : null));\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while (node !== null) {\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n", "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n"], "names": ["setRef", "ref", "value", "composeRefs", "refs", "node", "hasCleanup", "cleanups", "cleanup", "i", "useComposedRefs", "React.useCallback", "createSlot", "ownerName", "SlotClone", "createSlotClone", "Slot2", "React.forwardRef", "props", "forwardedRef", "children", "slotProps", "childrenA<PERSON>y", "React.Children", "slottable", "isSlottable", "newElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "React.isValidElement", "jsx", "React.cloneElement", "Slot", "childrenRef", "getElementRef", "props2", "mergeProps", "React.Fragment", "SLOTTABLE_IDENTIFIER", "childProps", "overrideProps", "propName", "slotPropValue", "childPropV<PERSON>ue", "args", "result", "element", "getter", "_a", "<PERSON><PERSON><PERSON><PERSON>", "_b", "NODES", "Primitive", "primitive", "Node", "<PERSON><PERSON><PERSON><PERSON>", "primitiveProps", "Comp", "dispatchDiscreteCustomEvent", "target", "event", "ReactDOM.flushSync", "composeEventHandlers", "originalEventHandler", "ourEventHandler", "checkForDefaultPrevented", "createContextScope", "scopeName", "createContextScopeDeps", "defaultContexts", "createContext3", "rootComponentName", "defaultContext", "BaseContext", "React.createContext", "index", "Provider", "scope", "context", "Context", "React.useMemo", "useContext2", "consumerName", "React.useContext", "createScope", "scopeContexts", "contexts", "composeContextScopes", "scopes", "baseScope", "scopeHooks", "createScope2", "overrideScopes", "nextScopes", "nextScopes2", "useScope", "currentScope", "useLayoutEffect2", "React.useLayoutEffect", "useInsertionEffect", "React", "useLayoutEffect", "useControllableState", "prop", "defaultProp", "onChange", "caller", "uncontrolledProp", "setUncontrolledProp", "onChangeRef", "useUncontrolledState", "isControlled", "isControlledRef", "React.useRef", "React.useEffect", "wasControlled", "setValue", "nextValue", "value2", "isFunction", "React.useState", "prevValueRef", "useCallbackRef", "callback", "callback<PERSON><PERSON>", "useEscapeKeydown", "onEscapeKeyDownProp", "ownerDocument", "onEscapeKeyDown", "handleKeyDown", "DISMISSABLE_LAYER_NAME", "CONTEXT_UPDATE", "POINTER_DOWN_OUTSIDE", "FOCUS_OUTSIDE", "originalBodyPointerEvents", "DismissableLayerContext", "Dismissa<PERSON><PERSON><PERSON><PERSON>", "disableOutsidePointerEvents", "onPointerDownOutside", "onFocusOutside", "onInteractOutside", "on<PERSON><PERSON><PERSON>", "layerProps", "setNode", "force", "composedRefs", "node2", "layers", "highestLayerWithOutsidePointerEventsDisabled", "highestLayerWithOutsidePointerEventsDisabledIndex", "isBodyPointerEventsDisabled", "isPointerEventsEnabled", "pointerDownOutside", "usePointerDownOutside", "isPointerDownOnBranch", "branch", "focusOutside", "useFocusOutside", "dispatchUpdate", "handleUpdate", "BRANCH_NAME", "DismissableLayerBranch", "handlePointerDownOutside", "isPointerInsideReactTreeRef", "handleClickRef", "handlePointerDown", "handleAndDispatchPointerDownOutsideEvent2", "handleAndDispatchCustomEvent", "eventDetail", "timerId", "handleFocusOutside", "isFocusInsideReactTreeRef", "handleFocus", "name", "handler", "detail", "discrete", "count", "useFocusGuards", "edgeGuards", "createFocusGuard", "AUTOFOCUS_ON_MOUNT", "AUTOFOCUS_ON_UNMOUNT", "EVENT_OPTIONS", "FOCUS_SCOPE_NAME", "FocusScope", "loop", "trapped", "onMountAutoFocusProp", "onUnmountAutoFocusProp", "scopeProps", "container", "<PERSON><PERSON><PERSON><PERSON>", "onMountAutoFocus", "onUnmountAutoFocus", "lastFocusedElementRef", "focusScope", "handleFocusIn2", "focus", "handleFocusOut2", "relatedTarget", "handleMutations2", "mutations", "mutation", "mutationObserver", "focusScopesStack", "previouslyFocusedElement", "mountEvent", "focusFirst", "removeLinks", "getTabbableCandidates", "unmountEvent", "isTabKey", "focusedElement", "container2", "first", "last", "getTabbableEdges", "candidates", "select", "candidate", "findVisible", "nodes", "walker", "isHiddenInput", "elements", "isHidden", "upTo", "isSelectableInput", "createFocusScopesStack", "stack", "activeFocusScope", "arrayRemove", "array", "item", "updatedArray", "items", "useReactId", "useId", "deterministicId", "id", "setId", "reactId", "PORTAL_NAME", "Portal", "containerProp", "portalProps", "mounted", "setMounted", "ReactDOM", "useStateMachine", "initialState", "machine", "React.useReducer", "state", "Presence", "present", "presence", "usePresence", "React2.Children", "React2.cloneElement", "React2.useState", "stylesRef", "React2.useRef", "prevPresentRef", "prevAnimationNameRef", "send", "React2.useEffect", "currentAnimationName", "getAnimationName", "styles", "wasPresent", "prevAnimationName", "timeoutId", "ownerWindow", "handleAnimationEnd", "isCurrentAnimation", "currentFillMode", "handleAnimationStart", "React2.useCallback", "getDefaultParent", "originalTarget", "sampleTarget", "counterMap", "uncontrolledNodes", "markerMap", "lockCount", "unwrapHost", "correctTargets", "parent", "targets", "<PERSON><PERSON><PERSON><PERSON>", "x", "applyAttributeToOthers", "parentNode", "markerName", "controlAttribute", "markerCounter", "hiddenNodes", "elementsToKeep", "elementsToStop", "keep", "el", "deep", "attr", "alreadyHidden", "counterValue", "markerValue", "e", "hideOthers", "activeParentNode", "__assign", "s", "n", "p", "__rest", "t", "__spread<PERSON><PERSON>y", "to", "from", "pack", "l", "ar", "zeroRightClassName", "fullWidthClassName", "noScrollbarsClassName", "removedBarSizeVariable", "assignRef", "initialValue", "useState", "useIsomorphicLayoutEffect", "currentV<PERSON>ues", "useMergeRefs", "defaultValue", "newValue", "oldValue", "prevRefs_1", "nextRefs_1", "current_1", "ItoI", "a", "innerCreateMedium", "defaults", "middleware", "buffer", "assigned", "medium", "data", "cb", "cbs", "pendingQueue", "executeQueue", "cycle", "filter", "createSidecarMedium", "options", "SideCar", "sideCar", "rest", "Target", "React.createElement", "exportSidecar", "exported", "effectCar", "nothing", "RemoveScroll", "parentRef", "callbacks", "setCallbacks", "forwardProps", "className", "removeScrollBar", "enabled", "shards", "noRelative", "noIsolation", "inert", "allowPinchZoom", "Container", "gapMode", "containerRef", "containerProps", "getNonce", "makeStyleTag", "tag", "nonce", "injectStyles", "css", "insertStyleTag", "head", "stylesheetSingleton", "counter", "stylesheet", "style", "styleHookSingleton", "sheet", "isDynamic", "styleSingleton", "useStyle", "Sheet", "dynamic", "zeroGap", "parse", "getOffset", "cs", "left", "top", "right", "getGapWidth", "offsets", "documentWidth", "windowWidth", "Style", "lockAttribute", "getStyles", "allowRelative", "important", "gap", "getCurrentUseCounter", "useLockAttribute", "newCounter", "RemoveScrollBar", "noImportant", "passiveSupported", "nonPassive", "alwaysContainsScroll", "elementCanBeScrolled", "overflow", "elementCouldBeVScrolled", "elementCouldBeHScrolled", "locationCouldBeScrolled", "axis", "current", "isScrollable", "elementCouldBeScrolled", "getScrollVariables", "scrollHeight", "clientHeight", "getVScrollVariables", "scrollTop", "getHScrollVariables", "scrollLeft", "scrollWidth", "clientWidth", "getDirectionFactor", "direction", "handleScroll", "end<PERSON>ar<PERSON>", "sourceDelta", "noOverscroll", "directionFactor", "delta", "targetInLock", "shouldCancelScroll", "isDeltaPositive", "availableScroll", "availableScrollTop", "position", "scroll_1", "capacity", "elementScroll", "parent_1", "getTouchXY", "getDeltaXY", "extractRef", "deltaCompare", "y", "generateStyle", "idCounter", "lockStack", "RemoveScrollSideCar", "shouldPreventQueue", "touchStartRef", "activeAxis", "lastProps", "allow_1", "shouldCancelEvent", "touch", "touchStart", "deltaX", "deltaY", "currentAxis", "moveDirection", "canBeScrolledInMainDirection", "cancelingAxis", "shouldPrevent", "_event", "sourceEvent", "shardNodes", "shouldStop", "shouldCancel", "should", "getOutermostShadowParent", "scrollTouchStart", "scrollWheel", "scrollTouchMove", "inst", "shadowParent", "ReactRemoveScroll"], "mappings": "yGAEA,SAASA,GAAOC,EAAKC,EAAO,CAC1B,GAAI,OAAOD,GAAQ,WACjB,OAAOA,EAAIC,CAAK,EACPD,GAAQ,OACjBA,EAAI,QAAUC,EAElB,CACA,SAASC,MAAeC,EAAM,CAC5B,OAAQC,GAAS,CACf,IAAIC,EAAa,GACjB,MAAMC,EAAWH,EAAK,IAAKH,GAAQ,CACjC,MAAMO,EAAUR,GAAOC,EAAKI,CAAI,EAChC,MAAI,CAACC,GAAc,OAAOE,GAAW,aACnCF,EAAa,IAERE,CACb,CAAK,EACD,GAAIF,EACF,MAAO,IAAM,CACX,QAASG,EAAI,EAAGA,EAAIF,EAAS,OAAQE,IAAK,CACxC,MAAMD,EAAUD,EAASE,CAAC,EACtB,OAAOD,GAAW,WACpBA,EAAS,EAETR,GAAOI,EAAKK,CAAC,EAAG,IAAI,CAEhC,CACO,CAEJ,CACH,CACA,SAASC,KAAmBN,EAAM,CAChC,OAAOO,EAAiB,YAACR,GAAY,GAAGC,CAAI,EAAGA,CAAI,CACrD,CC9BA,SAASQ,GAAWC,EAAW,CAC7B,MAAMC,EAA4BC,GAAgBF,CAAS,EACrDG,EAAQC,EAAAA,WAAiB,CAACC,EAAOC,IAAiB,CACtD,KAAM,CAAE,SAAAC,EAAU,GAAGC,CAAS,EAAKH,EAC7BI,EAAgBC,EAAAA,SAAe,QAAQH,CAAQ,EAC/CI,EAAYF,EAAc,KAAKG,EAAW,EAChD,GAAID,EAAW,CACb,MAAME,EAAaF,EAAU,MAAM,SAC7BG,EAAcL,EAAc,IAAKM,GACjCA,IAAUJ,EACRD,EAAc,SAAC,MAAMG,CAAU,EAAI,EAAUH,EAAc,SAAC,KAAK,IAAI,EAClEM,EAAAA,eAAqBH,CAAU,EAAIA,EAAW,MAAM,SAAW,KAE/DE,CAEV,EACD,OAAuBE,EAAAA,IAAIhB,EAAW,CAAE,GAAGO,EAAW,IAAKF,EAAc,SAAUU,EAAoB,eAACH,CAAU,EAAIK,EAAAA,aAAmBL,EAAY,OAAQC,CAAW,EAAI,KAAM,CACxL,CACI,OAAuBG,EAAAA,IAAIhB,EAAW,CAAE,GAAGO,EAAW,IAAKF,EAAc,SAAAC,EAAU,CACvF,CAAG,EACD,OAAAJ,EAAM,YAAc,GAAGH,CAAS,QACzBG,CACT,CACG,IAACgB,GAAuBpB,GAAW,MAAM,EAE5C,SAASG,GAAgBF,EAAW,CAClC,MAAMC,EAAYG,EAAAA,WAAiB,CAACC,EAAOC,IAAiB,CAC1D,KAAM,CAAE,SAAAC,EAAU,GAAGC,CAAS,EAAKH,EACnC,GAAIW,EAAAA,eAAqBT,CAAQ,EAAG,CAClC,MAAMa,EAAcC,GAAcd,CAAQ,EACpCe,EAASC,GAAWf,EAAWD,EAAS,KAAK,EACnD,OAAIA,EAAS,OAASiB,aACpBF,EAAO,IAAMhB,EAAehB,GAAYgB,EAAcc,CAAW,EAAIA,GAEhEF,EAAkB,aAACX,EAAUe,CAAM,CAChD,CACI,OAAOZ,EAAc,SAAC,MAAMH,CAAQ,EAAI,EAAIG,WAAe,KAAK,IAAI,EAAI,IAC5E,CAAG,EACD,OAAAT,EAAU,YAAc,GAAGD,CAAS,aAC7BC,CACT,CACA,IAAIwB,GAAuB,OAAO,iBAAiB,EAWnD,SAASb,GAAYG,EAAO,CAC1B,OAAOC,EAAoB,eAACD,CAAK,GAAK,OAAOA,EAAM,MAAS,YAAc,cAAeA,EAAM,MAAQA,EAAM,KAAK,YAAcU,EAClI,CACA,SAASF,GAAWf,EAAWkB,EAAY,CACzC,MAAMC,EAAgB,CAAE,GAAGD,CAAY,EACvC,UAAWE,KAAYF,EAAY,CACjC,MAAMG,EAAgBrB,EAAUoB,CAAQ,EAClCE,EAAiBJ,EAAWE,CAAQ,EACxB,WAAW,KAAKA,CAAQ,EAEpCC,GAAiBC,EACnBH,EAAcC,CAAQ,EAAI,IAAIG,IAAS,CACrC,MAAMC,EAASF,EAAe,GAAGC,CAAI,EACrC,OAAAF,EAAc,GAAGE,CAAI,EACdC,CACR,EACQH,IACTF,EAAcC,CAAQ,EAAIC,GAEnBD,IAAa,QACtBD,EAAcC,CAAQ,EAAI,CAAE,GAAGC,EAAe,GAAGC,CAAgB,EACxDF,IAAa,cACtBD,EAAcC,CAAQ,EAAI,CAACC,EAAeC,CAAc,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG,EAExF,CACE,MAAO,CAAE,GAAGtB,EAAW,GAAGmB,CAAe,CAC3C,CACA,SAASN,GAAcY,EAAS,SAC9B,IAAIC,GAASC,EAAA,OAAO,yBAAyBF,EAAQ,MAAO,KAAK,IAApD,YAAAE,EAAuD,IAChEC,EAAUF,GAAU,mBAAoBA,GAAUA,EAAO,eAC7D,OAAIE,EACKH,EAAQ,KAEjBC,GAASG,EAAA,OAAO,yBAAyBJ,EAAS,KAAK,IAA9C,YAAAI,EAAiD,IAC1DD,EAAUF,GAAU,mBAAoBA,GAAUA,EAAO,eACrDE,EACKH,EAAQ,MAAM,IAEhBA,EAAQ,MAAM,KAAOA,EAAQ,IACtC,CC3FA,IAAIK,GAAQ,CACV,IACA,SACA,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,SACA,OACA,MACA,IACF,EACIC,EAAYD,GAAM,OAAO,CAACE,EAAWhD,IAAS,CAChD,MAAM2B,EAAOpB,GAAW,aAAaP,CAAI,EAAE,EACrCiD,EAAOrC,EAAAA,WAAiB,CAACC,EAAOC,IAAiB,CACrD,KAAM,CAAE,QAAAoC,EAAS,GAAGC,CAAc,EAAKtC,EACjCuC,EAAOF,EAAUvB,EAAO3B,EAC9B,OAAI,OAAO,OAAW,MACpB,OAAO,OAAO,IAAI,UAAU,CAAC,EAAI,IAEZyB,EAAAA,IAAI2B,EAAM,CAAE,GAAGD,EAAgB,IAAKrC,EAAc,CAC7E,CAAG,EACD,OAAAmC,EAAK,YAAc,aAAajD,CAAI,GAC7B,CAAE,GAAGgD,EAAW,CAAChD,CAAI,EAAGiD,CAAM,CACvC,EAAG,CAAE,CAAA,EACL,SAASI,GAA4BC,EAAQC,EAAO,CAC9CD,GAAQE,GAAAA,UAAmB,IAAMF,EAAO,cAAcC,CAAK,CAAC,CAClE,CCtCA,SAASE,EAAqBC,EAAsBC,EAAiB,CAAE,yBAAAC,EAA2B,EAAM,EAAG,GAAI,CAC7G,OAAO,SAAqBL,EAAO,CAEjC,GADAG,GAAA,MAAAA,EAAuBH,GACnBK,IAA6B,IAAS,CAACL,EAAM,iBAC/C,OAAOI,GAAA,YAAAA,EAAkBJ,EAE5B,CACH,CCWA,SAASM,GAAmBC,EAAWC,EAAyB,GAAI,CAClE,IAAIC,EAAkB,CAAE,EACxB,SAASC,EAAeC,EAAmBC,EAAgB,CACzD,MAAMC,EAAcC,EAAmB,cAACF,CAAc,EAChDG,EAAQN,EAAgB,OAC9BA,EAAkB,CAAC,GAAGA,EAAiBG,CAAc,EACrD,MAAMI,EAAY1D,GAAU,OAC1B,KAAM,CAAE,MAAA2D,EAAO,SAAAzD,EAAU,GAAG0D,CAAS,EAAG5D,EAClC6D,IAAU/B,EAAA6B,GAAA,YAAAA,EAAQV,KAAR,YAAAnB,EAAqB2B,KAAUF,EACzCvE,EAAQ8E,EAAAA,QAAc,IAAMF,EAAS,OAAO,OAAOA,CAAO,CAAC,EACjE,OAAuBhD,EAAAA,IAAIiD,EAAQ,SAAU,CAAE,MAAA7E,EAAO,SAAAkB,CAAQ,CAAE,CACjE,EACDwD,EAAS,YAAcL,EAAoB,WAC3C,SAASU,EAAYC,EAAcL,EAAO,OACxC,MAAME,IAAU/B,EAAA6B,GAAA,YAAAA,EAAQV,KAAR,YAAAnB,EAAqB2B,KAAUF,EACzCK,EAAUK,EAAgB,WAACJ,CAAO,EACxC,GAAID,EAAS,OAAOA,EACpB,GAAIN,IAAmB,OAAQ,OAAOA,EACtC,MAAM,IAAI,MAAM,KAAKU,CAAY,4BAA4BX,CAAiB,IAAI,CACxF,CACI,MAAO,CAACK,EAAUK,CAAW,CACjC,CACE,MAAMG,EAAc,IAAM,CACxB,MAAMC,EAAgBhB,EAAgB,IAAKG,GAClCE,EAAAA,cAAoBF,CAAc,CAC1C,EACD,OAAO,SAAkBK,EAAO,CAC9B,MAAMS,GAAWT,GAAA,YAAAA,EAAQV,KAAckB,EACvC,OAAOL,EAAa,QAClB,KAAO,CAAE,CAAC,UAAUb,CAAS,EAAE,EAAG,CAAE,GAAGU,EAAO,CAACV,CAAS,EAAGmB,CAAQ,IACnE,CAACT,EAAOS,CAAQ,CACjB,CACF,CACF,EACD,OAAAF,EAAY,UAAYjB,EACjB,CAACG,EAAgBiB,GAAqBH,EAAa,GAAGhB,CAAsB,CAAC,CACtF,CACA,SAASmB,MAAwBC,EAAQ,CACvC,MAAMC,EAAYD,EAAO,CAAC,EAC1B,GAAIA,EAAO,SAAW,EAAG,OAAOC,EAChC,MAAML,EAAc,IAAM,CACxB,MAAMM,EAAaF,EAAO,IAAKG,IAAkB,CAC/C,SAAUA,EAAc,EACxB,UAAWA,EAAa,SAC9B,EAAM,EACF,OAAO,SAA2BC,EAAgB,CAChD,MAAMC,EAAaH,EAAW,OAAO,CAACI,EAAa,CAAE,SAAAC,EAAU,UAAA5B,KAAgB,CAE7E,MAAM6B,EADaD,EAASH,CAAc,EACV,UAAUzB,CAAS,EAAE,EACrD,MAAO,CAAE,GAAG2B,EAAa,GAAGE,CAAc,CAC3C,EAAE,EAAE,EACL,OAAOhB,UAAc,KAAO,CAAE,CAAC,UAAUS,EAAU,SAAS,EAAE,EAAGI,CAAU,GAAK,CAACA,CAAU,CAAC,CAC7F,CACF,EACD,OAAAT,EAAY,UAAYK,EAAU,UAC3BL,CACT,CCzEG,IAACa,EAAmB,6BAAY,SAAWC,EAAAA,gBAAwB,IAAM,CAC5E,ECAIC,GAAqBC,GAAM,uBAAuB,KAAI,EAAG,SAAU,CAAA,GAAKC,EAC5E,SAASC,GAAqB,CAC5B,KAAAC,EACA,YAAAC,EACA,SAAAC,EAAW,IAAM,CAChB,EACD,OAAAC,CACF,EAAG,CACD,KAAM,CAACC,EAAkBC,EAAqBC,CAAW,EAAIC,GAAqB,CAChF,YAAAN,EACA,SAAAC,CACJ,CAAG,EACKM,EAAeR,IAAS,OACxBrG,EAAQ6G,EAAeR,EAAOI,EAC1B,CACR,MAAMK,EAAkBC,EAAAA,OAAaV,IAAS,MAAM,EACpDW,EAAAA,UAAgB,IAAM,CACpB,MAAMC,EAAgBH,EAAgB,QAClCG,IAAkBJ,GAGpB,QAAQ,KACN,GAAGL,CAAM,qBAHES,EAAgB,aAAe,cAGR,OAFzBJ,EAAe,aAAe,cAEI,4KAC5C,EAEHC,EAAgB,QAAUD,CAChC,EAAO,CAACA,EAAcL,CAAM,CAAC,CAC7B,CACE,MAAMU,EAAWzG,EAAiB,YAC/B0G,GAAc,OACb,GAAIN,EAAc,CAChB,MAAMO,EAASC,GAAWF,CAAS,EAAIA,EAAUd,CAAI,EAAIc,EACrDC,IAAWf,KACbvD,EAAA6D,EAAY,UAAZ,MAAA7D,EAAA,KAAA6D,EAAsBS,GAEhC,MACQV,EAAoBS,CAAS,CAEhC,EACD,CAACN,EAAcR,EAAMK,EAAqBC,CAAW,CACtD,EACD,MAAO,CAAC3G,EAAOkH,CAAQ,CACzB,CACA,SAASN,GAAqB,CAC5B,YAAAN,EACA,SAAAC,CACF,EAAG,CACD,KAAM,CAACvG,EAAOkH,CAAQ,EAAII,EAAAA,SAAehB,CAAW,EAC9CiB,EAAeR,EAAY,OAAC/G,CAAK,EACjC2G,EAAcI,EAAY,OAACR,CAAQ,EACzC,OAAAN,GAAmB,IAAM,CACvBU,EAAY,QAAUJ,CAC1B,EAAK,CAACA,CAAQ,CAAC,EACbS,EAAAA,UAAgB,IAAM,OAChBO,EAAa,UAAYvH,KAC3B8C,EAAA6D,EAAY,UAAZ,MAAA7D,EAAA,KAAA6D,EAAsB3G,GACtBuH,EAAa,QAAUvH,EAE7B,EAAK,CAACA,EAAOuH,CAAY,CAAC,EACjB,CAACvH,EAAOkH,EAAUP,CAAW,CACtC,CACA,SAASU,GAAWrH,EAAO,CACzB,OAAO,OAAOA,GAAU,UAC1B,CChEA,SAASwH,EAAeC,EAAU,CAChC,MAAMC,EAAcX,EAAY,OAACU,CAAQ,EACzCT,OAAAA,EAAAA,UAAgB,IAAM,CACpBU,EAAY,QAAUD,CAC1B,CAAG,EACM3C,EAAa,QAAC,IAAM,IAAIpC,IAAI,OAAK,OAAAI,EAAA4E,EAAY,UAAZ,YAAA5E,EAAA,KAAA4E,EAAsB,GAAGhF,IAAO,EAAE,CAC5E,CCLA,SAASiF,GAAiBC,EAAqBC,EAAgB,mCAAY,SAAU,CACnF,MAAMC,EAAkBN,EAAeI,CAAmB,EAC1DZ,EAAAA,UAAgB,IAAM,CACpB,MAAMe,EAAiBrE,GAAU,CAC3BA,EAAM,MAAQ,UAChBoE,EAAgBpE,CAAK,CAExB,EACD,OAAAmE,EAAc,iBAAiB,UAAWE,EAAe,CAAE,QAAS,GAAM,EACnE,IAAMF,EAAc,oBAAoB,UAAWE,EAAe,CAAE,QAAS,GAAM,CAC9F,EAAK,CAACD,EAAiBD,CAAa,CAAC,CACrC,CCJA,IAAIG,GAAyB,mBACzBC,GAAiB,0BACjBC,GAAuB,sCACvBC,GAAgB,gCAChBC,GACAC,GAA0B7D,EAAAA,cAAoB,CAChD,OAAwB,IAAI,IAC5B,uCAAwD,IAAI,IAC5D,SAA0B,IAAI,GAChC,CAAC,EACG8D,GAAmBvH,EAAgB,WACrC,CAACC,EAAOC,IAAiB,CACvB,KAAM,CACJ,4BAAAsH,EAA8B,GAC9B,gBAAAT,EACA,qBAAAU,EACA,eAAAC,EACA,kBAAAC,EACA,UAAAC,EACA,GAAGC,CACT,EAAQ5H,EACE4D,EAAUK,EAAgB,WAACoD,EAAuB,EAClD,CAAClI,EAAM0I,CAAO,EAAIvB,EAAAA,SAAe,IAAI,EACrCO,GAAgB1H,GAAA,YAAAA,EAAM,iBAAiB,mCAAY,UACnD,EAAG2I,CAAK,EAAIxB,EAAc,SAAC,EAAE,EAC7ByB,EAAevI,EAAgBS,EAAe+H,GAAUH,EAAQG,CAAK,CAAC,EACtEC,EAAS,MAAM,KAAKrE,EAAQ,MAAM,EAClC,CAACsE,CAA4C,EAAI,CAAC,GAAGtE,EAAQ,sCAAsC,EAAE,MAAM,EAAE,EAC7GuE,EAAoDF,EAAO,QAAQC,CAA4C,EAC/GzE,EAAQtE,EAAO8I,EAAO,QAAQ9I,CAAI,EAAI,GACtCiJ,EAA8BxE,EAAQ,uCAAuC,KAAO,EACpFyE,EAAyB5E,GAAS0E,EAClCG,EAAqBC,GAAuB7F,GAAU,CAC1D,MAAMD,EAASC,EAAM,OACf8F,EAAwB,CAAC,GAAG5E,EAAQ,QAAQ,EAAE,KAAM6E,GAAWA,EAAO,SAAShG,CAAM,CAAC,EACxF,CAAC4F,GAA0BG,IAC/BhB,GAAA,MAAAA,EAAuB9E,GACvBgF,GAAA,MAAAA,EAAoBhF,GACfA,EAAM,kBAAkBiF,GAAA,MAAAA,IAC9B,EAAEd,CAAa,EACV6B,EAAeC,GAAiBjG,GAAU,CAC9C,MAAMD,EAASC,EAAM,OACG,CAAC,GAAGkB,EAAQ,QAAQ,EAAE,KAAM6E,GAAWA,EAAO,SAAShG,CAAM,CAAC,IAEtFgF,GAAA,MAAAA,EAAiB/E,GACjBgF,GAAA,MAAAA,EAAoBhF,GACfA,EAAM,kBAAkBiF,GAAA,MAAAA,IAC9B,EAAEd,CAAa,EAChB,OAAAF,GAAkBjE,GAAU,CACHe,IAAUG,EAAQ,OAAO,KAAO,IAEvDkD,GAAA,MAAAA,EAAkBpE,GACd,CAACA,EAAM,kBAAoBiF,IAC7BjF,EAAM,eAAgB,EACtBiF,EAAW,GAEd,EAAEd,CAAa,EAChBb,EAAAA,UAAgB,IAAM,CACpB,GAAK7G,EACL,OAAIoI,IACE3D,EAAQ,uCAAuC,OAAS,IAC1DwD,GAA4BP,EAAc,KAAK,MAAM,cACrDA,EAAc,KAAK,MAAM,cAAgB,QAE3CjD,EAAQ,uCAAuC,IAAIzE,CAAI,GAEzDyE,EAAQ,OAAO,IAAIzE,CAAI,EACvByJ,GAAgB,EACT,IAAM,CACPrB,GAA+B3D,EAAQ,uCAAuC,OAAS,IACzFiD,EAAc,KAAK,MAAM,cAAgBO,GAE5C,CACF,EAAE,CAACjI,EAAM0H,EAAeU,EAA6B3D,CAAO,CAAC,EAC9DoC,EAAAA,UAAgB,IACP,IAAM,CACN7G,IACLyE,EAAQ,OAAO,OAAOzE,CAAI,EAC1ByE,EAAQ,uCAAuC,OAAOzE,CAAI,EAC1DyJ,GAAgB,EACjB,EACA,CAACzJ,EAAMyE,CAAO,CAAC,EAClBoC,EAAAA,UAAgB,IAAM,CACpB,MAAM6C,EAAe,IAAMf,EAAM,EAAE,EACnC,gBAAS,iBAAiBb,GAAgB4B,CAAY,EAC/C,IAAM,SAAS,oBAAoB5B,GAAgB4B,CAAY,CACvE,EAAE,EAAE,EACkBjI,EAAG,IACxBsB,EAAU,IACV,CACE,GAAG0F,EACH,IAAKG,EACL,MAAO,CACL,cAAeK,EAA8BC,EAAyB,OAAS,OAAS,OACxF,GAAGrI,EAAM,KACV,EACD,eAAgB4C,EAAqB5C,EAAM,eAAgB0I,EAAa,cAAc,EACtF,cAAe9F,EAAqB5C,EAAM,cAAe0I,EAAa,aAAa,EACnF,qBAAsB9F,EACpB5C,EAAM,qBACNsI,EAAmB,oBAC7B,CACA,CACK,CACL,CACA,EACAhB,GAAiB,YAAcN,GAC/B,IAAI8B,GAAc,yBACdC,GAAyBhJ,EAAgB,WAAC,CAACC,EAAOC,IAAiB,CACrE,MAAM2D,EAAUK,EAAgB,WAACoD,EAAuB,EAClDtI,EAAMgH,EAAY,OAAC,IAAI,EACvBgC,EAAevI,EAAgBS,EAAclB,CAAG,EACtDiH,OAAAA,EAAAA,UAAgB,IAAM,CACpB,MAAM7G,EAAOJ,EAAI,QACjB,GAAII,EACF,OAAAyE,EAAQ,SAAS,IAAIzE,CAAI,EAClB,IAAM,CACXyE,EAAQ,SAAS,OAAOzE,CAAI,CAC7B,CAEP,EAAK,CAACyE,EAAQ,QAAQ,CAAC,EACEhD,EAAAA,IAAIsB,EAAU,IAAK,CAAE,GAAGlC,EAAO,IAAK+H,EAAc,CAC3E,CAAC,EACDgB,GAAuB,YAAcD,GACrC,SAASP,GAAsBf,EAAsBX,EAAgB,mCAAY,SAAU,CACzF,MAAMmC,EAA2BxC,EAAegB,CAAoB,EAC9DyB,EAA8BlD,EAAY,OAAC,EAAK,EAChDmD,EAAiBnD,EAAAA,OAAa,IAAM,CAC5C,CAAG,EACDC,OAAAA,EAAAA,UAAgB,IAAM,CACpB,MAAMmD,EAAqBzG,GAAU,CACnC,GAAIA,EAAM,QAAU,CAACuG,EAA4B,QAAS,CACxD,IAAIG,EAA4C,UAAW,CACzDC,GACEnC,GACA8B,EACAM,EACA,CAAE,SAAU,EAAI,CACjB,CACF,EAED,MAAMA,EAAc,CAAE,cAAe5G,CAAO,EACxCA,EAAM,cAAgB,SACxBmE,EAAc,oBAAoB,QAASqC,EAAe,OAAO,EACjEA,EAAe,QAAUE,EACzBvC,EAAc,iBAAiB,QAASqC,EAAe,QAAS,CAAE,KAAM,GAAM,GAE9EE,EAA2C,CAErD,MACQvC,EAAc,oBAAoB,QAASqC,EAAe,OAAO,EAEnED,EAA4B,QAAU,EACvC,EACKM,EAAU,OAAO,WAAW,IAAM,CACtC1C,EAAc,iBAAiB,cAAesC,CAAiB,CAChE,EAAE,CAAC,EACJ,MAAO,IAAM,CACX,OAAO,aAAaI,CAAO,EAC3B1C,EAAc,oBAAoB,cAAesC,CAAiB,EAClEtC,EAAc,oBAAoB,QAASqC,EAAe,OAAO,CAClE,CACL,EAAK,CAACrC,EAAemC,CAAwB,CAAC,EACrC,CAEL,qBAAsB,IAAMC,EAA4B,QAAU,EACnE,CACH,CACA,SAASN,GAAgBlB,EAAgBZ,EAAgB,mCAAY,SAAU,CAC7E,MAAM2C,EAAqBhD,EAAeiB,CAAc,EAClDgC,EAA4B1D,EAAY,OAAC,EAAK,EACpDC,OAAAA,EAAAA,UAAgB,IAAM,CACpB,MAAM0D,EAAehH,GAAU,CACzBA,EAAM,QAAU,CAAC+G,EAA0B,SAE7CJ,GAA6BlC,GAAeqC,EADxB,CAAE,cAAe9G,CAAO,EACiC,CAC3E,SAAU,EACpB,CAAS,CAEJ,EACD,OAAAmE,EAAc,iBAAiB,UAAW6C,CAAW,EAC9C,IAAM7C,EAAc,oBAAoB,UAAW6C,CAAW,CACzE,EAAK,CAAC7C,EAAe2C,CAAkB,CAAC,EAC/B,CACL,eAAgB,IAAMC,EAA0B,QAAU,GAC1D,cAAe,IAAMA,EAA0B,QAAU,EAC1D,CACH,CACA,SAASb,IAAiB,CACxB,MAAMlG,EAAQ,IAAI,YAAYuE,EAAc,EAC5C,SAAS,cAAcvE,CAAK,CAC9B,CACA,SAAS2G,GAA6BM,EAAMC,EAASC,EAAQ,CAAE,SAAAC,CAAQ,EAAI,CACzE,MAAMrH,EAASoH,EAAO,cAAc,OAC9BnH,EAAQ,IAAI,YAAYiH,EAAM,CAAE,QAAS,GAAO,WAAY,GAAM,OAAAE,EAAQ,EAC5ED,GAASnH,EAAO,iBAAiBkH,EAAMC,EAAS,CAAE,KAAM,GAAM,EAC9DE,EACFtH,GAA4BC,EAAQC,CAAK,EAEzCD,EAAO,cAAcC,CAAK,CAE9B,CC/MA,IAAIqH,EAAQ,EAKZ,SAASC,IAAiB,CACxBhE,EAAAA,UAAgB,IAAM,CACpB,MAAMiE,EAAa,SAAS,iBAAiB,0BAA0B,EACvE,gBAAS,KAAK,sBAAsB,aAAcA,EAAW,CAAC,GAAKC,IAAkB,EACrF,SAAS,KAAK,sBAAsB,YAAaD,EAAW,CAAC,GAAKC,IAAkB,EACpFH,IACO,IAAM,CACPA,IAAU,GACZ,SAAS,iBAAiB,0BAA0B,EAAE,QAAS5K,GAASA,EAAK,QAAQ,EAEvF4K,GACD,CACF,EAAE,EAAE,CACP,CACA,SAASG,IAAmB,CAC1B,MAAMtI,EAAU,SAAS,cAAc,MAAM,EAC7C,OAAAA,EAAQ,aAAa,yBAA0B,EAAE,EACjDA,EAAQ,SAAW,EACnBA,EAAQ,MAAM,QAAU,OACxBA,EAAQ,MAAM,QAAU,IACxBA,EAAQ,MAAM,SAAW,QACzBA,EAAQ,MAAM,cAAgB,OACvBA,CACT,CCxBA,IAAIuI,EAAqB,8BACrBC,EAAuB,gCACvBC,GAAgB,CAAE,QAAS,GAAO,WAAY,EAAM,EACpDC,GAAmB,aACnBC,GAAaxK,EAAgB,WAAC,CAACC,EAAOC,IAAiB,CACzD,KAAM,CACJ,KAAAuK,EAAO,GACP,QAAAC,EAAU,GACV,iBAAkBC,EAClB,mBAAoBC,EACpB,GAAGC,CACP,EAAM5K,EACE,CAAC6K,EAAWC,CAAY,EAAIxE,EAAAA,SAAe,IAAI,EAC/CyE,EAAmBvE,EAAekE,CAAoB,EACtDM,EAAqBxE,EAAemE,CAAsB,EAC1DM,EAAwBlF,EAAY,OAAC,IAAI,EACzCgC,EAAevI,EAAgBS,EAAed,GAAS2L,EAAa3L,CAAI,CAAC,EACzE+L,EAAanF,EAAAA,OAAa,CAC9B,OAAQ,GACR,OAAQ,CACN,KAAK,OAAS,EACf,EACD,QAAS,CACP,KAAK,OAAS,EACpB,CACG,CAAA,EAAE,QACHC,EAAAA,UAAgB,IAAM,CACpB,GAAIyE,EAAS,CACX,IAAIU,EAAiB,SAASzI,EAAO,CACnC,GAAIwI,EAAW,QAAU,CAACL,EAAW,OACrC,MAAMpI,EAASC,EAAM,OACjBmI,EAAU,SAASpI,CAAM,EAC3BwI,EAAsB,QAAUxI,EAEhC2I,EAAMH,EAAsB,QAAS,CAAE,OAAQ,EAAI,CAAE,CAE/D,EAASI,EAAkB,SAAS3I,EAAO,CACnC,GAAIwI,EAAW,QAAU,CAACL,EAAW,OACrC,MAAMS,EAAgB5I,EAAM,cACxB4I,IAAkB,OACjBT,EAAU,SAASS,CAAa,GACnCF,EAAMH,EAAsB,QAAS,CAAE,OAAQ,EAAI,CAAE,EAE/D,EAASM,EAAmB,SAASC,EAAW,CAExC,GADuB,SAAS,gBACT,SAAS,KAChC,UAAWC,KAAYD,EACjBC,EAAS,aAAa,OAAS,GAAGL,EAAMP,CAAS,CAExD,EAED,SAAS,iBAAiB,UAAWM,CAAc,EACnD,SAAS,iBAAiB,WAAYE,CAAe,EACrD,MAAMK,EAAmB,IAAI,iBAAiBH,CAAgB,EAC9D,OAAIV,GAAWa,EAAiB,QAAQb,EAAW,CAAE,UAAW,GAAM,QAAS,GAAM,EAC9E,IAAM,CACX,SAAS,oBAAoB,UAAWM,CAAc,EACtD,SAAS,oBAAoB,WAAYE,CAAe,EACxDK,EAAiB,WAAY,CAC9B,CACP,CACG,EAAE,CAACjB,EAASI,EAAWK,EAAW,MAAM,CAAC,EAC1ClF,EAAAA,UAAgB,IAAM,CACpB,GAAI6E,EAAW,CACbc,GAAiB,IAAIT,CAAU,EAC/B,MAAMU,EAA2B,SAAS,cAE1C,GAAI,CADwBf,EAAU,SAASe,CAAwB,EAC7C,CACxB,MAAMC,EAAa,IAAI,YAAY1B,EAAoBE,EAAa,EACpEQ,EAAU,iBAAiBV,EAAoBY,CAAgB,EAC/DF,EAAU,cAAcgB,CAAU,EAC7BA,EAAW,mBACdC,GAAWC,GAAYC,GAAsBnB,CAAS,CAAC,EAAG,CAAE,OAAQ,GAAM,EACtE,SAAS,gBAAkBe,GAC7BR,EAAMP,CAAS,EAG3B,CACM,MAAO,IAAM,CACXA,EAAU,oBAAoBV,EAAoBY,CAAgB,EAClE,WAAW,IAAM,CACf,MAAMkB,EAAe,IAAI,YAAY7B,EAAsBC,EAAa,EACxEQ,EAAU,iBAAiBT,EAAsBY,CAAkB,EACnEH,EAAU,cAAcoB,CAAY,EAC/BA,EAAa,kBAChBb,EAAMQ,GAA4B,SAAS,KAAM,CAAE,OAAQ,GAAM,EAEnEf,EAAU,oBAAoBT,EAAsBY,CAAkB,EACtEW,GAAiB,OAAOT,CAAU,CACnC,EAAE,CAAC,CACL,CACP,CACG,EAAE,CAACL,EAAWE,EAAkBC,EAAoBE,CAAU,CAAC,EAChE,MAAMnE,EAAgBtH,EAAiB,YACpCiD,GAAU,CAET,GADI,CAAC8H,GAAQ,CAACC,GACVS,EAAW,OAAQ,OACvB,MAAMgB,EAAWxJ,EAAM,MAAQ,OAAS,CAACA,EAAM,QAAU,CAACA,EAAM,SAAW,CAACA,EAAM,QAC5EyJ,EAAiB,SAAS,cAChC,GAAID,GAAYC,EAAgB,CAC9B,MAAMC,EAAa1J,EAAM,cACnB,CAAC2J,EAAOC,CAAI,EAAIC,GAAiBH,CAAU,EACfC,GAASC,EAIrC,CAAC5J,EAAM,UAAYyJ,IAAmBG,GACxC5J,EAAM,eAAgB,EAClB8H,GAAMY,EAAMiB,EAAO,CAAE,OAAQ,EAAI,CAAE,GAC9B3J,EAAM,UAAYyJ,IAAmBE,IAC9C3J,EAAM,eAAgB,EAClB8H,GAAMY,EAAMkB,EAAM,CAAE,OAAQ,EAAI,CAAE,GAPpCH,IAAmBC,GAAY1J,EAAM,eAAgB,CAUnE,CACK,EACD,CAAC8H,EAAMC,EAASS,EAAW,MAAM,CAClC,EACD,OAAuBtK,MAAIsB,EAAU,IAAK,CAAE,SAAU,GAAI,GAAG0I,EAAY,IAAK7C,EAAc,UAAWhB,CAAa,CAAE,CACxH,CAAC,EACDwD,GAAW,YAAcD,GACzB,SAASwB,GAAWU,EAAY,CAAE,OAAAC,EAAS,EAAK,EAAK,CAAA,EAAI,CACvD,MAAMb,EAA2B,SAAS,cAC1C,UAAWc,KAAaF,EAEtB,GADApB,EAAMsB,EAAW,CAAE,OAAAD,EAAQ,EACvB,SAAS,gBAAkBb,EAA0B,MAE7D,CACA,SAASW,GAAiB1B,EAAW,CACnC,MAAM2B,EAAaR,GAAsBnB,CAAS,EAC5CwB,EAAQM,GAAYH,EAAY3B,CAAS,EACzCyB,EAAOK,GAAYH,EAAW,QAAO,EAAI3B,CAAS,EACxD,MAAO,CAACwB,EAAOC,CAAI,CACrB,CACA,SAASN,GAAsBnB,EAAW,CACxC,MAAM+B,EAAQ,CAAE,EACVC,EAAS,SAAS,iBAAiBhC,EAAW,WAAW,aAAc,CAC3E,WAAa1L,GAAS,CACpB,MAAM2N,EAAgB3N,EAAK,UAAY,SAAWA,EAAK,OAAS,SAChE,OAAIA,EAAK,UAAYA,EAAK,QAAU2N,EAAsB,WAAW,YAC9D3N,EAAK,UAAY,EAAI,WAAW,cAAgB,WAAW,WACxE,CACA,CAAG,EACD,KAAO0N,EAAO,SAAU,GAAED,EAAM,KAAKC,EAAO,WAAW,EACvD,OAAOD,CACT,CACA,SAASD,GAAYI,EAAUlC,EAAW,CACxC,UAAWjJ,KAAWmL,EACpB,GAAI,CAACC,GAASpL,EAAS,CAAE,KAAMiJ,CAAS,CAAE,EAAG,OAAOjJ,CAExD,CACA,SAASoL,GAAS7N,EAAM,CAAE,KAAA8N,GAAQ,CAChC,GAAI,iBAAiB9N,CAAI,EAAE,aAAe,SAAU,MAAO,GAC3D,KAAOA,GAAM,CACX,GAAI8N,IAAS,QAAU9N,IAAS8N,EAAM,MAAO,GAC7C,GAAI,iBAAiB9N,CAAI,EAAE,UAAY,OAAQ,MAAO,GACtDA,EAAOA,EAAK,aAChB,CACE,MAAO,EACT,CACA,SAAS+N,GAAkBtL,EAAS,CAClC,OAAOA,aAAmB,kBAAoB,WAAYA,CAC5D,CACA,SAASwJ,EAAMxJ,EAAS,CAAE,OAAA6K,EAAS,EAAK,EAAK,CAAA,EAAI,CAC/C,GAAI7K,GAAWA,EAAQ,MAAO,CAC5B,MAAMgK,EAA2B,SAAS,cAC1ChK,EAAQ,MAAM,CAAE,cAAe,EAAI,CAAE,EACjCA,IAAYgK,GAA4BsB,GAAkBtL,CAAO,GAAK6K,GACxE7K,EAAQ,OAAQ,CACtB,CACA,CACA,IAAI+J,GAAmBwB,GAAwB,EAC/C,SAASA,IAAyB,CAChC,IAAIC,EAAQ,CAAE,EACd,MAAO,CACL,IAAIlC,EAAY,CACd,MAAMmC,EAAmBD,EAAM,CAAC,EAC5BlC,IAAemC,IACjBA,GAAA,MAAAA,EAAkB,SAEpBD,EAAQE,GAAYF,EAAOlC,CAAU,EACrCkC,EAAM,QAAQlC,CAAU,CACzB,EACD,OAAOA,EAAY,OACjBkC,EAAQE,GAAYF,EAAOlC,CAAU,GACrCpJ,EAAAsL,EAAM,CAAC,IAAP,MAAAtL,EAAU,QAChB,CACG,CACH,CACA,SAASwL,GAAYC,EAAOC,EAAM,CAChC,MAAMC,EAAe,CAAC,GAAGF,CAAK,EACxB9J,EAAQgK,EAAa,QAAQD,CAAI,EACvC,OAAI/J,IAAU,IACZgK,EAAa,OAAOhK,EAAO,CAAC,EAEvBgK,CACT,CACA,SAAS1B,GAAY2B,EAAO,CAC1B,OAAOA,EAAM,OAAQF,GAASA,EAAK,UAAY,GAAG,CACpD,CC5MA,IAAIG,GAAazI,GAAM,UAAU,KAAM,EAAC,SAAU,CAAA,IAAM,IAAA,IACpD6E,GAAQ,EACZ,SAAS6D,GAAMC,EAAiB,CAC9B,KAAM,CAACC,EAAIC,CAAK,EAAIzH,EAAc,SAACqH,GAAU,CAAE,EAC/CxI,OAAAA,EAAgB,IAAM,CACE4I,EAAOC,GAAYA,GAAW,OAAOjE,IAAO,CAAC,CACvE,EAAK,CAAC8D,CAAe,CAAC,EACOC,EAAK,SAASA,CAAE,GAAK,EAClD,CCHA,IAAIG,GAAc,SACdC,GAASnO,EAAgB,WAAC,CAACC,EAAOC,IAAiB,OACrD,KAAM,CAAE,UAAWkO,EAAe,GAAGC,CAAa,EAAGpO,EAC/C,CAACqO,EAASC,CAAU,EAAIhI,EAAAA,SAAe,EAAK,EAClDnB,EAAgB,IAAMmJ,EAAW,EAAI,EAAG,CAAA,CAAE,EAC1C,MAAMzD,EAAYsD,GAAiBE,KAAWvM,EAAA,mCAAY,WAAZ,YAAAA,EAAsB,MACpE,OAAO+I,EAAY0D,GAAS,aAA6B3N,EAAG,IAACsB,EAAU,IAAK,CAAE,GAAGkM,EAAa,IAAKnO,CAAc,CAAA,EAAG4K,CAAS,EAAI,IACnI,CAAC,EACDqD,GAAO,YAAcD,GCPrB,SAASO,GAAgBC,EAAcC,EAAS,CAC9C,OAAOC,EAAgB,WAAC,CAACC,EAAOlM,IACZgM,EAAQE,CAAK,EAAElM,CAAK,GAClBkM,EACnBH,CAAY,CACjB,CAGG,IAACI,GAAY7O,GAAU,CACxB,KAAM,CAAE,QAAA8O,EAAS,SAAA5O,CAAQ,EAAKF,EACxB+O,EAAWC,GAAYF,CAAO,EAC9BpO,EAAQ,OAAOR,GAAa,WAAaA,EAAS,CAAE,QAAS6O,EAAS,SAAS,CAAE,EAAIE,WAAgB,KAAK/O,CAAQ,EAClHnB,EAAMS,EAAgBuP,EAAS,IAAK/N,GAAcN,CAAK,CAAC,EAE9D,OADmB,OAAOR,GAAa,YAClB6O,EAAS,UAAYG,EAAAA,aAAoBxO,EAAO,CAAE,IAAA3B,CAAK,CAAA,EAAI,IAClF,EACA8P,GAAS,YAAc,WACvB,SAASG,GAAYF,EAAS,CAC5B,KAAM,CAAC3P,EAAM0I,CAAO,EAAIsH,WAAiB,EACnCC,EAAYC,EAAa,OAAC,IAAI,EAC9BC,EAAiBD,EAAa,OAACP,CAAO,EACtCS,EAAuBF,EAAa,OAAC,MAAM,EAC3CZ,EAAeK,EAAU,UAAY,YACrC,CAACF,EAAOY,CAAI,EAAIhB,GAAgBC,EAAc,CAClD,QAAS,CACP,QAAS,YACT,cAAe,kBAChB,EACD,iBAAkB,CAChB,MAAO,UACP,cAAe,WAChB,EACD,UAAW,CACT,MAAO,SACb,CACA,CAAG,EACDgB,OAAAA,EAAAA,UAAiB,IAAM,CACrB,MAAMC,EAAuBC,EAAiBP,EAAU,OAAO,EAC/DG,EAAqB,QAAUX,IAAU,UAAYc,EAAuB,MAChF,EAAK,CAACd,CAAK,CAAC,EACVzJ,EAAgB,IAAM,CACpB,MAAMyK,EAASR,EAAU,QACnBS,EAAaP,EAAe,QAElC,GAD0BO,IAAef,EAClB,CACrB,MAAMgB,EAAoBP,EAAqB,QACzCG,EAAuBC,EAAiBC,CAAM,EAChDd,EACFU,EAAK,OAAO,EACHE,IAAyB,SAAUE,GAAA,YAAAA,EAAQ,WAAY,OAChEJ,EAAK,SAAS,EAIZA,EADEK,GADgBC,IAAsBJ,EAEnC,gBAEA,SAFe,EAKxBJ,EAAe,QAAUR,CAC/B,CACA,EAAK,CAACA,EAASU,CAAI,CAAC,EAClBrK,EAAgB,IAAM,CACpB,GAAIhG,EAAM,CACR,IAAI4Q,EACJ,MAAMC,EAAc7Q,EAAK,cAAc,aAAe,OAChD8Q,EAAsBvN,GAAU,CAEpC,MAAMwN,EADuBP,EAAiBP,EAAU,OAAO,EACf,SAAS1M,EAAM,aAAa,EAC5E,GAAIA,EAAM,SAAWvD,GAAQ+Q,IAC3BV,EAAK,eAAe,EAChB,CAACF,EAAe,SAAS,CAC3B,MAAMa,EAAkBhR,EAAK,MAAM,kBACnCA,EAAK,MAAM,kBAAoB,WAC/B4Q,EAAYC,EAAY,WAAW,IAAM,CACnC7Q,EAAK,MAAM,oBAAsB,aACnCA,EAAK,MAAM,kBAAoBgR,EAE/C,CAAa,CACb,CAEO,EACKC,EAAwB1N,GAAU,CAClCA,EAAM,SAAWvD,IACnBoQ,EAAqB,QAAUI,EAAiBP,EAAU,OAAO,EAEpE,EACD,OAAAjQ,EAAK,iBAAiB,iBAAkBiR,CAAoB,EAC5DjR,EAAK,iBAAiB,kBAAmB8Q,CAAkB,EAC3D9Q,EAAK,iBAAiB,eAAgB8Q,CAAkB,EACjD,IAAM,CACXD,EAAY,aAAaD,CAAS,EAClC5Q,EAAK,oBAAoB,iBAAkBiR,CAAoB,EAC/DjR,EAAK,oBAAoB,kBAAmB8Q,CAAkB,EAC9D9Q,EAAK,oBAAoB,eAAgB8Q,CAAkB,CAC5D,CACP,MACMT,EAAK,eAAe,CAE1B,EAAK,CAACrQ,EAAMqQ,CAAI,CAAC,EACR,CACL,UAAW,CAAC,UAAW,kBAAkB,EAAE,SAASZ,CAAK,EACzD,IAAKyB,EAAAA,YAAoBrI,GAAU,CACjCoH,EAAU,QAAUpH,EAAQ,iBAAiBA,CAAK,EAAI,KACtDH,EAAQG,CAAK,CACnB,EAAO,CAAE,CAAA,CACN,CACH,CACA,SAAS2H,EAAiBC,EAAQ,CAChC,OAAOA,GAAA,YAAAA,EAAQ,gBAAiB,MAClC,CACA,SAAS5O,GAAcY,EAAS,SAC9B,IAAIC,GAASC,EAAA,OAAO,yBAAyBF,EAAQ,MAAO,KAAK,IAApD,YAAAE,EAAuD,IAChEC,EAAUF,GAAU,mBAAoBA,GAAUA,EAAO,eAC7D,OAAIE,EACKH,EAAQ,KAEjBC,GAASG,EAAA,OAAO,yBAAyBJ,EAAS,KAAK,IAA9C,YAAAI,EAAiD,IAC1DD,EAAUF,GAAU,mBAAoBA,GAAUA,EAAO,eACrDE,EACKH,EAAQ,MAAM,IAEhBA,EAAQ,MAAM,KAAOA,EAAQ,IACtC,CCpIA,IAAI0O,GAAmB,SAAUC,EAAgB,CAC7C,GAAI,OAAO,SAAa,IACpB,OAAO,KAEX,IAAIC,EAAe,MAAM,QAAQD,CAAc,EAAIA,EAAe,CAAC,EAAIA,EACvE,OAAOC,EAAa,cAAc,IACtC,EACIC,EAAa,IAAI,QACjBC,EAAoB,IAAI,QACxBC,EAAY,CAAE,EACdC,EAAY,EACZC,GAAa,SAAU1R,EAAM,CAC7B,OAAOA,IAASA,EAAK,MAAQ0R,GAAW1R,EAAK,UAAU,EAC3D,EACI2R,GAAiB,SAAUC,EAAQC,EAAS,CAC5C,OAAOA,EACF,IAAI,SAAUvO,EAAQ,CACvB,GAAIsO,EAAO,SAAStO,CAAM,EACtB,OAAOA,EAEX,IAAIwO,EAAkBJ,GAAWpO,CAAM,EACvC,OAAIwO,GAAmBF,EAAO,SAASE,CAAe,EAC3CA,GAEX,QAAQ,MAAM,cAAexO,EAAQ,0BAA2BsO,EAAQ,iBAAiB,EAClF,KACV,CAAA,EACI,OAAO,SAAUG,EAAG,CAAE,MAAO,EAAQA,EAAK,CACnD,EASIC,GAAyB,SAAUZ,EAAgBa,EAAYC,EAAYC,EAAkB,CAC7F,IAAIN,EAAUF,GAAeM,EAAY,MAAM,QAAQb,CAAc,EAAIA,EAAiB,CAACA,CAAc,CAAC,EACrGI,EAAUU,CAAU,IACrBV,EAAUU,CAAU,EAAI,IAAI,SAEhC,IAAIE,EAAgBZ,EAAUU,CAAU,EACpCG,EAAc,CAAE,EAChBC,EAAiB,IAAI,IACrBC,EAAiB,IAAI,IAAIV,CAAO,EAChCW,EAAO,SAAUC,EAAI,CACjB,CAACA,GAAMH,EAAe,IAAIG,CAAE,IAGhCH,EAAe,IAAIG,CAAE,EACrBD,EAAKC,EAAG,UAAU,EACrB,EACDZ,EAAQ,QAAQW,CAAI,EACpB,IAAIE,EAAO,SAAUd,EAAQ,CACrB,CAACA,GAAUW,EAAe,IAAIX,CAAM,GAGxC,MAAM,UAAU,QAAQ,KAAKA,EAAO,SAAU,SAAU5R,EAAM,CAC1D,GAAIsS,EAAe,IAAItS,CAAI,EACvB0S,EAAK1S,CAAI,MAGT,IAAI,CACA,IAAI2S,EAAO3S,EAAK,aAAamS,CAAgB,EACzCS,EAAgBD,IAAS,MAAQA,IAAS,QAC1CE,GAAgBvB,EAAW,IAAItR,CAAI,GAAK,GAAK,EAC7C8S,GAAeV,EAAc,IAAIpS,CAAI,GAAK,GAAK,EACnDsR,EAAW,IAAItR,EAAM6S,CAAY,EACjCT,EAAc,IAAIpS,EAAM8S,CAAW,EACnCT,EAAY,KAAKrS,CAAI,EACjB6S,IAAiB,GAAKD,GACtBrB,EAAkB,IAAIvR,EAAM,EAAI,EAEhC8S,IAAgB,GAChB9S,EAAK,aAAakS,EAAY,MAAM,EAEnCU,GACD5S,EAAK,aAAamS,EAAkB,MAAM,CAElE,OACuBY,EAAG,CACN,QAAQ,MAAM,kCAAmC/S,EAAM+S,CAAC,CAC5E,CAEA,CAAS,CACJ,EACD,OAAAL,EAAKT,CAAU,EACfK,EAAe,MAAO,EACtBb,IACO,UAAY,CACfY,EAAY,QAAQ,SAAUrS,EAAM,CAChC,IAAI6S,EAAevB,EAAW,IAAItR,CAAI,EAAI,EACtC8S,EAAcV,EAAc,IAAIpS,CAAI,EAAI,EAC5CsR,EAAW,IAAItR,EAAM6S,CAAY,EACjCT,EAAc,IAAIpS,EAAM8S,CAAW,EAC9BD,IACItB,EAAkB,IAAIvR,CAAI,GAC3BA,EAAK,gBAAgBmS,CAAgB,EAEzCZ,EAAkB,OAAOvR,CAAI,GAE5B8S,GACD9S,EAAK,gBAAgBkS,CAAU,CAE/C,CAAS,EACDT,IACKA,IAEDH,EAAa,IAAI,QACjBA,EAAa,IAAI,QACjBC,EAAoB,IAAI,QACxBC,EAAY,CAAE,EAErB,CACL,EAQWwB,GAAa,SAAU5B,EAAgBa,EAAYC,EAAY,CAClEA,IAAe,SAAUA,EAAa,oBAC1C,IAAIL,EAAU,MAAM,KAAK,MAAM,QAAQT,CAAc,EAAIA,EAAiB,CAACA,CAAc,CAAC,EACtF6B,EAAiC9B,GAAiBC,CAAc,EACpE,OAAK6B,GAKLpB,EAAQ,KAAK,MAAMA,EAAS,MAAM,KAAKoB,EAAiB,iBAAiB,qBAAqB,CAAC,CAAC,EACzFjB,GAAuBH,EAASoB,EAAkBf,EAAY,aAAa,GALvE,UAAY,CAAE,OAAO,IAAO,CAM3C,ECvGWgB,EAAW,UAAW,CAC/B,OAAAA,EAAW,OAAO,QAAU,SAAkB,EAAG,CAC7C,QAASC,EAAG/S,EAAI,EAAGgT,EAAI,UAAU,OAAQhT,EAAIgT,EAAGhT,IAAK,CACjD+S,EAAI,UAAU/S,CAAC,EACf,QAASiT,KAAKF,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGE,CAAC,IAAG,EAAEA,CAAC,EAAIF,EAAEE,CAAC,EACrF,CACM,OAAO,CACb,EACSH,EAAS,MAAM,KAAM,SAAS,CACvC,EAEO,SAASI,GAAOH,EAAGJ,EAAG,CAC3B,IAAIQ,EAAI,CAAE,EACV,QAASF,KAAKF,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGE,CAAC,GAAKN,EAAE,QAAQM,CAAC,EAAI,IAC9EE,EAAEF,CAAC,EAAIF,EAAEE,CAAC,GACd,GAAIF,GAAK,MAAQ,OAAO,OAAO,uBAA0B,WACrD,QAAS/S,EAAI,EAAGiT,EAAI,OAAO,sBAAsBF,CAAC,EAAG/S,EAAIiT,EAAE,OAAQjT,IAC3D2S,EAAE,QAAQM,EAAEjT,CAAC,CAAC,EAAI,GAAK,OAAO,UAAU,qBAAqB,KAAK+S,EAAGE,EAAEjT,CAAC,CAAC,IACzEmT,EAAEF,EAAEjT,CAAC,CAAC,EAAI+S,EAAEE,EAAEjT,CAAC,CAAC,GAE5B,OAAOmT,CACT,CAiKO,SAASC,GAAcC,EAAIC,EAAMC,EAAM,CAC5C,GAAIA,GAAQ,UAAU,SAAW,EAAG,QAASvT,EAAI,EAAGwT,EAAIF,EAAK,OAAQG,EAAIzT,EAAIwT,EAAGxT,KACxEyT,GAAM,EAAEzT,KAAKsT,MACRG,IAAIA,EAAK,MAAM,UAAU,MAAM,KAAKH,EAAM,EAAGtT,CAAC,GACnDyT,EAAGzT,CAAC,EAAIsT,EAAKtT,CAAC,GAGtB,OAAOqT,EAAG,OAAOI,GAAM,MAAM,UAAU,MAAM,KAAKH,CAAI,CAAC,CACzD,CC7NO,IAAII,EAAqB,4BACrBC,EAAqB,0BACrBC,GAAwB,0BAKxBC,GAAyB,iCCM7B,SAASC,EAAUtU,EAAKC,EAAO,CAClC,OAAI,OAAOD,GAAQ,WACfA,EAAIC,CAAK,EAEJD,IACLA,EAAI,QAAUC,GAEXD,CACX,CCNO,SAASyH,GAAe8M,EAAc7M,EAAU,CACnD,IAAI1H,EAAMwU,WAAS,UAAY,CAAE,MAAQ,CAErC,MAAOD,EAEP,SAAU7M,EAEV,OAAQ,CACJ,IAAI,SAAU,CACV,OAAO1H,EAAI,KACd,EACD,IAAI,QAAQC,EAAO,CACf,IAAIsN,EAAOvN,EAAI,MACXuN,IAAStN,IACTD,EAAI,MAAQC,EACZD,EAAI,SAASC,EAAOsN,CAAI,EAE/B,CACJ,CACT,CAAO,CAAE,EAAE,CAAC,EAER,OAAAvN,EAAI,SAAW0H,EACR1H,EAAI,MACf,CCnCA,IAAIyU,GAA4B,OAAO,OAAW,IAAcxO,EAAqB,gBAAGgB,EAAe,UACnGyN,GAAgB,IAAI,QAejB,SAASC,GAAaxU,EAAMyU,EAAc,CAC7C,IAAIjN,EAAcF,GAA+B,KAAM,SAAUoN,EAAU,CACvE,OAAO1U,EAAK,QAAQ,SAAUH,EAAK,CAAE,OAAOsU,EAAUtU,EAAK6U,CAAQ,EAAI,CAC/E,CAAK,EAED,OAAAJ,GAA0B,UAAY,CAClC,IAAIK,EAAWJ,GAAc,IAAI/M,CAAW,EAC5C,GAAImN,EAAU,CACV,IAAIC,EAAa,IAAI,IAAID,CAAQ,EAC7BE,EAAa,IAAI,IAAI7U,CAAI,EACzB8U,EAAYtN,EAAY,QAC5BoN,EAAW,QAAQ,SAAU/U,EAAK,CACzBgV,EAAW,IAAIhV,CAAG,GACnBsU,EAAUtU,EAAK,IAAI,CAEvC,CAAa,EACDgV,EAAW,QAAQ,SAAUhV,EAAK,CACzB+U,EAAW,IAAI/U,CAAG,GACnBsU,EAAUtU,EAAKiV,CAAS,CAE5C,CAAa,CACb,CACQP,GAAc,IAAI/M,EAAaxH,CAAI,CAC3C,EAAO,CAACA,CAAI,CAAC,EACFwH,CACX,CC3CA,SAASuN,GAAKC,EAAG,CACb,OAAOA,CACX,CACA,SAASC,GAAkBC,EAAUC,EAAY,CACzCA,IAAe,SAAUA,EAAaJ,IAC1C,IAAIK,EAAS,CAAE,EACXC,EAAW,GACXC,EAAS,CACT,KAAM,UAAY,CACd,GAAID,EACA,MAAM,IAAI,MAAM,kGAAkG,EAEtH,OAAID,EAAO,OACAA,EAAOA,EAAO,OAAS,CAAC,EAE5BF,CACV,EACD,UAAW,SAAUK,EAAM,CACvB,IAAIjH,EAAO6G,EAAWI,EAAMF,CAAQ,EACpC,OAAAD,EAAO,KAAK9G,CAAI,EACT,UAAY,CACf8G,EAASA,EAAO,OAAO,SAAUpD,EAAG,CAAE,OAAOA,IAAM1D,EAAO,CAC7D,CACJ,EACD,iBAAkB,SAAUkH,EAAI,CAE5B,IADAH,EAAW,GACJD,EAAO,QAAQ,CAClB,IAAIK,EAAML,EACVA,EAAS,CAAE,EACXK,EAAI,QAAQD,CAAE,CAC9B,CACYJ,EAAS,CACL,KAAM,SAAUpD,EAAG,CAAE,OAAOwD,EAAGxD,CAAC,CAAI,EACpC,OAAQ,UAAY,CAAE,OAAOoD,CAAS,CACzC,CACJ,EACD,aAAc,SAAUI,EAAI,CACxBH,EAAW,GACX,IAAIK,EAAe,CAAE,EACrB,GAAIN,EAAO,OAAQ,CACf,IAAIK,EAAML,EACVA,EAAS,CAAE,EACXK,EAAI,QAAQD,CAAE,EACdE,EAAeN,CAC/B,CACY,IAAIO,EAAe,UAAY,CAC3B,IAAIF,EAAMC,EACVA,EAAe,CAAE,EACjBD,EAAI,QAAQD,CAAE,CACjB,EACGI,EAAQ,UAAY,CAAE,OAAO,QAAQ,QAAO,EAAG,KAAKD,CAAY,CAAI,EACxEC,EAAO,EACPR,EAAS,CACL,KAAM,SAAUpD,EAAG,CACf0D,EAAa,KAAK1D,CAAC,EACnB4D,EAAO,CACV,EACD,OAAQ,SAAUC,EAAQ,CACtB,OAAAH,EAAeA,EAAa,OAAOG,CAAM,EAClCT,CACV,CACJ,CACJ,CACJ,EACD,OAAOE,CACX,CAMO,SAASQ,GAAoBC,EAAS,CACrCA,IAAY,SAAUA,EAAU,CAAA,GACpC,IAAIT,EAASL,GAAkB,IAAI,EACnC,OAAAK,EAAO,QAAUnC,EAAS,CAAE,MAAO,GAAM,IAAK,EAAO,EAAE4C,CAAO,EACvDT,CACX,CC3EA,IAAIU,GAAU,SAAUpT,EAAI,CACxB,IAAIqT,EAAUrT,EAAG,QAASsT,EAAO3C,GAAO3Q,EAAI,CAAC,SAAS,CAAC,EACvD,GAAI,CAACqT,EACD,MAAM,IAAI,MAAM,oEAAoE,EAExF,IAAIE,EAASF,EAAQ,KAAM,EAC3B,GAAI,CAACE,EACD,MAAM,IAAI,MAAM,0BAA0B,EAE9C,OAAOC,EAAmB,cAACD,EAAQhD,EAAS,CAAE,EAAE+C,CAAI,CAAC,CACzD,EACAF,GAAQ,gBAAkB,GACnB,SAASK,GAAcf,EAAQgB,EAAU,CAC5C,OAAAhB,EAAO,UAAUgB,CAAQ,EAClBN,EACX,CChBO,IAAIO,GAAYT,GAAqB,ECIxCU,EAAU,UAAY,CAE1B,EAIIC,EAAe5V,EAAgB,WAAC,SAAUC,EAAO4V,EAAW,CAC5D,IAAI7W,EAAMgH,EAAY,OAAC,IAAI,EACvBjE,EAAKwE,EAAAA,SAAe,CACpB,gBAAiBoP,EACjB,eAAgBA,EAChB,mBAAoBA,CAC5B,CAAK,EAAGG,EAAY/T,EAAG,CAAC,EAAGgU,EAAehU,EAAG,CAAC,EACtCiU,EAAe/V,EAAM,aAAcE,EAAWF,EAAM,SAAUgW,EAAYhW,EAAM,UAAWiW,EAAkBjW,EAAM,gBAAiBkW,EAAUlW,EAAM,QAASmW,EAASnW,EAAM,OAAQmV,EAAUnV,EAAM,QAASoW,EAAapW,EAAM,WAAYqW,EAAcrW,EAAM,YAAasW,EAAQtW,EAAM,MAAOuW,EAAiBvW,EAAM,eAAgBgC,EAAKhC,EAAM,GAAIwW,EAAYxU,IAAO,OAAS,MAAQA,EAAIyU,EAAUzW,EAAM,QAASoV,EAAO3C,GAAOzS,EAAO,CAAC,eAAgB,WAAY,YAAa,kBAAmB,UAAW,SAAU,UAAW,aAAc,cAAe,QAAS,iBAAkB,KAAM,SAAS,CAAC,EACnlBkV,EAAUC,EACVuB,EAAehD,GAAa,CAAC3U,EAAK6W,CAAS,CAAC,EAC5Ce,EAAiBtE,EAASA,EAAS,CAAA,EAAI+C,CAAI,EAAGS,CAAS,EAC3D,OAAQP,EAAAA,cAAoBnU,EAAAA,SAAgB,KACxC+U,GAAYZ,EAAmB,cAACJ,EAAS,CAAE,QAASO,GAAW,gBAAiBQ,EAAiB,OAAQE,EAAQ,WAAYC,EAAY,YAAaC,EAAa,MAAOC,EAAO,aAAcR,EAAc,eAAgB,CAAC,CAACS,EAAgB,QAASxX,EAAK,QAAS0X,CAAS,CAAA,EAC/QV,EAAgBlV,EAAAA,aAAmBR,EAAAA,SAAe,KAAKH,CAAQ,EAAGmS,EAASA,EAAS,CAAE,EAAEsE,CAAc,EAAG,CAAE,IAAKD,CAAc,CAAA,CAAC,EAAMpB,EAAAA,cAAoBkB,EAAWnE,EAAS,CAAE,EAAEsE,EAAgB,CAAE,UAAWX,EAAW,IAAKU,CAAY,CAAE,EAAGxW,CAAQ,CAAE,CACjQ,CAAC,EACDyV,EAAa,aAAe,CACxB,QAAS,GACT,gBAAiB,GACjB,MAAO,EACX,EACAA,EAAa,WAAa,CACtB,UAAWzC,EACX,UAAWD,CACf,EC9BO,IAAI2D,GAAW,UAAY,CAI9B,GAAI,OAAO,kBAAsB,IAC7B,OAAO,iBAGf,ECXA,SAASC,IAAe,CACpB,GAAI,CAAC,SACD,OAAO,KACX,IAAIC,EAAM,SAAS,cAAc,OAAO,EACxCA,EAAI,KAAO,WACX,IAAIC,EAAQH,GAAU,EACtB,OAAIG,GACAD,EAAI,aAAa,QAASC,CAAK,EAE5BD,CACX,CACA,SAASE,GAAaF,EAAKG,EAAK,CAExBH,EAAI,WAEJA,EAAI,WAAW,QAAUG,EAGzBH,EAAI,YAAY,SAAS,eAAeG,CAAG,CAAC,CAEpD,CACA,SAASC,GAAeJ,EAAK,CACzB,IAAIK,EAAO,SAAS,MAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC,EACnEA,EAAK,YAAYL,CAAG,CACxB,CACO,IAAIM,GAAsB,UAAY,CACzC,IAAIC,EAAU,EACVC,EAAa,KACjB,MAAO,CACH,IAAK,SAAUC,EAAO,CACdF,GAAW,IACNC,EAAaT,QACdG,GAAaM,EAAYC,CAAK,EAC9BL,GAAeI,CAAU,GAGjCD,GACH,EACD,OAAQ,UAAY,CAChBA,IACI,CAACA,GAAWC,IACZA,EAAW,YAAcA,EAAW,WAAW,YAAYA,CAAU,EACrEA,EAAa,KAEpB,CACJ,CACL,ECpCWE,GAAqB,UAAY,CACxC,IAAIC,EAAQL,GAAqB,EACjC,OAAO,SAAUxH,EAAQ8H,EAAW,CAChC1R,EAAAA,UAAgB,UAAY,CACxB,OAAAyR,EAAM,IAAI7H,CAAM,EACT,UAAY,CACf6H,EAAM,OAAQ,CACjB,CACb,EAAW,CAAC7H,GAAU8H,CAAS,CAAC,CAC3B,CACL,ECdWC,GAAiB,UAAY,CACpC,IAAIC,EAAWJ,GAAoB,EAC/BK,EAAQ,SAAU/V,EAAI,CACtB,IAAI8N,EAAS9N,EAAG,OAAQgW,EAAUhW,EAAG,QACrC,OAAA8V,EAAShI,EAAQkI,CAAO,EACjB,IACV,EACD,OAAOD,CACX,ECfWE,GAAU,CACjB,KAAM,EACN,IAAK,EACL,MAAO,EACP,IAAK,CACT,EACIC,GAAQ,SAAU9G,EAAG,CAAE,OAAO,SAASA,GAAK,GAAI,EAAE,GAAK,CAAI,EAC3D+G,GAAY,SAAUxB,EAAS,CAC/B,IAAIyB,EAAK,OAAO,iBAAiB,SAAS,IAAI,EAC1CC,EAAOD,EAAGzB,IAAY,UAAY,cAAgB,YAAY,EAC9D2B,EAAMF,EAAGzB,IAAY,UAAY,aAAe,WAAW,EAC3D4B,EAAQH,EAAGzB,IAAY,UAAY,eAAiB,aAAa,EACrE,MAAO,CAACuB,GAAMG,CAAI,EAAGH,GAAMI,CAAG,EAAGJ,GAAMK,CAAK,CAAC,CACjD,EACWC,GAAc,SAAU7B,EAAS,CAExC,GADIA,IAAY,SAAUA,EAAU,UAChC,OAAO,OAAW,IAClB,OAAOsB,GAEX,IAAIQ,EAAUN,GAAUxB,CAAO,EAC3B+B,EAAgB,SAAS,gBAAgB,YACzCC,EAAc,OAAO,WACzB,MAAO,CACH,KAAMF,EAAQ,CAAC,EACf,IAAKA,EAAQ,CAAC,EACd,MAAOA,EAAQ,CAAC,EAChB,IAAK,KAAK,IAAI,EAAGE,EAAcD,EAAgBD,EAAQ,CAAC,EAAIA,EAAQ,CAAC,CAAC,CACzE,CACL,ECxBIG,GAAQf,GAAgB,EACjBgB,EAAgB,qBAIvBC,GAAY,SAAU9W,EAAI+W,EAAepC,EAASqC,EAAW,CAC7D,IAAIX,EAAOrW,EAAG,KAAMsW,EAAMtW,EAAG,IAAKuW,EAAQvW,EAAG,MAAOiX,EAAMjX,EAAG,IAC7D,OAAI2U,IAAY,SAAUA,EAAU,UAC7B;AAAA,KAAQ,OAAOtD,GAAuB;AAAA,qBAA0B,EAAE,OAAO2F,EAAW;AAAA,mBAAuB,EAAE,OAAOC,EAAK,KAAK,EAAE,OAAOD,EAAW;AAAA;AAAA,QAAiB,EAAE,OAAOH,EAAe;AAAA,sBAA4B,EAAE,OAAOG,EAAW;AAAA;AAAA,KAA4C,EAAE,OAAO,CACnSD,GAAiB,sBAAsB,OAAOC,EAAW,GAAG,EAC5DrC,IAAY,UACR;AAAA,oBAAuB,OAAO0B,EAAM;AAAA,kBAAwB,EAAE,OAAOC,EAAK;AAAA,oBAA0B,EAAE,OAAOC,EAAO;AAAA;AAAA;AAAA,mBAAgE,EAAE,OAAOU,EAAK,KAAK,EAAE,OAAOD,EAAW;AAAA,KAAS,EACxOrC,IAAY,WAAa,kBAAkB,OAAOsC,EAAK,KAAK,EAAE,OAAOD,EAAW,GAAG,CAC3F,EACS,OAAO,OAAO,EACd,KAAK,EAAE,EAAG;AAAA;AAAA;AAAA,IAAgB,EAAE,OAAO7F,EAAoB;AAAA,YAAiB,EAAE,OAAO8F,EAAK,KAAK,EAAE,OAAOD,EAAW;AAAA;AAAA;AAAA,IAAiB,EAAE,OAAO5F,EAAoB;AAAA,mBAAwB,EAAE,OAAO6F,EAAK,KAAK,EAAE,OAAOD,EAAW;AAAA;AAAA;AAAA,IAAiB,EAAE,OAAO7F,EAAoB,IAAI,EAAE,OAAOA,EAAoB;AAAA,cAAmB,EAAE,OAAO6F,EAAW;AAAA;AAAA;AAAA,IAAiB,EAAE,OAAO5F,EAAoB,IAAI,EAAE,OAAOA,EAAoB;AAAA,qBAA0B,EAAE,OAAO4F,EAAW;AAAA;AAAA;AAAA,QAAqB,EAAE,OAAOH,EAAe;AAAA,KAAW,EAAE,OAAOvF,GAAwB,IAAI,EAAE,OAAO2F,EAAK;AAAA;AAAA,CAAY,CAC/kB,EACIC,GAAuB,UAAY,CACnC,IAAI3B,EAAU,SAAS,SAAS,KAAK,aAAasB,CAAa,GAAK,IAAK,EAAE,EAC3E,OAAO,SAAStB,CAAO,EAAIA,EAAU,CACzC,EACW4B,GAAmB,UAAY,CACtCjT,EAAAA,UAAgB,UAAY,CACxB,gBAAS,KAAK,aAAa2S,GAAgBK,KAAyB,GAAG,UAAU,EAC1E,UAAY,CACf,IAAIE,EAAaF,GAAoB,EAAK,EACtCE,GAAc,EACd,SAAS,KAAK,gBAAgBP,CAAa,EAG3C,SAAS,KAAK,aAAaA,EAAeO,EAAW,SAAQ,CAAE,CAEtE,CACJ,EAAE,EAAE,CACT,EAIWC,GAAkB,SAAUrX,EAAI,CACvC,IAAIsU,EAAatU,EAAG,WAAYsX,EAActX,EAAG,YAAaE,EAAKF,EAAG,QAAS2U,EAAUzU,IAAO,OAAS,SAAWA,EACpHiX,GAAkB,EAMlB,IAAIF,EAAMjV,UAAc,UAAY,CAAE,OAAOwU,GAAY7B,CAAO,CAAE,EAAI,CAACA,CAAO,CAAC,EAC/E,OAAOnB,EAAmB,cAACoD,GAAO,CAAE,OAAQE,GAAUG,EAAK,CAAC3C,EAAYK,EAAU2C,EAA6B,GAAf,YAAiB,CAAC,CAAE,CACxH,ECpDIC,GAAmB,GACvB,GAAI,OAAO,OAAW,IAClB,GAAI,CACA,IAAIpE,EAAU,OAAO,eAAe,CAAA,EAAI,UAAW,CAC/C,IAAK,UAAY,CACb,OAAAoE,GAAmB,GACZ,EACV,CACb,CAAS,EAED,OAAO,iBAAiB,OAAQpE,EAASA,CAAO,EAEhD,OAAO,oBAAoB,OAAQA,EAASA,CAAO,CAC3D,MACgB,CACRoE,GAAmB,EAC3B,CAEO,IAAIC,EAAaD,GAAmB,CAAE,QAAS,EAAO,EAAG,GClB5DE,GAAuB,SAAUpa,EAAM,CAEvC,OAAOA,EAAK,UAAY,UAC5B,EACIqa,GAAuB,SAAUra,EAAMsa,EAAU,CACjD,GAAI,EAAEta,aAAgB,SAClB,MAAO,GAEX,IAAIyQ,EAAS,OAAO,iBAAiBzQ,CAAI,EACzC,OAEAyQ,EAAO6J,CAAQ,IAAM,UAEjB,EAAE7J,EAAO,YAAcA,EAAO,WAAa,CAAC2J,GAAqBpa,CAAI,GAAKyQ,EAAO6J,CAAQ,IAAM,UACvG,EACIC,GAA0B,SAAUva,EAAM,CAAE,OAAOqa,GAAqBra,EAAM,WAAW,CAAI,EAC7Fwa,GAA0B,SAAUxa,EAAM,CAAE,OAAOqa,GAAqBra,EAAM,WAAW,CAAI,EACtFya,GAA0B,SAAUC,EAAM1a,EAAM,CACvD,IAAI0H,EAAgB1H,EAAK,cACrB2a,EAAU3a,EACd,EAAG,CAEK,OAAO,WAAe,KAAe2a,aAAmB,aACxDA,EAAUA,EAAQ,MAEtB,IAAIC,EAAeC,GAAuBH,EAAMC,CAAO,EACvD,GAAIC,EAAc,CACd,IAAIjY,EAAKmY,GAAmBJ,EAAMC,CAAO,EAAGI,EAAepY,EAAG,CAAC,EAAGqY,EAAerY,EAAG,CAAC,EACrF,GAAIoY,EAAeC,EACf,MAAO,EAEvB,CACQL,EAAUA,EAAQ,UAC1B,OAAaA,GAAWA,IAAYjT,EAAc,MAC9C,MAAO,EACX,EACIuT,GAAsB,SAAUtY,EAAI,CACpC,IAAIuY,EAAYvY,EAAG,UAAWoY,EAAepY,EAAG,aAAcqY,EAAerY,EAAG,aAChF,MAAO,CACHuY,EACAH,EACAC,CACH,CACL,EACIG,GAAsB,SAAUxY,EAAI,CACpC,IAAIyY,EAAazY,EAAG,WAAY0Y,EAAc1Y,EAAG,YAAa2Y,EAAc3Y,EAAG,YAC/E,MAAO,CACHyY,EACAC,EACAC,CACH,CACL,EACIT,GAAyB,SAAUH,EAAM1a,EAAM,CAC/C,OAAO0a,IAAS,IAAMH,GAAwBva,CAAI,EAAIwa,GAAwBxa,CAAI,CACtF,EACI8a,GAAqB,SAAUJ,EAAM1a,EAAM,CAC3C,OAAO0a,IAAS,IAAMO,GAAoBjb,CAAI,EAAImb,GAAoBnb,CAAI,CAC9E,EACIub,GAAqB,SAAUb,EAAMc,EAAW,CAMhD,OAAOd,IAAS,KAAOc,IAAc,MAAQ,GAAK,CACtD,EACWC,GAAe,SAAUf,EAAMgB,EAAWnY,EAAOoY,EAAaC,EAAc,CACnF,IAAIC,EAAkBN,GAAmBb,EAAM,OAAO,iBAAiBgB,CAAS,EAAE,SAAS,EACvFI,EAAQD,EAAkBF,EAE1BrY,EAASC,EAAM,OACfwY,EAAeL,EAAU,SAASpY,CAAM,EACxC0Y,EAAqB,GACrBC,EAAkBH,EAAQ,EAC1BI,EAAkB,EAClBC,EAAqB,EACzB,EAAG,CACC,GAAI,CAAC7Y,EACD,MAEJ,IAAIX,EAAKmY,GAAmBJ,EAAMpX,CAAM,EAAG8Y,EAAWzZ,EAAG,CAAC,EAAG0Z,EAAW1Z,EAAG,CAAC,EAAG2Z,EAAW3Z,EAAG,CAAC,EAC1F4Z,EAAgBF,EAAWC,EAAWT,EAAkBO,GACxDA,GAAYG,IACR1B,GAAuBH,EAAMpX,CAAM,IACnC4Y,GAAmBK,EACnBJ,GAAsBC,GAG9B,IAAII,EAAWlZ,EAAO,WAGtBA,EAAUkZ,GAAYA,EAAS,WAAa,KAAK,uBAAyBA,EAAS,KAAOA,CAC7F,OAEA,CAACT,GAAgBzY,IAAW,SAAS,MAEjCyY,IAAiBL,EAAU,SAASpY,CAAM,GAAKoY,IAAcpY,IAElE,OAAI2Y,GACkB,KAAK,IAAIC,CAAe,EAAI,GAGzC,CAACD,GACY,KAAK,IAAIE,CAAkB,EAAI,KACjDH,EAAqB,IAElBA,CACX,ECrGWS,EAAa,SAAUlZ,EAAO,CACrC,MAAO,mBAAoBA,EAAQ,CAACA,EAAM,eAAe,CAAC,EAAE,QAASA,EAAM,eAAe,CAAC,EAAE,OAAO,EAAI,CAAC,EAAG,CAAC,CACjH,EACWmZ,GAAa,SAAUnZ,EAAO,CAAE,MAAO,CAACA,EAAM,OAAQA,EAAM,MAAM,CAAI,EAC7EoZ,GAAa,SAAU/c,EAAK,CAC5B,OAAOA,GAAO,YAAaA,EAAMA,EAAI,QAAUA,CACnD,EACIgd,GAAe,SAAU7K,EAAG8K,EAAG,CAAE,OAAO9K,EAAE,CAAC,IAAM8K,EAAE,CAAC,GAAK9K,EAAE,CAAC,IAAM8K,EAAE,CAAC,CAAI,EACzEC,GAAgB,SAAUnO,EAAI,CAAE,MAAO;AAAA,yBAA4B,OAAOA,EAAI;AAAA,wBAAmD,EAAE,OAAOA,EAAI;AAAA,CAA2B,CAAI,EAC7KoO,GAAY,EACZC,EAAY,CAAE,EACX,SAASC,GAAoBpc,EAAO,CACvC,IAAIqc,EAAqBtW,EAAY,OAAC,EAAE,EACpCuW,EAAgBvW,EAAY,OAAC,CAAC,EAAG,CAAC,CAAC,EACnCwW,EAAaxW,EAAAA,OAAc,EAC3B+H,EAAKxH,EAAc,SAAC4V,IAAW,EAAE,CAAC,EAClCxD,EAAQpS,EAAAA,SAAeqR,EAAc,EAAE,CAAC,EACxC6E,EAAYzW,EAAY,OAAC/F,CAAK,EAClCgG,EAAAA,UAAgB,UAAY,CACxBwW,EAAU,QAAUxc,CAC5B,EAAO,CAACA,CAAK,CAAC,EACVgG,EAAAA,UAAgB,UAAY,CACxB,GAAIhG,EAAM,MAAO,CACb,SAAS,KAAK,UAAU,IAAI,uBAAuB,OAAO8N,CAAE,CAAC,EAC7D,IAAI2O,EAAU9J,GAAc,CAAC3S,EAAM,QAAQ,OAAO,GAAIA,EAAM,QAAU,CAAA,GAAI,IAAI8b,EAAU,EAAG,EAAI,EAAE,OAAO,OAAO,EAC/G,OAAAW,EAAQ,QAAQ,SAAU7K,EAAI,CAAE,OAAOA,EAAG,UAAU,IAAI,uBAAuB,OAAO9D,CAAE,CAAC,CAAE,CAAE,EACtF,UAAY,CACf,SAAS,KAAK,UAAU,OAAO,uBAAuB,OAAOA,CAAE,CAAC,EAChE2O,EAAQ,QAAQ,SAAU7K,EAAI,CAAE,OAAOA,EAAG,UAAU,OAAO,uBAAuB,OAAO9D,CAAE,CAAC,CAAE,CAAE,CACnG,CACb,CAEA,EAAO,CAAC9N,EAAM,MAAOA,EAAM,QAAQ,QAASA,EAAM,MAAM,CAAC,EACrD,IAAI0c,EAAoBjd,EAAAA,YAAkB,SAAUiD,EAAOqO,EAAQ,CAC/D,GAAK,YAAarO,GAASA,EAAM,QAAQ,SAAW,GAAOA,EAAM,OAAS,SAAWA,EAAM,QACvF,MAAO,CAAC8Z,EAAU,QAAQ,eAE9B,IAAIG,EAAQf,EAAWlZ,CAAK,EACxBka,EAAaN,EAAc,QAC3BO,EAAS,WAAYna,EAAQA,EAAM,OAASka,EAAW,CAAC,EAAID,EAAM,CAAC,EACnEG,EAAS,WAAYpa,EAAQA,EAAM,OAASka,EAAW,CAAC,EAAID,EAAM,CAAC,EACnEI,EACAta,EAASC,EAAM,OACfsa,EAAgB,KAAK,IAAIH,CAAM,EAAI,KAAK,IAAIC,CAAM,EAAI,IAAM,IAEhE,GAAI,YAAapa,GAASsa,IAAkB,KAAOva,EAAO,OAAS,QAC/D,MAAO,GAEX,IAAIwa,EAA+BrD,GAAwBoD,EAAeva,CAAM,EAChF,GAAI,CAACwa,EACD,MAAO,GAUX,GARIA,EACAF,EAAcC,GAGdD,EAAcC,IAAkB,IAAM,IAAM,IAC5CC,EAA+BrD,GAAwBoD,EAAeva,CAAM,GAG5E,CAACwa,EACD,MAAO,GAKX,GAHI,CAACV,EAAW,SAAW,mBAAoB7Z,IAAUma,GAAUC,KAC/DP,EAAW,QAAUQ,GAErB,CAACA,EACD,MAAO,GAEX,IAAIG,EAAgBX,EAAW,SAAWQ,EAC1C,OAAOnC,GAAasC,EAAenM,EAAQrO,EAAOwa,IAAkB,IAAML,EAASC,CAAY,CAClG,EAAE,EAAE,EACDK,EAAgB1d,cAAkB,SAAU2d,EAAQ,CACpD,IAAI1a,EAAQ0a,EACZ,GAAI,GAACjB,EAAU,QAAUA,EAAUA,EAAU,OAAS,CAAC,IAAMzD,GAI7D,KAAIuC,EAAQ,WAAYvY,EAAQmZ,GAAWnZ,CAAK,EAAIkZ,EAAWlZ,CAAK,EAChE2a,EAAchB,EAAmB,QAAQ,OAAO,SAAUnK,EAAG,CAAE,OAAOA,EAAE,OAASxP,EAAM,OAASwP,EAAE,SAAWxP,EAAM,QAAUA,EAAM,SAAWwP,EAAE,eAAiB6J,GAAa7J,EAAE,MAAO+I,CAAK,CAAI,CAAA,EAAE,CAAC,EAEvM,GAAIoC,GAAeA,EAAY,OAAQ,CAC/B3a,EAAM,YACNA,EAAM,eAAgB,EAE1B,MACZ,CAEQ,GAAI,CAAC2a,EAAa,CACd,IAAIC,GAAcd,EAAU,QAAQ,QAAU,CAAE,GAC3C,IAAIV,EAAU,EACd,OAAO,OAAO,EACd,OAAO,SAAU3c,EAAM,CAAE,OAAOA,EAAK,SAASuD,EAAM,MAAM,EAAI,EAC/D6a,EAAaD,EAAW,OAAS,EAAIZ,EAAkBha,EAAO4a,EAAW,CAAC,CAAC,EAAI,CAACd,EAAU,QAAQ,YAClGe,GACI7a,EAAM,YACNA,EAAM,eAAgB,CAG1C,EACK,EAAE,EAAE,EACD8a,EAAe/d,EAAAA,YAAkB,SAAUkK,EAAMsR,EAAOxY,EAAQgb,EAAQ,CACxE,IAAI/a,EAAQ,CAAE,KAAMiH,EAAM,MAAOsR,EAAO,OAAQxY,EAAQ,OAAQgb,EAAQ,aAAcC,GAAyBjb,CAAM,CAAG,EACxH4Z,EAAmB,QAAQ,KAAK3Z,CAAK,EACrC,WAAW,UAAY,CACnB2Z,EAAmB,QAAUA,EAAmB,QAAQ,OAAO,SAAUnK,EAAG,CAAE,OAAOA,IAAMxP,EAAQ,CACtG,EAAE,CAAC,CACP,EAAE,EAAE,EACDib,EAAmBle,cAAkB,SAAUiD,EAAO,CACtD4Z,EAAc,QAAUV,EAAWlZ,CAAK,EACxC6Z,EAAW,QAAU,MACxB,EAAE,EAAE,EACDqB,EAAcne,cAAkB,SAAUiD,EAAO,CACjD8a,EAAa9a,EAAM,KAAMmZ,GAAWnZ,CAAK,EAAGA,EAAM,OAAQga,EAAkBha,EAAO1C,EAAM,QAAQ,OAAO,CAAC,CAC5G,EAAE,EAAE,EACD6d,EAAkBpe,cAAkB,SAAUiD,EAAO,CACrD8a,EAAa9a,EAAM,KAAMkZ,EAAWlZ,CAAK,EAAGA,EAAM,OAAQga,EAAkBha,EAAO1C,EAAM,QAAQ,OAAO,CAAC,CAC5G,EAAE,EAAE,EACLgG,EAAAA,UAAgB,UAAY,CACxB,OAAAmW,EAAU,KAAKzD,CAAK,EACpB1Y,EAAM,aAAa,CACf,gBAAiB4d,EACjB,eAAgBA,EAChB,mBAAoBC,CAChC,CAAS,EACD,SAAS,iBAAiB,QAASV,EAAe7D,CAAU,EAC5D,SAAS,iBAAiB,YAAa6D,EAAe7D,CAAU,EAChE,SAAS,iBAAiB,aAAcqE,EAAkBrE,CAAU,EAC7D,UAAY,CACf6C,EAAYA,EAAU,OAAO,SAAU2B,EAAM,CAAE,OAAOA,IAASpF,EAAQ,EACvE,SAAS,oBAAoB,QAASyE,EAAe7D,CAAU,EAC/D,SAAS,oBAAoB,YAAa6D,EAAe7D,CAAU,EACnE,SAAS,oBAAoB,aAAcqE,EAAkBrE,CAAU,CAC1E,CACJ,EAAE,EAAE,EACL,IAAIrD,EAAkBjW,EAAM,gBAAiBsW,EAAQtW,EAAM,MAC3D,OAAQsV,EAAAA,cAAoBnU,EAAAA,SAAgB,KACxCmV,EAAQhB,EAAAA,cAAoBoD,EAAO,CAAE,OAAQuD,GAAcnO,CAAE,CAAG,CAAA,EAAI,KACpEmI,EAAkBX,EAAmB,cAAC6D,GAAiB,CAAE,WAAYnZ,EAAM,WAAY,QAASA,EAAM,OAAS,CAAA,EAAI,IAAI,CAC/H,CACA,SAAS0d,GAAyBve,EAAM,CAEpC,QADI4e,EAAe,KACZ5e,IAAS,MACRA,aAAgB,aAChB4e,EAAe5e,EAAK,KACpBA,EAAOA,EAAK,MAEhBA,EAAOA,EAAK,WAEhB,OAAO4e,CACX,CCzJA,MAAA7I,GAAeK,GAAcE,GAAW2G,EAAmB,ECCxD,IAAC4B,GAAoBje,EAAgB,WAAC,SAAUC,EAAOjB,EAAK,CAAE,OAAQuW,EAAmB,cAACK,EAActD,EAAS,CAAA,EAAIrS,EAAO,CAAE,IAAKjB,EAAK,QAASmW,EAAO,CAAE,CAAC,CAAK,CAAA,EACnK8I,GAAkB,WAAarI,EAAa", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]}