{"version": 3, "file": "index-92JRbkOu.js", "sources": ["../../node_modules/react-dom/client.js", "../../src/services/queryClient.ts", "../../node_modules/react-i18next/dist/es/utils.js", "../../node_modules/react-i18next/dist/es/unescape.js", "../../node_modules/react-i18next/dist/es/defaults.js", "../../node_modules/react-i18next/dist/es/i18nInstance.js", "../../node_modules/react-i18next/dist/es/initReactI18next.js", "../../node_modules/react-i18next/dist/es/context.js", "../../node_modules/react-i18next/dist/es/useTranslation.js", "../../node_modules/lucide-react/dist/esm/shared/src/utils.js", "../../node_modules/lucide-react/dist/esm/defaultAttributes.js", "../../node_modules/lucide-react/dist/esm/Icon.js", "../../node_modules/lucide-react/dist/esm/createLucideIcon.js", "../../node_modules/lucide-react/dist/esm/icons/book-open.js", "../../node_modules/lucide-react/dist/esm/icons/clock.js", "../../node_modules/lucide-react/dist/esm/icons/github.js", "../../node_modules/lucide-react/dist/esm/icons/mail.js", "../../node_modules/lucide-react/dist/esm/icons/menu.js", "../../node_modules/lucide-react/dist/esm/icons/search.js", "../../node_modules/lucide-react/dist/esm/icons/star.js", "../../node_modules/lucide-react/dist/esm/icons/trending-up.js", "../../node_modules/lucide-react/dist/esm/icons/twitter.js", "../../node_modules/lucide-react/dist/esm/icons/user.js", "../../node_modules/clsx/dist/clsx.mjs", "../../node_modules/class-variance-authority/dist/index.mjs", "../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "../../src/utils/cn.ts", "../../src/components/ui/button.tsx", "../../src/components/ui/input.tsx", "../../node_modules/zustand/esm/vanilla.mjs", "../../node_modules/zustand/esm/react.mjs", "../../node_modules/zustand/esm/middleware.mjs", "../../src/stores/authStore.ts", "../../src/components/layout/Header.tsx", "../../src/components/layout/Footer.tsx", "../../src/components/layout/Layout.tsx", "../../src/components/ui/card.tsx", "../../src/pages/HomePage.tsx", "../../node_modules/@radix-ui/react-label/dist/index.mjs", "../../src/components/ui/label.tsx", "../../src/pages/LoginPage.tsx", "../../src/pages/BrowsePage.tsx", "../../src/pages/MangaDetailPage.tsx", "../../src/pages/ReaderPage.tsx", "../../src/pages/RegisterPage.tsx", "../../src/pages/BookmarksPage.tsx", "../../src/pages/HistoryPage.tsx", "../../src/pages/ProfilePage.tsx", "../../src/router/index.tsx", "../../node_modules/i18next/dist/esm/i18next.js", "../../node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js", "../../src/i18n/index.ts", "../../src/App.tsx", "../../src/main.tsx"], "sourcesContent": ["'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n", "import { QueryClient } from '@tanstack/react-query';\n\nexport const queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)\n      retry: (failureCount, error: any) => {\n        // Don't retry on 4xx errors\n        if (error?.status >= 400 && error?.status < 500) {\n          return false;\n        }\n        return failureCount < 3;\n      },\n      refetchOnWindowFocus: false,\n    },\n    mutations: {\n      retry: false,\n    },\n  },\n});\n", "export const warn = (i18n, code, msg, rest) => {\n  const args = [msg, {\n    code,\n    ...(rest || {})\n  }];\n  if (i18n?.services?.logger?.forward) {\n    return i18n.services.logger.forward(args, 'warn', 'react-i18next::', true);\n  }\n  if (isString(args[0])) args[0] = `react-i18next:: ${args[0]}`;\n  if (i18n?.services?.logger?.warn) {\n    i18n.services.logger.warn(...args);\n  } else if (console?.warn) {\n    console.warn(...args);\n  }\n};\nconst alreadyWarned = {};\nexport const warnOnce = (i18n, code, msg, rest) => {\n  if (isString(msg) && alreadyWarned[msg]) return;\n  if (isString(msg)) alreadyWarned[msg] = new Date();\n  warn(i18n, code, msg, rest);\n};\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nexport const loadNamespaces = (i18n, ns, cb) => {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n};\nexport const loadLanguages = (i18n, lng, ns, cb) => {\n  if (isString(ns)) ns = [ns];\n  if (i18n.options.preload && i18n.options.preload.indexOf(lng) > -1) return loadNamespaces(i18n, ns, cb);\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n};\nexport const hasLoadedNamespace = (ns, i18n, options = {}) => {\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce(i18n, 'NO_LANGUAGES', 'i18n.languages were undefined or empty', {\n      languages: i18n.languages\n    });\n    return true;\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n?.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n};\nexport const getDisplayName = Component => Component.displayName || Component.name || (isString(Component) && Component.length > 0 ? Component : 'Unknown');\nexport const isString = obj => typeof obj === 'string';\nexport const isObject = obj => typeof obj === 'object' && obj !== null;", "const matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nexport const unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);", "import { unescape } from './unescape.js';\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape\n};\nexport const setDefaults = (options = {}) => {\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n};\nexport const getDefaults = () => defaultOptions;", "let i18nInstance;\nexport const setI18n = instance => {\n  i18nInstance = instance;\n};\nexport const getI18n = () => i18nInstance;", "import { setDefaults } from './defaults.js';\nimport { setI18n } from './i18nInstance.js';\nexport const initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    setDefaults(instance.options.react);\n    setI18n(instance);\n  }\n};", "import { createContext } from 'react';\nimport { getDefaults, setDefaults } from './defaults.js';\nimport { getI18n, setI18n } from './i18nInstance.js';\nimport { initReactI18next } from './initReactI18next.js';\nexport { getDefaults, setDefaults, getI18n, setI18n, initReactI18next };\nexport const I18nContext = createContext();\nexport class ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nexport const composeInitialProps = ForComponent => async ctx => {\n  const componentsInitialProps = (await ForComponent.getInitialProps?.(ctx)) ?? {};\n  const i18nInitialProps = getInitialProps();\n  return {\n    ...componentsInitialProps,\n    ...i18nInitialProps\n  };\n};\nexport const getInitialProps = () => {\n  const i18n = getI18n();\n  const namespaces = i18n.reportNamespaces?.getUsedNamespaces() ?? [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n};", "import { useState, useEffect, useContext, useRef, useCallback } from 'react';\nimport { getI18n, getDefaults, ReportNamespaces, I18nContext } from './context.js';\nimport { warnOnce, loadNamespaces, loadLanguages, hasLoadedNamespace, isString, isObject } from './utils.js';\nconst usePrevious = (value, ignore) => {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nconst alwaysNewT = (i18n, language, namespace, keyPrefix) => i18n.getFixedT(language, namespace, keyPrefix);\nconst useMemoizedT = (i18n, language, namespace, keyPrefix) => useCallback(alwaysNewT(i18n, language, namespace, keyPrefix), [i18n, language, namespace, keyPrefix]);\nexport const useTranslation = (ns, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new ReportNamespaces();\n  if (!i18n) {\n    warnOnce(i18n, 'NO_I18NEXT_INSTANCE', 'useTranslation: You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if (isString(optsOrDefaultValue)) return optsOrDefaultValue;\n      if (isObject(optsOrDefaultValue) && isString(optsOrDefaultValue.defaultValue)) return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react?.wait) warnOnce(i18n, 'DEPRECATED_OPTION', 'useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...getDefaults(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options?.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  i18n.reportNamespaces.addUsedNamespaces?.(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => hasLoadedNamespace(n, i18n, i18nOptions));\n  const memoGetT = useMemoizedT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const getT = () => memoGetT;\n  const getNewT = () => alwaysNewT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const [t, setT] = useState(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = useRef(true);\n  useEffect(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        loadLanguages(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      } else {\n        loadNamespaces(i18n, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getNewT);\n    }\n    const boundReset = () => {\n      if (isMounted.current) setT(getNewT);\n    };\n    if (bindI18n) i18n?.on(bindI18n, boundReset);\n    if (bindI18nStore) i18n?.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (i18n) bindI18n?.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  useEffect(() => {\n    if (isMounted.current && ready) {\n      setT(getT);\n    }\n  }, [i18n, keyPrefix, ready]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      loadLanguages(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      loadNamespaces(i18n, namespaces, () => resolve());\n    }\n  });\n};", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nconst toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string) => string.replace(\n  /^([A-Z])|[\\s-_]+(\\w)/g,\n  (match, p1, p2) => p2 ? p2.toUpperCase() : p1.toLowerCase()\n);\nconst toPascalCase = (string) => {\n  const camelCase = toCamelCase(string);\n  return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = (...classes) => classes.filter((className, index, array) => {\n  return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n}).join(\" \").trim();\nconst hasA11yProp = (props) => {\n  for (const prop in props) {\n    if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n      return true;\n    }\n  }\n};\n\nexport { hasA11yProp, mergeClasses, toCamelCase, toKebabCase, toPascalCase };\n//# sourceMappingURL=utils.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nvar defaultAttributes = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: 24,\n  height: 24,\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: 2,\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\"\n};\n\nexport { defaultAttributes as default };\n//# sourceMappingURL=defaultAttributes.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\n\nconst Icon = forwardRef(\n  ({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    className = \"\",\n    children,\n    iconNode,\n    ...rest\n  }, ref) => createElement(\n    \"svg\",\n    {\n      ref,\n      ...defaultAttributes,\n      width: size,\n      height: size,\n      stroke: color,\n      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      className: mergeClasses(\"lucide\", className),\n      ...!children && !hasA11yProp(rest) && { \"aria-hidden\": \"true\" },\n      ...rest\n    },\n    [\n      ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n      ...Array.isArray(children) ? children : [children]\n    ]\n  )\n);\n\nexport { Icon as default };\n//# sourceMappingURL=Icon.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\n\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(\n    ({ className, ...props }, ref) => createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className\n      ),\n      ...props\n    })\n  );\n  Component.displayName = toPascalCase(iconName);\n  return Component;\n};\n\nexport { createLucideIcon as default };\n//# sourceMappingURL=createLucideIcon.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M12 7v14\", key: \"1akyts\" }],\n  [\n    \"path\",\n    {\n      d: \"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z\",\n      key: \"ruj8y\"\n    }\n  ]\n];\nconst BookOpen = createLucideIcon(\"book-open\", __iconNode);\n\nexport { __iconNode, BookOpen as default };\n//# sourceMappingURL=book-open.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"polyline\", { points: \"12 6 12 12 16 14\", key: \"68esgv\" }]\n];\nconst Clock = createLucideIcon(\"clock\", __iconNode);\n\nexport { __iconNode, Clock as default };\n//# sourceMappingURL=clock.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4\",\n      key: \"tonef\"\n    }\n  ],\n  [\"path\", { d: \"M9 18c-4.51 2-5-2-7-2\", key: \"9comsn\" }]\n];\nconst Github = createLucideIcon(\"github\", __iconNode);\n\nexport { __iconNode, Github as default };\n//# sourceMappingURL=github.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7\", key: \"132q7q\" }],\n  [\"rect\", { x: \"2\", y: \"4\", width: \"20\", height: \"16\", rx: \"2\", key: \"izxlao\" }]\n];\nconst Mail = createLucideIcon(\"mail\", __iconNode);\n\nexport { __iconNode, Mail as default };\n//# sourceMappingURL=mail.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M4 12h16\", key: \"1lakjw\" }],\n  [\"path\", { d: \"M4 18h16\", key: \"19g7jn\" }],\n  [\"path\", { d: \"M4 6h16\", key: \"1o0s65\" }]\n];\nconst Menu = createLucideIcon(\"menu\", __iconNode);\n\nexport { __iconNode, Menu as default };\n//# sourceMappingURL=menu.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m21 21-4.34-4.34\", key: \"14j7rj\" }],\n  [\"circle\", { cx: \"11\", cy: \"11\", r: \"8\", key: \"4ej97u\" }]\n];\nconst Search = createLucideIcon(\"search\", __iconNode);\n\nexport { __iconNode, Search as default };\n//# sourceMappingURL=search.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z\",\n      key: \"r04s7s\"\n    }\n  ]\n];\nconst Star = createLucideIcon(\"star\", __iconNode);\n\nexport { __iconNode, Star as default };\n//# sourceMappingURL=star.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M16 7h6v6\", key: \"box55l\" }],\n  [\"path\", { d: \"m22 7-8.5 8.5-5-5L2 17\", key: \"1t1m79\" }]\n];\nconst TrendingUp = createLucideIcon(\"trending-up\", __iconNode);\n\nexport { __iconNode, TrendingUp as default };\n//# sourceMappingURL=trending-up.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z\",\n      key: \"pff0z6\"\n    }\n  ]\n];\nconst Twitter = createLucideIcon(\"twitter\", __iconNode);\n\nexport { __iconNode, Twitter as default };\n//# sourceMappingURL=twitter.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2\", key: \"975kel\" }],\n  [\"circle\", { cx: \"12\", cy: \"7\", r: \"4\", key: \"17ys0d\" }]\n];\nconst User = createLucideIcon(\"user\", __iconNode);\n\nexport { __iconNode, User as default };\n//# sourceMappingURL=user.js.map\n", "function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;", "/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n", "const CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = config => {\n  const classMap = createClassMap(config);\n  const {\n    conflictingClassGroups,\n    conflictingClassGroupModifiers\n  } = config;\n  const getClassGroupId = className => {\n    const classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  };\n  const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier) => {\n    const conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]];\n    }\n    return conflicts;\n  };\n  return {\n    getClassGroupId,\n    getConflictingClassGroupIds\n  };\n};\nconst getGroupRecursive = (classParts, classPartObject) => {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  const currentClassPart = classParts[0];\n  const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  const classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(({\n    validator\n  }) => validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = className => {\n  if (arbitraryPropertyRegex.test(className)) {\n    const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n};\n/**\n * Exported for testing only\n */\nconst createClassMap = config => {\n  const {\n    theme,\n    classGroups\n  } = config;\n  const classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  for (const classGroupId in classGroups) {\n    processClassesRecursively(classGroups[classGroupId], classMap, classGroupId, theme);\n  }\n  return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme) => {\n  classGroup.forEach(classDefinition => {\n    if (typeof classDefinition === 'string') {\n      const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(([key, classGroup]) => {\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n};\nconst getPart = (classPartObject, path) => {\n  let currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(pathPart => {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n};\nconst isThemeGetter = func => func.isThemeGetter;\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = maxCacheSize => {\n  if (maxCacheSize < 1) {\n    return {\n      get: () => undefined,\n      set: () => {}\n    };\n  }\n  let cacheSize = 0;\n  let cache = new Map();\n  let previousCache = new Map();\n  const update = (key, value) => {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  };\n  return {\n    get(key) {\n      let value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst MODIFIER_SEPARATOR = ':';\nconst MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length;\nconst createParseClassName = config => {\n  const {\n    prefix,\n    experimentalParseClassName\n  } = config;\n  /**\n   * Parse class name into parts.\n   *\n   * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS\n   * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n   */\n  let parseClassName = className => {\n    const modifiers = [];\n    let bracketDepth = 0;\n    let parenDepth = 0;\n    let modifierStart = 0;\n    let postfixModifierPosition;\n    for (let index = 0; index < className.length; index++) {\n      let currentCharacter = className[index];\n      if (bracketDepth === 0 && parenDepth === 0) {\n        if (currentCharacter === MODIFIER_SEPARATOR) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + MODIFIER_SEPARATOR_LENGTH;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      } else if (currentCharacter === '(') {\n        parenDepth++;\n      } else if (currentCharacter === ')') {\n        parenDepth--;\n      }\n    }\n    const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier);\n    const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier;\n    const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    };\n  };\n  if (prefix) {\n    const fullPrefix = prefix + MODIFIER_SEPARATOR;\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => className.startsWith(fullPrefix) ? parseClassNameOriginal(className.substring(fullPrefix.length)) : {\n      isExternal: true,\n      modifiers: [],\n      hasImportantModifier: false,\n      baseClassName: className,\n      maybePostfixModifierPosition: undefined\n    };\n  }\n  if (experimentalParseClassName) {\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => experimentalParseClassName({\n      className,\n      parseClassName: parseClassNameOriginal\n    });\n  }\n  return parseClassName;\n};\nconst stripImportantModifier = baseClassName => {\n  if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(0, baseClassName.length - 1);\n  }\n  /**\n   * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.\n   * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864\n   */\n  if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(1);\n  }\n  return baseClassName;\n};\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nconst createSortModifiers = config => {\n  const orderSensitiveModifiers = Object.fromEntries(config.orderSensitiveModifiers.map(modifier => [modifier, true]));\n  const sortModifiers = modifiers => {\n    if (modifiers.length <= 1) {\n      return modifiers;\n    }\n    const sortedModifiers = [];\n    let unsortedModifiers = [];\n    modifiers.forEach(modifier => {\n      const isPositionSensitive = modifier[0] === '[' || orderSensitiveModifiers[modifier];\n      if (isPositionSensitive) {\n        sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n        unsortedModifiers = [];\n      } else {\n        unsortedModifiers.push(modifier);\n      }\n    });\n    sortedModifiers.push(...unsortedModifiers.sort());\n    return sortedModifiers;\n  };\n  return sortModifiers;\n};\nconst createConfigUtils = config => ({\n  cache: createLruCache(config.cacheSize),\n  parseClassName: createParseClassName(config),\n  sortModifiers: createSortModifiers(config),\n  ...createClassGroupUtils(config)\n});\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils) => {\n  const {\n    parseClassName,\n    getClassGroupId,\n    getConflictingClassGroupIds,\n    sortModifiers\n  } = configUtils;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  const classGroupsInConflict = [];\n  const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n  let result = '';\n  for (let index = classNames.length - 1; index >= 0; index -= 1) {\n    const originalClassName = classNames[index];\n    const {\n      isExternal,\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    } = parseClassName(originalClassName);\n    if (isExternal) {\n      result = originalClassName + (result.length > 0 ? ' ' + result : result);\n      continue;\n    }\n    let hasPostfixModifier = !!maybePostfixModifierPosition;\n    let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    if (!classGroupId) {\n      if (!hasPostfixModifier) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      hasPostfixModifier = false;\n    }\n    const variantModifier = sortModifiers(modifiers).join(':');\n    const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    const classId = modifierId + classGroupId;\n    if (classGroupsInConflict.includes(classId)) {\n      // Tailwind class omitted due to conflict\n      continue;\n    }\n    classGroupsInConflict.push(classId);\n    const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n    for (let i = 0; i < conflictGroups.length; ++i) {\n      const group = conflictGroups[i];\n      classGroupsInConflict.push(modifierId + group);\n    }\n    // Tailwind class not in conflict\n    result = originalClassName + (result.length > 0 ? ' ' + result : result);\n  }\n  return result;\n};\n\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  let index = 0;\n  let argument;\n  let resolvedValue;\n  let string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nconst toValue = mix => {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  let resolvedValue;\n  let string = '';\n  for (let k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n  let configUtils;\n  let cacheGet;\n  let cacheSet;\n  let functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    const config = createConfigRest.reduce((previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig), createConfigFirst());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    const cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    const result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nconst fromTheme = key => {\n  const themeGetter = theme => theme[key] || [];\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:(\\w[\\w-]*):)?(.+)\\]$/i;\nconst arbitraryVariableRegex = /^\\((?:(\\w[\\w-]*):)?(.+)\\)$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isFraction = value => fractionRegex.test(value);\nconst isNumber = value => !!value && !Number.isNaN(Number(value));\nconst isInteger = value => !!value && Number.isInteger(Number(value));\nconst isPercent = value => value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isTshirtSize = value => tshirtUnitRegex.test(value);\nconst isAny = () => true;\nconst isLengthOnly = value =>\n// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n// For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n// I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\nlengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = () => false;\nconst isShadow = value => shadowRegex.test(value);\nconst isImage = value => imageRegex.test(value);\nconst isAnyNonArbitrary = value => !isArbitraryValue(value) && !isArbitraryVariable(value);\nconst isArbitrarySize = value => getIsArbitraryValue(value, isLabelSize, isNever);\nconst isArbitraryValue = value => arbitraryValueRegex.test(value);\nconst isArbitraryLength = value => getIsArbitraryValue(value, isLabelLength, isLengthOnly);\nconst isArbitraryNumber = value => getIsArbitraryValue(value, isLabelNumber, isNumber);\nconst isArbitraryPosition = value => getIsArbitraryValue(value, isLabelPosition, isNever);\nconst isArbitraryImage = value => getIsArbitraryValue(value, isLabelImage, isImage);\nconst isArbitraryShadow = value => getIsArbitraryValue(value, isLabelShadow, isShadow);\nconst isArbitraryVariable = value => arbitraryVariableRegex.test(value);\nconst isArbitraryVariableLength = value => getIsArbitraryVariable(value, isLabelLength);\nconst isArbitraryVariableFamilyName = value => getIsArbitraryVariable(value, isLabelFamilyName);\nconst isArbitraryVariablePosition = value => getIsArbitraryVariable(value, isLabelPosition);\nconst isArbitraryVariableSize = value => getIsArbitraryVariable(value, isLabelSize);\nconst isArbitraryVariableImage = value => getIsArbitraryVariable(value, isLabelImage);\nconst isArbitraryVariableShadow = value => getIsArbitraryVariable(value, isLabelShadow, true);\n// Helpers\nconst getIsArbitraryValue = (value, testLabel, testValue) => {\n  const result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return testValue(result[2]);\n  }\n  return false;\n};\nconst getIsArbitraryVariable = (value, testLabel, shouldMatchNoLabel = false) => {\n  const result = arbitraryVariableRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return shouldMatchNoLabel;\n  }\n  return false;\n};\n// Labels\nconst isLabelPosition = label => label === 'position' || label === 'percentage';\nconst isLabelImage = label => label === 'image' || label === 'url';\nconst isLabelSize = label => label === 'length' || label === 'size' || label === 'bg-size';\nconst isLabelLength = label => label === 'length';\nconst isLabelNumber = label => label === 'number';\nconst isLabelFamilyName = label => label === 'family-name';\nconst isLabelShadow = label => label === 'shadow';\nconst validators = /*#__PURE__*/Object.defineProperty({\n  __proto__: null,\n  isAny,\n  isAnyNonArbitrary,\n  isArbitraryImage,\n  isArbitraryLength,\n  isArbitraryNumber,\n  isArbitraryPosition,\n  isArbitraryShadow,\n  isArbitrarySize,\n  isArbitraryValue,\n  isArbitraryVariable,\n  isArbitraryVariableFamilyName,\n  isArbitraryVariableImage,\n  isArbitraryVariableLength,\n  isArbitraryVariablePosition,\n  isArbitraryVariableShadow,\n  isArbitraryVariableSize,\n  isFraction,\n  isInteger,\n  isNumber,\n  isPercent,\n  isTshirtSize\n}, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getDefaultConfig = () => {\n  /**\n   * Theme getters for theme variable namespaces\n   * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces\n   */\n  /***/\n  const themeColor = fromTheme('color');\n  const themeFont = fromTheme('font');\n  const themeText = fromTheme('text');\n  const themeFontWeight = fromTheme('font-weight');\n  const themeTracking = fromTheme('tracking');\n  const themeLeading = fromTheme('leading');\n  const themeBreakpoint = fromTheme('breakpoint');\n  const themeContainer = fromTheme('container');\n  const themeSpacing = fromTheme('spacing');\n  const themeRadius = fromTheme('radius');\n  const themeShadow = fromTheme('shadow');\n  const themeInsetShadow = fromTheme('inset-shadow');\n  const themeTextShadow = fromTheme('text-shadow');\n  const themeDropShadow = fromTheme('drop-shadow');\n  const themeBlur = fromTheme('blur');\n  const themePerspective = fromTheme('perspective');\n  const themeAspect = fromTheme('aspect');\n  const themeEase = fromTheme('ease');\n  const themeAnimate = fromTheme('animate');\n  /**\n   * Helpers to avoid repeating the same scales\n   *\n   * We use functions that create a new array every time they're called instead of static arrays.\n   * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.\n   */\n  /***/\n  const scaleBreak = () => ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'];\n  const scalePosition = () => ['center', 'top', 'bottom', 'left', 'right', 'top-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-top', 'top-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-top', 'bottom-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-bottom', 'bottom-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-bottom'];\n  const scalePositionWithArbitrary = () => [...scalePosition(), isArbitraryVariable, isArbitraryValue];\n  const scaleOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'];\n  const scaleOverscroll = () => ['auto', 'contain', 'none'];\n  const scaleUnambiguousSpacing = () => [isArbitraryVariable, isArbitraryValue, themeSpacing];\n  const scaleInset = () => [isFraction, 'full', 'auto', ...scaleUnambiguousSpacing()];\n  const scaleGridTemplateColsRows = () => [isInteger, 'none', 'subgrid', isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartAndEnd = () => ['auto', {\n    span: ['full', isInteger, isArbitraryVariable, isArbitraryValue]\n  }, isInteger, isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartOrEnd = () => [isInteger, 'auto', isArbitraryVariable, isArbitraryValue];\n  const scaleGridAutoColsRows = () => ['auto', 'min', 'max', 'fr', isArbitraryVariable, isArbitraryValue];\n  const scaleAlignPrimaryAxis = () => ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch', 'baseline', 'center-safe', 'end-safe'];\n  const scaleAlignSecondaryAxis = () => ['start', 'end', 'center', 'stretch', 'center-safe', 'end-safe'];\n  const scaleMargin = () => ['auto', ...scaleUnambiguousSpacing()];\n  const scaleSizing = () => [isFraction, 'auto', 'full', 'dvw', 'dvh', 'lvw', 'lvh', 'svw', 'svh', 'min', 'max', 'fit', ...scaleUnambiguousSpacing()];\n  const scaleColor = () => [themeColor, isArbitraryVariable, isArbitraryValue];\n  const scaleBgPosition = () => [...scalePosition(), isArbitraryVariablePosition, isArbitraryPosition, {\n    position: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleBgRepeat = () => ['no-repeat', {\n    repeat: ['', 'x', 'y', 'space', 'round']\n  }];\n  const scaleBgSize = () => ['auto', 'cover', 'contain', isArbitraryVariableSize, isArbitrarySize, {\n    size: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleGradientStopPosition = () => [isPercent, isArbitraryVariableLength, isArbitraryLength];\n  const scaleRadius = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', 'full', themeRadius, isArbitraryVariable, isArbitraryValue];\n  const scaleBorderWidth = () => ['', isNumber, isArbitraryVariableLength, isArbitraryLength];\n  const scaleLineStyle = () => ['solid', 'dashed', 'dotted', 'double'];\n  const scaleBlendMode = () => ['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten', 'color-dodge', 'color-burn', 'hard-light', 'soft-light', 'difference', 'exclusion', 'hue', 'saturation', 'color', 'luminosity'];\n  const scaleMaskImagePosition = () => [isNumber, isPercent, isArbitraryVariablePosition, isArbitraryPosition];\n  const scaleBlur = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', themeBlur, isArbitraryVariable, isArbitraryValue];\n  const scaleRotate = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleScale = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleSkew = () => [isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleTranslate = () => [isFraction, 'full', ...scaleUnambiguousSpacing()];\n  return {\n    cacheSize: 500,\n    theme: {\n      animate: ['spin', 'ping', 'pulse', 'bounce'],\n      aspect: ['video'],\n      blur: [isTshirtSize],\n      breakpoint: [isTshirtSize],\n      color: [isAny],\n      container: [isTshirtSize],\n      'drop-shadow': [isTshirtSize],\n      ease: ['in', 'out', 'in-out'],\n      font: [isAnyNonArbitrary],\n      'font-weight': ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black'],\n      'inset-shadow': [isTshirtSize],\n      leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose'],\n      perspective: ['dramatic', 'near', 'normal', 'midrange', 'distant', 'none'],\n      radius: [isTshirtSize],\n      shadow: [isTshirtSize],\n      spacing: ['px', isNumber],\n      text: [isTshirtSize],\n      'text-shadow': [isTshirtSize],\n      tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest']\n    },\n    classGroups: {\n      // --------------\n      // --- Layout ---\n      // --------------\n      /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */\n      aspect: [{\n        aspect: ['auto', 'square', isFraction, isArbitraryValue, isArbitraryVariable, themeAspect]\n      }],\n      /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       * @deprecated since Tailwind CSS v4.0.0\n       */\n      container: ['container'],\n      /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */\n      columns: [{\n        columns: [isNumber, isArbitraryValue, isArbitraryVariable, themeContainer]\n      }],\n      /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */\n      'break-after': [{\n        'break-after': scaleBreak()\n      }],\n      /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */\n      'break-before': [{\n        'break-before': scaleBreak()\n      }],\n      /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */\n      'break-inside': [{\n        'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column']\n      }],\n      /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */\n      'box-decoration': [{\n        'box-decoration': ['slice', 'clone']\n      }],\n      /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */\n      box: [{\n        box: ['border', 'content']\n      }],\n      /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */\n      display: ['block', 'inline-block', 'inline', 'flex', 'inline-flex', 'table', 'inline-table', 'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row-group', 'table-row', 'flow-root', 'grid', 'inline-grid', 'contents', 'list-item', 'hidden'],\n      /**\n       * Screen Reader Only\n       * @see https://tailwindcss.com/docs/display#screen-reader-only\n       */\n      sr: ['sr-only', 'not-sr-only'],\n      /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */\n      float: [{\n        float: ['right', 'left', 'none', 'start', 'end']\n      }],\n      /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */\n      clear: [{\n        clear: ['left', 'right', 'both', 'none', 'start', 'end']\n      }],\n      /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */\n      isolation: ['isolate', 'isolation-auto'],\n      /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */\n      'object-fit': [{\n        object: ['contain', 'cover', 'fill', 'none', 'scale-down']\n      }],\n      /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */\n      'object-position': [{\n        object: scalePositionWithArbitrary()\n      }],\n      /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      overflow: [{\n        overflow: scaleOverflow()\n      }],\n      /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-x': [{\n        'overflow-x': scaleOverflow()\n      }],\n      /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-y': [{\n        'overflow-y': scaleOverflow()\n      }],\n      /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      overscroll: [{\n        overscroll: scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-x': [{\n        'overscroll-x': scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-y': [{\n        'overscroll-y': scaleOverscroll()\n      }],\n      /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */\n      position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n      /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      inset: [{\n        inset: scaleInset()\n      }],\n      /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-x': [{\n        'inset-x': scaleInset()\n      }],\n      /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-y': [{\n        'inset-y': scaleInset()\n      }],\n      /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      start: [{\n        start: scaleInset()\n      }],\n      /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      end: [{\n        end: scaleInset()\n      }],\n      /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      top: [{\n        top: scaleInset()\n      }],\n      /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      right: [{\n        right: scaleInset()\n      }],\n      /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      bottom: [{\n        bottom: scaleInset()\n      }],\n      /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      left: [{\n        left: scaleInset()\n      }],\n      /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */\n      visibility: ['visible', 'invisible', 'collapse'],\n      /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */\n      z: [{\n        z: [isInteger, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------------\n      // --- Flexbox and Grid ---\n      // ------------------------\n      /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */\n      basis: [{\n        basis: [isFraction, 'full', 'auto', themeContainer, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */\n      'flex-direction': [{\n        flex: ['row', 'row-reverse', 'col', 'col-reverse']\n      }],\n      /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */\n      'flex-wrap': [{\n        flex: ['nowrap', 'wrap', 'wrap-reverse']\n      }],\n      /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */\n      flex: [{\n        flex: [isNumber, isFraction, 'auto', 'initial', 'none', isArbitraryValue]\n      }],\n      /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */\n      grow: [{\n        grow: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */\n      shrink: [{\n        shrink: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */\n      order: [{\n        order: [isInteger, 'first', 'last', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */\n      'grid-cols': [{\n        'grid-cols': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start-end': [{\n        col: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start': [{\n        'col-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-end': [{\n        'col-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */\n      'grid-rows': [{\n        'grid-rows': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start-end': [{\n        row: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start': [{\n        'row-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-end': [{\n        'row-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */\n      'grid-flow': [{\n        'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense']\n      }],\n      /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */\n      'auto-cols': [{\n        'auto-cols': scaleGridAutoColsRows()\n      }],\n      /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */\n      'auto-rows': [{\n        'auto-rows': scaleGridAutoColsRows()\n      }],\n      /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */\n      gap: [{\n        gap: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-x': [{\n        'gap-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-y': [{\n        'gap-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */\n      'justify-content': [{\n        justify: [...scaleAlignPrimaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */\n      'justify-items': [{\n        'justify-items': [...scaleAlignSecondaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */\n      'justify-self': [{\n        'justify-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */\n      'align-content': [{\n        content: ['normal', ...scaleAlignPrimaryAxis()]\n      }],\n      /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */\n      'align-items': [{\n        items: [...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */\n      'align-self': [{\n        self: ['auto', ...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */\n      'place-content': [{\n        'place-content': scaleAlignPrimaryAxis()\n      }],\n      /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */\n      'place-items': [{\n        'place-items': [...scaleAlignSecondaryAxis(), 'baseline']\n      }],\n      /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */\n      'place-self': [{\n        'place-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      // Spacing\n      /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */\n      p: [{\n        p: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */\n      px: [{\n        px: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */\n      py: [{\n        py: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */\n      ps: [{\n        ps: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pe: [{\n        pe: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pt: [{\n        pt: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pr: [{\n        pr: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pb: [{\n        pb: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pl: [{\n        pl: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */\n      m: [{\n        m: scaleMargin()\n      }],\n      /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mx: [{\n        mx: scaleMargin()\n      }],\n      /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */\n      my: [{\n        my: scaleMargin()\n      }],\n      /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ms: [{\n        ms: scaleMargin()\n      }],\n      /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */\n      me: [{\n        me: scaleMargin()\n      }],\n      /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mt: [{\n        mt: scaleMargin()\n      }],\n      /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mr: [{\n        mr: scaleMargin()\n      }],\n      /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mb: [{\n        mb: scaleMargin()\n      }],\n      /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ml: [{\n        ml: scaleMargin()\n      }],\n      /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x': [{\n        'space-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x-reverse': ['space-x-reverse'],\n      /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y': [{\n        'space-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y-reverse': ['space-y-reverse'],\n      // --------------\n      // --- Sizing ---\n      // --------------\n      /**\n       * Size\n       * @see https://tailwindcss.com/docs/width#setting-both-width-and-height\n       */\n      size: [{\n        size: scaleSizing()\n      }],\n      /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */\n      w: [{\n        w: [themeContainer, 'screen', ...scaleSizing()]\n      }],\n      /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */\n      'min-w': [{\n        'min-w': [themeContainer, 'screen', /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */\n      'max-w': [{\n        'max-w': [themeContainer, 'screen', 'none', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'prose', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        {\n          screen: [themeBreakpoint]\n        }, ...scaleSizing()]\n      }],\n      /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */\n      h: [{\n        h: ['screen', 'lh', ...scaleSizing()]\n      }],\n      /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */\n      'min-h': [{\n        'min-h': ['screen', 'lh', 'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */\n      'max-h': [{\n        'max-h': ['screen', 'lh', ...scaleSizing()]\n      }],\n      // ------------------\n      // --- Typography ---\n      // ------------------\n      /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */\n      'font-size': [{\n        text: ['base', themeText, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */\n      'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n      /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */\n      'font-style': ['italic', 'not-italic'],\n      /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */\n      'font-weight': [{\n        font: [themeFontWeight, isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Font Stretch\n       * @see https://tailwindcss.com/docs/font-stretch\n       */\n      'font-stretch': [{\n        'font-stretch': ['ultra-condensed', 'extra-condensed', 'condensed', 'semi-condensed', 'normal', 'semi-expanded', 'expanded', 'extra-expanded', 'ultra-expanded', isPercent, isArbitraryValue]\n      }],\n      /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */\n      'font-family': [{\n        font: [isArbitraryVariableFamilyName, isArbitraryValue, themeFont]\n      }],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-normal': ['normal-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-ordinal': ['ordinal'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-slashed-zero': ['slashed-zero'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n      /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */\n      tracking: [{\n        tracking: [themeTracking, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */\n      'line-clamp': [{\n        'line-clamp': [isNumber, 'none', isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */\n      leading: [{\n        leading: [/** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        themeLeading, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */\n      'list-image': [{\n        'list-image': ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */\n      'list-style-position': [{\n        list: ['inside', 'outside']\n      }],\n      /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */\n      'list-style-type': [{\n        list: ['disc', 'decimal', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */\n      'text-alignment': [{\n        text: ['left', 'center', 'right', 'justify', 'start', 'end']\n      }],\n      /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://v3.tailwindcss.com/docs/placeholder-color\n       */\n      'placeholder-color': [{\n        placeholder: scaleColor()\n      }],\n      /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */\n      'text-color': [{\n        text: scaleColor()\n      }],\n      /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */\n      'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n      /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */\n      'text-decoration-style': [{\n        decoration: [...scaleLineStyle(), 'wavy']\n      }],\n      /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */\n      'text-decoration-thickness': [{\n        decoration: [isNumber, 'from-font', 'auto', isArbitraryVariable, isArbitraryLength]\n      }],\n      /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */\n      'text-decoration-color': [{\n        decoration: scaleColor()\n      }],\n      /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */\n      'underline-offset': [{\n        'underline-offset': [isNumber, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */\n      'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n      /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */\n      'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n      /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */\n      'text-wrap': [{\n        text: ['wrap', 'nowrap', 'balance', 'pretty']\n      }],\n      /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */\n      indent: [{\n        indent: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */\n      'vertical-align': [{\n        align: ['baseline', 'top', 'middle', 'bottom', 'text-top', 'text-bottom', 'sub', 'super', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */\n      whitespace: [{\n        whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces']\n      }],\n      /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */\n      break: [{\n        break: ['normal', 'words', 'all', 'keep']\n      }],\n      /**\n       * Overflow Wrap\n       * @see https://tailwindcss.com/docs/overflow-wrap\n       */\n      wrap: [{\n        wrap: ['break-word', 'anywhere', 'normal']\n      }],\n      /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */\n      hyphens: [{\n        hyphens: ['none', 'manual', 'auto']\n      }],\n      /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */\n      content: [{\n        content: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -------------------\n      // --- Backgrounds ---\n      // -------------------\n      /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */\n      'bg-attachment': [{\n        bg: ['fixed', 'local', 'scroll']\n      }],\n      /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */\n      'bg-clip': [{\n        'bg-clip': ['border', 'padding', 'content', 'text']\n      }],\n      /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */\n      'bg-origin': [{\n        'bg-origin': ['border', 'padding', 'content']\n      }],\n      /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */\n      'bg-position': [{\n        bg: scaleBgPosition()\n      }],\n      /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */\n      'bg-repeat': [{\n        bg: scaleBgRepeat()\n      }],\n      /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */\n      'bg-size': [{\n        bg: scaleBgSize()\n      }],\n      /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */\n      'bg-image': [{\n        bg: ['none', {\n          linear: [{\n            to: ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl']\n          }, isInteger, isArbitraryVariable, isArbitraryValue],\n          radial: ['', isArbitraryVariable, isArbitraryValue],\n          conic: [isInteger, isArbitraryVariable, isArbitraryValue]\n        }, isArbitraryVariableImage, isArbitraryImage]\n      }],\n      /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */\n      'bg-color': [{\n        bg: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from-pos': [{\n        from: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via-pos': [{\n        via: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to-pos': [{\n        to: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from': [{\n        from: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via': [{\n        via: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to': [{\n        to: scaleColor()\n      }],\n      // ---------------\n      // --- Borders ---\n      // ---------------\n      /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      rounded: [{\n        rounded: scaleRadius()\n      }],\n      /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-s': [{\n        'rounded-s': scaleRadius()\n      }],\n      /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-e': [{\n        'rounded-e': scaleRadius()\n      }],\n      /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-t': [{\n        'rounded-t': scaleRadius()\n      }],\n      /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-r': [{\n        'rounded-r': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-b': [{\n        'rounded-b': scaleRadius()\n      }],\n      /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-l': [{\n        'rounded-l': scaleRadius()\n      }],\n      /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ss': [{\n        'rounded-ss': scaleRadius()\n      }],\n      /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-se': [{\n        'rounded-se': scaleRadius()\n      }],\n      /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ee': [{\n        'rounded-ee': scaleRadius()\n      }],\n      /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-es': [{\n        'rounded-es': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tl': [{\n        'rounded-tl': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tr': [{\n        'rounded-tr': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-br': [{\n        'rounded-br': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-bl': [{\n        'rounded-bl': scaleRadius()\n      }],\n      /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w': [{\n        border: scaleBorderWidth()\n      }],\n      /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-x': [{\n        'border-x': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-y': [{\n        'border-y': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-s': [{\n        'border-s': scaleBorderWidth()\n      }],\n      /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-e': [{\n        'border-e': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-t': [{\n        'border-t': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-r': [{\n        'border-r': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-b': [{\n        'border-b': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-l': [{\n        'border-l': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x': [{\n        'divide-x': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x-reverse': ['divide-x-reverse'],\n      /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y': [{\n        'divide-y': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y-reverse': ['divide-y-reverse'],\n      /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */\n      'border-style': [{\n        border: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style\n       */\n      'divide-style': [{\n        divide: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color': [{\n        border: scaleColor()\n      }],\n      /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-x': [{\n        'border-x': scaleColor()\n      }],\n      /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-y': [{\n        'border-y': scaleColor()\n      }],\n      /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-s': [{\n        'border-s': scaleColor()\n      }],\n      /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-e': [{\n        'border-e': scaleColor()\n      }],\n      /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-t': [{\n        'border-t': scaleColor()\n      }],\n      /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-r': [{\n        'border-r': scaleColor()\n      }],\n      /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-b': [{\n        'border-b': scaleColor()\n      }],\n      /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-l': [{\n        'border-l': scaleColor()\n      }],\n      /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */\n      'divide-color': [{\n        divide: scaleColor()\n      }],\n      /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */\n      'outline-style': [{\n        outline: [...scaleLineStyle(), 'none', 'hidden']\n      }],\n      /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */\n      'outline-offset': [{\n        'outline-offset': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */\n      'outline-w': [{\n        outline: ['', isNumber, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */\n      'outline-color': [{\n        outline: scaleColor()\n      }],\n      // ---------------\n      // --- Effects ---\n      // ---------------\n      /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */\n      shadow: [{\n        shadow: [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color\n       */\n      'shadow-color': [{\n        shadow: scaleColor()\n      }],\n      /**\n       * Inset Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow\n       */\n      'inset-shadow': [{\n        'inset-shadow': ['none', themeInsetShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Inset Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color\n       */\n      'inset-shadow-color': [{\n        'inset-shadow': scaleColor()\n      }],\n      /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring\n       */\n      'ring-w': [{\n        ring: scaleBorderWidth()\n      }],\n      /**\n       * Ring Width Inset\n       * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-w-inset': ['ring-inset'],\n      /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color\n       */\n      'ring-color': [{\n        ring: scaleColor()\n      }],\n      /**\n       * Ring Offset Width\n       * @see https://v3.tailwindcss.com/docs/ring-offset-width\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-w': [{\n        'ring-offset': [isNumber, isArbitraryLength]\n      }],\n      /**\n       * Ring Offset Color\n       * @see https://v3.tailwindcss.com/docs/ring-offset-color\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-color': [{\n        'ring-offset': scaleColor()\n      }],\n      /**\n       * Inset Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring\n       */\n      'inset-ring-w': [{\n        'inset-ring': scaleBorderWidth()\n      }],\n      /**\n       * Inset Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color\n       */\n      'inset-ring-color': [{\n        'inset-ring': scaleColor()\n      }],\n      /**\n       * Text Shadow\n       * @see https://tailwindcss.com/docs/text-shadow\n       */\n      'text-shadow': [{\n        'text-shadow': ['none', themeTextShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Text Shadow Color\n       * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color\n       */\n      'text-shadow-color': [{\n        'text-shadow': scaleColor()\n      }],\n      /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */\n      opacity: [{\n        opacity: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */\n      'mix-blend': [{\n        'mix-blend': [...scaleBlendMode(), 'plus-darker', 'plus-lighter']\n      }],\n      /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */\n      'bg-blend': [{\n        'bg-blend': scaleBlendMode()\n      }],\n      /**\n       * Mask Clip\n       * @see https://tailwindcss.com/docs/mask-clip\n       */\n      'mask-clip': [{\n        'mask-clip': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }, 'mask-no-clip'],\n      /**\n       * Mask Composite\n       * @see https://tailwindcss.com/docs/mask-composite\n       */\n      'mask-composite': [{\n        mask: ['add', 'subtract', 'intersect', 'exclude']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image-linear-pos': [{\n        'mask-linear': [isNumber]\n      }],\n      'mask-image-linear-from-pos': [{\n        'mask-linear-from': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-to-pos': [{\n        'mask-linear-to': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-from-color': [{\n        'mask-linear-from': scaleColor()\n      }],\n      'mask-image-linear-to-color': [{\n        'mask-linear-to': scaleColor()\n      }],\n      'mask-image-t-from-pos': [{\n        'mask-t-from': scaleMaskImagePosition()\n      }],\n      'mask-image-t-to-pos': [{\n        'mask-t-to': scaleMaskImagePosition()\n      }],\n      'mask-image-t-from-color': [{\n        'mask-t-from': scaleColor()\n      }],\n      'mask-image-t-to-color': [{\n        'mask-t-to': scaleColor()\n      }],\n      'mask-image-r-from-pos': [{\n        'mask-r-from': scaleMaskImagePosition()\n      }],\n      'mask-image-r-to-pos': [{\n        'mask-r-to': scaleMaskImagePosition()\n      }],\n      'mask-image-r-from-color': [{\n        'mask-r-from': scaleColor()\n      }],\n      'mask-image-r-to-color': [{\n        'mask-r-to': scaleColor()\n      }],\n      'mask-image-b-from-pos': [{\n        'mask-b-from': scaleMaskImagePosition()\n      }],\n      'mask-image-b-to-pos': [{\n        'mask-b-to': scaleMaskImagePosition()\n      }],\n      'mask-image-b-from-color': [{\n        'mask-b-from': scaleColor()\n      }],\n      'mask-image-b-to-color': [{\n        'mask-b-to': scaleColor()\n      }],\n      'mask-image-l-from-pos': [{\n        'mask-l-from': scaleMaskImagePosition()\n      }],\n      'mask-image-l-to-pos': [{\n        'mask-l-to': scaleMaskImagePosition()\n      }],\n      'mask-image-l-from-color': [{\n        'mask-l-from': scaleColor()\n      }],\n      'mask-image-l-to-color': [{\n        'mask-l-to': scaleColor()\n      }],\n      'mask-image-x-from-pos': [{\n        'mask-x-from': scaleMaskImagePosition()\n      }],\n      'mask-image-x-to-pos': [{\n        'mask-x-to': scaleMaskImagePosition()\n      }],\n      'mask-image-x-from-color': [{\n        'mask-x-from': scaleColor()\n      }],\n      'mask-image-x-to-color': [{\n        'mask-x-to': scaleColor()\n      }],\n      'mask-image-y-from-pos': [{\n        'mask-y-from': scaleMaskImagePosition()\n      }],\n      'mask-image-y-to-pos': [{\n        'mask-y-to': scaleMaskImagePosition()\n      }],\n      'mask-image-y-from-color': [{\n        'mask-y-from': scaleColor()\n      }],\n      'mask-image-y-to-color': [{\n        'mask-y-to': scaleColor()\n      }],\n      'mask-image-radial': [{\n        'mask-radial': [isArbitraryVariable, isArbitraryValue]\n      }],\n      'mask-image-radial-from-pos': [{\n        'mask-radial-from': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-to-pos': [{\n        'mask-radial-to': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-from-color': [{\n        'mask-radial-from': scaleColor()\n      }],\n      'mask-image-radial-to-color': [{\n        'mask-radial-to': scaleColor()\n      }],\n      'mask-image-radial-shape': [{\n        'mask-radial': ['circle', 'ellipse']\n      }],\n      'mask-image-radial-size': [{\n        'mask-radial': [{\n          closest: ['side', 'corner'],\n          farthest: ['side', 'corner']\n        }]\n      }],\n      'mask-image-radial-pos': [{\n        'mask-radial-at': scalePosition()\n      }],\n      'mask-image-conic-pos': [{\n        'mask-conic': [isNumber]\n      }],\n      'mask-image-conic-from-pos': [{\n        'mask-conic-from': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-to-pos': [{\n        'mask-conic-to': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-from-color': [{\n        'mask-conic-from': scaleColor()\n      }],\n      'mask-image-conic-to-color': [{\n        'mask-conic-to': scaleColor()\n      }],\n      /**\n       * Mask Mode\n       * @see https://tailwindcss.com/docs/mask-mode\n       */\n      'mask-mode': [{\n        mask: ['alpha', 'luminance', 'match']\n      }],\n      /**\n       * Mask Origin\n       * @see https://tailwindcss.com/docs/mask-origin\n       */\n      'mask-origin': [{\n        'mask-origin': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }],\n      /**\n       * Mask Position\n       * @see https://tailwindcss.com/docs/mask-position\n       */\n      'mask-position': [{\n        mask: scaleBgPosition()\n      }],\n      /**\n       * Mask Repeat\n       * @see https://tailwindcss.com/docs/mask-repeat\n       */\n      'mask-repeat': [{\n        mask: scaleBgRepeat()\n      }],\n      /**\n       * Mask Size\n       * @see https://tailwindcss.com/docs/mask-size\n       */\n      'mask-size': [{\n        mask: scaleBgSize()\n      }],\n      /**\n       * Mask Type\n       * @see https://tailwindcss.com/docs/mask-type\n       */\n      'mask-type': [{\n        'mask-type': ['alpha', 'luminance']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image': [{\n        mask: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ---------------\n      // --- Filters ---\n      // ---------------\n      /**\n       * Filter\n       * @see https://tailwindcss.com/docs/filter\n       */\n      filter: [{\n        filter: [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */\n      blur: [{\n        blur: scaleBlur()\n      }],\n      /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */\n      brightness: [{\n        brightness: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */\n      contrast: [{\n        contrast: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */\n      'drop-shadow': [{\n        'drop-shadow': [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeDropShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Drop Shadow Color\n       * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color\n       */\n      'drop-shadow-color': [{\n        'drop-shadow': scaleColor()\n      }],\n      /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */\n      grayscale: [{\n        grayscale: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */\n      'hue-rotate': [{\n        'hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */\n      invert: [{\n        invert: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */\n      saturate: [{\n        saturate: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */\n      sepia: [{\n        sepia: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Filter\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */\n      'backdrop-filter': [{\n        'backdrop-filter': [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */\n      'backdrop-blur': [{\n        'backdrop-blur': scaleBlur()\n      }],\n      /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */\n      'backdrop-brightness': [{\n        'backdrop-brightness': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */\n      'backdrop-contrast': [{\n        'backdrop-contrast': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */\n      'backdrop-grayscale': [{\n        'backdrop-grayscale': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */\n      'backdrop-hue-rotate': [{\n        'backdrop-hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */\n      'backdrop-invert': [{\n        'backdrop-invert': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */\n      'backdrop-opacity': [{\n        'backdrop-opacity': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */\n      'backdrop-saturate': [{\n        'backdrop-saturate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */\n      'backdrop-sepia': [{\n        'backdrop-sepia': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      // --------------\n      // --- Tables ---\n      // --------------\n      /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */\n      'border-collapse': [{\n        border: ['collapse', 'separate']\n      }],\n      /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing': [{\n        'border-spacing': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-x': [{\n        'border-spacing-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-y': [{\n        'border-spacing-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */\n      'table-layout': [{\n        table: ['auto', 'fixed']\n      }],\n      /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */\n      caption: [{\n        caption: ['top', 'bottom']\n      }],\n      // ---------------------------------\n      // --- Transitions and Animation ---\n      // ---------------------------------\n      /**\n       * Transition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */\n      transition: [{\n        transition: ['', 'all', 'colors', 'opacity', 'shadow', 'transform', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Behavior\n       * @see https://tailwindcss.com/docs/transition-behavior\n       */\n      'transition-behavior': [{\n        transition: ['normal', 'discrete']\n      }],\n      /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */\n      duration: [{\n        duration: [isNumber, 'initial', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */\n      ease: [{\n        ease: ['linear', 'initial', themeEase, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */\n      delay: [{\n        delay: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */\n      animate: [{\n        animate: ['none', themeAnimate, isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------\n      // --- Transforms ---\n      // ------------------\n      /**\n       * Backface Visibility\n       * @see https://tailwindcss.com/docs/backface-visibility\n       */\n      backface: [{\n        backface: ['hidden', 'visible']\n      }],\n      /**\n       * Perspective\n       * @see https://tailwindcss.com/docs/perspective\n       */\n      perspective: [{\n        perspective: [themePerspective, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Perspective Origin\n       * @see https://tailwindcss.com/docs/perspective-origin\n       */\n      'perspective-origin': [{\n        'perspective-origin': scalePositionWithArbitrary()\n      }],\n      /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      rotate: [{\n        rotate: scaleRotate()\n      }],\n      /**\n       * Rotate X\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-x': [{\n        'rotate-x': scaleRotate()\n      }],\n      /**\n       * Rotate Y\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-y': [{\n        'rotate-y': scaleRotate()\n      }],\n      /**\n       * Rotate Z\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-z': [{\n        'rotate-z': scaleRotate()\n      }],\n      /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */\n      scale: [{\n        scale: scaleScale()\n      }],\n      /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-x': [{\n        'scale-x': scaleScale()\n      }],\n      /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-y': [{\n        'scale-y': scaleScale()\n      }],\n      /**\n       * Scale Z\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-z': [{\n        'scale-z': scaleScale()\n      }],\n      /**\n       * Scale 3D\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-3d': ['scale-3d'],\n      /**\n       * Skew\n       * @see https://tailwindcss.com/docs/skew\n       */\n      skew: [{\n        skew: scaleSkew()\n      }],\n      /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-x': [{\n        'skew-x': scaleSkew()\n      }],\n      /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-y': [{\n        'skew-y': scaleSkew()\n      }],\n      /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */\n      transform: [{\n        transform: [isArbitraryVariable, isArbitraryValue, '', 'none', 'gpu', 'cpu']\n      }],\n      /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */\n      'transform-origin': [{\n        origin: scalePositionWithArbitrary()\n      }],\n      /**\n       * Transform Style\n       * @see https://tailwindcss.com/docs/transform-style\n       */\n      'transform-style': [{\n        transform: ['3d', 'flat']\n      }],\n      /**\n       * Translate\n       * @see https://tailwindcss.com/docs/translate\n       */\n      translate: [{\n        translate: scaleTranslate()\n      }],\n      /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-x': [{\n        'translate-x': scaleTranslate()\n      }],\n      /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-y': [{\n        'translate-y': scaleTranslate()\n      }],\n      /**\n       * Translate Z\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-z': [{\n        'translate-z': scaleTranslate()\n      }],\n      /**\n       * Translate None\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-none': ['translate-none'],\n      // ---------------------\n      // --- Interactivity ---\n      // ---------------------\n      /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */\n      accent: [{\n        accent: scaleColor()\n      }],\n      /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */\n      appearance: [{\n        appearance: ['none', 'auto']\n      }],\n      /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */\n      'caret-color': [{\n        caret: scaleColor()\n      }],\n      /**\n       * Color Scheme\n       * @see https://tailwindcss.com/docs/color-scheme\n       */\n      'color-scheme': [{\n        scheme: ['normal', 'dark', 'light', 'light-dark', 'only-dark', 'only-light']\n      }],\n      /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */\n      cursor: [{\n        cursor: ['auto', 'default', 'pointer', 'wait', 'text', 'move', 'help', 'not-allowed', 'none', 'context-menu', 'progress', 'cell', 'crosshair', 'vertical-text', 'alias', 'copy', 'no-drop', 'grab', 'grabbing', 'all-scroll', 'col-resize', 'row-resize', 'n-resize', 'e-resize', 's-resize', 'w-resize', 'ne-resize', 'nw-resize', 'se-resize', 'sw-resize', 'ew-resize', 'ns-resize', 'nesw-resize', 'nwse-resize', 'zoom-in', 'zoom-out', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Field Sizing\n       * @see https://tailwindcss.com/docs/field-sizing\n       */\n      'field-sizing': [{\n        'field-sizing': ['fixed', 'content']\n      }],\n      /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */\n      'pointer-events': [{\n        'pointer-events': ['auto', 'none']\n      }],\n      /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */\n      resize: [{\n        resize: ['none', '', 'y', 'x']\n      }],\n      /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */\n      'scroll-behavior': [{\n        scroll: ['auto', 'smooth']\n      }],\n      /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-m': [{\n        'scroll-m': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mx': [{\n        'scroll-mx': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-my': [{\n        'scroll-my': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ms': [{\n        'scroll-ms': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-me': [{\n        'scroll-me': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mt': [{\n        'scroll-mt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mr': [{\n        'scroll-mr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mb': [{\n        'scroll-mb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ml': [{\n        'scroll-ml': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-p': [{\n        'scroll-p': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-px': [{\n        'scroll-px': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-py': [{\n        'scroll-py': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-ps': [{\n        'scroll-ps': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pe': [{\n        'scroll-pe': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pt': [{\n        'scroll-pt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pr': [{\n        'scroll-pr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pb': [{\n        'scroll-pb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pl': [{\n        'scroll-pl': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */\n      'snap-align': [{\n        snap: ['start', 'end', 'center', 'align-none']\n      }],\n      /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */\n      'snap-stop': [{\n        snap: ['normal', 'always']\n      }],\n      /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-type': [{\n        snap: ['none', 'x', 'y', 'both']\n      }],\n      /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-strictness': [{\n        snap: ['mandatory', 'proximity']\n      }],\n      /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      touch: [{\n        touch: ['auto', 'none', 'manipulation']\n      }],\n      /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-x': [{\n        'touch-pan': ['x', 'left', 'right']\n      }],\n      /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-y': [{\n        'touch-pan': ['y', 'up', 'down']\n      }],\n      /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-pz': ['touch-pinch-zoom'],\n      /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */\n      select: [{\n        select: ['none', 'text', 'all', 'auto']\n      }],\n      /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */\n      'will-change': [{\n        'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -----------\n      // --- SVG ---\n      // -----------\n      /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */\n      fill: [{\n        fill: ['none', ...scaleColor()]\n      }],\n      /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */\n      'stroke-w': [{\n        stroke: [isNumber, isArbitraryVariableLength, isArbitraryLength, isArbitraryNumber]\n      }],\n      /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */\n      stroke: [{\n        stroke: ['none', ...scaleColor()]\n      }],\n      // ---------------------\n      // --- Accessibility ---\n      // ---------------------\n      /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */\n      'forced-color-adjust': [{\n        'forced-color-adjust': ['auto', 'none']\n      }]\n    },\n    conflictingClassGroups: {\n      overflow: ['overflow-x', 'overflow-y'],\n      overscroll: ['overscroll-x', 'overscroll-y'],\n      inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n      'inset-x': ['right', 'left'],\n      'inset-y': ['top', 'bottom'],\n      flex: ['basis', 'grow', 'shrink'],\n      gap: ['gap-x', 'gap-y'],\n      p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n      px: ['pr', 'pl'],\n      py: ['pt', 'pb'],\n      m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n      mx: ['mr', 'ml'],\n      my: ['mt', 'mb'],\n      size: ['w', 'h'],\n      'font-size': ['leading'],\n      'fvn-normal': ['fvn-ordinal', 'fvn-slashed-zero', 'fvn-figure', 'fvn-spacing', 'fvn-fraction'],\n      'fvn-ordinal': ['fvn-normal'],\n      'fvn-slashed-zero': ['fvn-normal'],\n      'fvn-figure': ['fvn-normal'],\n      'fvn-spacing': ['fvn-normal'],\n      'fvn-fraction': ['fvn-normal'],\n      'line-clamp': ['display', 'overflow'],\n      rounded: ['rounded-s', 'rounded-e', 'rounded-t', 'rounded-r', 'rounded-b', 'rounded-l', 'rounded-ss', 'rounded-se', 'rounded-ee', 'rounded-es', 'rounded-tl', 'rounded-tr', 'rounded-br', 'rounded-bl'],\n      'rounded-s': ['rounded-ss', 'rounded-es'],\n      'rounded-e': ['rounded-se', 'rounded-ee'],\n      'rounded-t': ['rounded-tl', 'rounded-tr'],\n      'rounded-r': ['rounded-tr', 'rounded-br'],\n      'rounded-b': ['rounded-br', 'rounded-bl'],\n      'rounded-l': ['rounded-tl', 'rounded-bl'],\n      'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n      'border-w': ['border-w-x', 'border-w-y', 'border-w-s', 'border-w-e', 'border-w-t', 'border-w-r', 'border-w-b', 'border-w-l'],\n      'border-w-x': ['border-w-r', 'border-w-l'],\n      'border-w-y': ['border-w-t', 'border-w-b'],\n      'border-color': ['border-color-x', 'border-color-y', 'border-color-s', 'border-color-e', 'border-color-t', 'border-color-r', 'border-color-b', 'border-color-l'],\n      'border-color-x': ['border-color-r', 'border-color-l'],\n      'border-color-y': ['border-color-t', 'border-color-b'],\n      translate: ['translate-x', 'translate-y', 'translate-none'],\n      'translate-none': ['translate', 'translate-x', 'translate-y', 'translate-z'],\n      'scroll-m': ['scroll-mx', 'scroll-my', 'scroll-ms', 'scroll-me', 'scroll-mt', 'scroll-mr', 'scroll-mb', 'scroll-ml'],\n      'scroll-mx': ['scroll-mr', 'scroll-ml'],\n      'scroll-my': ['scroll-mt', 'scroll-mb'],\n      'scroll-p': ['scroll-px', 'scroll-py', 'scroll-ps', 'scroll-pe', 'scroll-pt', 'scroll-pr', 'scroll-pb', 'scroll-pl'],\n      'scroll-px': ['scroll-pr', 'scroll-pl'],\n      'scroll-py': ['scroll-pt', 'scroll-pb'],\n      touch: ['touch-x', 'touch-y', 'touch-pz'],\n      'touch-x': ['touch'],\n      'touch-y': ['touch'],\n      'touch-pz': ['touch']\n    },\n    conflictingClassGroupModifiers: {\n      'font-size': ['leading']\n    },\n    orderSensitiveModifiers: ['*', '**', 'after', 'backdrop', 'before', 'details-content', 'file', 'first-letter', 'first-line', 'marker', 'placeholder', 'selection']\n  };\n};\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nconst mergeConfigs = (baseConfig, {\n  cacheSize,\n  prefix,\n  experimentalParseClassName,\n  extend = {},\n  override = {}\n}) => {\n  overrideProperty(baseConfig, 'cacheSize', cacheSize);\n  overrideProperty(baseConfig, 'prefix', prefix);\n  overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n  overrideConfigProperties(baseConfig.theme, override.theme);\n  overrideConfigProperties(baseConfig.classGroups, override.classGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroupModifiers, override.conflictingClassGroupModifiers);\n  overrideProperty(baseConfig, 'orderSensitiveModifiers', override.orderSensitiveModifiers);\n  mergeConfigProperties(baseConfig.theme, extend.theme);\n  mergeConfigProperties(baseConfig.classGroups, extend.classGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroupModifiers, extend.conflictingClassGroupModifiers);\n  mergeArrayProperties(baseConfig, extend, 'orderSensitiveModifiers');\n  return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue) => {\n  if (overrideValue !== undefined) {\n    baseObject[overrideKey] = overrideValue;\n  }\n};\nconst overrideConfigProperties = (baseObject, overrideObject) => {\n  if (overrideObject) {\n    for (const key in overrideObject) {\n      overrideProperty(baseObject, key, overrideObject[key]);\n    }\n  }\n};\nconst mergeConfigProperties = (baseObject, mergeObject) => {\n  if (mergeObject) {\n    for (const key in mergeObject) {\n      mergeArrayProperties(baseObject, mergeObject, key);\n    }\n  }\n};\nconst mergeArrayProperties = (baseObject, mergeObject, key) => {\n  const mergeValue = mergeObject[key];\n  if (mergeValue !== undefined) {\n    baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue;\n  }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig) => typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(() => mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\nexport { createTailwindMerge, extendTailwindMerge, fromTheme, getDefaultConfig, mergeConfigs, twJoin, twMerge, validators };\n//# sourceMappingURL=bundle-mjs.mjs.map\n", "import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../utils/cn\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "import * as React from \"react\"\n\nimport { cn } from \"../../utils/cn\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n", "const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n", "import React from 'react';\nimport { createStore } from 'zustand/vanilla';\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = React.useSyncExternalStore(\n    api.subscribe,\n    () => selector(api.getState()),\n    () => selector(api.getInitialState())\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = createStore(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\n\nexport { create, useStore };\n", "const reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...args) => api.dispatch(...args), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst removeStoreFromTrackedConnections = (name, store) => {\n  if (store === void 0) return;\n  const connectionInfo = trackedConnections.get(name);\n  if (!connectionInfo) return;\n  delete connectionInfo.stores[store];\n  if (Object.keys(connectionInfo.stores).length === 0) {\n    trackedConnections.delete(name);\n  }\n};\nconst findCallerName = (stack) => {\n  var _a, _b;\n  if (!stack) return void 0;\n  const traceLines = stack.split(\"\\n\");\n  const apiSetStateLineIndex = traceLines.findIndex(\n    (traceLine) => traceLine.includes(\"api.setState\")\n  );\n  if (apiSetStateLineIndex < 0) return void 0;\n  const callerLine = ((_a = traceLines[apiSetStateLineIndex + 1]) == null ? void 0 : _a.trim()) || \"\";\n  return (_b = /.+ (.+) .+/.exec(callerLine)) == null ? void 0 : _b[1];\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extensionConnector) {\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const inferredActionType = findCallerName(new Error().stack);\n    const action = nameOrAction === void 0 ? { type: anonymousActionType || inferredActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  api.devtools = {\n    cleanup: () => {\n      if (connection && typeof connection.unsubscribe === \"function\") {\n        connection.unsubscribe();\n      }\n      removeStoreFromTrackedConnections(options.name, store);\n    }\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...args) => {\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && args[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...args);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, fn) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) fn(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nfunction combine(initialState, create) {\n  return (...args) => Object.assign({}, initialState, create(...args));\n}\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(name, JSON.stringify(newValue, options == null ? void 0 : options.replacer)),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persistImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            const migration = options.migrate(\n              deserializedStorageValue.state,\n              deserializedStorageValue.version\n            );\n            if (migration instanceof Promise) {\n              return migration.then((result) => [true, result]);\n            }\n            return [true, migration];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persist = persistImpl;\n\nexport { combine, createJSONStorage, devtools, persist, redux, subscribeWithSelector };\n", "import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport type { User, LoginCredentials } from '../types';\n\ninterface AuthState {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (credentials: LoginCredentials) => Promise<void>;\n  logout: () => void;\n  updateProfile: (data: Partial<User>) => Promise<void>;\n  setUser: (user: User | null) => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n\n      login: async (credentials: LoginCredentials) => {\n        set({ isLoading: true });\n        try {\n          // TODO: Implement actual API call\n          console.log('Login with:', credentials);\n          \n          // Mock user data\n          const mockUser: User = {\n            id: '1',\n            username: 'testuser',\n            email: credentials.email,\n            role: 'reader',\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n          };\n\n          set({ \n            user: mockUser, \n            isAuthenticated: true, \n            isLoading: false \n          });\n        } catch (error) {\n          console.error('Login failed:', error);\n          set({ isLoading: false });\n          throw error;\n        }\n      },\n\n      logout: () => {\n        set({ \n          user: null, \n          isAuthenticated: false \n        });\n      },\n\n      updateProfile: async (data: Partial<User>) => {\n        const { user } = get();\n        if (!user) return;\n\n        set({ isLoading: true });\n        try {\n          // TODO: Implement actual API call\n          const updatedUser = { ...user, ...data };\n          set({ \n            user: updatedUser, \n            isLoading: false \n          });\n        } catch (error) {\n          console.error('Profile update failed:', error);\n          set({ isLoading: false });\n          throw error;\n        }\n      },\n\n      setUser: (user: User | null) => {\n        set({ \n          user, \n          isAuthenticated: !!user \n        });\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({ \n        user: state.user, \n        isAuthenticated: state.isAuthenticated \n      }),\n    }\n  )\n);\n", "import React from 'react';\nimport { <PERSON> } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport { Search, User, Menu, BookOpen } from 'lucide-react';\nimport { Button } from '../ui/button';\nimport { Input } from '../ui/input';\nimport { useAuthStore } from '../../stores/authStore';\n\nexport const Header: React.FC = () => {\n  const { t } = useTranslation('common');\n  const { user, isAuthenticated, logout } = useAuthStore();\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-16 items-center\">\n        {/* Logo */}\n        <Link to=\"/\" className=\"flex items-center space-x-2\">\n          <BookOpen className=\"h-6 w-6\" />\n          <span className=\"font-bold text-xl\">BlogTruyen</span>\n        </Link>\n\n        {/* Navigation */}\n        <nav className=\"hidden md:flex items-center space-x-6 ml-8\">\n          <Link \n            to=\"/\" \n            className=\"text-sm font-medium transition-colors hover:text-primary\"\n          >\n            {t('navigation.home')}\n          </Link>\n          <Link \n            to=\"/browse\" \n            className=\"text-sm font-medium transition-colors hover:text-primary\"\n          >\n            {t('navigation.browse')}\n          </Link>\n          {isAuthenticated && (\n            <>\n              <Link \n                to=\"/bookmarks\" \n                className=\"text-sm font-medium transition-colors hover:text-primary\"\n              >\n                {t('navigation.bookmarks')}\n              </Link>\n              <Link \n                to=\"/history\" \n                className=\"text-sm font-medium transition-colors hover:text-primary\"\n              >\n                {t('navigation.history')}\n              </Link>\n            </>\n          )}\n        </nav>\n\n        {/* Search */}\n        <div className=\"flex-1 flex justify-center px-4\">\n          <div className=\"relative w-full max-w-sm\">\n            <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder={t('actions.search')}\n              className=\"pl-8\"\n            />\n          </div>\n        </div>\n\n        {/* User Actions */}\n        <div className=\"flex items-center space-x-2\">\n          {isAuthenticated ? (\n            <div className=\"flex items-center space-x-2\">\n              <Button variant=\"ghost\" size=\"sm\">\n                <User className=\"h-4 w-4 mr-2\" />\n                {user?.username}\n              </Button>\n              <Button variant=\"ghost\" size=\"sm\" onClick={logout}>\n                {t('navigation.logout')}\n              </Button>\n            </div>\n          ) : (\n            <div className=\"flex items-center space-x-2\">\n              <Link to=\"/login\">\n                <Button variant=\"ghost\" size=\"sm\">\n                  {t('navigation.login')}\n                </Button>\n              </Link>\n              <Link to=\"/register\">\n                <Button size=\"sm\">\n                  {t('navigation.register')}\n                </Button>\n              </Link>\n            </div>\n          )}\n          \n          {/* Mobile menu */}\n          <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\n            <Menu className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n    </header>\n  );\n};\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { BookOpen, Github, Twitter, Mail } from 'lucide-react';\n\nexport const Footer: React.FC = () => {\n  const { t } = useTranslation('common');\n\n  return (\n    <footer className=\"border-t bg-background\">\n      <div className=\"container py-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Logo & Description */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <BookOpen className=\"h-6 w-6\" />\n              <span className=\"font-bold text-xl\">BlogTruyen</span>\n            </div>\n            <p className=\"text-sm text-muted-foreground\">\n              Nền tảng đọc truyện tranh online hiện đại với trải nghiệm đọc tuyệt vời.\n            </p>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-semibold\"><PERSON><PERSON><PERSON> k<PERSON><PERSON> n<PERSON>h</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <a href=\"/\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  {t('navigation.home')}\n                </a>\n              </li>\n              <li>\n                <a href=\"/browse\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  {t('navigation.browse')}\n                </a>\n              </li>\n              <li>\n                <a href=\"/about\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Về chúng tôi\n                </a>\n              </li>\n              <li>\n                <a href=\"/contact\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Liên hệ\n                </a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-semibold\">Hỗ trợ</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <a href=\"/help\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Trợ giúp\n                </a>\n              </li>\n              <li>\n                <a href=\"/faq\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  FAQ\n                </a>\n              </li>\n              <li>\n                <a href=\"/privacy\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Chính sách bảo mật\n                </a>\n              </li>\n              <li>\n                <a href=\"/terms\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Điều khoản sử dụng\n                </a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Social Links */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-semibold\">Kết nối</h3>\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                <Github className=\"h-5 w-5\" />\n              </a>\n              <a href=\"#\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                <Twitter className=\"h-5 w-5\" />\n              </a>\n              <a href=\"#\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                <Mail className=\"h-5 w-5\" />\n              </a>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t mt-8 pt-8 text-center text-sm text-muted-foreground\">\n          <p>&copy; 2024 BlogTruyen. Tất cả quyền được bảo lưu.</p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n", "import React from 'react';\nimport { Outlet } from 'react-router-dom';\nimport { Header } from './Header';\nimport { Footer } from './Footer';\n\nexport const Layout: React.FC = () => {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header />\n      <main className=\"flex-1\">\n        <Outlet />\n      </main>\n      <Footer />\n    </div>\n  );\n};\n", "import * as React from \"react\"\n\nimport { cn } from \"../../utils/cn\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n", "import React from \"react\";\n// import { useTranslation } from \"react-i18next\";\nimport { <PERSON> } from \"react-router-dom\";\nimport { Button } from \"../components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"../components/ui/card\";\nimport { BookO<PERSON>, TrendingUp, Clock, Star } from \"lucide-react\";\n\nexport const HomePage: React.FC = () => {\n  // const { t } = useTranslation(['common', 'manga']);\n\n  return (\n    <div className=\"container py-8\">\n      {/* Hero Section */}\n      <section className=\"text-center py-12\">\n        <h1 className=\"text-4xl font-bold tracking-tight lg:text-6xl mb-6\">\n          Chào mừng đến với BlogTruyen\n        </h1>\n        <p className=\"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto\">\n          <PERSON>h<PERSON><PERSON> ph<PERSON> thế giới truyện tranh với hàng ngàn bộ manga chất lượng cao.\n          <PERSON><PERSON><PERSON>h<PERSON>, kh<PERSON><PERSON> qu<PERSON> c<PERSON>o, tr<PERSON><PERSON> nghi<PERSON> tuy<PERSON> vời.\n        </p>\n        <div className=\"flex gap-4 justify-center\">\n          <Link to=\"/browse\">\n            <Button size=\"lg\">\n              <BookOpen className=\"mr-2 h-4 w-4\" />\n              Bắt đầu đọc\n            </Button>\n          </Link>\n          <Link to=\"/register\">\n            <Button variant=\"outline\" size=\"lg\">\n              Đăng ký miễn phí\n            </Button>\n          </Link>\n        </div>\n      </section>\n\n      {/* Features */}\n      <section className=\"py-12\">\n        <h2 className=\"text-3xl font-bold text-center mb-8\">\n          Tính năng nổi bật\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card>\n            <CardHeader className=\"text-center\">\n              <TrendingUp className=\"h-8 w-8 mx-auto mb-2 text-primary\" />\n              <CardTitle className=\"text-lg\">Xu hướng</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"text-center\">\n                Theo dõi những bộ truyện hot nhất, được cập nhật liên tục\n              </CardDescription>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"text-center\">\n              <Clock className=\"h-8 w-8 mx-auto mb-2 text-primary\" />\n              <CardTitle className=\"text-lg\">Lịch sử đọc</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"text-center\">\n                Lưu lại tiến trình đọc, tiếp tục từ nơi bạn đã dừng lại\n              </CardDescription>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"text-center\">\n              <Star className=\"h-8 w-8 mx-auto mb-2 text-primary\" />\n              <CardTitle className=\"text-lg\">Đánh giá</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"text-center\">\n                Đánh giá và bình luận về những bộ truyện yêu thích\n              </CardDescription>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"text-center\">\n              <BookOpen className=\"h-8 w-8 mx-auto mb-2 text-primary\" />\n              <CardTitle className=\"text-lg\">Đa nền tảng</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"text-center\">\n                Đọc mọi lúc mọi nơi trên điện thoại, máy tính bảng, laptop\n              </CardDescription>\n            </CardContent>\n          </Card>\n        </div>\n      </section>\n\n      {/* Popular Manga Section */}\n      <section className=\"py-12\">\n        <div className=\"flex justify-between items-center mb-8\">\n          <h2 className=\"text-3xl font-bold\">Truyện phổ biến</h2>\n          <Link to=\"/browse\">\n            <Button variant=\"outline\">Xem tất cả</Button>\n          </Link>\n        </div>\n\n        <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\">\n          {/* Mock manga cards */}\n          {Array.from({ length: 6 }).map((_, index) => (\n            <Card\n              key={index}\n              className=\"overflow-hidden hover:shadow-lg transition-shadow\"\n            >\n              <div className=\"aspect-[3/4] bg-muted\"></div>\n              <CardContent className=\"p-3\">\n                <h3 className=\"font-semibold text-sm truncate\">\n                  Tên truyện {index + 1}\n                </h3>\n                <p className=\"text-xs text-muted-foreground\">Tác giả</p>\n                <div className=\"flex items-center mt-1\">\n                  <Star className=\"h-3 w-3 fill-yellow-400 text-yellow-400\" />\n                  <span className=\"text-xs ml-1\">4.5</span>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </section>\n\n      {/* Latest Updates */}\n      <section className=\"py-12\">\n        <div className=\"flex justify-between items-center mb-8\">\n          <h2 className=\"text-3xl font-bold\">Cập nhật mới nhất</h2>\n          <Link to=\"/browse?sort=updated\">\n            <Button variant=\"outline\">Xem tất cả</Button>\n          </Link>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {Array.from({ length: 6 }).map((_, index) => (\n            <Card key={index} className=\"hover:shadow-lg transition-shadow\">\n              <CardContent className=\"p-4\">\n                <div className=\"flex space-x-4\">\n                  <div className=\"w-16 h-20 bg-muted rounded\"></div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"font-semibold mb-1\">\n                      Tên truyện {index + 1}\n                    </h3>\n                    <p className=\"text-sm text-muted-foreground mb-2\">\n                      Chapter 123\n                    </p>\n                    <p className=\"text-xs text-muted-foreground\">2 giờ trước</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </section>\n    </div>\n  );\n};\n", "\"use client\";\n\n// src/label.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Label\";\nvar Label = React.forwardRef((props, forwardedRef) => {\n  return /* @__PURE__ */ jsx(\n    Primitive.label,\n    {\n      ...props,\n      ref: forwardedRef,\n      onMouseDown: (event) => {\n        const target = event.target;\n        if (target.closest(\"button, input, select, textarea\")) return;\n        props.onMouseDown?.(event);\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }\n    }\n  );\n});\nLabel.displayName = NAME;\nvar Root = Label;\nexport {\n  Label,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../utils/cn\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n", "import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport { Button } from '../components/ui/button';\nimport { Input } from '../components/ui/input';\nimport { Label } from '../components/ui/label';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';\nimport { useAuthStore } from '../stores/authStore';\nimport { BookOpen } from 'lucide-react';\n\nexport const LoginPage: React.FC = () => {\n  const { t } = useTranslation('common');\n  const navigate = useNavigate();\n  const { login, isLoading } = useAuthStore();\n  \n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n  });\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.email) {\n      newErrors.email = t('forms.required');\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = t('forms.invalidEmail');\n    }\n\n    if (!formData.password) {\n      newErrors.password = t('forms.required');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    try {\n      await login(formData);\n      navigate('/');\n    } catch (error) {\n      console.error('Login failed:', error);\n      setErrors({ general: 'Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin.' });\n    }\n  };\n\n  return (\n    <div className=\"container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"text-center\">\n          <div className=\"flex justify-center mb-4\">\n            <BookOpen className=\"h-8 w-8 text-primary\" />\n          </div>\n          <CardTitle className=\"text-2xl\">{t('navigation.login')}</CardTitle>\n          <CardDescription>\n            Đăng nhập để truy cập tài khoản của bạn\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {errors.general && (\n              <div className=\"text-sm text-destructive text-center p-2 bg-destructive/10 rounded\">\n                {errors.general}\n              </div>\n            )}\n            \n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">{t('forms.email')}</Label>\n              <Input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                value={formData.email}\n                onChange={handleChange}\n                placeholder=\"<EMAIL>\"\n                className={errors.email ? 'border-destructive' : ''}\n              />\n              {errors.email && (\n                <p className=\"text-sm text-destructive\">{errors.email}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"password\">{t('forms.password')}</Label>\n              <Input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                value={formData.password}\n                onChange={handleChange}\n                className={errors.password ? 'border-destructive' : ''}\n              />\n              {errors.password && (\n                <p className=\"text-sm text-destructive\">{errors.password}</p>\n              )}\n            </div>\n\n            <Button \n              type=\"submit\" \n              className=\"w-full\" \n              disabled={isLoading}\n            >\n              {isLoading ? 'Đang đăng nhập...' : t('navigation.login')}\n            </Button>\n          </form>\n\n          <div className=\"mt-6 text-center text-sm\">\n            <p className=\"text-muted-foreground\">\n              Chưa có tài khoản?{' '}\n              <Link to=\"/register\" className=\"text-primary hover:underline\">\n                {t('navigation.register')}\n              </Link>\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nexport const BrowsePage: React.FC = () => {\n  const { t } = useTranslation(['common', 'manga']);\n\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">{t('navigation.browse')}</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          Trang duyệt truyện đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from 'react';\n\nexport const MangaDetailPage: React.FC = () => {\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">Chi tiết truyện</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          Trang chi tiết truyện đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from 'react';\n\nexport const ReaderPage: React.FC = () => {\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\"><PERSON><PERSON><PERSON> truy<PERSON>n</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          Trang đọc truyện đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nexport const RegisterPage: React.FC = () => {\n  const { t } = useTranslation('common');\n\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">{t('navigation.register')}</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          Trang đăng ký đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nexport const BookmarksPage: React.FC = () => {\n  const { t } = useTranslation('common');\n\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">{t('navigation.bookmarks')}</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          Trang đánh dấu đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nexport const HistoryPage: React.FC = () => {\n  const { t } = useTranslation('common');\n\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">{t('navigation.history')}</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          <PERSON><PERSON> lịch sử đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nexport const ProfilePage: React.FC = () => {\n  const { t } = useTranslation('common');\n\n  return (\n    <div className=\"container py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">{t('navigation.profile')}</h1>\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">\n          <PERSON><PERSON> hồ sơ đang được phát triển...\n        </p>\n      </div>\n    </div>\n  );\n};\n", "import React from \"react\";\nimport { createBrowserRouter, RouterProvider } from \"react-router-dom\";\nimport { Layout } from \"../components/layout/Layout\";\nimport { HomePage } from \"../pages/HomePage\";\nimport { LoginPage } from \"../pages/LoginPage\";\n\n// Import pages directly for now (can be lazy loaded later)\nimport {\n  BrowsePage,\n  MangaDetailPage,\n  ReaderPage,\n  RegisterPage,\n  BookmarksPage,\n  HistoryPage,\n  ProfilePage,\n} from \"../pages\";\n\nconst router = createBrowserRouter([\n  {\n    path: \"/\",\n    element: <Layout />,\n    children: [\n      {\n        index: true,\n        element: <HomePage />,\n      },\n      {\n        path: \"login\",\n        element: <LoginPage />,\n      },\n      {\n        path: \"register\",\n        element: <RegisterPage />,\n      },\n      {\n        path: \"browse\",\n        element: <BrowsePage />,\n      },\n      {\n        path: \"manga/:id\",\n        element: <MangaDetailPage />,\n      },\n      {\n        path: \"read/:chapterId\",\n        element: <ReaderPage />,\n      },\n      {\n        path: \"bookmarks\",\n        element: <BookmarksPage />,\n      },\n      {\n        path: \"history\",\n        element: <HistoryPage />,\n      },\n      {\n        path: \"profile\",\n        element: <ProfilePage />,\n      },\n    ],\n  },\n]);\n\nexport const AppRouter: React.FC = () => {\n  return <RouterProvider router={router} />;\n};\n", "const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last?.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  if (!Object.prototype.hasOwnProperty.call(obj, k)) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = (obj, path, keySeparator = '.') => {\n  if (!obj) return undefined;\n  if (obj[path]) {\n    if (!Object.prototype.hasOwnProperty.call(obj, path)) return undefined;\n    return obj[path];\n  }\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code?.replace('_', '-');\n\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    console?.[type]?.apply?.(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger, options = {}) {\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger, options = {}) {\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log(...args) {\n    return this.forward(args, 'log', '', true);\n  }\n  warn(...args) {\n    return this.forward(args, 'warn', '', true);\n  }\n  error(...args) {\n    return this.forward(args, 'error', '');\n  }\n  deprecate(...args) {\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\n\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event, ...args) {\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\n\nclass ResourceStore extends EventEmitter {\n  constructor(data, options = {\n    ns: ['translation'],\n    defaultNS: 'translation'\n  }) {\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key, options = {}) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data?.[lng]?.[ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value, options = {\n    silent: false\n  }) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources, options = {\n    silent: false\n  }) {\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite, options = {\n    silent: false,\n    skipCopy: false\n  }) {\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\n\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      value = this.processors[processor]?.process(value, key, options, translator) ?? value;\n    });\n    return value;\n  }\n};\n\nconst checkedLoadedFor = {};\nconst shouldHandleAsObject = res => !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\nclass Translator extends EventEmitter {\n  constructor(services, options = {}) {\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key, o = {\n    interpolation: {}\n  }) {\n    const opt = {\n      ...o\n    };\n    if (key == null) return false;\n    const resolved = this.resolve(key, opt);\n    return resolved?.res !== undefined;\n  }\n  extractFromKey(key, opt) {\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    let namespaces = opt.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !opt.keySeparator && !this.options.userDefinedNsSeparator && !opt.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, o, lastKey) {\n    let opt = typeof o === 'object' ? {\n      ...o\n    } : o;\n    if (typeof opt !== 'object' && this.options.overloadTranslationOptionHandler) {\n      opt = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') opt = {\n      ...opt\n    };\n    if (!opt) opt = {};\n    if (keys == null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = opt.returnDetails !== undefined ? opt.returnDetails : this.options.returnDetails;\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], opt);\n    const namespace = namespaces[namespaces.length - 1];\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const lng = opt.lng || this.language;\n    const appendNamespaceToCIMode = opt.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng?.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(opt)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(opt)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, opt);\n    let res = resolved?.res;\n    const resUsedKey = resolved?.usedKey || key;\n    const resExactUsedKey = resolved?.exactUsedKey || key;\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = opt.joinArrays !== undefined ? opt.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n    const hasDefaultValue = Translator.hasDefaultValue(opt);\n    const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, opt) : '';\n    const defaultValueSuffixOrdinalFallback = opt.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, {\n      ordinal: false\n    }) : '';\n    const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n    const defaultValue = needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] || opt[`defaultValue${defaultValueSuffix}`] || opt[`defaultValue${defaultValueSuffixOrdinalFallback}`] || opt.defaultValue;\n    let resForObjHndl = res;\n    if (handleAsObjectInI18nFormat && !res && hasDefaultValue) {\n      resForObjHndl = defaultValue;\n    }\n    const handleAsObject = shouldHandleAsObject(resForObjHndl);\n    const resType = Object.prototype.toString.apply(resForObjHndl);\n    if (handleAsObjectInI18nFormat && resForObjHndl && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(resForObjHndl))) {\n      if (!opt.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, resForObjHndl, {\n          ...opt,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(opt);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(resForObjHndl);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in resForObjHndl) {\n          if (Object.prototype.hasOwnProperty.call(resForObjHndl, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            if (hasDefaultValue && !res) {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                defaultValue: shouldHandleAsObject(defaultValue) ? defaultValue[m] : undefined,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            } else {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            }\n            if (copy[m] === deepKey) copy[m] = resForObjHndl[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, opt, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = opt.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...opt,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, opt.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(opt.lng || this.language);\n        } else {\n          lngs.push(opt.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, opt);\n          } else if (this.backendConnector?.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, opt);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, opt);\n              if (needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, opt[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, opt, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) {\n        res = `${namespace}${nsSeparator}${key}`;\n      }\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}${nsSeparator}${key}` : key, usedDefault ? res : undefined, opt);\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(opt);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, opt, resolved, lastKey) {\n    if (this.i18nFormat?.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...opt\n      }, opt.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!opt.skipInterpolation) {\n      if (opt.interpolation) this.interpolator.init({\n        ...opt,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...opt.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (opt?.interpolation?.skipOnVariables !== undefined ? opt.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = opt.replace && !isString(opt.replace) ? opt.replace : opt;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, opt.lng || this.language || resolved.usedLng, opt);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) opt.nest = false;\n      }\n      if (!opt.lng && resolved && resolved.res) opt.lng = this.language || resolved.usedLng;\n      if (opt.nest !== false) res = this.interpolator.nest(res, (...args) => {\n        if (lastKey?.[0] === args[0] && !opt.context) {\n          this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return this.translate(...args, key);\n      }, opt);\n      if (opt.interpolation) this.interpolator.reset();\n    }\n    const postProcess = opt.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res != null && postProcessorNames?.length && opt.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(opt)\n        },\n        ...opt\n      } : opt, this);\n    }\n    return res;\n  }\n  resolve(keys, opt = {}) {\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, opt);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n      const needsContextHandling = opt.context !== undefined && (isString(opt.context) || typeof opt.context === 'number') && opt.context !== '';\n      const codes = opt.lngs ? opt.lngs : this.languageUtils.toResolveHierarchy(opt.lng || this.language, opt.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils?.hasLoadedNamespace && !this.utils?.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat?.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, opt);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, opt.count, opt);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${opt.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, opt);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key, options = {}) {\n    if (this.i18nFormat?.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails(options = {}) {\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      let formattedCode;\n      try {\n        formattedCode = Intl.getCanonicalLocales(code)[0];\n      } catch (e) {}\n      if (formattedCode && this.options.lowerCaseLng) {\n        formattedCode = formattedCode.toLowerCase();\n      }\n      if (formattedCode) return formattedCode;\n      if (this.options.lowerCaseLng) {\n        return code.toLowerCase();\n      }\n      return code;\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngScOnly = this.getScriptPartFromCode(code);\n        if (this.isSupportedCode(lngScOnly)) return found = lngScOnly;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes((fallbackCode === false ? [] : fallbackCode) || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\n\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst dummyRule = {\n  select: count => count === 1 ? 'one' : 'other',\n  resolvedOptions: () => ({\n    pluralCategories: ['one', 'other']\n  })\n};\nclass PluralResolver {\n  constructor(languageUtils, options = {}) {\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code, options = {}) {\n    const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n    const type = options.ordinal ? 'ordinal' : 'cardinal';\n    const cacheKey = JSON.stringify({\n      cleanedCode,\n      type\n    });\n    if (cacheKey in this.pluralRulesCache) {\n      return this.pluralRulesCache[cacheKey];\n    }\n    let rule;\n    try {\n      rule = new Intl.PluralRules(cleanedCode, {\n        type\n      });\n    } catch (err) {\n      if (!Intl) {\n        this.logger.error('No Intl support, please use an Intl polyfill!');\n        return dummyRule;\n      }\n      if (!code.match(/-|_/)) return dummyRule;\n      const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n      rule = this.getRule(lngPart, options);\n    }\n    this.pluralRulesCache[cacheKey] = rule;\n    return rule;\n  }\n  needsPlural(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    return rule?.resolvedOptions().pluralCategories.length > 1;\n  }\n  getPluralFormsOfKey(code, key, options = {}) {\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    if (!rule) return [];\n    return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n  }\n  getSuffix(code, count, options = {}) {\n    const rule = this.getRule(code, options);\n    if (rule) {\n      return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return this.getSuffix('dev', count, options);\n  }\n}\n\nconst deepFindWithDefaults = (data, defaultData, key, keySeparator = '.', ignoreJSONStructure = true) => {\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options?.interpolation?.format || (value => value);\n    this.init(options);\n  }\n  init(options = {}) {\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp?.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options?.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options?.interpolation?.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc, options = {}) {\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if ((matchedSingleQuotes?.length ?? 0) % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      let doReduce = false;\n      if (match[0].indexOf(this.formatSeparator) !== -1 && !/{.*}/.test(match[1])) {\n        const r = match[1].split(this.formatSeparator).map(elem => elem.trim());\n        match[1] = r.shift();\n        formatters = r;\n        doReduce = true;\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (doReduce) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\n\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (v, l, o) => {\n    let optForCache = o;\n    if (o && o.interpolationkey && o.formatParams && o.formatParams[o.interpolationkey] && o[o.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [o.interpolationkey]: undefined\n      };\n    }\n    const key = l + JSON.stringify(optForCache);\n    let frm = cache[key];\n    if (!frm) {\n      frm = fn(getCleanedCode(l), o);\n      cache[key] = frm;\n    }\n    return frm(v);\n  };\n};\nconst createNonCachedFormatter = fn => (v, l, o) => fn(getCleanedCode(l), o)(v);\nclass Formatter {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.init(options);\n  }\n  init(services, options = {\n    interpolation: {}\n  }) {\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n    const cf = options.cacheInBuiltFormats ? createCachedFormatter : createNonCachedFormatter;\n    this.formats = {\n      number: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: cf((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: cf((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: cf((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng, options = {}) {\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options?.formatParams?.[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\n\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services, options = {}) {\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    this.backend?.init?.(services, options.backend, options);\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ; else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName, tried = 0, wait = this.retryTimeout, callback) {\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces, options = {}, callback) {\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name, prefix = '') {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate, options = {}, clb = () => {}) {\n    if (this.services?.utils?.hasLoadedNamespace && !this.services?.utils?.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend?.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\n\nconst get = () => ({\n  debug: false,\n  initAsync: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  },\n  cacheInBuiltFormats: true\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs?.indexOf?.('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  if (typeof options.initImmediate === 'boolean') options.initAsync = options.initImmediate;\n  return options;\n};\n\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor(options = {}, callback) {\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initAsync) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init(options = {}, callback) {\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (options.defaultNS == null && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    this.options.interpolation = {\n      ...defOpts.interpolation,\n      ...this.options.interpolation\n    };\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = (...args) => this.store[fcName](...args);\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = (...args) => {\n        this.store[fcName](...args);\n        return this;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initAsync) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language, callback = noop) {\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng?.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      this.options.preload?.forEach?.(l => append(l));\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n    if (!this.resolvedLanguage && this.languages.indexOf(l) < 0 && this.store.hasLanguageSomeTranslations(l)) {\n      this.resolvedLanguage = l;\n      this.languages.unshift(l);\n    }\n  }\n  changeLanguage(lng, callback) {\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        if (this.isLanguageChangingTo === lng) {\n          setLngProps(l);\n          this.translator.changeLanguage(l);\n          this.isLanguageChangingTo = undefined;\n          this.emit('languageChanged', l);\n          this.logger.log('languageChanged', l);\n        }\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve((...args) => this.t(...args));\n      if (callback) callback(err, (...args) => this.t(...args));\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const fl = isString(lngs) ? lngs : lngs && lngs[0];\n      const l = this.store.hasLanguageSomeTranslations(fl) ? fl : this.services.languageUtils.getBestMatchFromCodes(isString(lngs) ? [lngs] : lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        this.services.languageDetector?.cacheUserLanguage?.(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    const fixedT = (key, opts, ...rest) => {\n      let o;\n      if (typeof opts !== 'object') {\n        o = this.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        o = {\n          ...opts\n        };\n      }\n      o.lng = o.lng || fixedT.lng;\n      o.lngs = o.lngs || fixedT.lngs;\n      o.ns = o.ns || fixedT.ns;\n      if (o.keyPrefix !== '') o.keyPrefix = o.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = this.options.keySeparator || '.';\n      let resultKey;\n      if (o.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${o.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = o.keyPrefix ? `${o.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return this.t(resultKey, o);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t(...args) {\n    return this.translator?.translate(...args);\n  }\n  exists(...args) {\n    return this.translator?.exists(...args);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns, options = {}) {\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages?.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services?.languageUtils || new LanguageUtil(get());\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance(options = {}, callback) {\n    return new I18n(options, callback);\n  }\n  cloneInstance(options = {}, callback = noop) {\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      const clonedData = Object.keys(this.store.data).reduce((prev, l) => {\n        prev[l] = {\n          ...this.store.data[l]\n        };\n        prev[l] = Object.keys(prev[l]).reduce((acc, n) => {\n          acc[n] = {\n            ...prev[l][n]\n          };\n          return acc;\n        }, prev[l]);\n        return prev;\n      }, {});\n      clone.store = new ResourceStore(clonedData, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', (event, ...args) => {\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\n\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\n\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };\n", "const {\n  slice,\n  forEach\n} = [];\nfunction defaults(obj) {\n  forEach.call(slice.call(arguments, 1), source => {\n    if (source) {\n      for (const prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nfunction hasXSS(input) {\n  if (typeof input !== 'string') return false;\n\n  // Common XSS attack patterns\n  const xssPatterns = [/<\\s*script.*?>/i, /<\\s*\\/\\s*script\\s*>/i, /<\\s*img.*?on\\w+\\s*=/i, /<\\s*\\w+\\s*on\\w+\\s*=.*?>/i, /javascript\\s*:/i, /vbscript\\s*:/i, /expression\\s*\\(/i, /eval\\s*\\(/i, /alert\\s*\\(/i, /document\\.cookie/i, /document\\.write\\s*\\(/i, /window\\.location/i, /innerHTML/i];\n  return xssPatterns.some(pattern => pattern.test(input));\n}\n\n// eslint-disable-next-line no-control-regex\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nconst serializeCookie = function (name, val) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    path: '/'\n  };\n  const opt = options;\n  const value = encodeURIComponent(val);\n  let str = `${name}=${value}`;\n  if (opt.maxAge > 0) {\n    const maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += `; Max-Age=${Math.floor(maxAge)}`;\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    const sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  if (opt.partitioned) str += '; Partitioned';\n  return str;\n};\nconst cookie = {\n  create(name, value, minutes, domain) {\n    let cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, encodeURIComponent(value), cookieOptions);\n  },\n  read(name) {\n    const nameEQ = `${name}=`;\n    const ca = document.cookie.split(';');\n    for (let i = 0; i < ca.length; i++) {\n      let c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove(name) {\n    this.create(name, '', -1);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  // Deconstruct the options object and extract the lookupCookie property\n  lookup(_ref) {\n    let {\n      lookupCookie\n    } = _ref;\n    if (lookupCookie && typeof document !== 'undefined') {\n      return cookie.read(lookupCookie) || undefined;\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupCookie, cookieMinutes, cookieDomain, and cookieOptions properties\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupCookie,\n      cookieMinutes,\n      cookieDomain,\n      cookieOptions\n    } = _ref2;\n    if (lookupCookie && typeof document !== 'undefined') {\n      cookie.create(lookupCookie, lng, cookieMinutes, cookieDomain, cookieOptions);\n    }\n  }\n};\n\nvar querystring = {\n  name: 'querystring',\n  // Deconstruct the options object and extract the lookupQuerystring property\n  lookup(_ref) {\n    let {\n      lookupQuerystring\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      let {\n        search\n      } = window.location;\n      if (!window.location.search && window.location.hash?.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      const query = search.substring(1);\n      const params = query.split('&');\n      for (let i = 0; i < params.length; i++) {\n        const pos = params[i].indexOf('=');\n        if (pos > 0) {\n          const key = params[i].substring(0, pos);\n          if (key === lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\n\nlet hasLocalStorageSupport = null;\nconst localStorageAvailable = () => {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = typeof window !== 'undefined' && window.localStorage !== null;\n    if (!hasLocalStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  lookup(_ref) {\n    let {\n      lookupLocalStorage\n    } = _ref;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      return window.localStorage.getItem(lookupLocalStorage) || undefined; // Undefined ensures type consistency with the previous version of this function\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupLocalStorage\n    } = _ref2;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(lookupLocalStorage, lng);\n    }\n  }\n};\n\nlet hasSessionStorageSupport = null;\nconst sessionStorageAvailable = () => {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = typeof window !== 'undefined' && window.sessionStorage !== null;\n    if (!hasSessionStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup(_ref) {\n    let {\n      lookupSessionStorage\n    } = _ref;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      return window.sessionStorage.getItem(lookupSessionStorage) || undefined;\n    }\n    return undefined;\n  },\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupSessionStorage\n    } = _ref2;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(lookupSessionStorage, lng);\n    }\n  }\n};\n\nvar navigator$1 = {\n  name: 'navigator',\n  lookup(options) {\n    const found = [];\n    if (typeof navigator !== 'undefined') {\n      const {\n        languages,\n        userLanguage,\n        language\n      } = navigator;\n      if (languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (let i = 0; i < languages.length; i++) {\n          found.push(languages[i]);\n        }\n      }\n      if (userLanguage) {\n        found.push(userLanguage);\n      }\n      if (language) {\n        found.push(language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\n\nvar htmlTag = {\n  name: 'htmlTag',\n  // Deconstruct the options object and extract the htmlTag property\n  lookup(_ref) {\n    let {\n      htmlTag\n    } = _ref;\n    let found;\n    const internalHtmlTag = htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (internalHtmlTag && typeof internalHtmlTag.getAttribute === 'function') {\n      found = internalHtmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\n\nvar path = {\n  name: 'path',\n  // Deconstruct the options object and extract the lookupFromPathIndex property\n  lookup(_ref) {\n    let {\n      lookupFromPathIndex\n    } = _ref;\n    if (typeof window === 'undefined') return undefined;\n    const language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n    if (!Array.isArray(language)) return undefined;\n    const index = typeof lookupFromPathIndex === 'number' ? lookupFromPathIndex : 0;\n    return language[index]?.replace('/', '');\n  }\n};\n\nvar subdomain = {\n  name: 'subdomain',\n  lookup(_ref) {\n    let {\n      lookupFromSubdomainIndex\n    } = _ref;\n    // If given get the subdomain index else 1\n    const internalLookupFromSubdomainIndex = typeof lookupFromSubdomainIndex === 'number' ? lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group match which should be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    const language = typeof window !== 'undefined' && window.location?.hostname?.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[internalLookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nlet canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nconst order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nconst getDefaults = () => ({\n  order,\n  lookupQuerystring: 'lng',\n  lookupCookie: 'i18next',\n  lookupLocalStorage: 'i18nextLng',\n  lookupSessionStorage: 'i18nextLng',\n  // cache user language\n  caches: ['localStorage'],\n  excludeCacheFor: ['cimode'],\n  // cookieMinutes: 10,\n  // cookieDomain: 'myDomain'\n\n  convertDetectedLanguage: l => l\n});\nclass Browser {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  init() {\n    let services = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      languageUtils: {}\n    };\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.services = services;\n    this.options = defaults(options, this.options || {}, getDefaults());\n    if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n      this.options.convertDetectedLanguage = l => l.replace('-', '_');\n    }\n\n    // backwards compatibility\n    if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n    this.i18nOptions = i18nOptions;\n    this.addDetector(cookie$1);\n    this.addDetector(querystring);\n    this.addDetector(localStorage);\n    this.addDetector(sessionStorage);\n    this.addDetector(navigator$1);\n    this.addDetector(htmlTag);\n    this.addDetector(path);\n    this.addDetector(subdomain);\n  }\n  addDetector(detector) {\n    this.detectors[detector.name] = detector;\n    return this;\n  }\n  detect() {\n    let detectionOrder = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.options.order;\n    let detected = [];\n    detectionOrder.forEach(detectorName => {\n      if (this.detectors[detectorName]) {\n        let lookup = this.detectors[detectorName].lookup(this.options);\n        if (lookup && typeof lookup === 'string') lookup = [lookup];\n        if (lookup) detected = detected.concat(lookup);\n      }\n    });\n    detected = detected.filter(d => d !== undefined && d !== null && !hasXSS(d)).map(d => this.options.convertDetectedLanguage(d));\n    if (this.services && this.services.languageUtils && this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n    return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n  }\n  cacheUserLanguage(lng) {\n    let caches = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.options.caches;\n    if (!caches) return;\n    if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n    caches.forEach(cacheName => {\n      if (this.detectors[cacheName]) this.detectors[cacheName].cacheUserLanguage(lng, this.options);\n    });\n  }\n}\nBrowser.type = 'languageDetector';\n\nexport { Browser as default };\n", "import i18n from 'i18next';\nimport { initReactI18next } from 'react-i18next';\nimport LanguageDetector from 'i18next-browser-languagedetector';\n\n// Import translation files\nimport enCommon from './locales/en/common.json';\nimport enManga from './locales/en/manga.json';\nimport enReader from './locales/en/reader.json';\nimport viCommon from './locales/vi/common.json';\nimport viManga from './locales/vi/manga.json';\nimport viReader from './locales/vi/reader.json';\n\nconst resources = {\n  en: {\n    common: enCommon,\n    manga: enManga,\n    reader: enReader,\n  },\n  vi: {\n    common: viCommon,\n    manga: viManga,\n    reader: viReader,\n  },\n};\n\ni18n\n  .use(LanguageDetector)\n  .use(initReactI18next)\n  .init({\n    resources,\n    fallbackLng: 'en',\n    debug: import.meta.env.DEV,\n    \n    // Namespace configuration\n    defaultNS: 'common',\n    ns: ['common', 'manga', 'reader'],\n    \n    interpolation: {\n      escapeValue: false, // React already escapes values\n    },\n    \n    detection: {\n      order: ['localStorage', 'navigator', 'htmlTag'],\n      caches: ['localStorage'],\n    },\n  });\n\nexport default i18n;\n", "import { QueryClientProvider } from \"@tanstack/react-query\";\nimport { ReactQueryDevtools } from \"@tanstack/react-query-devtools\";\nimport { queryClient } from \"./services/queryClient\";\nimport { AppRouter } from \"./router\";\nimport \"./i18n\";\n\nfunction App() {\n  return (\n    <QueryClientProvider client={queryClient}>\n      <AppRouter />\n      {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}\n    </QueryClientProvider>\n  );\n}\n\nexport default App;\n", "import { StrictMode } from 'react'\nimport { createRoot } from 'react-dom/client'\nimport './index.css'\nimport App from './App.tsx'\n\ncreateRoot(document.getElementById('root')!).render(\n  <StrictMode>\n    <App />\n  </StrictMode>,\n)\n"], "names": ["m", "require$$0", "client", "queryClient", "QueryClient", "failureCount", "error", "warn", "i18n", "code", "msg", "rest", "args", "_b", "_a", "isString", "_d", "_c", "alreadyWarned", "warnOnce", "loadedClb", "cb", "initialized", "loadNamespaces", "ns", "loadLanguages", "lng", "hasLoadedNamespace", "options", "i18nInstance", "loadNotPending", "obj", "isObject", "matchHtmlEntity", "htmlEntities", "unescapeHtmlEntity", "unescape", "text", "defaultOptions", "setDefaults", "getDefaults", "setI18n", "instance", "getI18n", "initReactI18next", "I18nContext", "createContext", "ReportNamespaces", "namespaces", "usePrevious", "value", "ignore", "ref", "useRef", "useEffect", "alwaysNewT", "language", "namespace", "keyPrefix", "useMemoizedT", "useCallback", "useTranslation", "props", "i18nFromProps", "i18nFromContext", "defaultNSFromContext", "useContext", "notReadyT", "k", "optsOrDefaultValue", "retNotReady", "i18nOptions", "useSuspense", "ready", "n", "memoGetT", "getT", "getNewT", "t", "setT", "useState", "joinedNS", "previousJoinedNS", "isMounted", "bindI18n", "bindI18nStore", "boundReset", "e", "ret", "resolve", "toKebabCase", "string", "toCamelCase", "match", "p1", "p2", "toPascalCase", "camelCase", "mergeClasses", "classes", "className", "index", "array", "hasA11yProp", "prop", "defaultAttributes", "Icon", "forwardRef", "color", "size", "strokeWidth", "absoluteStrokeWidth", "children", "iconNode", "createElement", "tag", "attrs", "createLucideIcon", "iconName", "Component", "__iconNode", "BookOpen", "Clock", "<PERSON><PERSON><PERSON>", "Mail", "<PERSON><PERSON>", "Search", "Star", "TrendingUp", "Twitter", "User", "r", "f", "o", "clsx", "falsyToString", "cx", "cva", "base", "config", "_config_compoundVariants", "variants", "defaultVariants", "getVariantClassNames", "variant", "variantProp", "defaultVariantProp", "variant<PERSON><PERSON>", "propsWithoutUndefined", "acc", "param", "key", "getCompoundVariantClassNames", "cvClass", "cvClassName", "compoundVariantOptions", "CLASS_PART_SEPARATOR", "createClassGroupUtils", "classMap", "createClassMap", "conflictingClassGroups", "conflictingClassGroupModifiers", "classParts", "getGroupRecursive", "getGroupIdForArbitraryProperty", "classGroupId", "hasPostfixModifier", "conflicts", "classPartObject", "currentClassPart", "nextClassPartObject", "classGroupFromNextClassPart", "classRest", "validator", "arbitraryPropertyRegex", "arbitraryPropertyClassName", "property", "theme", "classGroups", "processClassesRecursively", "classGroup", "classDefinition", "classPartObjectToEdit", "get<PERSON>art", "isThemeGetter", "path", "currentClassPartObject", "pathPart", "func", "createLruCache", "maxCacheSize", "cacheSize", "cache", "previousCache", "update", "IMPORTANT_MODIFIER", "MODIFIER_SEPARATOR", "MODIFIER_SEPARATOR_LENGTH", "createParseClassName", "prefix", "experimentalParseClassName", "parseClassName", "modifiers", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modifierStart", "postfixModifierPosition", "currentCharacter", "baseClassNameWithImportantModifier", "baseClassName", "stripImportantModifier", "hasImportantModifier", "maybePostfixModifierPosition", "fullPrefix", "parseClassNameOriginal", "createSortModifiers", "orderSensitiveModifiers", "modifier", "sortedModifiers", "unsortedModifiers", "createConfigUtils", "SPLIT_CLASSES_REGEX", "mergeClassList", "classList", "configUtils", "getClassGroupId", "getConflictingClassGroupIds", "sortModifiers", "classGroupsInConflict", "classNames", "result", "originalClassName", "isExternal", "variantModifier", "modifierId", "classId", "conflictGroups", "i", "group", "twJoin", "argument", "resolvedValue", "toValue", "mix", "createTailwindMerge", "createConfigFirst", "createConfigRest", "cacheGet", "cacheSet", "functionToCall", "initTailwindMerge", "previousConfig", "createConfigCurrent", "tailwindMerge", "cachedResult", "fromTheme", "themeGetter", "arbitraryValueRegex", "arbitraryVariableRegex", "fractionRegex", "tshirtUnitRegex", "lengthUnitRegex", "colorFunctionRegex", "shadowRegex", "imageRegex", "isFraction", "isNumber", "isInteger", "isPercent", "isTshirtSize", "isAny", "is<PERSON>engthOnly", "isNever", "is<PERSON><PERSON>ow", "isImage", "isAnyNonArbitrary", "isArbitraryValue", "isArbitraryVariable", "isArbitrarySize", "getIsArbitraryValue", "isLabelSize", "isArbitraryLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isArbitraryNumber", "isLabelNumber", "isArbitraryPosition", "isLabelPosition", "isArbitraryImage", "isLabelImage", "isArbitraryShadow", "isLabel<PERSON><PERSON>ow", "isArbitraryVariableLength", "getIsArbitraryVariable", "isArbitraryVariableFamilyName", "isLabelFamilyName", "isArbitraryVariablePosition", "isArbitraryVariableSize", "isArbitraryVariableImage", "isArbitraryVariableShadow", "test<PERSON><PERSON><PERSON>", "testValue", "shouldMatchNoLabel", "label", "getDefaultConfig", "themeColor", "themeFont", "themeText", "themeFontWeight", "themeTracking", "themeLeading", "themeBreakpoint", "themeContainer", "themeSpacing", "themeRadius", "themeShadow", "themeInsetShadow", "themeTextShadow", "themeDropShadow", "themeBlur", "themePerspective", "themeAspect", "themeEase", "themeAnimate", "scaleBreak", "scalePosition", "scalePositionWithArbitrary", "scaleOverflow", "scaleOverscroll", "scaleUnambiguousSpacing", "scaleInset", "scaleGridTemplateColsRows", "scaleGridColRowStartAndEnd", "scaleGridColRowStartOrEnd", "scaleGridAutoColsRows", "scaleAlignPrimaryAxis", "scaleAlignSecondaryAxis", "scaleMargin", "scaleSizing", "scaleColor", "scaleBgPosition", "scaleBgRepeat", "scaleBgSize", "scaleGradientStopPosition", "scaleRadius", "scaleBorderWidth", "scaleLineStyle", "scaleBlendMode", "scaleMaskImagePosition", "scaleBlur", "scaleRotate", "scaleScale", "scaleSkew", "scaleTranslate", "twMerge", "cn", "inputs", "buttonVariants", "<PERSON><PERSON>", "React.forwardRef", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot", "jsx", "Input", "type", "createStoreImpl", "createState", "state", "listeners", "setState", "partial", "replace", "nextState", "previousState", "listener", "getState", "api", "initialState", "createStore", "identity", "arg", "useStore", "selector", "slice", "React", "createImpl", "useBoundStore", "create", "createJSONStorage", "getStorage", "storage", "name", "parse", "str2", "str", "newValue", "toThenable", "fn", "input", "onFulfilled", "_onRejected", "_onFulfilled", "onRejected", "persistImpl", "baseOptions", "set", "get", "persistedState", "currentState", "hasHydrated", "hydrationListeners", "finishHydrationListeners", "setItem", "savedSetState", "config<PERSON><PERSON><PERSON>", "stateFromStorage", "hydrate", "_a2", "postRehydrationCallback", "deserializedStorageValue", "migration", "migrationResult", "migrated", "migratedState", "newOptions", "persist", "useAuthStore", "credentials", "mockUser", "data", "user", "updatedUser", "Header", "isAuthenticated", "logout", "jsxs", "Link", "Fragment", "Footer", "Layout", "Outlet", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "HomePage", "_", "NAME", "Label", "forwardedRef", "Primitive", "event", "Root", "labelVariants", "LabelPrimitive.Root", "LoginPage", "navigate", "useNavigate", "login", "isLoading", "formData", "setFormData", "errors", "setErrors", "handleChange", "prev", "validateForm", "newErrors", "handleSubmit", "BrowsePage", "MangaDetailPage", "ReaderPage", "RegisterPage", "BookmarksPage", "HistoryPage", "ProfilePage", "router", "createBrowserRouter", "AppRouter", "RouterProvider", "defer", "res", "rej", "promise", "reject", "makeString", "object", "copy", "a", "s", "lastOfPathSeparatorRegExp", "<PERSON><PERSON><PERSON>", "canNotTraverseDeeper", "getLastOfPath", "Empty", "stack", "stackIndex", "set<PERSON>ath", "p", "last", "push<PERSON><PERSON>", "concat", "<PERSON><PERSON><PERSON>", "getPathWithDefaults", "defaultData", "deepExtend", "target", "source", "overwrite", "regexEscape", "_entityMap", "escape", "RegExpCache", "capacity", "pattern", "regExpFromCache", "regExpNew", "chars", "looksLikeObjectPathRegExpCache", "looksLikeObjectPath", "nsSeparator", "keySeparator", "possibleChars", "c", "matched", "ki", "deepFind", "tokens", "current", "next", "nextPath", "j", "getCleanedCode", "consoleLogger", "<PERSON><PERSON>", "concreteLogger", "lvl", "debugOnly", "moduleName", "baseLogger", "EventEmitter", "events", "numListeners", "observer", "numTimesAdded", "ResourceStore", "ignoreJSONStructure", "resources", "deep", "pack", "v", "postProcessor", "module", "processors", "translator", "processor", "checkedLoadedFor", "shouldHandleAsObject", "Translator", "services", "opt", "resolved", "wouldCheckForNsInKey", "seemsNaturalLanguage", "parts", "keys", "last<PERSON>ey", "returnDetails", "appendNamespaceToCIMode", "resUsed<PERSON><PERSON>", "resExactUsedKey", "noObject", "joinArrays", "handleAsObjectInI18nFormat", "needsPluralHandling", "hasDefaultValue", "defaultValueSuffix", "defaultValueSuffixOrdinalFallback", "needsZeroSuffixLookup", "defaultValue", "resForObjHndl", "handleAsObject", "resType", "resTypeIsArray", "newKeyToUse", "<PERSON><PERSON><PERSON>", "usedDefault", "usedKey", "resForMissing", "updateMissing", "fk", "lngs", "fallbackLngs", "send", "l", "specificDefaultValue", "defaultForMissing", "suffixes", "suffix", "skipOnVariables", "nestBef", "nb", "na", "nestAft", "postProcess", "postProcessorNames", "found", "exactUsed<PERSON>ey", "usedLng", "usedNS", "extracted", "needsContextHandling", "codes", "finalKeys", "pluralSuffix", "zeroSuffix", "ordinalPrefix", "<PERSON><PERSON>ey", "<PERSON><PERSON><PERSON>", "optionsKeys", "useOptionsReplaceForData", "option", "LanguageUtil", "formattedCode", "cleanedLng", "lngScOnly", "lngOnly", "supportedLng", "fallbacks", "fallbackCode", "fallbackCodes", "addCode", "fc", "suffixesOrder", "dummyRule", "count", "PluralResolver", "languageUtils", "cleanedCode", "cache<PERSON>ey", "rule", "lngPart", "pluralCategory1", "pluralCategory2", "pluralCategory", "deepFindWithDefaults", "regexSafe", "val", "Interpolator", "escape$1", "escapeValue", "useRawValueToEscape", "prefixEscaped", "suffixEscaped", "formatSeparator", "unescapeSuffix", "unescapePrefix", "nestingPrefix", "nestingPrefixEscaped", "nestingSuffix", "nestingSuffixEscaped", "nestingOptionsSeparator", "maxReplaces", "alwaysFormat", "getOrResetRegExp", "existingRegExp", "replaces", "handleFormat", "missingInterpolationHandler", "todo", "matchedVar", "temp", "safeValue", "clonedOptions", "handleHasOptions", "inheritedOptions", "sep", "optionsString", "matchedSingleQuotes", "matchedDoubleQuotes", "formatters", "doReduce", "elem", "parseFormatStr", "formatStr", "formatName", "formatOptions", "optStr", "<PERSON><PERSON><PERSON>", "createCachedFormatter", "optForCache", "frm", "createNonCachedFormatter", "<PERSON><PERSON><PERSON>", "cf", "formatter", "format", "formats", "lastIndex", "mem", "formatted", "valOptions", "removePending", "q", "Connector", "backend", "store", "languages", "callback", "toLoad", "pending", "toLoadLanguages", "toLoadNamespaces", "hasAllNamespaces", "err", "loaded", "loadedKeys", "fcName", "tried", "wait", "resolver", "fallback<PERSON><PERSON><PERSON>", "isUpdate", "clb", "_e", "opts", "transformOptions", "noop", "bindMemberFunctions", "inst", "I18n", "defOpts", "createClassOnDemand", "ClassOrObject", "lu", "deferred", "load", "finish", "usedCallback", "append", "li", "lngInLngs", "setLngProps", "done", "setLng", "fl", "fixedT", "<PERSON><PERSON><PERSON>", "fallbackLng", "lastLng", "loadState", "preResult", "preloaded", "newLngs", "rtlLngs", "forkResourceStore", "mergedOptions", "clone", "clonedData", "for<PERSON>ach", "defaults", "hasXSS", "fieldContentRegExp", "serializeCookie", "maxAge", "cookie", "minutes", "domain", "cookieOptions", "nameEQ", "ca", "cookie$1", "_ref", "lookup<PERSON><PERSON><PERSON>", "_ref2", "cookieMinutes", "cookieDomain", "querystring", "lookupQuerystring", "search", "params", "pos", "hasLocalStorageSupport", "localStorageAvailable", "<PERSON><PERSON><PERSON>", "localStorage", "lookupLocalStorage", "hasSessionStorageSupport", "sessionStorageAvailable", "sessionStorage", "lookupSessionStorage", "navigator$1", "userLanguage", "htmlTag", "internalHtmlTag", "lookupFromPathIndex", "subdomain", "lookupFromSubdomainIndex", "internalLookupFromSubdomainIndex", "canCookies", "order", "Browser", "detector", "detectionOrder", "detected", "detectorName", "lookup", "d", "caches", "cacheName", "enCommon", "enManga", "en<PERSON><PERSON><PERSON>", "vi<PERSON><PERSON><PERSON>", "viManga", "vi<PERSON><PERSON><PERSON>", "LanguageDetector", "App", "QueryClientProvider", "createRoot", "StrictMode"], "mappings": "89BAEA,IAAIA,EAAIC,GAAmB,EAEzB,OAAAC,cAAqBF,EAAE,WACvBE,eAAsBF,EAAE,2BCHb,MAAAG,GAAc,IAAIC,GAAY,CACzC,eAAgB,CACd,QAAS,CACP,UAAW,EAAI,GAAK,IACpB,OAAQ,GAAK,GAAK,IAClB,MAAO,CAACC,EAAcC,KAEhBA,GAAA,YAAAA,EAAO,SAAU,MAAOA,GAAA,YAAAA,EAAO,QAAS,IACnC,GAEFD,EAAe,EAExB,qBAAsB,EACxB,EACA,UAAW,CACT,MAAO,EAAA,CACT,CAEJ,CAAC,ECpBYE,GAAO,CAACC,EAAMC,EAAMC,EAAKC,IAAS,aAC7C,MAAMC,EAAO,CAACF,EAAK,CACjB,KAAAD,EACA,GAAIE,GAAQ,CAAE,CAClB,CAAG,EACD,IAAIE,GAAAC,EAAAN,GAAA,YAAAA,EAAM,WAAN,YAAAM,EAAgB,SAAhB,MAAAD,EAAwB,QAC1B,OAAOL,EAAK,SAAS,OAAO,QAAQI,EAAM,OAAQ,kBAAmB,EAAI,EAEvEG,GAASH,EAAK,CAAC,CAAC,IAAGA,EAAK,CAAC,EAAI,mBAAmBA,EAAK,CAAC,CAAC,KACvDI,GAAAC,EAAAT,GAAA,YAAAA,EAAM,WAAN,YAAAS,EAAgB,SAAhB,MAAAD,EAAwB,KAC1BR,EAAK,SAAS,OAAO,KAAK,GAAGI,CAAI,EACxB,uBAAS,MAClB,QAAQ,KAAK,GAAGA,CAAI,CAExB,EACMM,GAAgB,CAAE,EACXC,GAAW,CAACX,EAAMC,EAAMC,EAAKC,IAAS,CAC7CI,GAASL,CAAG,GAAKQ,GAAcR,CAAG,IAClCK,GAASL,CAAG,IAAGQ,GAAcR,CAAG,EAAI,IAAI,MAC5CH,GAAKC,EAAMC,EAAMC,EAAKC,CAAI,EAC5B,EACMS,GAAY,CAACZ,EAAMa,IAAO,IAAM,CACpC,GAAIb,EAAK,cACPa,EAAI,MACC,CACL,MAAMC,EAAc,IAAM,CACxB,WAAW,IAAM,CACfd,EAAK,IAAI,cAAec,CAAW,CACpC,EAAE,CAAC,EACJD,EAAI,CACL,EACDb,EAAK,GAAG,cAAec,CAAW,CACtC,CACA,EACaC,GAAiB,CAACf,EAAMgB,EAAIH,IAAO,CAC9Cb,EAAK,eAAegB,EAAIJ,GAAUZ,EAAMa,CAAE,CAAC,CAC7C,EACaI,GAAgB,CAACjB,EAAMkB,EAAKF,EAAIH,IAAO,CAElD,GADIN,GAASS,CAAE,IAAGA,EAAK,CAACA,CAAE,GACtBhB,EAAK,QAAQ,SAAWA,EAAK,QAAQ,QAAQ,QAAQkB,CAAG,EAAI,GAAI,OAAOH,GAAef,EAAMgB,EAAIH,CAAE,EACtGG,EAAG,QAAQ,GAAK,CACVhB,EAAK,QAAQ,GAAG,QAAQ,CAAC,EAAI,GAAGA,EAAK,QAAQ,GAAG,KAAK,CAAC,CAC9D,CAAG,EACDA,EAAK,cAAckB,EAAKN,GAAUZ,EAAMa,CAAE,CAAC,CAC7C,EACaM,GAAqB,CAACH,EAAIhB,EAAMoB,EAAU,CAAA,IACjD,CAACpB,EAAK,WAAa,CAACA,EAAK,UAAU,QACrCW,GAASX,EAAM,eAAgB,yCAA0C,CACvE,UAAWA,EAAK,SACtB,CAAK,EACM,IAEFA,EAAK,mBAAmBgB,EAAI,CACjC,IAAKI,EAAQ,IACb,SAAU,CAACC,EAAcC,IAAmB,OAC1C,KAAIhB,EAAAc,EAAQ,WAAR,YAAAd,EAAkB,QAAQ,qBAAsB,IAAMe,EAAa,SAAS,iBAAiB,SAAWA,EAAa,sBAAwB,CAACC,EAAeD,EAAa,qBAAsBL,CAAE,EAAG,MAAO,EACtN,CACA,CAAG,EAGUT,GAAWgB,GAAO,OAAOA,GAAQ,SACjCC,GAAWD,GAAO,OAAOA,GAAQ,UAAYA,IAAQ,KC7D5DE,GAAkB,oGAClBC,GAAe,CACnB,QAAS,IACT,QAAS,IACT,OAAQ,IACR,QAAS,IACT,OAAQ,IACR,QAAS,IACT,SAAU,IACV,QAAS,IACT,SAAU,IACV,QAAS,IACT,SAAU,IACV,SAAU,IACV,SAAU,IACV,SAAU,IACV,QAAS,IACT,SAAU,IACV,WAAY,IACZ,UAAW,IACX,SAAU,IACV,QAAS,GACX,EACMC,GAAqBnC,GAAKkC,GAAalC,CAAC,EACjCoC,GAAWC,GAAQA,EAAK,QAAQJ,GAAiBE,EAAkB,ECvBhF,IAAIG,GAAiB,CACnB,SAAU,kBACV,cAAe,GACf,oBAAqB,GACrB,2BAA4B,GAC5B,mBAAoB,GACpB,2BAA4B,CAAC,KAAM,SAAU,IAAK,GAAG,EACrD,YAAa,GACb,SAAAF,EACF,EACO,MAAMG,GAAc,CAACX,EAAU,KAAO,CAC3CU,GAAiB,CACf,GAAGA,GACH,GAAGV,CACJ,CACH,EACaY,GAAc,IAAMF,GCjBjC,IAAIT,GACG,MAAMY,GAAUC,GAAY,CACjCb,GAAea,CACjB,EACaC,GAAU,IAAMd,GCFhBe,GAAmB,CAC9B,KAAM,WACN,KAAKF,EAAU,CACbH,GAAYG,EAAS,QAAQ,KAAK,EAClCD,GAAQC,CAAQ,CACpB,CACA,ECHaG,GAAcC,EAAAA,cAAe,EACnC,MAAMC,EAAiB,CAC5B,aAAc,CACZ,KAAK,eAAiB,CAAE,CAC5B,CACE,kBAAkBC,EAAY,CAC5BA,EAAW,QAAQxB,GAAM,CAClB,KAAK,eAAeA,CAAE,IAAG,KAAK,eAAeA,CAAE,EAAI,GAC9D,CAAK,CACL,CACE,mBAAoB,CAClB,OAAO,OAAO,KAAK,KAAK,cAAc,CAC1C,CACA,CCfA,MAAMyB,GAAc,CAACC,EAAOC,IAAW,CACrC,MAAMC,EAAMC,EAAAA,OAAQ,EACpBC,OAAAA,EAAAA,UAAU,IAAM,CACdF,EAAI,QAAiCF,CACzC,EAAK,CAACA,EAAOC,CAAM,CAAC,EACXC,EAAI,OACb,EACMG,GAAa,CAAC/C,EAAMgD,EAAUC,EAAWC,IAAclD,EAAK,UAAUgD,EAAUC,EAAWC,CAAS,EACpGC,GAAe,CAACnD,EAAMgD,EAAUC,EAAWC,IAAcE,EAAW,YAACL,GAAW/C,EAAMgD,EAAUC,EAAWC,CAAS,EAAG,CAAClD,EAAMgD,EAAUC,EAAWC,CAAS,CAAC,EACtJG,GAAiB,CAACrC,EAAIsC,EAAQ,KAAO,aAChD,KAAM,CACJ,KAAMC,CACV,EAAMD,EACE,CACJ,KAAME,EACN,UAAWC,CACf,EAAMC,EAAU,WAACrB,EAAW,GAAK,CAAE,EAC3BrC,EAAOuD,GAAiBC,GAAmBrB,GAAS,EAE1D,GADInC,GAAQ,CAACA,EAAK,mBAAkBA,EAAK,iBAAmB,IAAIuC,IAC5D,CAACvC,EAAM,CACTW,GAASX,EAAM,sBAAuB,wFAAwF,EAC9H,MAAM2D,EAAY,CAACC,EAAGC,IAChBtD,GAASsD,CAAkB,EAAUA,EACrCrC,GAASqC,CAAkB,GAAKtD,GAASsD,EAAmB,YAAY,EAAUA,EAAmB,aAClG,MAAM,QAAQD,CAAC,EAAIA,EAAEA,EAAE,OAAS,CAAC,EAAIA,EAExCE,EAAc,CAACH,EAAW,CAAA,EAAI,EAAK,EACzC,OAAAG,EAAY,EAAIH,EAChBG,EAAY,KAAO,CAAE,EACrBA,EAAY,MAAQ,GACbA,CACX,EACMxD,EAAAN,EAAK,QAAQ,QAAb,MAAAM,EAAoB,MAAMK,GAASX,EAAM,oBAAqB,qHAAqH,EACvL,MAAM+D,EAAc,CAClB,GAAG/B,GAAa,EAChB,GAAGhC,EAAK,QAAQ,MAChB,GAAGsD,CACJ,EACK,CACJ,YAAAU,EACA,UAAAd,CACJ,EAAMa,EACJ,IAAIvB,EAAaxB,GAAMyC,KAAwBpD,EAAAL,EAAK,UAAL,YAAAK,EAAc,WAC7DmC,EAAajC,GAASiC,CAAU,EAAI,CAACA,CAAU,EAAIA,GAAc,CAAC,aAAa,GAC/EhC,GAAAC,EAAAT,EAAK,kBAAiB,oBAAtB,MAAAQ,EAAA,KAAAC,EAA0C+B,GAC1C,MAAMyB,GAASjE,EAAK,eAAiBA,EAAK,uBAAyBwC,EAAW,MAAM0B,GAAK/C,GAAmB+C,EAAGlE,EAAM+D,CAAW,CAAC,EAC3HI,EAAWhB,GAAanD,EAAMsD,EAAM,KAAO,KAAMS,EAAY,SAAW,WAAavB,EAAaA,EAAW,CAAC,EAAGU,CAAS,EAC1HkB,EAAO,IAAMD,EACbE,EAAU,IAAMtB,GAAW/C,EAAMsD,EAAM,KAAO,KAAMS,EAAY,SAAW,WAAavB,EAAaA,EAAW,CAAC,EAAGU,CAAS,EAC7H,CAACoB,EAAGC,CAAI,EAAIC,EAAAA,SAASJ,CAAI,EAC/B,IAAIK,EAAWjC,EAAW,KAAM,EAC5Bc,EAAM,MAAKmB,EAAW,GAAGnB,EAAM,GAAG,GAAGmB,CAAQ,IACjD,MAAMC,EAAmBjC,GAAYgC,CAAQ,EACvCE,EAAY9B,EAAM,OAAC,EAAI,EAC7BC,EAAAA,UAAU,IAAM,CACd,KAAM,CACJ,SAAA8B,EACA,cAAAC,CACN,EAAQd,EACJY,EAAU,QAAU,GAChB,CAACV,GAAS,CAACD,IACTV,EAAM,IACRrC,GAAcjB,EAAMsD,EAAM,IAAKd,EAAY,IAAM,CAC3CmC,EAAU,SAASJ,EAAKF,CAAO,CAC7C,CAAS,EAEDtD,GAAef,EAAMwC,EAAY,IAAM,CACjCmC,EAAU,SAASJ,EAAKF,CAAO,CAC7C,CAAS,GAGDJ,GAASS,GAAoBA,IAAqBD,GAAYE,EAAU,SAC1EJ,EAAKF,CAAO,EAEd,MAAMS,EAAa,IAAM,CACnBH,EAAU,SAASJ,EAAKF,CAAO,CACpC,EACD,OAAIO,IAAU5E,GAAA,MAAAA,EAAM,GAAG4E,EAAUE,IAC7BD,IAAe7E,GAAA,MAAAA,EAAM,MAAM,GAAG6E,EAAeC,IAC1C,IAAM,CACXH,EAAU,QAAU,GAChB3E,IAAM4E,GAAA,MAAAA,EAAU,MAAM,KAAK,QAAQG,GAAK/E,EAAK,IAAI+E,EAAGD,CAAU,IAC9DD,GAAiB7E,GAAM6E,EAAc,MAAM,GAAG,EAAE,QAAQE,GAAK/E,EAAK,MAAM,IAAI+E,EAAGD,CAAU,CAAC,CAC/F,CACL,EAAK,CAAC9E,EAAMyE,CAAQ,CAAC,EACnB3B,EAAAA,UAAU,IAAM,CACV6B,EAAU,SAAWV,GACvBM,EAAKH,CAAI,CAEZ,EAAE,CAACpE,EAAMkD,EAAWe,CAAK,CAAC,EAC3B,MAAMe,EAAM,CAACV,EAAGtE,EAAMiE,CAAK,EAK3B,GAJAe,EAAI,EAAIV,EACRU,EAAI,KAAOhF,EACXgF,EAAI,MAAQf,EACRA,GACA,CAACA,GAAS,CAACD,EAAa,OAAOgB,EACnC,MAAM,IAAI,QAAQC,GAAW,CACvB3B,EAAM,IACRrC,GAAcjB,EAAMsD,EAAM,IAAKd,EAAY,IAAMyC,GAAS,EAE1DlE,GAAef,EAAMwC,EAAY,IAAMyC,EAAO,CAAE,CAEtD,CAAG,CACH,EC1GA;AAAA;AAAA;AAAA;AAAA;AAAA,GAOA,MAAMC,GAAeC,GAAWA,EAAO,QAAQ,qBAAsB,OAAO,EAAE,YAAa,EACrFC,GAAeD,GAAWA,EAAO,QACrC,wBACA,CAACE,EAAOC,EAAIC,IAAOA,EAAKA,EAAG,YAAW,EAAKD,EAAG,YAAW,CAC3D,EACME,GAAgBL,GAAW,CAC/B,MAAMM,EAAYL,GAAYD,CAAM,EACpC,OAAOM,EAAU,OAAO,CAAC,EAAE,YAAW,EAAKA,EAAU,MAAM,CAAC,CAC9D,EACMC,GAAe,IAAIC,IAAYA,EAAQ,OAAO,CAACC,EAAWC,EAAOC,IAC9D,EAAQF,GAAcA,EAAU,KAAI,IAAO,IAAME,EAAM,QAAQF,CAAS,IAAMC,CACtF,EAAE,KAAK,GAAG,EAAE,KAAM,EACbE,GAAezC,GAAU,CAC7B,UAAW0C,KAAQ1C,EACjB,GAAI0C,EAAK,WAAW,OAAO,GAAKA,IAAS,QAAUA,IAAS,QAC1D,MAAO,EAGb,ECzBA;AAAA;AAAA;AAAA;AAAA;AAAA,GAOA,IAAIC,GAAoB,CACtB,MAAO,6BACP,MAAO,GACP,OAAQ,GACR,QAAS,YACT,KAAM,OACN,OAAQ,eACR,YAAa,EACb,cAAe,QACf,eAAgB,OAClB,ECjBA;AAAA;AAAA;AAAA;AAAA;AAAA,GAWA,MAAMC,GAAOC,EAAU,WACrB,CAAC,CACC,MAAAC,EAAQ,eACR,KAAAC,EAAO,GACP,YAAAC,EAAc,EACd,oBAAAC,EACA,UAAAX,EAAY,GACZ,SAAAY,EACA,SAAAC,EACA,GAAGtG,CACJ,EAAEyC,IAAQ8D,EAAa,cACtB,MACA,CACE,IAAA9D,EACA,GAAGqD,GACH,MAAOI,EACP,OAAQA,EACR,OAAQD,EACR,YAAaG,EAAsB,OAAOD,CAAW,EAAI,GAAK,OAAOD,CAAI,EAAIC,EAC7E,UAAWZ,GAAa,SAAUE,CAAS,EAC3C,GAAG,CAACY,GAAY,CAACT,GAAY5F,CAAI,GAAK,CAAE,cAAe,MAAQ,EAC/D,GAAGA,CACJ,EACD,CACE,GAAGsG,EAAS,IAAI,CAAC,CAACE,EAAKC,CAAK,IAAMF,EAAa,cAACC,EAAKC,CAAK,CAAC,EAC3D,GAAG,MAAM,QAAQJ,CAAQ,EAAIA,EAAW,CAACA,CAAQ,CACvD,CACA,CACA,ECvCA;AAAA;AAAA;AAAA;AAAA;AAAA,GAWA,MAAMK,EAAmB,CAACC,EAAUL,IAAa,CAC/C,MAAMM,EAAYZ,EAAU,WAC1B,CAAC,CAAE,UAAAP,EAAW,GAAGtC,CAAO,EAAEV,IAAQ8D,EAAa,cAACR,GAAM,CACpD,IAAAtD,EACA,SAAA6D,EACA,UAAWf,GACT,UAAUR,GAAYM,GAAasB,CAAQ,CAAC,CAAC,GAC7C,UAAUA,CAAQ,GAClBlB,CACD,EACD,GAAGtC,CACJ,CAAA,CACF,EACD,OAAAyD,EAAU,YAAcvB,GAAasB,CAAQ,EACtCC,CACT,EC1BA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMC,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CACE,OACA,CACE,EAAG,qIACH,IAAK,OACX,CACA,CACA,EACMC,GAAWJ,EAAiB,YAAaG,EAAU,ECnBzD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,KAAM,IAAK,SAAU,EACzD,CAAC,WAAY,CAAE,OAAQ,mBAAoB,IAAK,QAAU,CAAA,CAC5D,EACME,GAAQL,EAAiB,QAASG,EAAU,ECblD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CACE,OACA,CACE,EAAG,2PACH,IAAK,OACX,CACG,EACD,CAAC,OAAQ,CAAE,EAAG,wBAAyB,IAAK,QAAU,CAAA,CACxD,EACMG,GAASN,EAAiB,SAAUG,EAAU,ECnBpD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,0CAA2C,IAAK,QAAQ,CAAE,EACxE,CAAC,OAAQ,CAAE,EAAG,IAAK,EAAG,IAAK,MAAO,KAAM,OAAQ,KAAM,GAAI,IAAK,IAAK,QAAU,CAAA,CAChF,EACMI,GAAOP,EAAiB,OAAQG,EAAU,ECbhD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,UAAW,IAAK,QAAU,CAAA,CAC1C,EACMK,GAAOR,EAAiB,OAAQG,EAAU,ECdhD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,mBAAoB,IAAK,QAAQ,CAAE,EACjD,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,IAAK,IAAK,QAAU,CAAA,CAC1D,EACMM,GAAST,EAAiB,SAAUG,EAAU,ECbpD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CACE,OACA,CACE,EAAG,+WACH,IAAK,QACX,CACA,CACA,EACMO,GAAOV,EAAiB,OAAQG,EAAU,EClBhD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,YAAa,IAAK,QAAQ,CAAE,EAC1C,CAAC,OAAQ,CAAE,EAAG,yBAA0B,IAAK,QAAU,CAAA,CACzD,EACMQ,GAAaX,EAAiB,cAAeG,EAAU,ECb7D;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CACE,OACA,CACE,EAAG,0IACH,IAAK,QACX,CACA,CACA,EACMS,GAAUZ,EAAiB,UAAWG,EAAU,EClBtD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,4CAA6C,IAAK,QAAQ,CAAE,EAC1E,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,IAAK,EAAG,IAAK,IAAK,QAAU,CAAA,CACzD,EACMU,GAAOb,EAAiB,OAAQG,EAAU,ECbhD,SAASW,GAAE5C,EAAE,CAAC,IAAIT,EAAEsD,EAAE1D,EAAE,GAAG,GAAa,OAAOa,GAAjB,UAA8B,OAAOA,GAAjB,SAAmBb,GAAGa,UAAoB,OAAOA,GAAjB,SAAmB,GAAG,MAAM,QAAQA,CAAC,EAAE,CAAC,IAAI8C,EAAE9C,EAAE,OAAO,IAAIT,EAAE,EAAEA,EAAEuD,EAAEvD,IAAIS,EAAET,CAAC,IAAIsD,EAAED,GAAE5C,EAAET,CAAC,CAAC,KAAKJ,IAAIA,GAAG,KAAKA,GAAG0D,EAAE,KAAM,KAAIA,KAAK7C,EAAEA,EAAE6C,CAAC,IAAI1D,IAAIA,GAAG,KAAKA,GAAG0D,GAAG,OAAO1D,CAAC,CAAQ,SAAS4D,IAAM,CAAC,QAAQ/C,EAAET,EAAEsD,EAAE,EAAE1D,EAAE,GAAG2D,EAAE,UAAU,OAAOD,EAAEC,EAAED,KAAK7C,EAAE,UAAU6C,CAAC,KAAKtD,EAAEqD,GAAE5C,CAAC,KAAKb,IAAIA,GAAG,KAAKA,GAAGI,GAAG,OAAOJ,CAAC,CCe/W,MAAM6D,GAAiBrF,GAAQ,OAAOA,GAAU,UAAY,GAAGA,CAAK,GAAKA,IAAU,EAAI,IAAMA,EAChFsF,GAAKF,GACLG,GAAM,CAACC,EAAMC,IAAU7E,GAAQ,CACpC,IAAI8E,EACJ,IAAKD,GAAW,KAA4B,OAASA,EAAO,WAAa,KAAM,OAAOH,GAAGE,EAAM5E,GAAU,KAA2B,OAASA,EAAM,MAAOA,GAAU,KAA2B,OAASA,EAAM,SAAS,EACvN,KAAM,CAAE,SAAA+E,EAAU,gBAAAC,CAAe,EAAKH,EAChCI,EAAuB,OAAO,KAAKF,CAAQ,EAAE,IAAKG,GAAU,CAC9D,MAAMC,EAAcnF,GAAU,KAA2B,OAASA,EAAMkF,CAAO,EACzEE,EAAqBJ,GAAoB,KAAqC,OAASA,EAAgBE,CAAO,EACpH,GAAIC,IAAgB,KAAM,OAAO,KACjC,MAAME,EAAaZ,GAAcU,CAAW,GAAKV,GAAcW,CAAkB,EACjF,OAAOL,EAASG,CAAO,EAAEG,CAAU,CAC/C,CAAS,EACKC,EAAwBtF,GAAS,OAAO,QAAQA,CAAK,EAAE,OAAO,CAACuF,EAAKC,IAAQ,CAC9E,GAAI,CAACC,EAAKrG,CAAK,EAAIoG,EACnB,OAAIpG,IAAU,SAGdmG,EAAIE,CAAG,EAAIrG,GACJmG,CACV,EAAE,EAAE,EACCG,EAA+Bb,GAAW,OAAsCC,EAA2BD,EAAO,oBAAsB,MAAQC,IAA6B,OAAvG,OAAyHA,EAAyB,OAAO,CAACS,EAAKC,IAAQ,CAC/O,GAAI,CAAE,MAAOG,EAAS,UAAWC,EAAa,GAAGC,CAAsB,EAAKL,EAC5E,OAAO,OAAO,QAAQK,CAAsB,EAAE,MAAOL,GAAQ,CACzD,GAAI,CAACC,EAAKrG,CAAK,EAAIoG,EACnB,OAAO,MAAM,QAAQpG,CAAK,EAAIA,EAAM,SAAS,CACzC,GAAG4F,EACH,GAAGM,CACvB,EAAkBG,CAAG,CAAC,EAAK,CACP,GAAGT,EACH,GAAGM,CACvB,EAAmBG,CAAG,IAAMrG,CAC5B,CAAa,EAAI,CACD,GAAGmG,EACHI,EACAC,CAChB,EAAgBL,CACP,EAAE,EAAE,EACL,OAAOb,GAAGE,EAAMK,EAAsBS,EAA8B1F,GAAU,KAA2B,OAASA,EAAM,MAAOA,GAAU,KAA2B,OAASA,EAAM,SAAS,CAC/L,ECtDC8F,GAAuB,IACvBC,GAAwBlB,GAAU,CACtC,MAAMmB,EAAWC,GAAepB,CAAM,EAChC,CACJ,uBAAAqB,EACA,+BAAAC,CACJ,EAAMtB,EAgBJ,MAAO,CACL,gBAhBsBvC,GAAa,CACnC,MAAM8D,EAAa9D,EAAU,MAAMwD,EAAoB,EAEvD,OAAIM,EAAW,CAAC,IAAM,IAAMA,EAAW,SAAW,GAChDA,EAAW,MAAO,EAEbC,GAAkBD,EAAYJ,CAAQ,GAAKM,GAA+BhE,CAAS,CAC3F,EAUC,4BATkC,CAACiE,EAAcC,IAAuB,CACxE,MAAMC,EAAYP,EAAuBK,CAAY,GAAK,CAAE,EAC5D,OAAIC,GAAsBL,EAA+BI,CAAY,EAC5D,CAAC,GAAGE,EAAW,GAAGN,EAA+BI,CAAY,CAAC,EAEhEE,CACR,CAIA,CACH,EACMJ,GAAoB,CAACD,EAAYM,IAAoB,OACzD,GAAIN,EAAW,SAAW,EACxB,OAAOM,EAAgB,aAEzB,MAAMC,EAAmBP,EAAW,CAAC,EAC/BQ,EAAsBF,EAAgB,SAAS,IAAIC,CAAgB,EACnEE,EAA8BD,EAAsBP,GAAkBD,EAAW,MAAM,CAAC,EAAGQ,CAAmB,EAAI,OACxH,GAAIC,EACF,OAAOA,EAET,GAAIH,EAAgB,WAAW,SAAW,EACxC,OAEF,MAAMI,EAAYV,EAAW,KAAKN,EAAoB,EACtD,OAAO9I,EAAA0J,EAAgB,WAAW,KAAK,CAAC,CACtC,UAAAK,CACJ,IAAQA,EAAUD,CAAS,CAAC,IAFnB,YAAA9J,EAEsB,YAC/B,EACMgK,GAAyB,aACzBV,GAAiChE,GAAa,CAClD,GAAI0E,GAAuB,KAAK1E,CAAS,EAAG,CAC1C,MAAM2E,EAA6BD,GAAuB,KAAK1E,CAAS,EAAE,CAAC,EACrE4E,EAAWD,GAAA,YAAAA,EAA4B,UAAU,EAAGA,EAA2B,QAAQ,GAAG,GAChG,GAAIC,EAEF,MAAO,cAAgBA,CAE7B,CACA,EAIMjB,GAAiBpB,GAAU,CAC/B,KAAM,CACJ,MAAAsC,EACA,YAAAC,CACJ,EAAMvC,EACEmB,EAAW,CACf,SAAU,IAAI,IACd,WAAY,CAAA,CACb,EACD,UAAWO,KAAgBa,EACzBC,GAA0BD,EAAYb,CAAY,EAAGP,EAAUO,EAAcY,CAAK,EAEpF,OAAOnB,CACT,EACMqB,GAA4B,CAACC,EAAYZ,EAAiBH,EAAcY,IAAU,CACtFG,EAAW,QAAQC,GAAmB,CACpC,GAAI,OAAOA,GAAoB,SAAU,CACvC,MAAMC,EAAwBD,IAAoB,GAAKb,EAAkBe,GAAQf,EAAiBa,CAAe,EACjHC,EAAsB,aAAejB,EACrC,MACN,CACI,GAAI,OAAOgB,GAAoB,WAAY,CACzC,GAAIG,GAAcH,CAAe,EAAG,CAClCF,GAA0BE,EAAgBJ,CAAK,EAAGT,EAAiBH,EAAcY,CAAK,EACtF,MACR,CACMT,EAAgB,WAAW,KAAK,CAC9B,UAAWa,EACX,aAAAhB,CACR,CAAO,EACD,MACN,CACI,OAAO,QAAQgB,CAAe,EAAE,QAAQ,CAAC,CAAC9B,EAAK6B,CAAU,IAAM,CAC7DD,GAA0BC,EAAYG,GAAQf,EAAiBjB,CAAG,EAAGc,EAAcY,CAAK,CAC9F,CAAK,CACL,CAAG,CACH,EACMM,GAAU,CAACf,EAAiBiB,IAAS,CACzC,IAAIC,EAAyBlB,EAC7B,OAAAiB,EAAK,MAAM7B,EAAoB,EAAE,QAAQ+B,GAAY,CAC9CD,EAAuB,SAAS,IAAIC,CAAQ,GAC/CD,EAAuB,SAAS,IAAIC,EAAU,CAC5C,SAAU,IAAI,IACd,WAAY,CAAA,CACpB,CAAO,EAEHD,EAAyBA,EAAuB,SAAS,IAAIC,CAAQ,CACzE,CAAG,EACMD,CACT,EACMF,GAAgBI,GAAQA,EAAK,cAG7BC,GAAiBC,GAAgB,CACrC,GAAIA,EAAe,EACjB,MAAO,CACL,IAAK,IAAA,GACL,IAAK,IAAM,CAAA,CACZ,EAEH,IAAIC,EAAY,EACZC,EAAQ,IAAI,IACZC,EAAgB,IAAI,IACxB,MAAMC,EAAS,CAAC3C,EAAKrG,IAAU,CAC7B8I,EAAM,IAAIzC,EAAKrG,CAAK,EACpB6I,IACIA,EAAYD,IACdC,EAAY,EACZE,EAAgBD,EAChBA,EAAQ,IAAI,IAEf,EACD,MAAO,CACL,IAAIzC,EAAK,CACP,IAAIrG,EAAQ8I,EAAM,IAAIzC,CAAG,EACzB,GAAIrG,IAAU,OACZ,OAAOA,EAET,IAAKA,EAAQ+I,EAAc,IAAI1C,CAAG,KAAO,OACvC,OAAA2C,EAAO3C,EAAKrG,CAAK,EACVA,CAEV,EACD,IAAIqG,EAAKrG,EAAO,CACV8I,EAAM,IAAIzC,CAAG,EACfyC,EAAM,IAAIzC,EAAKrG,CAAK,EAEpBgJ,EAAO3C,EAAKrG,CAAK,CAEzB,CACG,CACH,EACMiJ,GAAqB,IACrBC,GAAqB,IACrBC,GAA4BD,GAAmB,OAC/CE,GAAuB3D,GAAU,CACrC,KAAM,CACJ,OAAA4D,EACA,2BAAAC,CACJ,EAAM7D,EAOJ,IAAI8D,EAAiBrG,GAAa,CAChC,MAAMsG,EAAY,CAAE,EACpB,IAAIC,EAAe,EACfC,EAAa,EACbC,EAAgB,EAChBC,EACJ,QAASzG,EAAQ,EAAGA,EAAQD,EAAU,OAAQC,IAAS,CACrD,IAAI0G,EAAmB3G,EAAUC,CAAK,EACtC,GAAIsG,IAAiB,GAAKC,IAAe,EAAG,CAC1C,GAAIG,IAAqBX,GAAoB,CAC3CM,EAAU,KAAKtG,EAAU,MAAMyG,EAAexG,CAAK,CAAC,EACpDwG,EAAgBxG,EAAQgG,GACxB,QACV,CACQ,GAAIU,IAAqB,IAAK,CAC5BD,EAA0BzG,EAC1B,QACV,CACA,CACU0G,IAAqB,IACvBJ,IACSI,IAAqB,IAC9BJ,IACSI,IAAqB,IAC9BH,IACSG,IAAqB,KAC9BH,GAER,CACI,MAAMI,EAAqCN,EAAU,SAAW,EAAItG,EAAYA,EAAU,UAAUyG,CAAa,EAC3GI,EAAgBC,GAAuBF,CAAkC,EACzEG,EAAuBF,IAAkBD,EACzCI,EAA+BN,GAA2BA,EAA0BD,EAAgBC,EAA0BD,EAAgB,OACpJ,MAAO,CACL,UAAAH,EACA,qBAAAS,EACA,cAAAF,EACA,6BAAAG,CACD,CACF,EACD,GAAIb,EAAQ,CACV,MAAMc,EAAad,EAASH,GACtBkB,EAAyBb,EAC/BA,EAAiBrG,GAAaA,EAAU,WAAWiH,CAAU,EAAIC,EAAuBlH,EAAU,UAAUiH,EAAW,MAAM,CAAC,EAAI,CAChI,WAAY,GACZ,UAAW,CAAE,EACb,qBAAsB,GACtB,cAAejH,EACf,6BAA8B,MAC/B,CACL,CACE,GAAIoG,EAA4B,CAC9B,MAAMc,EAAyBb,EAC/BA,EAAiBrG,GAAaoG,EAA2B,CACvD,UAAApG,EACA,eAAgBkH,CACtB,CAAK,CACL,CACE,OAAOb,CACT,EACMS,GAAyBD,GACzBA,EAAc,SAASd,EAAkB,EACpCc,EAAc,UAAU,EAAGA,EAAc,OAAS,CAAC,EAMxDA,EAAc,WAAWd,EAAkB,EACtCc,EAAc,UAAU,CAAC,EAE3BA,EAQHM,GAAsB5E,GAAU,CACpC,MAAM6E,EAA0B,OAAO,YAAY7E,EAAO,wBAAwB,IAAI8E,GAAY,CAACA,EAAU,EAAI,CAAC,CAAC,EAmBnH,OAlBsBf,GAAa,CACjC,GAAIA,EAAU,QAAU,EACtB,OAAOA,EAET,MAAMgB,EAAkB,CAAE,EAC1B,IAAIC,EAAoB,CAAE,EAC1B,OAAAjB,EAAU,QAAQe,GAAY,CACAA,EAAS,CAAC,IAAM,KAAOD,EAAwBC,CAAQ,GAEjFC,EAAgB,KAAK,GAAGC,EAAkB,KAAI,EAAIF,CAAQ,EAC1DE,EAAoB,CAAE,GAEtBA,EAAkB,KAAKF,CAAQ,CAEvC,CAAK,EACDC,EAAgB,KAAK,GAAGC,EAAkB,KAAI,CAAE,EACzCD,CACR,CAEH,EACME,GAAoBjF,IAAW,CACnC,MAAOkD,GAAelD,EAAO,SAAS,EACtC,eAAgB2D,GAAqB3D,CAAM,EAC3C,cAAe4E,GAAoB5E,CAAM,EACzC,GAAGkB,GAAsBlB,CAAM,CACjC,GACMkF,GAAsB,MACtBC,GAAiB,CAACC,EAAWC,IAAgB,CACjD,KAAM,CACJ,eAAAvB,EACA,gBAAAwB,EACA,4BAAAC,EACA,cAAAC,CACJ,EAAMH,EAQEI,EAAwB,CAAE,EAC1BC,EAAaN,EAAU,KAAI,EAAG,MAAMF,EAAmB,EAC7D,IAAIS,EAAS,GACb,QAASjI,EAAQgI,EAAW,OAAS,EAAGhI,GAAS,EAAGA,GAAS,EAAG,CAC9D,MAAMkI,EAAoBF,EAAWhI,CAAK,EACpC,CACJ,WAAAmI,EACA,UAAA9B,EACA,qBAAAS,EACA,cAAAF,EACA,6BAAAG,CACN,EAAQX,EAAe8B,CAAiB,EACpC,GAAIC,EAAY,CACdF,EAASC,GAAqBD,EAAO,OAAS,EAAI,IAAMA,EAASA,GACjE,QACN,CACI,IAAIhE,EAAqB,CAAC,CAAC8C,EACvB/C,EAAe4D,EAAgB3D,EAAqB2C,EAAc,UAAU,EAAGG,CAA4B,EAAIH,CAAa,EAChI,GAAI,CAAC5C,EAAc,CACjB,GAAI,CAACC,EAAoB,CAEvBgE,EAASC,GAAqBD,EAAO,OAAS,EAAI,IAAMA,EAASA,GACjE,QACR,CAEM,GADAjE,EAAe4D,EAAgBhB,CAAa,EACxC,CAAC5C,EAAc,CAEjBiE,EAASC,GAAqBD,EAAO,OAAS,EAAI,IAAMA,EAASA,GACjE,QACR,CACMhE,EAAqB,EAC3B,CACI,MAAMmE,EAAkBN,EAAczB,CAAS,EAAE,KAAK,GAAG,EACnDgC,EAAavB,EAAuBsB,EAAkBtC,GAAqBsC,EAC3EE,EAAUD,EAAarE,EAC7B,GAAI+D,EAAsB,SAASO,CAAO,EAExC,SAEFP,EAAsB,KAAKO,CAAO,EAClC,MAAMC,EAAiBV,EAA4B7D,EAAcC,CAAkB,EACnF,QAASuE,EAAI,EAAGA,EAAID,EAAe,OAAQ,EAAEC,EAAG,CAC9C,MAAMC,EAAQF,EAAeC,CAAC,EAC9BT,EAAsB,KAAKM,EAAaI,CAAK,CACnD,CAEIR,EAASC,GAAqBD,EAAO,OAAS,EAAI,IAAMA,EAASA,EACrE,CACE,OAAOA,CACT,EAWA,SAASS,IAAS,CAChB,IAAI1I,EAAQ,EACR2I,EACAC,EACAtJ,EAAS,GACb,KAAOU,EAAQ,UAAU,SACnB2I,EAAW,UAAU3I,GAAO,KAC1B4I,EAAgBC,GAAQF,CAAQ,KAClCrJ,IAAWA,GAAU,KACrBA,GAAUsJ,GAIhB,OAAOtJ,CACT,CACA,MAAMuJ,GAAUC,GAAO,CACrB,GAAI,OAAOA,GAAQ,SACjB,OAAOA,EAET,IAAIF,EACAtJ,EAAS,GACb,QAASvB,EAAI,EAAGA,EAAI+K,EAAI,OAAQ/K,IAC1B+K,EAAI/K,CAAC,IACH6K,EAAgBC,GAAQC,EAAI/K,CAAC,CAAC,KAChCuB,IAAWA,GAAU,KACrBA,GAAUsJ,GAIhB,OAAOtJ,CACT,EACA,SAASyJ,GAAoBC,KAAsBC,EAAkB,CACnE,IAAItB,EACAuB,EACAC,EACAC,EAAiBC,EACrB,SAASA,EAAkB3B,EAAW,CACpC,MAAMpF,EAAS2G,EAAiB,OAAO,CAACK,EAAgBC,IAAwBA,EAAoBD,CAAc,EAAGN,GAAmB,EACxI,OAAArB,EAAcJ,GAAkBjF,CAAM,EACtC4G,EAAWvB,EAAY,MAAM,IAC7BwB,EAAWxB,EAAY,MAAM,IAC7ByB,EAAiBI,EACVA,EAAc9B,CAAS,CAClC,CACE,SAAS8B,EAAc9B,EAAW,CAChC,MAAM+B,EAAeP,EAASxB,CAAS,EACvC,GAAI+B,EACF,OAAOA,EAET,MAAMxB,EAASR,GAAeC,EAAWC,CAAW,EACpD,OAAAwB,EAASzB,EAAWO,CAAM,EACnBA,CACX,CACE,OAAO,UAA6B,CAClC,OAAOmB,EAAeV,GAAO,MAAM,KAAM,SAAS,CAAC,CACpD,CACH,CACA,MAAMgB,EAAYxG,GAAO,CACvB,MAAMyG,EAAc/E,GAASA,EAAM1B,CAAG,GAAK,CAAE,EAC7C,OAAAyG,EAAY,cAAgB,GACrBA,CACT,EACMC,GAAsB,8BACtBC,GAAyB,8BACzBC,GAAgB,aAChBC,GAAkB,mCAClBC,GAAkB,4HAClBC,GAAqB,2CAErBC,GAAc,kEACdC,GAAa,+FACbC,GAAavN,GAASiN,GAAc,KAAKjN,CAAK,EAC9CwN,EAAWxN,GAAS,CAAC,CAACA,GAAS,CAAC,OAAO,MAAM,OAAOA,CAAK,CAAC,EAC1DyN,EAAYzN,GAAS,CAAC,CAACA,GAAS,OAAO,UAAU,OAAOA,CAAK,CAAC,EAC9D0N,GAAY1N,GAASA,EAAM,SAAS,GAAG,GAAKwN,EAASxN,EAAM,MAAM,EAAG,EAAE,CAAC,EACvE2N,EAAe3N,GAASkN,GAAgB,KAAKlN,CAAK,EAClD4N,GAAQ,IAAM,GACdC,GAAe7N,GAIrBmN,GAAgB,KAAKnN,CAAK,GAAK,CAACoN,GAAmB,KAAKpN,CAAK,EACvD8N,GAAU,IAAM,GAChBC,GAAW/N,GAASqN,GAAY,KAAKrN,CAAK,EAC1CgO,GAAUhO,GAASsN,GAAW,KAAKtN,CAAK,EACxCiO,GAAoBjO,GAAS,CAACkO,EAAiBlO,CAAK,GAAK,CAACmO,EAAoBnO,CAAK,EACnFoO,GAAkBpO,GAASqO,GAAoBrO,EAAOsO,GAAaR,EAAO,EAC1EI,EAAmBlO,GAAS+M,GAAoB,KAAK/M,CAAK,EAC1DuO,GAAoBvO,GAASqO,GAAoBrO,EAAOwO,GAAeX,EAAY,EACnFY,GAAoBzO,GAASqO,GAAoBrO,EAAO0O,GAAelB,CAAQ,EAC/EmB,GAAsB3O,GAASqO,GAAoBrO,EAAO4O,GAAiBd,EAAO,EAClFe,GAAmB7O,GAASqO,GAAoBrO,EAAO8O,GAAcd,EAAO,EAC5Ee,GAAoB/O,GAASqO,GAAoBrO,EAAOgP,GAAejB,EAAQ,EAC/EI,EAAsBnO,GAASgN,GAAuB,KAAKhN,CAAK,EAChEiP,GAA4BjP,GAASkP,GAAuBlP,EAAOwO,EAAa,EAChFW,GAAgCnP,GAASkP,GAAuBlP,EAAOoP,EAAiB,EACxFC,GAA8BrP,GAASkP,GAAuBlP,EAAO4O,EAAe,EACpFU,GAA0BtP,GAASkP,GAAuBlP,EAAOsO,EAAW,EAC5EiB,GAA2BvP,GAASkP,GAAuBlP,EAAO8O,EAAY,EAC9EU,GAA4BxP,GAASkP,GAAuBlP,EAAOgP,GAAe,EAAI,EAEtFX,GAAsB,CAACrO,EAAOyP,EAAWC,IAAc,CAC3D,MAAMtE,EAAS2B,GAAoB,KAAK/M,CAAK,EAC7C,OAAIoL,EACEA,EAAO,CAAC,EACHqE,EAAUrE,EAAO,CAAC,CAAC,EAErBsE,EAAUtE,EAAO,CAAC,CAAC,EAErB,EACT,EACM8D,GAAyB,CAAClP,EAAOyP,EAAWE,EAAqB,KAAU,CAC/E,MAAMvE,EAAS4B,GAAuB,KAAKhN,CAAK,EAChD,OAAIoL,EACEA,EAAO,CAAC,EACHqE,EAAUrE,EAAO,CAAC,CAAC,EAErBuE,EAEF,EACT,EAEMf,GAAkBgB,GAASA,IAAU,YAAcA,IAAU,aAC7Dd,GAAec,GAASA,IAAU,SAAWA,IAAU,MACvDtB,GAAcsB,GAASA,IAAU,UAAYA,IAAU,QAAUA,IAAU,UAC3EpB,GAAgBoB,GAASA,IAAU,SACnClB,GAAgBkB,GAASA,IAAU,SACnCR,GAAoBQ,GAASA,IAAU,cACvCZ,GAAgBY,GAASA,IAAU,SA2BnCC,GAAmB,IAAM,CAM7B,MAAMC,EAAajD,EAAU,OAAO,EAC9BkD,EAAYlD,EAAU,MAAM,EAC5BmD,EAAYnD,EAAU,MAAM,EAC5BoD,EAAkBpD,EAAU,aAAa,EACzCqD,EAAgBrD,EAAU,UAAU,EACpCsD,EAAetD,EAAU,SAAS,EAClCuD,EAAkBvD,EAAU,YAAY,EACxCwD,EAAiBxD,EAAU,WAAW,EACtCyD,EAAezD,EAAU,SAAS,EAClC0D,EAAc1D,EAAU,QAAQ,EAChC2D,EAAc3D,EAAU,QAAQ,EAChC4D,EAAmB5D,EAAU,cAAc,EAC3C6D,EAAkB7D,EAAU,aAAa,EACzC8D,EAAkB9D,EAAU,aAAa,EACzC+D,EAAY/D,EAAU,MAAM,EAC5BgE,EAAmBhE,EAAU,aAAa,EAC1CiE,EAAcjE,EAAU,QAAQ,EAChCkE,EAAYlE,EAAU,MAAM,EAC5BmE,EAAenE,EAAU,SAAS,EAQlCoE,EAAa,IAAM,CAAC,OAAQ,QAAS,MAAO,aAAc,OAAQ,OAAQ,QAAS,QAAQ,EAC3FC,EAAgB,IAAM,CAAC,SAAU,MAAO,SAAU,OAAQ,QAAS,WAEzE,WAAY,YAEZ,YAAa,eAEb,eAAgB,cAEhB,aAAa,EACPC,EAA6B,IAAM,CAAC,GAAGD,EAAa,EAAI/C,EAAqBD,CAAgB,EAC7FkD,EAAgB,IAAM,CAAC,OAAQ,SAAU,OAAQ,UAAW,QAAQ,EACpEC,EAAkB,IAAM,CAAC,OAAQ,UAAW,MAAM,EAClDC,EAA0B,IAAM,CAACnD,EAAqBD,EAAkBoC,CAAY,EACpFiB,EAAa,IAAM,CAAChE,GAAY,OAAQ,OAAQ,GAAG+D,GAAyB,EAC5EE,EAA4B,IAAM,CAAC/D,EAAW,OAAQ,UAAWU,EAAqBD,CAAgB,EACtGuD,EAA6B,IAAM,CAAC,OAAQ,CAChD,KAAM,CAAC,OAAQhE,EAAWU,EAAqBD,CAAgB,CACnE,EAAKT,EAAWU,EAAqBD,CAAgB,EAC7CwD,GAA4B,IAAM,CAACjE,EAAW,OAAQU,EAAqBD,CAAgB,EAC3FyD,EAAwB,IAAM,CAAC,OAAQ,MAAO,MAAO,KAAMxD,EAAqBD,CAAgB,EAChG0D,EAAwB,IAAM,CAAC,QAAS,MAAO,SAAU,UAAW,SAAU,SAAU,UAAW,WAAY,cAAe,UAAU,EACxIC,EAA0B,IAAM,CAAC,QAAS,MAAO,SAAU,UAAW,cAAe,UAAU,EAC/FC,EAAc,IAAM,CAAC,OAAQ,GAAGR,EAAuB,CAAE,EACzDS,EAAc,IAAM,CAACxE,GAAY,OAAQ,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,GAAG+D,GAAyB,EAC5IU,EAAa,IAAM,CAAClC,EAAY3B,EAAqBD,CAAgB,EACrE+D,GAAkB,IAAM,CAAC,GAAGf,EAAa,EAAI7B,GAA6BV,GAAqB,CACnG,SAAU,CAACR,EAAqBD,CAAgB,CACpD,CAAG,EACKgE,GAAgB,IAAM,CAAC,YAAa,CACxC,OAAQ,CAAC,GAAI,IAAK,IAAK,QAAS,OAAO,CAC3C,CAAG,EACKC,EAAc,IAAM,CAAC,OAAQ,QAAS,UAAW7C,GAAyBlB,GAAiB,CAC/F,KAAM,CAACD,EAAqBD,CAAgB,CAChD,CAAG,EACKkE,EAA4B,IAAM,CAAC1E,GAAWuB,GAA2BV,EAAiB,EAC1F8D,EAAc,IAAM,CAE1B,GAAI,OAAQ,OAAQ9B,EAAapC,EAAqBD,CAAgB,EAChEoE,EAAmB,IAAM,CAAC,GAAI9E,EAAUyB,GAA2BV,EAAiB,EACpFgE,GAAiB,IAAM,CAAC,QAAS,SAAU,SAAU,QAAQ,EAC7DC,GAAiB,IAAM,CAAC,SAAU,WAAY,SAAU,UAAW,SAAU,UAAW,cAAe,aAAc,aAAc,aAAc,aAAc,YAAa,MAAO,aAAc,QAAS,YAAY,EACtNC,EAAyB,IAAM,CAACjF,EAAUE,GAAW2B,GAA6BV,EAAmB,EACrG+D,GAAY,IAAM,CAExB,GAAI,OAAQ9B,EAAWzC,EAAqBD,CAAgB,EACtDyE,GAAc,IAAM,CAAC,OAAQnF,EAAUW,EAAqBD,CAAgB,EAC5E0E,GAAa,IAAM,CAAC,OAAQpF,EAAUW,EAAqBD,CAAgB,EAC3E2E,GAAY,IAAM,CAACrF,EAAUW,EAAqBD,CAAgB,EAClE4E,GAAiB,IAAM,CAACvF,GAAY,OAAQ,GAAG+D,EAAuB,CAAE,EAC9E,MAAO,CACL,UAAW,IACX,MAAO,CACL,QAAS,CAAC,OAAQ,OAAQ,QAAS,QAAQ,EAC3C,OAAQ,CAAC,OAAO,EAChB,KAAM,CAAC3D,CAAY,EACnB,WAAY,CAACA,CAAY,EACzB,MAAO,CAACC,EAAK,EACb,UAAW,CAACD,CAAY,EACxB,cAAe,CAACA,CAAY,EAC5B,KAAM,CAAC,KAAM,MAAO,QAAQ,EAC5B,KAAM,CAACM,EAAiB,EACxB,cAAe,CAAC,OAAQ,aAAc,QAAS,SAAU,SAAU,WAAY,OAAQ,YAAa,OAAO,EAC3G,eAAgB,CAACN,CAAY,EAC7B,QAAS,CAAC,OAAQ,QAAS,OAAQ,SAAU,UAAW,OAAO,EAC/D,YAAa,CAAC,WAAY,OAAQ,SAAU,WAAY,UAAW,MAAM,EACzE,OAAQ,CAACA,CAAY,EACrB,OAAQ,CAACA,CAAY,EACrB,QAAS,CAAC,KAAMH,CAAQ,EACxB,KAAM,CAACG,CAAY,EACnB,cAAe,CAACA,CAAY,EAC5B,SAAU,CAAC,UAAW,QAAS,SAAU,OAAQ,QAAS,QAAQ,CACnE,EACD,YAAa,CAQX,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,SAAUJ,GAAYW,EAAkBC,EAAqB2C,CAAW,CACjG,CAAO,EAMD,UAAW,CAAC,WAAW,EAKvB,QAAS,CAAC,CACR,QAAS,CAACtD,EAAUU,EAAkBC,EAAqBkC,CAAc,CACjF,CAAO,EAKD,cAAe,CAAC,CACd,cAAeY,EAAU,CACjC,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgBA,EAAU,CAClC,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,OAAQ,QAAS,aAAc,cAAc,CACtE,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkB,CAAC,QAAS,OAAO,CAC3C,CAAO,EAKD,IAAK,CAAC,CACJ,IAAK,CAAC,SAAU,SAAS,CACjC,CAAO,EAKD,QAAS,CAAC,QAAS,eAAgB,SAAU,OAAQ,cAAe,QAAS,eAAgB,gBAAiB,aAAc,eAAgB,qBAAsB,qBAAsB,qBAAsB,kBAAmB,YAAa,YAAa,OAAQ,cAAe,WAAY,YAAa,QAAQ,EAKnT,GAAI,CAAC,UAAW,aAAa,EAK7B,MAAO,CAAC,CACN,MAAO,CAAC,QAAS,OAAQ,OAAQ,QAAS,KAAK,CACvD,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAAC,OAAQ,QAAS,OAAQ,OAAQ,QAAS,KAAK,CAC/D,CAAO,EAKD,UAAW,CAAC,UAAW,gBAAgB,EAKvC,aAAc,CAAC,CACb,OAAQ,CAAC,UAAW,QAAS,OAAQ,OAAQ,YAAY,CACjE,CAAO,EAKD,kBAAmB,CAAC,CAClB,OAAQE,EAA0B,CAC1C,CAAO,EAKD,SAAU,CAAC,CACT,SAAUC,EAAa,CAC/B,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAa,CACnC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAa,CACnC,CAAO,EAKD,WAAY,CAAC,CACX,WAAYC,EAAe,CACnC,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgBA,EAAe,CACvC,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgBA,EAAe,CACvC,CAAO,EAKD,SAAU,CAAC,SAAU,QAAS,WAAY,WAAY,QAAQ,EAK9D,MAAO,CAAC,CACN,MAAOE,EAAU,CACzB,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,EAAU,CAC7B,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,EAAU,CAC7B,CAAO,EAKD,MAAO,CAAC,CACN,MAAOA,EAAU,CACzB,CAAO,EAKD,IAAK,CAAC,CACJ,IAAKA,EAAU,CACvB,CAAO,EAKD,IAAK,CAAC,CACJ,IAAKA,EAAU,CACvB,CAAO,EAKD,MAAO,CAAC,CACN,MAAOA,EAAU,CACzB,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQA,EAAU,CAC1B,CAAO,EAKD,KAAM,CAAC,CACL,KAAMA,EAAU,CACxB,CAAO,EAKD,WAAY,CAAC,UAAW,YAAa,UAAU,EAK/C,EAAG,CAAC,CACF,EAAG,CAAC9D,EAAW,OAAQU,EAAqBD,CAAgB,CACpE,CAAO,EAQD,MAAO,CAAC,CACN,MAAO,CAACX,GAAY,OAAQ,OAAQ8C,EAAgB,GAAGiB,EAAyB,CAAA,CACxF,CAAO,EAKD,iBAAkB,CAAC,CACjB,KAAM,CAAC,MAAO,cAAe,MAAO,aAAa,CACzD,CAAO,EAKD,YAAa,CAAC,CACZ,KAAM,CAAC,SAAU,OAAQ,cAAc,CAC/C,CAAO,EAKD,KAAM,CAAC,CACL,KAAM,CAAC9D,EAAUD,GAAY,OAAQ,UAAW,OAAQW,CAAgB,CAChF,CAAO,EAKD,KAAM,CAAC,CACL,KAAM,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CAClE,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CACpE,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAACT,EAAW,QAAS,OAAQ,OAAQU,EAAqBD,CAAgB,CACzF,CAAO,EAKD,YAAa,CAAC,CACZ,YAAasD,EAAyB,CAC9C,CAAO,EAKD,gBAAiB,CAAC,CAChB,IAAKC,EAA0B,CACvC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaC,GAAyB,CAC9C,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAAyB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaF,EAAyB,CAC9C,CAAO,EAKD,gBAAiB,CAAC,CAChB,IAAKC,EAA0B,CACvC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaC,GAAyB,CAC9C,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAAyB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,MAAO,MAAO,QAAS,YAAa,WAAW,CACrE,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaC,EAAqB,CAC1C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAqB,CAC1C,CAAO,EAKD,IAAK,CAAC,CACJ,IAAKL,EAAuB,CACpC,CAAO,EAKD,QAAS,CAAC,CACR,QAASA,EAAuB,CACxC,CAAO,EAKD,QAAS,CAAC,CACR,QAASA,EAAuB,CACxC,CAAO,EAKD,kBAAmB,CAAC,CAClB,QAAS,CAAC,GAAGM,EAAqB,EAAI,QAAQ,CACtD,CAAO,EAKD,gBAAiB,CAAC,CAChB,gBAAiB,CAAC,GAAGC,EAAuB,EAAI,QAAQ,CAChE,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,OAAQ,GAAGA,EAAyB,CAAA,CAC7D,CAAO,EAKD,gBAAiB,CAAC,CAChB,QAAS,CAAC,SAAU,GAAGD,EAAuB,CAAA,CACtD,CAAO,EAKD,cAAe,CAAC,CACd,MAAO,CAAC,GAAGC,IAA2B,CACpC,SAAU,CAAC,GAAI,MAAM,CACtB,CAAA,CACT,CAAO,EAKD,aAAc,CAAC,CACb,KAAM,CAAC,OAAQ,GAAGA,IAA2B,CAC3C,SAAU,CAAC,GAAI,MAAM,CACtB,CAAA,CACT,CAAO,EAKD,gBAAiB,CAAC,CAChB,gBAAiBD,EAAqB,CAC9C,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAAC,GAAGC,EAAuB,EAAI,UAAU,CAChE,CAAO,EAKD,aAAc,CAAC,CACb,aAAc,CAAC,OAAQ,GAAGA,EAAyB,CAAA,CAC3D,CAAO,EAMD,EAAG,CAAC,CACF,EAAGP,EAAuB,CAClC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAuB,CACnC,CAAO,EAKD,EAAG,CAAC,CACF,EAAGQ,EAAW,CACtB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAW,CACvB,CAAO,EAKD,UAAW,CAAC,CACV,UAAWR,EAAuB,CAC1C,CAAO,EAKD,kBAAmB,CAAC,iBAAiB,EAKrC,UAAW,CAAC,CACV,UAAWA,EAAuB,CAC1C,CAAO,EAKD,kBAAmB,CAAC,iBAAiB,EAQrC,KAAM,CAAC,CACL,KAAMS,EAAW,CACzB,CAAO,EAKD,EAAG,CAAC,CACF,EAAG,CAAC1B,EAAgB,SAAU,GAAG0B,EAAa,CAAA,CACtD,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC1B,EAAgB,SAC1B,OAAQ,GAAG0B,EAAa,CAAA,CAChC,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC1B,EAAgB,SAAU,OACpC,QACA,CACE,OAAQ,CAACD,CAAe,CACzB,EAAE,GAAG2B,EAAa,CAAA,CAC3B,CAAO,EAKD,EAAG,CAAC,CACF,EAAG,CAAC,SAAU,KAAM,GAAGA,EAAa,CAAA,CAC5C,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,SAAU,KAAM,OAAQ,GAAGA,EAAa,CAAA,CAC1D,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,SAAU,KAAM,GAAGA,EAAa,CAAA,CAClD,CAAO,EAQD,YAAa,CAAC,CACZ,KAAM,CAAC,OAAQ/B,EAAWf,GAA2BV,EAAiB,CAC9E,CAAO,EAKD,iBAAkB,CAAC,cAAe,sBAAsB,EAKxD,aAAc,CAAC,SAAU,YAAY,EAKrC,cAAe,CAAC,CACd,KAAM,CAAC0B,EAAiB9B,EAAqBM,EAAiB,CACtE,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,kBAAmB,kBAAmB,YAAa,iBAAkB,SAAU,gBAAiB,WAAY,iBAAkB,iBAAkBf,GAAWQ,CAAgB,CACpM,CAAO,EAKD,cAAe,CAAC,CACd,KAAM,CAACiB,GAA+BjB,EAAkB6B,CAAS,CACzE,CAAO,EAKD,aAAc,CAAC,aAAa,EAK5B,cAAe,CAAC,SAAS,EAKzB,mBAAoB,CAAC,cAAc,EAKnC,aAAc,CAAC,cAAe,eAAe,EAK7C,cAAe,CAAC,oBAAqB,cAAc,EAKnD,eAAgB,CAAC,qBAAsB,mBAAmB,EAK1D,SAAU,CAAC,CACT,SAAU,CAACG,EAAe/B,EAAqBD,CAAgB,CACvE,CAAO,EAKD,aAAc,CAAC,CACb,aAAc,CAACV,EAAU,OAAQW,EAAqBM,EAAiB,CAC/E,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CACT0B,EAAc,GAAGmB,EAAyB,CAAA,CAClD,CAAO,EAKD,aAAc,CAAC,CACb,aAAc,CAAC,OAAQnD,EAAqBD,CAAgB,CACpE,CAAO,EAKD,sBAAuB,CAAC,CACtB,KAAM,CAAC,SAAU,SAAS,CAClC,CAAO,EAKD,kBAAmB,CAAC,CAClB,KAAM,CAAC,OAAQ,UAAW,OAAQC,EAAqBD,CAAgB,CAC/E,CAAO,EAKD,iBAAkB,CAAC,CACjB,KAAM,CAAC,OAAQ,SAAU,QAAS,UAAW,QAAS,KAAK,CACnE,CAAO,EAMD,oBAAqB,CAAC,CACpB,YAAa8D,EAAU,CAC/B,CAAO,EAKD,aAAc,CAAC,CACb,KAAMA,EAAU,CACxB,CAAO,EAKD,kBAAmB,CAAC,YAAa,WAAY,eAAgB,cAAc,EAK3E,wBAAyB,CAAC,CACxB,WAAY,CAAC,GAAGO,GAAc,EAAI,MAAM,CAChD,CAAO,EAKD,4BAA6B,CAAC,CAC5B,WAAY,CAAC/E,EAAU,YAAa,OAAQW,EAAqBI,EAAiB,CAC1F,CAAO,EAKD,wBAAyB,CAAC,CACxB,WAAYyD,EAAU,CAC9B,CAAO,EAKD,mBAAoB,CAAC,CACnB,mBAAoB,CAACxE,EAAU,OAAQW,EAAqBD,CAAgB,CACpF,CAAO,EAKD,iBAAkB,CAAC,YAAa,YAAa,aAAc,aAAa,EAKxE,gBAAiB,CAAC,WAAY,gBAAiB,WAAW,EAK1D,YAAa,CAAC,CACZ,KAAM,CAAC,OAAQ,SAAU,UAAW,QAAQ,CACpD,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQoD,EAAuB,CACvC,CAAO,EAKD,iBAAkB,CAAC,CACjB,MAAO,CAAC,WAAY,MAAO,SAAU,SAAU,WAAY,cAAe,MAAO,QAASnD,EAAqBD,CAAgB,CACvI,CAAO,EAKD,WAAY,CAAC,CACX,WAAY,CAAC,SAAU,SAAU,MAAO,WAAY,WAAY,cAAc,CACtF,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAAC,SAAU,QAAS,MAAO,MAAM,CAChD,CAAO,EAKD,KAAM,CAAC,CACL,KAAM,CAAC,aAAc,WAAY,QAAQ,CACjD,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,OAAQ,SAAU,MAAM,CAC1C,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,OAAQC,EAAqBD,CAAgB,CAC/D,CAAO,EAQD,gBAAiB,CAAC,CAChB,GAAI,CAAC,QAAS,QAAS,QAAQ,CACvC,CAAO,EAKD,UAAW,CAAC,CACV,UAAW,CAAC,SAAU,UAAW,UAAW,MAAM,CAC1D,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,SAAU,UAAW,SAAS,CACpD,CAAO,EAKD,cAAe,CAAC,CACd,GAAI+D,GAAe,CAC3B,CAAO,EAKD,YAAa,CAAC,CACZ,GAAIC,GAAa,CACzB,CAAO,EAKD,UAAW,CAAC,CACV,GAAIC,EAAW,CACvB,CAAO,EAKD,WAAY,CAAC,CACX,GAAI,CAAC,OAAQ,CACX,OAAQ,CAAC,CACP,GAAI,CAAC,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,IAAI,CAC3D,EAAa1E,EAAWU,EAAqBD,CAAgB,EACnD,OAAQ,CAAC,GAAIC,EAAqBD,CAAgB,EAClD,MAAO,CAACT,EAAWU,EAAqBD,CAAgB,CACzD,EAAEqB,GAA0BV,EAAgB,CACrD,CAAO,EAKD,WAAY,CAAC,CACX,GAAImD,EAAU,CACtB,CAAO,EAKD,oBAAqB,CAAC,CACpB,KAAMI,EAAyB,CACvC,CAAO,EAKD,mBAAoB,CAAC,CACnB,IAAKA,EAAyB,CACtC,CAAO,EAKD,kBAAmB,CAAC,CAClB,GAAIA,EAAyB,CACrC,CAAO,EAKD,gBAAiB,CAAC,CAChB,KAAMJ,EAAU,CACxB,CAAO,EAKD,eAAgB,CAAC,CACf,IAAKA,EAAU,CACvB,CAAO,EAKD,cAAe,CAAC,CACd,GAAIA,EAAU,CACtB,CAAO,EAQD,QAAS,CAAC,CACR,QAASK,EAAW,CAC5B,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAW,CAChC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAW,CAChC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAW,CAChC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAW,CAChC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAW,CAChC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAW,CAChC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAW,CACjC,CAAO,EAKD,WAAY,CAAC,CACX,OAAQC,EAAgB,CAChC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAgB,CACpC,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,EAAgB,CACpC,CAAO,EAKD,mBAAoB,CAAC,kBAAkB,EAKvC,WAAY,CAAC,CACX,WAAYA,EAAgB,CACpC,CAAO,EAKD,mBAAoB,CAAC,kBAAkB,EAKvC,eAAgB,CAAC,CACf,OAAQ,CAAC,GAAGC,GAAgB,EAAE,SAAU,MAAM,CACtD,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQ,CAAC,GAAGA,GAAgB,EAAE,SAAU,MAAM,CACtD,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQP,EAAU,CAC1B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAU,CAC9B,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQA,EAAU,CAC1B,CAAO,EAKD,gBAAiB,CAAC,CAChB,QAAS,CAAC,GAAGO,GAAgB,EAAE,OAAQ,QAAQ,CACvD,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkB,CAAC/E,EAAUW,EAAqBD,CAAgB,CAC1E,CAAO,EAKD,YAAa,CAAC,CACZ,QAAS,CAAC,GAAIV,EAAUyB,GAA2BV,EAAiB,CAC5E,CAAO,EAKD,gBAAiB,CAAC,CAChB,QAASyD,EAAU,CAC3B,CAAO,EAQD,OAAQ,CAAC,CACP,OAAQ,CAER,GAAI,OAAQxB,EAAahB,GAA2BT,EAAiB,CAC7E,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQiD,EAAU,CAC1B,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,OAAQvB,EAAkBjB,GAA2BT,EAAiB,CAC/F,CAAO,EAKD,qBAAsB,CAAC,CACrB,eAAgBiD,EAAU,CAClC,CAAO,EAKD,SAAU,CAAC,CACT,KAAMM,EAAgB,CAC9B,CAAO,EAOD,eAAgB,CAAC,YAAY,EAK7B,aAAc,CAAC,CACb,KAAMN,EAAU,CACxB,CAAO,EAOD,gBAAiB,CAAC,CAChB,cAAe,CAACxE,EAAUe,EAAiB,CACnD,CAAO,EAOD,oBAAqB,CAAC,CACpB,cAAeyD,EAAU,CACjC,CAAO,EAKD,eAAgB,CAAC,CACf,aAAcM,EAAgB,CACtC,CAAO,EAKD,mBAAoB,CAAC,CACnB,aAAcN,EAAU,CAChC,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAAC,OAAQtB,EAAiBlB,GAA2BT,EAAiB,CAC7F,CAAO,EAKD,oBAAqB,CAAC,CACpB,cAAeiD,EAAU,CACjC,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAACxE,EAAUW,EAAqBD,CAAgB,CACjE,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,GAAGsE,GAAgB,EAAE,cAAe,cAAc,CACxE,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,GAAc,CAClC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,SAAU,UAAW,UAAW,OAAQ,SAAU,MAAM,CACvE,EAAE,cAAc,EAKjB,iBAAkB,CAAC,CACjB,KAAM,CAAC,MAAO,WAAY,YAAa,SAAS,CACxD,CAAO,EAKD,wBAAyB,CAAC,CACxB,cAAe,CAAChF,CAAQ,CAChC,CAAO,EACD,6BAA8B,CAAC,CAC7B,mBAAoBiF,EAAsB,CAClD,CAAO,EACD,2BAA4B,CAAC,CAC3B,iBAAkBA,EAAsB,CAChD,CAAO,EACD,+BAAgC,CAAC,CAC/B,mBAAoBT,EAAU,CACtC,CAAO,EACD,6BAA8B,CAAC,CAC7B,iBAAkBA,EAAU,CACpC,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAsB,CAC7C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAsB,CAC3C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAU,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAU,CAC/B,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAsB,CAC7C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAsB,CAC3C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAU,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAU,CAC/B,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAsB,CAC7C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAsB,CAC3C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAU,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAU,CAC/B,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAsB,CAC7C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAsB,CAC3C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAU,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAU,CAC/B,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAsB,CAC7C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAsB,CAC3C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAU,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAU,CAC/B,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAsB,CAC7C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAsB,CAC3C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAU,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAU,CAC/B,CAAO,EACD,oBAAqB,CAAC,CACpB,cAAe,CAAC7D,EAAqBD,CAAgB,CAC7D,CAAO,EACD,6BAA8B,CAAC,CAC7B,mBAAoBuE,EAAsB,CAClD,CAAO,EACD,2BAA4B,CAAC,CAC3B,iBAAkBA,EAAsB,CAChD,CAAO,EACD,+BAAgC,CAAC,CAC/B,mBAAoBT,EAAU,CACtC,CAAO,EACD,6BAA8B,CAAC,CAC7B,iBAAkBA,EAAU,CACpC,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAe,CAAC,SAAU,SAAS,CAC3C,CAAO,EACD,yBAA0B,CAAC,CACzB,cAAe,CAAC,CACd,QAAS,CAAC,OAAQ,QAAQ,EAC1B,SAAU,CAAC,OAAQ,QAAQ,CAC5B,CAAA,CACT,CAAO,EACD,wBAAyB,CAAC,CACxB,iBAAkBd,EAAa,CACvC,CAAO,EACD,uBAAwB,CAAC,CACvB,aAAc,CAAC1D,CAAQ,CAC/B,CAAO,EACD,4BAA6B,CAAC,CAC5B,kBAAmBiF,EAAsB,CACjD,CAAO,EACD,0BAA2B,CAAC,CAC1B,gBAAiBA,EAAsB,CAC/C,CAAO,EACD,8BAA+B,CAAC,CAC9B,kBAAmBT,EAAU,CACrC,CAAO,EACD,4BAA6B,CAAC,CAC5B,gBAAiBA,EAAU,CACnC,CAAO,EAKD,YAAa,CAAC,CACZ,KAAM,CAAC,QAAS,YAAa,OAAO,CAC5C,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAAC,SAAU,UAAW,UAAW,OAAQ,SAAU,MAAM,CAChF,CAAO,EAKD,gBAAiB,CAAC,CAChB,KAAMC,GAAe,CAC7B,CAAO,EAKD,cAAe,CAAC,CACd,KAAMC,GAAa,CAC3B,CAAO,EAKD,YAAa,CAAC,CACZ,KAAMC,EAAW,CACzB,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,QAAS,WAAW,CAC1C,CAAO,EAKD,aAAc,CAAC,CACb,KAAM,CAAC,OAAQhE,EAAqBD,CAAgB,CAC5D,CAAO,EAQD,OAAQ,CAAC,CACP,OAAQ,CAER,GAAI,OAAQC,EAAqBD,CAAgB,CACzD,CAAO,EAKD,KAAM,CAAC,CACL,KAAMwE,GAAS,CACvB,CAAO,EAKD,WAAY,CAAC,CACX,WAAY,CAAClF,EAAUW,EAAqBD,CAAgB,CACpE,CAAO,EAKD,SAAU,CAAC,CACT,SAAU,CAACV,EAAUW,EAAqBD,CAAgB,CAClE,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAEf,GAAI,OAAQyC,EAAiBnB,GAA2BT,EAAiB,CACjF,CAAO,EAKD,oBAAqB,CAAC,CACpB,cAAeiD,EAAU,CACjC,CAAO,EAKD,UAAW,CAAC,CACV,UAAW,CAAC,GAAIxE,EAAUW,EAAqBD,CAAgB,CACvE,CAAO,EAKD,aAAc,CAAC,CACb,aAAc,CAACV,EAAUW,EAAqBD,CAAgB,CACtE,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CACpE,CAAO,EAKD,SAAU,CAAC,CACT,SAAU,CAACV,EAAUW,EAAqBD,CAAgB,CAClE,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CACnE,CAAO,EAKD,kBAAmB,CAAC,CAClB,kBAAmB,CAEnB,GAAI,OAAQC,EAAqBD,CAAgB,CACzD,CAAO,EAKD,gBAAiB,CAAC,CAChB,gBAAiBwE,GAAS,CAClC,CAAO,EAKD,sBAAuB,CAAC,CACtB,sBAAuB,CAAClF,EAAUW,EAAqBD,CAAgB,CAC/E,CAAO,EAKD,oBAAqB,CAAC,CACpB,oBAAqB,CAACV,EAAUW,EAAqBD,CAAgB,CAC7E,CAAO,EAKD,qBAAsB,CAAC,CACrB,qBAAsB,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CAClF,CAAO,EAKD,sBAAuB,CAAC,CACtB,sBAAuB,CAACV,EAAUW,EAAqBD,CAAgB,CAC/E,CAAO,EAKD,kBAAmB,CAAC,CAClB,kBAAmB,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CAC/E,CAAO,EAKD,mBAAoB,CAAC,CACnB,mBAAoB,CAACV,EAAUW,EAAqBD,CAAgB,CAC5E,CAAO,EAKD,oBAAqB,CAAC,CACpB,oBAAqB,CAACV,EAAUW,EAAqBD,CAAgB,CAC7E,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkB,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CAC9E,CAAO,EAQD,kBAAmB,CAAC,CAClB,OAAQ,CAAC,WAAY,UAAU,CACvC,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkBoD,EAAuB,CACjD,CAAO,EAKD,mBAAoB,CAAC,CACnB,mBAAoBA,EAAuB,CACnD,CAAO,EAKD,mBAAoB,CAAC,CACnB,mBAAoBA,EAAuB,CACnD,CAAO,EAKD,eAAgB,CAAC,CACf,MAAO,CAAC,OAAQ,OAAO,CAC/B,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,MAAO,QAAQ,CACjC,CAAO,EAQD,WAAY,CAAC,CACX,WAAY,CAAC,GAAI,MAAO,SAAU,UAAW,SAAU,YAAa,OAAQnD,EAAqBD,CAAgB,CACzH,CAAO,EAKD,sBAAuB,CAAC,CACtB,WAAY,CAAC,SAAU,UAAU,CACzC,CAAO,EAKD,SAAU,CAAC,CACT,SAAU,CAACV,EAAU,UAAWW,EAAqBD,CAAgB,CAC7E,CAAO,EAKD,KAAM,CAAC,CACL,KAAM,CAAC,SAAU,UAAW6C,EAAW5C,EAAqBD,CAAgB,CACpF,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAACV,EAAUW,EAAqBD,CAAgB,CAC/D,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,OAAQ8C,EAAc7C,EAAqBD,CAAgB,CAC7E,CAAO,EAQD,SAAU,CAAC,CACT,SAAU,CAAC,SAAU,SAAS,CACtC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC2C,EAAkB1C,EAAqBD,CAAgB,CAC7E,CAAO,EAKD,qBAAsB,CAAC,CACrB,qBAAsBiD,EAA0B,CACxD,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQwB,GAAW,CAC3B,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,GAAW,CAC/B,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,GAAW,CAC/B,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,GAAW,CAC/B,CAAO,EAKD,MAAO,CAAC,CACN,MAAOC,GAAU,CACzB,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAAU,CAC7B,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAAU,CAC7B,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAAU,CAC7B,CAAO,EAKD,WAAY,CAAC,UAAU,EAKvB,KAAM,CAAC,CACL,KAAMC,GAAS,CACvB,CAAO,EAKD,SAAU,CAAC,CACT,SAAUA,GAAS,CAC3B,CAAO,EAKD,SAAU,CAAC,CACT,SAAUA,GAAS,CAC3B,CAAO,EAKD,UAAW,CAAC,CACV,UAAW,CAAC1E,EAAqBD,EAAkB,GAAI,OAAQ,MAAO,KAAK,CACnF,CAAO,EAKD,mBAAoB,CAAC,CACnB,OAAQiD,EAA0B,CAC1C,CAAO,EAKD,kBAAmB,CAAC,CAClB,UAAW,CAAC,KAAM,MAAM,CAChC,CAAO,EAKD,UAAW,CAAC,CACV,UAAW2B,GAAc,CACjC,CAAO,EAKD,cAAe,CAAC,CACd,cAAeA,GAAc,CACrC,CAAO,EAKD,cAAe,CAAC,CACd,cAAeA,GAAc,CACrC,CAAO,EAKD,cAAe,CAAC,CACd,cAAeA,GAAc,CACrC,CAAO,EAKD,iBAAkB,CAAC,gBAAgB,EAQnC,OAAQ,CAAC,CACP,OAAQd,EAAU,CAC1B,CAAO,EAKD,WAAY,CAAC,CACX,WAAY,CAAC,OAAQ,MAAM,CACnC,CAAO,EAKD,cAAe,CAAC,CACd,MAAOA,EAAU,CACzB,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQ,CAAC,SAAU,OAAQ,QAAS,aAAc,YAAa,YAAY,CACnF,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,UAAW,UAAW,OAAQ,OAAQ,OAAQ,OAAQ,cAAe,OAAQ,eAAgB,WAAY,OAAQ,YAAa,gBAAiB,QAAS,OAAQ,UAAW,OAAQ,WAAY,aAAc,aAAc,aAAc,WAAY,WAAY,WAAY,WAAY,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,cAAe,cAAe,UAAW,WAAY7D,EAAqBD,CAAgB,CAC1d,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,QAAS,SAAS,CAC3C,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkB,CAAC,OAAQ,MAAM,CACzC,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,GAAI,IAAK,GAAG,CACrC,CAAO,EAKD,kBAAmB,CAAC,CAClB,OAAQ,CAAC,OAAQ,QAAQ,CACjC,CAAO,EAKD,WAAY,CAAC,CACX,WAAYoD,EAAuB,CAC3C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,EAAuB,CAC3C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAuB,CAC5C,CAAO,EAKD,aAAc,CAAC,CACb,KAAM,CAAC,QAAS,MAAO,SAAU,YAAY,CACrD,CAAO,EAKD,YAAa,CAAC,CACZ,KAAM,CAAC,SAAU,QAAQ,CACjC,CAAO,EAKD,YAAa,CAAC,CACZ,KAAM,CAAC,OAAQ,IAAK,IAAK,MAAM,CACvC,CAAO,EAKD,kBAAmB,CAAC,CAClB,KAAM,CAAC,YAAa,WAAW,CACvC,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAAC,OAAQ,OAAQ,cAAc,CAC9C,CAAO,EAKD,UAAW,CAAC,CACV,YAAa,CAAC,IAAK,OAAQ,OAAO,CAC1C,CAAO,EAKD,UAAW,CAAC,CACV,YAAa,CAAC,IAAK,KAAM,MAAM,CACvC,CAAO,EAKD,WAAY,CAAC,kBAAkB,EAK/B,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,OAAQ,MAAO,MAAM,CAC9C,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAAC,OAAQ,SAAU,WAAY,YAAanD,EAAqBD,CAAgB,CACxG,CAAO,EAQD,KAAM,CAAC,CACL,KAAM,CAAC,OAAQ,GAAG8D,EAAY,CAAA,CACtC,CAAO,EAKD,WAAY,CAAC,CACX,OAAQ,CAACxE,EAAUyB,GAA2BV,GAAmBE,EAAiB,CAC1F,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,GAAGuD,EAAY,CAAA,CACxC,CAAO,EAQD,sBAAuB,CAAC,CACtB,sBAAuB,CAAC,OAAQ,MAAM,CACvC,CAAA,CACF,EACD,uBAAwB,CACtB,SAAU,CAAC,aAAc,YAAY,EACrC,WAAY,CAAC,eAAgB,cAAc,EAC3C,MAAO,CAAC,UAAW,UAAW,QAAS,MAAO,MAAO,QAAS,SAAU,MAAM,EAC9E,UAAW,CAAC,QAAS,MAAM,EAC3B,UAAW,CAAC,MAAO,QAAQ,EAC3B,KAAM,CAAC,QAAS,OAAQ,QAAQ,EAChC,IAAK,CAAC,QAAS,OAAO,EACtB,EAAG,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EAClD,GAAI,CAAC,KAAM,IAAI,EACf,GAAI,CAAC,KAAM,IAAI,EACf,EAAG,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EAClD,GAAI,CAAC,KAAM,IAAI,EACf,GAAI,CAAC,KAAM,IAAI,EACf,KAAM,CAAC,IAAK,GAAG,EACf,YAAa,CAAC,SAAS,EACvB,aAAc,CAAC,cAAe,mBAAoB,aAAc,cAAe,cAAc,EAC7F,cAAe,CAAC,YAAY,EAC5B,mBAAoB,CAAC,YAAY,EACjC,aAAc,CAAC,YAAY,EAC3B,cAAe,CAAC,YAAY,EAC5B,eAAgB,CAAC,YAAY,EAC7B,aAAc,CAAC,UAAW,UAAU,EACpC,QAAS,CAAC,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,YAAY,EACtM,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,iBAAkB,CAAC,mBAAoB,kBAAkB,EACzD,WAAY,CAAC,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,YAAY,EAC3H,aAAc,CAAC,aAAc,YAAY,EACzC,aAAc,CAAC,aAAc,YAAY,EACzC,eAAgB,CAAC,iBAAkB,iBAAkB,iBAAkB,iBAAkB,iBAAkB,iBAAkB,iBAAkB,gBAAgB,EAC/J,iBAAkB,CAAC,iBAAkB,gBAAgB,EACrD,iBAAkB,CAAC,iBAAkB,gBAAgB,EACrD,UAAW,CAAC,cAAe,cAAe,gBAAgB,EAC1D,iBAAkB,CAAC,YAAa,cAAe,cAAe,aAAa,EAC3E,WAAY,CAAC,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,WAAW,EACnH,YAAa,CAAC,YAAa,WAAW,EACtC,YAAa,CAAC,YAAa,WAAW,EACtC,WAAY,CAAC,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,WAAW,EACnH,YAAa,CAAC,YAAa,WAAW,EACtC,YAAa,CAAC,YAAa,WAAW,EACtC,MAAO,CAAC,UAAW,UAAW,UAAU,EACxC,UAAW,CAAC,OAAO,EACnB,UAAW,CAAC,OAAO,EACnB,WAAY,CAAC,OAAO,CACrB,EACD,+BAAgC,CAC9B,YAAa,CAAC,SAAS,CACxB,EACD,wBAAyB,CAAC,IAAK,KAAM,QAAS,WAAY,SAAU,kBAAmB,OAAQ,eAAgB,aAAc,SAAU,cAAe,WAAW,CAClK,CACH,EAsDMe,GAAuB7G,GAAoB2D,EAAgB,ECr9F1D,SAASmD,KAAMC,EAAsB,CACnC,OAAAF,GAAQ3N,GAAK6N,CAAM,CAAC,CAC7B,CCCA,MAAMC,GAAiB3N,GACrB,yRACA,CACE,SAAU,CACR,QAAS,CACP,QAAS,yDACT,YACE,qEACF,QACE,iFACF,UACE,+DACF,MAAO,+CACP,KAAM,iDACR,EACA,KAAM,CACJ,QAAS,iBACT,GAAI,sBACJ,GAAI,uBACJ,KAAM,WAAA,CAEV,EACA,gBAAiB,CACf,QAAS,UACT,KAAM,SAAA,CACR,CAEJ,EAQM4N,EAASC,EAAM,WACnB,CAAC,CAAE,UAAAlQ,EAAW,QAAA4C,EAAS,KAAAnC,EAAM,QAAA0P,EAAU,GAAO,GAAGzS,CAAM,EAAGV,IAAQ,CAC1D,MAAAoT,EAAOD,EAAUE,GAAO,SAE5B,OAAAC,EAAA,IAACF,EAAA,CACC,UAAWN,EAAGE,GAAe,CAAE,QAAApN,EAAS,KAAAnC,EAAM,UAAAT,CAAA,CAAW,CAAC,EAC1D,IAAAhD,EACC,GAAGU,CAAA,CACN,CAAA,CAGN,EACAuS,EAAO,YAAc,SC9CrB,MAAMM,GAAQL,EAAM,WAClB,CAAC,CAAE,UAAAlQ,EAAW,KAAAwQ,EAAM,GAAG9S,CAAA,EAASV,IAE5BsT,EAAA,IAAC,QAAA,CACC,KAAAE,EACA,UAAWV,EACT,+VACA9P,CACF,EACA,IAAAhD,EACC,GAAGU,CAAA,CACN,CAGN,EACA6S,GAAM,YAAc,QCtBpB,MAAME,GAAmBC,GAAgB,CACvC,IAAIC,EACJ,MAAMC,EAA4B,IAAI,IAChCC,EAAW,CAACC,EAASC,IAAY,CACrC,MAAMC,EAAY,OAAOF,GAAY,WAAaA,EAAQH,CAAK,EAAIG,EACnE,GAAI,CAAC,OAAO,GAAGE,EAAWL,CAAK,EAAG,CAChC,MAAMM,EAAgBN,EACtBA,EAASI,IAA4B,OAAOC,GAAc,UAAYA,IAAc,MAAQA,EAAY,OAAO,OAAO,CAAE,EAAEL,EAAOK,CAAS,EAC1IJ,EAAU,QAASM,GAAaA,EAASP,EAAOM,CAAa,CAAC,CACpE,CACG,EACKE,EAAW,IAAMR,EAMjBS,EAAM,CAAE,SAAAP,EAAU,SAAAM,EAAU,gBALV,IAAME,EAKqB,UAJhCH,IACjBN,EAAU,IAAIM,CAAQ,EACf,IAAMN,EAAU,OAAOM,CAAQ,EAEsB,EACxDG,EAAeV,EAAQD,EAAYG,EAAUM,EAAUC,CAAG,EAChE,OAAOA,CACT,EACME,GAAeZ,GAAgBA,EAAcD,GAAgBC,CAAW,EAAID,GClB5Ec,GAAYC,GAAQA,EAC1B,SAASC,GAASL,EAAKM,EAAWH,GAAU,CAC1C,MAAMI,EAAQC,GAAM,qBAClBR,EAAI,UACJ,IAAMM,EAASN,EAAI,UAAU,EAC7B,IAAMM,EAASN,EAAI,gBAAiB,CAAA,CACrC,EACD,OAAAQ,GAAM,cAAcD,CAAK,EAClBA,CACT,CACA,MAAME,GAAcnB,GAAgB,CAClC,MAAMU,EAAME,GAAYZ,CAAW,EAC7BoB,EAAiBJ,GAAaD,GAASL,EAAKM,CAAQ,EAC1D,cAAO,OAAOI,EAAeV,CAAG,EACzBU,CACT,EACMC,GAAUrB,GAAwDmB,GC8PxE,SAASG,GAAkBC,EAAYzW,EAAS,CAC1C,IAAA0W,EACA,GAAA,CACFA,EAAUD,EAAW,OACX,CACV,MAAA,CAoBK,MAlBgB,CACrB,QAAUE,GAAS,CACb,IAAAzX,EACE,MAAA0X,EAASC,GACTA,IAAS,KACJ,KAEF,KAAK,MAAMA,EAAwB,MAAwB,EAE9DC,GAAO5X,EAAKwX,EAAQ,QAAQC,CAAI,IAAM,KAAOzX,EAAK,KACxD,OAAI4X,aAAe,QACVA,EAAI,KAAKF,CAAK,EAEhBA,EAAME,CAAG,CAClB,EACA,QAAS,CAACH,EAAMI,IAAaL,EAAQ,QAAQC,EAAM,KAAK,UAAUI,EAA4B,MAAyB,CAAC,EACxH,WAAaJ,GAASD,EAAQ,WAAWC,CAAI,CAC/C,CAEF,CACA,MAAMK,GAAcC,GAAQC,GAAU,CAChC,GAAA,CACI,MAAAxK,EAASuK,EAAGC,CAAK,EACvB,OAAIxK,aAAkB,QACbA,EAEF,CACL,KAAKyK,EAAa,CACT,OAAAH,GAAWG,CAAW,EAAEzK,CAAM,CACvC,EACA,MAAM0K,EAAa,CACV,OAAA,IAAA,CAEX,QACOzT,EAAG,CACH,MAAA,CACL,KAAK0T,EAAc,CACV,OAAA,IACT,EACA,MAAMC,EAAY,CACT,OAAAN,GAAWM,CAAU,EAAE3T,CAAC,CAAA,CAEnC,CAAA,CAEJ,EACM4T,GAAc,CAACxQ,EAAQyQ,IAAgB,CAACC,EAAKC,EAAK9B,IAAQ,CAC9D,IAAI5V,EAAU,CACZ,QAASwW,GAAkB,IAAM,YAAY,EAC7C,WAAarB,GAAUA,EACvB,QAAS,EACT,MAAO,CAACwC,EAAgBC,KAAkB,CACxC,GAAGA,EACH,GAAGD,CAAA,GAEL,GAAGH,CACL,EACIK,EAAc,GACZ,MAAAC,MAAyC,IACzCC,MAA+C,IACrD,IAAIrB,EAAU1W,EAAQ,QACtB,GAAI,CAAC0W,EACI,OAAA3P,EACL,IAAI/H,IAAS,CACH,QAAA,KACN,uDAAuDgB,EAAQ,IAAI,gDACrE,EACAyX,EAAI,GAAGzY,CAAI,CACb,EACA0Y,EACA9B,CACF,EAEF,MAAMoC,EAAU,IAAM,CACpB,MAAM7C,EAAQnV,EAAQ,WAAW,CAAE,GAAG0X,IAAO,EACtC,OAAAhB,EAAQ,QAAQ1W,EAAQ,KAAM,CACnC,MAAAmV,EACA,QAASnV,EAAQ,OAAA,CAClB,CACH,EACMiY,EAAgBrC,EAAI,SACtBA,EAAA,SAAW,CAACT,EAAOI,IAAY,CACjC0C,EAAc9C,EAAOI,CAAO,EACvByC,EAAQ,CACf,EACA,MAAME,EAAenR,EACnB,IAAI/H,IAAS,CACXyY,EAAI,GAAGzY,CAAI,EACNgZ,EAAQ,CACf,EACAN,EACA9B,CACF,EACAA,EAAI,gBAAkB,IAAMsC,EACxB,IAAAC,EACJ,MAAMC,EAAU,IAAM,CACpB,IAAIlZ,EAAID,EACR,GAAI,CAACyX,EAAS,OACAmB,EAAA,GACKC,EAAA,QAASrY,GAAO,CAC7B,IAAA4Y,EACJ,OAAO5Y,GAAI4Y,EAAMX,EAAU,IAAA,KAAOW,EAAMH,CAAY,CAAA,CACrD,EACD,MAAMI,IAA4BrZ,EAAKe,EAAQ,qBAAuB,KAAO,OAASf,EAAG,KAAKe,GAAUd,EAAKwY,EAAI,IAAM,KAAOxY,EAAKgZ,CAAY,IAAM,OACrJ,OAAOlB,GAAWN,EAAQ,QAAQ,KAAKA,CAAO,CAAC,EAAE1W,EAAQ,IAAI,EAAE,KAAMuY,GAA6B,CAChG,GAAIA,EACF,GAAI,OAAOA,EAAyB,SAAY,UAAYA,EAAyB,UAAYvY,EAAQ,QAAS,CAChH,GAAIA,EAAQ,QAAS,CACnB,MAAMwY,EAAYxY,EAAQ,QACxBuY,EAAyB,MACzBA,EAAyB,OAC3B,EACA,OAAIC,aAAqB,QAChBA,EAAU,KAAM9L,GAAW,CAAC,GAAMA,CAAM,CAAC,EAE3C,CAAC,GAAM8L,CAAS,CAAA,CAEjB,QAAA,MACN,uFACF,CAAA,KAEO,OAAA,CAAC,GAAOD,EAAyB,KAAK,EAG1C,MAAA,CAAC,GAAO,MAAM,CAAA,CACtB,EAAE,KAAME,GAAoB,CACvB,IAAAJ,EACE,KAAA,CAACK,EAAUC,CAAa,EAAIF,EAMlC,GALAN,EAAmBnY,EAAQ,MACzB2Y,GACCN,EAAMX,MAAU,KAAOW,EAAMH,CAChC,EACAT,EAAIU,EAAkB,EAAI,EACtBO,EACF,OAAOV,EAAQ,CACjB,CACD,EAAE,KAAK,IAAM,CACZM,GAA2B,MAAgBA,EAAwBH,EAAkB,MAAM,EAC3FA,EAAmBT,EAAI,EACTG,EAAA,GACdE,EAAyB,QAAStY,GAAOA,EAAG0Y,CAAgB,CAAC,CAAA,CAC9D,EAAE,MAAOxU,GAAM,CACd2U,GAA2B,MAAgBA,EAAwB,OAAQ3U,CAAC,CAAA,CAC7E,CACH,EACA,OAAAiS,EAAI,QAAU,CACZ,WAAagD,GAAe,CAChB5Y,EAAA,CACR,GAAGA,EACH,GAAG4Y,CACL,EACIA,EAAW,UACblC,EAAUkC,EAAW,QAEzB,EACA,aAAc,IAAM,CAClBlC,GAAW,MAAgBA,EAAQ,WAAW1W,EAAQ,IAAI,CAC5D,EACA,WAAY,IAAMA,EAClB,UAAW,IAAMoY,EAAQ,EACzB,YAAa,IAAMP,EACnB,UAAYpY,IACVqY,EAAmB,IAAIrY,CAAE,EAClB,IAAM,CACXqY,EAAmB,OAAOrY,CAAE,CAC9B,GAEF,kBAAoBA,IAClBsY,EAAyB,IAAItY,CAAE,EACxB,IAAM,CACXsY,EAAyB,OAAOtY,CAAE,CACpC,EAEJ,EACKO,EAAQ,eACHoY,EAAA,EAEHD,GAAoBD,CAC7B,EACMW,GAAUtB,GC5bHuB,GAAevC,GAAkB,EAC5CsC,GACE,CAACpB,EAAKC,KAAS,CACb,KAAM,KACN,gBAAiB,GACjB,UAAW,GAEX,MAAO,MAAOqB,GAAkC,CAC1CtB,EAAA,CAAE,UAAW,GAAM,EACnB,GAAA,CAEM,QAAA,IAAI,cAAesB,CAAW,EAGtC,MAAMC,EAAiB,CACrB,GAAI,IACJ,SAAU,WACV,MAAOD,EAAY,MACnB,KAAM,SACN,UAAW,IAAI,KAAK,EAAE,YAAY,EAClC,UAAW,IAAI,KAAK,EAAE,YAAY,CACpC,EAEItB,EAAA,CACF,KAAMuB,EACN,gBAAiB,GACjB,UAAW,EAAA,CACZ,QACMta,EAAO,CACN,cAAA,MAAM,gBAAiBA,CAAK,EAChC+Y,EAAA,CAAE,UAAW,GAAO,EAClB/Y,CAAA,CAEV,EAEA,OAAQ,IAAM,CACR+Y,EAAA,CACF,KAAM,KACN,gBAAiB,EAAA,CAClB,CACH,EAEA,cAAe,MAAOwB,GAAwB,CACtC,KAAA,CAAE,KAAAC,CAAK,EAAIxB,EAAI,EACrB,GAAKwB,EAED,CAAAzB,EAAA,CAAE,UAAW,GAAM,EACnB,GAAA,CAEF,MAAM0B,EAAc,CAAE,GAAGD,EAAM,GAAGD,CAAK,EACnCxB,EAAA,CACF,KAAM0B,EACN,UAAW,EAAA,CACZ,QACMza,EAAO,CACN,cAAA,MAAM,yBAA0BA,CAAK,EACzC+Y,EAAA,CAAE,UAAW,GAAO,EAClB/Y,CAAA,EAEV,EAEA,QAAUwa,GAAsB,CAC1BzB,EAAA,CACF,KAAAyB,EACA,gBAAiB,CAAC,CAACA,CAAA,CACpB,CAAA,CACH,GAEF,CACE,KAAM,eACN,WAAa/D,IAAW,CACtB,KAAMA,EAAM,KACZ,gBAAiBA,EAAM,eACzB,EAAA,CACF,CAEJ,EClFaiE,GAAmB,IAAM,CACpC,KAAM,CAAE,EAAAlW,CAAA,EAAMjB,GAAe,QAAQ,EAC/B,CAAE,KAAAiX,EAAM,gBAAAG,EAAiB,OAAAC,CAAA,EAAWR,GAAa,EAEvD,aACG,SAAO,CAAA,UAAU,+GAChB,SAACS,EAAA,KAAA,MAAA,CAAI,UAAU,mCAEb,SAAA,CAAAA,EAAA,KAACC,EAAK,CAAA,GAAG,IAAI,UAAU,8BACrB,SAAA,CAAC1E,EAAAA,IAAAjP,GAAA,CAAS,UAAU,SAAU,CAAA,EAC7BiP,EAAA,IAAA,OAAA,CAAK,UAAU,oBAAoB,SAAU,YAAA,CAAA,CAAA,EAChD,EAGAyE,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAAAzE,EAAA,IAAC0E,EAAA,CACC,GAAG,IACH,UAAU,2DAET,WAAE,iBAAiB,CAAA,CACtB,EACA1E,EAAA,IAAC0E,EAAA,CACC,GAAG,UACH,UAAU,2DAET,WAAE,mBAAmB,CAAA,CACxB,EACCH,GAEGE,EAAA,KAAAE,WAAA,CAAA,SAAA,CAAA3E,EAAA,IAAC0E,EAAA,CACC,GAAG,aACH,UAAU,2DAET,WAAE,sBAAsB,CAAA,CAC3B,EACA1E,EAAA,IAAC0E,EAAA,CACC,GAAG,WACH,UAAU,2DAET,WAAE,oBAAoB,CAAA,CAAA,CACzB,CACF,CAAA,CAAA,EAEJ,QAGC,MAAI,CAAA,UAAU,kCACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,2BACb,SAAA,CAACzE,EAAAA,IAAA5O,GAAA,CAAO,UAAU,uDAAwD,CAAA,EAC1E4O,EAAA,IAACC,GAAA,CACC,YAAa7R,EAAE,gBAAgB,EAC/B,UAAU,MAAA,CAAA,CACZ,CAAA,CACF,CACF,CAAA,EAGAqW,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACZ,SAAA,CACCF,EAAAE,EAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAA,EAAA,KAAC9E,EAAO,CAAA,QAAQ,QAAQ,KAAK,KAC3B,SAAA,CAACK,EAAAA,IAAAxO,GAAA,CAAK,UAAU,cAAe,CAAA,EAC9B4S,GAAA,YAAAA,EAAM,QAAA,EACT,EACApE,EAAAA,IAACL,EAAO,CAAA,QAAQ,QAAQ,KAAK,KAAK,QAAS6E,EACxC,SAAEpW,EAAA,mBAAmB,CACxB,CAAA,CAAA,CACF,CAAA,EAEAqW,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAzE,EAAA,IAAC0E,EAAK,CAAA,GAAG,SACP,SAAA1E,EAAA,IAACL,EAAO,CAAA,QAAQ,QAAQ,KAAK,KAC1B,SAAAvR,EAAE,kBAAkB,CACvB,CAAA,EACF,EACA4R,EAAA,IAAC0E,EAAK,CAAA,GAAG,YACP,SAAA1E,EAAAA,IAACL,EAAO,CAAA,KAAK,KACV,SAAAvR,EAAE,qBAAqB,CAC1B,CAAA,CACF,CAAA,CAAA,EACF,EAID4R,EAAA,IAAAL,EAAA,CAAO,QAAQ,QAAQ,KAAK,OAAO,UAAU,YAC5C,SAACK,EAAAA,IAAA7O,GAAA,CAAK,UAAU,SAAA,CAAU,CAC5B,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EC/FayT,GAAmB,IAAM,CACpC,KAAM,CAAE,EAAAxW,CAAA,EAAMjB,GAAe,QAAQ,EAErC,aACG,SAAO,CAAA,UAAU,yBAChB,SAACsX,EAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACzE,EAAAA,IAAAjP,GAAA,CAAS,UAAU,SAAU,CAAA,EAC7BiP,EAAA,IAAA,OAAA,CAAK,UAAU,oBAAoB,SAAU,YAAA,CAAA,CAAA,EAChD,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gCAAgC,SAE7C,0EAAA,CAAA,CAAA,EACF,EAGAyE,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACzE,EAAA,IAAA,KAAA,CAAG,UAAU,gBAAgB,SAAc,iBAAA,EAC5CyE,EAAAA,KAAC,KAAG,CAAA,UAAU,oBACZ,SAAA,CAACzE,EAAA,IAAA,KAAA,CACC,SAACA,EAAAA,IAAA,IAAA,CAAE,KAAK,IAAI,UAAU,6DACnB,SAAA5R,EAAE,iBAAiB,CACtB,CAAA,EACF,EACA4R,EAAA,IAAC,KACC,CAAA,SAAAA,EAAAA,IAAC,IAAE,CAAA,KAAK,UAAU,UAAU,6DACzB,SAAA5R,EAAE,mBAAmB,CACxB,CAAA,EACF,EACA4R,EAAAA,IAAC,MACC,SAACA,EAAA,IAAA,IAAA,CAAE,KAAK,SAAS,UAAU,6DAA6D,SAAA,cAAA,CAExF,CACF,CAAA,EACAA,EAAAA,IAAC,MACC,SAACA,EAAAA,IAAA,IAAA,CAAE,KAAK,WAAW,UAAU,6DAA6D,SAAA,SAE1F,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAyE,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACzE,EAAA,IAAA,KAAA,CAAG,UAAU,gBAAgB,SAAM,SAAA,EACpCyE,EAAAA,KAAC,KAAG,CAAA,UAAU,oBACZ,SAAA,CAACzE,EAAAA,IAAA,KAAA,CACC,eAAC,IAAE,CAAA,KAAK,QAAQ,UAAU,6DAA6D,oBAEvF,CACF,CAAA,EACAA,EAAAA,IAAC,MACC,SAACA,EAAA,IAAA,IAAA,CAAE,KAAK,OAAO,UAAU,6DAA6D,SAAA,KAAA,CAEtF,CACF,CAAA,EACAA,EAAAA,IAAC,MACC,SAACA,EAAA,IAAA,IAAA,CAAE,KAAK,WAAW,UAAU,6DAA6D,SAAA,oBAAA,CAE1F,CACF,CAAA,EACAA,EAAAA,IAAC,MACC,SAACA,EAAAA,IAAA,IAAA,CAAE,KAAK,SAAS,UAAU,6DAA6D,SAAA,oBAExF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAyE,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACzE,EAAA,IAAA,KAAA,CAAG,UAAU,gBAAgB,SAAO,UAAA,EACrCyE,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACzE,EAAAA,IAAA,IAAA,CAAE,KAAK,IAAI,UAAU,6DACpB,SAACA,EAAAA,IAAA/O,GAAA,CAAO,UAAU,SAAA,CAAU,CAC9B,CAAA,EACA+O,EAAAA,IAAC,IAAE,CAAA,KAAK,IAAI,UAAU,6DACpB,SAACA,EAAAA,IAAAzO,GAAA,CAAQ,UAAU,SAAA,CAAU,CAC/B,CAAA,EACAyO,EAAAA,IAAC,IAAE,CAAA,KAAK,IAAI,UAAU,6DACpB,SAACA,EAAA,IAAA9O,GAAA,CAAK,UAAU,SAAU,CAAA,CAC5B,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,+DACb,SAAC8O,EAAA,IAAA,IAAA,CAAE,wDAAkD,CAAA,CACvD,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EC9Fa6E,GAAmB,IAE5BJ,EAAA,KAAC,MAAI,CAAA,UAAU,6BACb,SAAA,CAAAzE,EAAA,IAACsE,GAAO,EAAA,QACP,OAAK,CAAA,UAAU,SACd,SAAAtE,MAAC8E,IAAO,CAAA,EACV,QACCF,GAAO,CAAA,CAAA,CAAA,EACV,ECTEG,GAAOnF,EAGX,WAAA,CAAC,CAAE,UAAAlQ,EAAW,GAAGtC,CAAM,EAAGV,IAC1BsT,EAAA,IAAC,MAAA,CACC,IAAAtT,EACA,UAAW8S,EACT,2DACA9P,CACF,EACC,GAAGtC,CAAA,CACN,CACD,EACD2X,GAAK,YAAc,OAEnB,MAAMC,GAAapF,EAGjB,WAAA,CAAC,CAAE,UAAAlQ,EAAW,GAAGtC,CAAS,EAAAV,UACzB,MAAI,CAAA,IAAAA,EAAU,UAAW8S,EAAG,gCAAiC9P,CAAS,EAAI,GAAGtC,EAAO,CACtF,EACD4X,GAAW,YAAc,aAEzB,MAAMC,GAAYrF,EAGhB,WAAA,CAAC,CAAE,UAAAlQ,EAAW,GAAGtC,CAAM,EAAGV,IAC1BsT,EAAA,IAAC,KAAA,CACC,IAAAtT,EACA,UAAW8S,EACT,qDACA9P,CACF,EACC,GAAGtC,CAAA,CACN,CACD,EACD6X,GAAU,YAAc,YAExB,MAAMC,GAAkBtF,EAGtB,WAAA,CAAC,CAAE,UAAAlQ,EAAW,GAAGtC,CAAM,EAAGV,IAC1BsT,EAAA,IAAC,IAAA,CACC,IAAAtT,EACA,UAAW8S,EAAG,gCAAiC9P,CAAS,EACvD,GAAGtC,CAAA,CACN,CACD,EACD8X,GAAgB,YAAc,kBAE9B,MAAMC,GAAcvF,EAGlB,WAAA,CAAC,CAAE,UAAAlQ,EAAW,GAAGtC,CAAS,EAAAV,UACzB,MAAI,CAAA,IAAAA,EAAU,UAAW8S,EAAG,WAAY9P,CAAS,EAAI,GAAGtC,EAAO,CACjE,EACD+X,GAAY,YAAc,cAE1B,MAAMC,GAAaxF,EAGjB,WAAA,CAAC,CAAE,UAAAlQ,EAAW,GAAGtC,CAAS,EAAAV,UACzB,MAAI,CAAA,IAAAA,EAAU,UAAW8S,EAAG,6BAA8B9P,CAAS,EAAI,GAAGtC,EAAO,CACnF,EACDgY,GAAW,YAAc,aCvDlB,MAAMC,GAAqB,IAI9BZ,EAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAACA,EAAAA,KAAA,UAAA,CAAQ,UAAU,oBACjB,SAAA,CAACzE,EAAA,IAAA,KAAA,CAAG,UAAU,qDAAqD,SAEnE,+BAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,uDAAuD,SAGpE,8HAAA,EACAyE,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACb,SAAA,CAAAzE,EAAAA,IAAC0E,GAAK,GAAG,UACP,SAACD,EAAA,KAAA9E,EAAA,CAAO,KAAK,KACX,SAAA,CAACK,EAAAA,IAAAjP,GAAA,CAAS,UAAU,cAAe,CAAA,EAAE,aAAA,CAAA,CAEvC,CACF,CAAA,EACAiP,EAAA,IAAC0E,EAAK,CAAA,GAAG,YACP,SAAA1E,EAAAA,IAACL,EAAO,CAAA,QAAQ,UAAU,KAAK,KAAK,SAAA,kBAAA,CAEpC,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGA8E,EAAAA,KAAC,UAAQ,CAAA,UAAU,QACjB,SAAA,CAACzE,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAEpD,oBAAA,EACAyE,EAAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAAAA,OAACM,GACC,CAAA,SAAA,CAACN,EAAAA,KAAAO,GAAA,CAAW,UAAU,cACpB,SAAA,CAAChF,EAAAA,IAAA1O,GAAA,CAAW,UAAU,mCAAoC,CAAA,EACzD0O,EAAA,IAAAiF,GAAA,CAAU,UAAU,UAAU,SAAQ,UAAA,CAAA,CAAA,EACzC,QACCE,GACC,CAAA,SAAAnF,MAACkF,IAAgB,UAAU,cAAc,oEAEzC,CAAA,CACF,CAAA,CAAA,EACF,SAECH,GACC,CAAA,SAAA,CAACN,EAAAA,KAAAO,GAAA,CAAW,UAAU,cACpB,SAAA,CAAChF,EAAAA,IAAAhP,GAAA,CAAM,UAAU,mCAAoC,CAAA,EACpDgP,EAAA,IAAAiF,GAAA,CAAU,UAAU,UAAU,SAAW,aAAA,CAAA,CAAA,EAC5C,QACCE,GACC,CAAA,SAAAnF,MAACkF,IAAgB,UAAU,cAAc,kEAEzC,CAAA,CACF,CAAA,CAAA,EACF,SAECH,GACC,CAAA,SAAA,CAACN,EAAAA,KAAAO,GAAA,CAAW,UAAU,cACpB,SAAA,CAAChF,EAAAA,IAAA3O,GAAA,CAAK,UAAU,mCAAoC,CAAA,EACnD2O,EAAA,IAAAiF,GAAA,CAAU,UAAU,UAAU,SAAQ,UAAA,CAAA,CAAA,EACzC,QACCE,GACC,CAAA,SAAAnF,MAACkF,IAAgB,UAAU,cAAc,6DAEzC,CAAA,CACF,CAAA,CAAA,EACF,SAECH,GACC,CAAA,SAAA,CAACN,EAAAA,KAAAO,GAAA,CAAW,UAAU,cACpB,SAAA,CAAChF,EAAAA,IAAAjP,GAAA,CAAS,UAAU,mCAAoC,CAAA,EACvDiP,EAAA,IAAAiF,GAAA,CAAU,UAAU,UAAU,SAAW,aAAA,CAAA,CAAA,EAC5C,QACCE,GACC,CAAA,SAAAnF,MAACkF,IAAgB,UAAU,cAAc,qEAEzC,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAT,EAAAA,KAAC,UAAQ,CAAA,UAAU,QACjB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACzE,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAe,kBAAA,EAClDA,EAAAA,IAAC0E,GAAK,GAAG,UACP,eAAC/E,EAAO,CAAA,QAAQ,UAAU,SAAA,YAAU,CAAA,CACtC,CAAA,CAAA,EACF,EAECK,EAAA,IAAA,MAAA,CAAI,UAAU,uDAEZ,eAAM,KAAK,CAAE,OAAQ,CAAA,CAAG,EAAE,IAAI,CAACsF,EAAG3V,IACjC8U,EAAA,KAACM,GAAA,CAEC,UAAU,oDAEV,SAAA,CAAC/E,EAAAA,IAAA,MAAA,CAAI,UAAU,uBAAwB,CAAA,EACvCyE,EAAAA,KAACU,GAAY,CAAA,UAAU,MACrB,SAAA,CAACV,EAAAA,KAAA,KAAA,CAAG,UAAU,iCAAiC,SAAA,CAAA,cACjC9U,EAAQ,CAAA,EACtB,EACCqQ,EAAA,IAAA,IAAA,CAAE,UAAU,gCAAgC,SAAO,UAAA,EACpDyE,EAAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAACzE,EAAAA,IAAA3O,GAAA,CAAK,UAAU,yCAA0C,CAAA,EACzD2O,EAAA,IAAA,OAAA,CAAK,UAAU,eAAe,SAAG,KAAA,CAAA,CAAA,CACpC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,EAbKrQ,CAAA,CAeR,CACH,CAAA,CAAA,EACF,EAGA8U,EAAAA,KAAC,UAAQ,CAAA,UAAU,QACjB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACzE,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAiB,oBAAA,EACpDA,EAAAA,IAAC0E,GAAK,GAAG,uBACP,eAAC/E,EAAO,CAAA,QAAQ,UAAU,SAAA,YAAU,CAAA,CACtC,CAAA,CAAA,EACF,EAEAK,EAAA,IAAC,MAAI,CAAA,UAAU,uDACZ,SAAA,MAAM,KAAK,CAAE,OAAQ,CAAA,CAAG,EAAE,IAAI,CAACsF,EAAG3V,IACjCqQ,EAAAA,IAAC+E,GAAiB,CAAA,UAAU,oCAC1B,SAAA/E,EAAA,IAACmF,GAAY,CAAA,UAAU,MACrB,SAAAV,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACzE,EAAAA,IAAA,MAAA,CAAI,UAAU,4BAA6B,CAAA,EAC5CyE,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,qBAAqB,SAAA,CAAA,cACrB9U,EAAQ,CAAA,EACtB,EACCqQ,EAAA,IAAA,IAAA,CAAE,UAAU,qCAAqC,SAElD,cAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gCAAgC,SAAW,aAAA,CAAA,CAAA,CAC1D,CAAA,CAAA,CAAA,CACF,CACF,CAAA,GAdSrQ,CAeX,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EC1JJ,IAAI4V,GAAO,QACPC,GAAQ5F,EAAgB,WAAC,CAACxS,EAAOqY,IACZzF,EAAG,IACxB0F,GAAU,MACV,CACE,GAAGtY,EACH,IAAKqY,EACL,YAAcE,GAAU,OACPA,EAAM,OACV,QAAQ,iCAAiC,KACpDvb,EAAAgD,EAAM,cAAN,MAAAhD,EAAA,KAAAgD,EAAoBuY,GAChB,CAACA,EAAM,kBAAoBA,EAAM,OAAS,GAAGA,EAAM,eAAgB,EAC/E,CACA,CACG,CACF,EACDH,GAAM,YAAcD,GACpB,IAAIK,GAAOJ,GCjBX,MAAMK,GAAgB9T,GACpB,4FACF,EAEMyT,GAAQ5F,EAIZ,WAAA,CAAC,CAAE,UAAAlQ,EAAW,GAAGtC,CAAM,EAAGV,IAC1BsT,EAAA,IAAC8F,GAAA,CACC,IAAApZ,EACA,UAAW8S,EAAGqG,GAAc,EAAGnW,CAAS,EACvC,GAAGtC,CAAA,CACN,CACD,EACDoY,GAAM,YAAcM,GAAoB,YCXjC,MAAMC,GAAsB,IAAM,CACvC,KAAM,CAAE,EAAA3X,CAAA,EAAMjB,GAAe,QAAQ,EAC/B6Y,EAAWC,GAAY,EACvB,CAAE,MAAAC,EAAO,UAAAC,CAAU,EAAInC,GAAa,EAEpC,CAACoC,EAAUC,CAAW,EAAI/X,WAAS,CACvC,MAAO,GACP,SAAU,EAAA,CACX,EACK,CAACgY,EAAQC,CAAS,EAAIjY,EAAAA,SAAiC,CAAA,CAAE,EAEzDkY,EAAgB3X,GAA2C,CAC/D,KAAM,CAAE,KAAAgT,EAAM,MAAArV,CAAM,EAAIqC,EAAE,OACdwX,EAAAI,IAAS,CAAE,GAAGA,EAAM,CAAC5E,CAAI,EAAGrV,GAAQ,EAE5C8Z,EAAOzE,CAAI,GACH0E,EAAAE,IAAS,CAAE,GAAGA,EAAM,CAAC5E,CAAI,EAAG,IAAK,CAE/C,EAEM6E,EAAe,IAAM,CACzB,MAAMC,EAAoC,CAAC,EAEvC,OAACP,EAAS,MAEF,eAAe,KAAKA,EAAS,KAAK,IAClCO,EAAA,MAAQvY,EAAE,oBAAoB,GAF9BuY,EAAA,MAAQvY,EAAE,gBAAgB,EAKjCgY,EAAS,WACFO,EAAA,SAAWvY,EAAE,gBAAgB,GAGzCmY,EAAUI,CAAS,EACZ,OAAO,KAAKA,CAAS,EAAE,SAAW,CAC3C,EAEMC,EAAe,MAAO/X,GAAuB,CAG7C,GAFJA,EAAE,eAAe,EAEb,EAAC6X,IAED,GAAA,CACF,MAAMR,EAAME,CAAQ,EACpBJ,EAAS,GAAG,QACLpc,EAAO,CACN,QAAA,MAAM,gBAAiBA,CAAK,EAC1B2c,EAAA,CAAE,QAAS,uDAAwD,CAAA,CAEjF,EAEA,aACG,MAAI,CAAA,UAAU,2EACb,SAAC9B,EAAA,KAAAM,GAAA,CAAK,UAAU,kBACd,SAAA,CAACN,EAAAA,KAAAO,GAAA,CAAW,UAAU,cACpB,SAAA,CAAAhF,EAAAA,IAAC,OAAI,UAAU,2BACb,eAACjP,GAAS,CAAA,UAAU,uBAAuB,CAC7C,CAAA,QACCkU,GAAU,CAAA,UAAU,WAAY,SAAA7W,EAAE,kBAAkB,EAAE,EACvD4R,EAAAA,IAACkF,IAAgB,SAEjB,yCAAA,CAAA,CAAA,EACF,SACCC,GACC,CAAA,SAAA,CAAAV,EAAA,KAAC,OAAK,CAAA,SAAUmC,EAAc,UAAU,YACrC,SAAA,CAAAN,EAAO,SACLtG,EAAA,IAAA,MAAA,CAAI,UAAU,qEACZ,WAAO,QACV,EAGFyE,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAzE,MAACwF,GAAM,CAAA,QAAQ,QAAS,SAAApX,EAAE,aAAa,EAAE,EACzC4R,EAAA,IAACC,GAAA,CACC,GAAG,QACH,KAAK,QACL,KAAK,QACL,MAAOmG,EAAS,MAChB,SAAUI,EACV,YAAY,iBACZ,UAAWF,EAAO,MAAQ,qBAAuB,EAAA,CACnD,EACCA,EAAO,OACNtG,EAAA,IAAC,KAAE,UAAU,2BAA4B,WAAO,KAAM,CAAA,CAAA,EAE1D,EAEAyE,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAzE,MAACwF,GAAM,CAAA,QAAQ,WAAY,SAAApX,EAAE,gBAAgB,EAAE,EAC/C4R,EAAA,IAACC,GAAA,CACC,GAAG,WACH,KAAK,WACL,KAAK,WACL,MAAOmG,EAAS,SAChB,SAAUI,EACV,UAAWF,EAAO,SAAW,qBAAuB,EAAA,CACtD,EACCA,EAAO,UACNtG,EAAA,IAAC,KAAE,UAAU,2BAA4B,WAAO,QAAS,CAAA,CAAA,EAE7D,EAEAA,EAAA,IAACL,EAAA,CACC,KAAK,SACL,UAAU,SACV,SAAUwG,EAET,SAAAA,EAAY,oBAAsB/X,EAAE,kBAAkB,CAAA,CAAA,CACzD,EACF,QAEC,MAAI,CAAA,UAAU,2BACb,SAACqW,EAAA,KAAA,IAAA,CAAE,UAAU,wBAAwB,SAAA,CAAA,qBAChB,IACnBzE,MAAC0E,GAAK,GAAG,YAAY,UAAU,+BAC5B,SAAAtW,EAAE,qBAAqB,CAC1B,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EClIayY,GAAuB,IAAM,CACxC,KAAM,CAAE,EAAAzY,CAAE,EAAIjB,GAAe,CAAC,SAAU,OAAO,CAAC,EAG9C,OAAAsX,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAzE,MAAC,KAAG,CAAA,UAAU,0BAA2B,SAAA5R,EAAE,mBAAmB,EAAE,EAChE4R,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,4CAErC,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECda8G,GAA4B,IAErCrC,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACzE,EAAA,IAAA,KAAA,CAAG,UAAU,0BAA0B,SAAe,kBAAA,EACvDA,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,+CAErC,CAAA,CACF,CAAA,CAAA,EACF,ECTS+G,GAAuB,IAEhCtC,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACzE,EAAA,IAAA,KAAA,CAAG,UAAU,0BAA0B,SAAU,aAAA,EAClDA,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,0CAErC,CAAA,CACF,CAAA,CAAA,EACF,ECRSgH,GAAyB,IAAM,CAC1C,KAAM,CAAE,EAAA5Y,CAAA,EAAMjB,GAAe,QAAQ,EAGnC,OAAAsX,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAzE,MAAC,KAAG,CAAA,UAAU,0BAA2B,SAAA5R,EAAE,qBAAqB,EAAE,EAClE4R,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,uCAErC,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECbaiH,GAA0B,IAAM,CAC3C,KAAM,CAAE,EAAA7Y,CAAA,EAAMjB,GAAe,QAAQ,EAGnC,OAAAsX,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAzE,MAAC,KAAG,CAAA,UAAU,0BAA2B,SAAA5R,EAAE,sBAAsB,EAAE,EACnE4R,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,wCAErC,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECbakH,GAAwB,IAAM,CACzC,KAAM,CAAE,EAAA9Y,CAAA,EAAMjB,GAAe,QAAQ,EAGnC,OAAAsX,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAzE,MAAC,KAAG,CAAA,UAAU,0BAA2B,SAAA5R,EAAE,oBAAoB,EAAE,EACjE4R,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,uCAErC,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECbamH,GAAwB,IAAM,CACzC,KAAM,CAAE,EAAA/Y,CAAA,EAAMjB,GAAe,QAAQ,EAGnC,OAAAsX,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAzE,MAAC,KAAG,CAAA,UAAU,0BAA2B,SAAA5R,EAAE,oBAAoB,EAAE,EACjE4R,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,qCAErC,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,ECCMoH,GAASC,GAAoB,CACjC,CACE,KAAM,IACN,cAAUxC,GAAO,EAAA,EACjB,SAAU,CACR,CACE,MAAO,GACP,cAAUQ,GAAS,CAAA,CAAA,CACrB,EACA,CACE,KAAM,QACN,cAAUU,GAAU,CAAA,CAAA,CACtB,EACA,CACE,KAAM,WACN,cAAUiB,GAAa,CAAA,CAAA,CACzB,EACA,CACE,KAAM,SACN,cAAUH,GAAW,CAAA,CAAA,CACvB,EACA,CACE,KAAM,YACN,cAAUC,GAAgB,CAAA,CAAA,CAC5B,EACA,CACE,KAAM,kBACN,cAAUC,GAAW,CAAA,CAAA,CACvB,EACA,CACE,KAAM,YACN,cAAUE,GAAc,CAAA,CAAA,CAC1B,EACA,CACE,KAAM,UACN,cAAUC,GAAY,CAAA,CAAA,CACxB,EACA,CACE,KAAM,UACN,cAAUC,GAAY,CAAA,CAAA,CAAA,CACxB,CACF,CAEJ,CAAC,EAEYG,GAAsB,IAC1BtH,MAACuH,IAAe,OAAAH,GAAgB,EC/DnC/c,EAAWgB,GAAO,OAAOA,GAAQ,SACjCmc,GAAQ,IAAM,CAClB,IAAIC,EACAC,EACJ,MAAMC,EAAU,IAAI,QAAQ,CAAC5Y,EAAS6Y,IAAW,CAC/CH,EAAM1Y,EACN2Y,EAAME,CACV,CAAG,EACD,OAAAD,EAAQ,QAAUF,EAClBE,EAAQ,OAASD,EACVC,CACT,EACME,GAAaC,GACbA,GAAU,KAAa,GACpB,GAAKA,EAERC,GAAO,CAACC,EAAGC,EAAG,IAAM,CACxBD,EAAE,QAAQ1e,GAAK,CACT2e,EAAE3e,CAAC,IAAG,EAAEA,CAAC,EAAI2e,EAAE3e,CAAC,EACxB,CAAG,CACH,EACM4e,GAA4B,OAC5BC,GAAWtV,GAAOA,GAAOA,EAAI,QAAQ,KAAK,EAAI,GAAKA,EAAI,QAAQqV,GAA2B,GAAG,EAAIrV,EACjGuV,GAAuBN,GAAU,CAACA,GAAUzd,EAASyd,CAAM,EAC3DO,GAAgB,CAACP,EAAQ/S,EAAMuT,IAAU,CAC7C,MAAMC,EAASle,EAAS0K,CAAI,EAAWA,EAAK,MAAM,GAAG,EAArBA,EAChC,IAAIyT,EAAa,EACjB,KAAOA,EAAaD,EAAM,OAAS,GAAG,CACpC,GAAIH,GAAqBN,CAAM,EAAG,MAAO,CAAE,EAC3C,MAAMjV,EAAMsV,GAASI,EAAMC,CAAU,CAAC,EAClC,CAACV,EAAOjV,CAAG,GAAKyV,IAAOR,EAAOjV,CAAG,EAAI,IAAIyV,GACzC,OAAO,UAAU,eAAe,KAAKR,EAAQjV,CAAG,EAClDiV,EAASA,EAAOjV,CAAG,EAEnBiV,EAAS,CAAE,EAEb,EAAEU,CACN,CACE,OAAIJ,GAAqBN,CAAM,EAAU,CAAE,EACpC,CACL,IAAKA,EACL,EAAGK,GAASI,EAAMC,CAAU,CAAC,CAC9B,CACH,EACMC,GAAU,CAACX,EAAQ/S,EAAMkN,IAAa,CAC1C,KAAM,CACJ,IAAA5W,EACA,EAAAqC,CACD,EAAG2a,GAAcP,EAAQ/S,EAAM,MAAM,EACtC,GAAI1J,IAAQ,QAAa0J,EAAK,SAAW,EAAG,CAC1C1J,EAAIqC,CAAC,EAAIuU,EACT,MACJ,CACE,IAAIpT,EAAIkG,EAAKA,EAAK,OAAS,CAAC,EACxB2T,EAAI3T,EAAK,MAAM,EAAGA,EAAK,OAAS,CAAC,EACjC4T,EAAON,GAAcP,EAAQY,EAAG,MAAM,EAC1C,KAAOC,EAAK,MAAQ,QAAaD,EAAE,QACjC7Z,EAAI,GAAG6Z,EAAEA,EAAE,OAAS,CAAC,CAAC,IAAI7Z,CAAC,GAC3B6Z,EAAIA,EAAE,MAAM,EAAGA,EAAE,OAAS,CAAC,EAC3BC,EAAON,GAAcP,EAAQY,EAAG,MAAM,EAClCC,GAAA,MAAAA,EAAM,KAAO,OAAOA,EAAK,IAAI,GAAGA,EAAK,CAAC,IAAI9Z,CAAC,EAAE,EAAM,MACrD8Z,EAAK,IAAM,QAGfA,EAAK,IAAI,GAAGA,EAAK,CAAC,IAAI9Z,CAAC,EAAE,EAAIoT,CAC/B,EACM2G,GAAW,CAACd,EAAQ/S,EAAMkN,EAAU4G,IAAW,CACnD,KAAM,CACJ,IAAAxd,EACA,EAAAqC,CACD,EAAG2a,GAAcP,EAAQ/S,EAAM,MAAM,EACtC1J,EAAIqC,CAAC,EAAIrC,EAAIqC,CAAC,GAAK,CAAE,EACrBrC,EAAIqC,CAAC,EAAE,KAAKuU,CAAQ,CACtB,EACM6G,GAAU,CAAChB,EAAQ/S,IAAS,CAChC,KAAM,CACJ,IAAA1J,EACA,EAAAqC,CACJ,EAAM2a,GAAcP,EAAQ/S,CAAI,EAC9B,GAAK1J,GACA,OAAO,UAAU,eAAe,KAAKA,EAAKqC,CAAC,EAChD,OAAOrC,EAAIqC,CAAC,CACd,EACMqb,GAAsB,CAAC5E,EAAM6E,EAAanW,IAAQ,CACtD,MAAMrG,EAAQsc,GAAQ3E,EAAMtR,CAAG,EAC/B,OAAIrG,IAAU,OACLA,EAEFsc,GAAQE,EAAanW,CAAG,CACjC,EACMoW,GAAa,CAACC,EAAQC,EAAQC,IAAc,CAChD,UAAWtZ,KAAQqZ,EACbrZ,IAAS,aAAeA,IAAS,gBAC/BA,KAAQoZ,EACN7e,EAAS6e,EAAOpZ,CAAI,CAAC,GAAKoZ,EAAOpZ,CAAI,YAAa,QAAUzF,EAAS8e,EAAOrZ,CAAI,CAAC,GAAKqZ,EAAOrZ,CAAI,YAAa,OAC5GsZ,IAAWF,EAAOpZ,CAAI,EAAIqZ,EAAOrZ,CAAI,GAEzCmZ,GAAWC,EAAOpZ,CAAI,EAAGqZ,EAAOrZ,CAAI,EAAGsZ,CAAS,EAGlDF,EAAOpZ,CAAI,EAAIqZ,EAAOrZ,CAAI,GAIhC,OAAOoZ,CACT,EACMG,GAAcrH,GAAOA,EAAI,QAAQ,sCAAuC,MAAM,EACpF,IAAIsH,GAAa,CACf,IAAK,QACL,IAAK,OACL,IAAK,OACL,IAAK,SACL,IAAK,QACL,IAAK,QACP,EACA,MAAMC,GAASpF,GACT9Z,EAAS8Z,CAAI,EACRA,EAAK,QAAQ,aAAc8D,GAAKqB,GAAWrB,CAAC,CAAC,EAE/C9D,EAET,MAAMqF,EAAY,CAChB,YAAYC,EAAU,CACpB,KAAK,SAAWA,EAChB,KAAK,UAAY,IAAI,IACrB,KAAK,YAAc,CAAE,CACzB,CACE,UAAUC,EAAS,CACjB,MAAMC,EAAkB,KAAK,UAAU,IAAID,CAAO,EAClD,GAAIC,IAAoB,OACtB,OAAOA,EAET,MAAMC,EAAY,IAAI,OAAOF,CAAO,EACpC,OAAI,KAAK,YAAY,SAAW,KAAK,UACnC,KAAK,UAAU,OAAO,KAAK,YAAY,MAAK,CAAE,EAEhD,KAAK,UAAU,IAAIA,EAASE,CAAS,EACrC,KAAK,YAAY,KAAKF,CAAO,EACtBE,CACX,CACA,CACA,MAAMC,GAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,GAAG,EAChCC,GAAiC,IAAIN,GAAY,EAAE,EACnDO,GAAsB,CAAClX,EAAKmX,EAAaC,IAAiB,CAC9DD,EAAcA,GAAe,GAC7BC,EAAeA,GAAgB,GAC/B,MAAMC,EAAgBL,GAAM,OAAOM,GAAKH,EAAY,QAAQG,CAAC,EAAI,GAAKF,EAAa,QAAQE,CAAC,EAAI,CAAC,EACjG,GAAID,EAAc,SAAW,EAAG,MAAO,GACvC,MAAMzY,EAAIqY,GAA+B,UAAU,IAAII,EAAc,IAAIC,GAAKA,IAAM,IAAM,MAAQA,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG,EACjH,IAAIC,EAAU,CAAC3Y,EAAE,KAAKoB,CAAG,EACzB,GAAI,CAACuX,EAAS,CACZ,MAAMC,EAAKxX,EAAI,QAAQoX,CAAY,EAC/BI,EAAK,GAAK,CAAC5Y,EAAE,KAAKoB,EAAI,UAAU,EAAGwX,CAAE,CAAC,IACxCD,EAAU,GAEhB,CACE,OAAOA,CACT,EACME,GAAW,CAACjf,EAAK0J,EAAMkV,EAAe,MAAQ,CAClD,GAAI,CAAC5e,EAAK,OACV,GAAIA,EAAI0J,CAAI,EACV,OAAK,OAAO,UAAU,eAAe,KAAK1J,EAAK0J,CAAI,EAC5C1J,EAAI0J,CAAI,EADuC,OAGxD,MAAMwV,EAASxV,EAAK,MAAMkV,CAAY,EACtC,IAAIO,EAAUnf,EACd,QAAS,EAAI,EAAG,EAAIkf,EAAO,QAAS,CAClC,GAAI,CAACC,GAAW,OAAOA,GAAY,SACjC,OAEF,IAAIC,EACAC,EAAW,GACf,QAASC,EAAI,EAAGA,EAAIJ,EAAO,OAAQ,EAAEI,EAMnC,GALIA,IAAM,IACRD,GAAYT,GAEdS,GAAYH,EAAOI,CAAC,EACpBF,EAAOD,EAAQE,CAAQ,EACnBD,IAAS,OAAW,CACtB,GAAI,CAAC,SAAU,SAAU,SAAS,EAAE,QAAQ,OAAOA,CAAI,EAAI,IAAME,EAAIJ,EAAO,OAAS,EACnF,SAEF,GAAKI,EAAI,EAAI,EACb,KACR,CAEIH,EAAUC,CACd,CACE,OAAOD,CACT,EACMI,GAAiB7gB,GAAQA,GAAA,YAAAA,EAAM,QAAQ,IAAK,KAE5C8gB,GAAgB,CACpB,KAAM,SACN,IAAI3gB,EAAM,CACR,KAAK,OAAO,MAAOA,CAAI,CACxB,EACD,KAAKA,EAAM,CACT,KAAK,OAAO,OAAQA,CAAI,CACzB,EACD,MAAMA,EAAM,CACV,KAAK,OAAO,QAASA,CAAI,CAC1B,EACD,OAAOgW,EAAMhW,EAAM,UACjBC,GAAAC,EAAA,6BAAU8V,KAAV,YAAA9V,EAAiB,QAAjB,MAAAD,EAAA,KAAAC,EAAyB,QAASF,EACtC,CACA,EACA,MAAM4gB,EAAO,CACX,YAAYC,EAAgB7f,EAAU,GAAI,CACxC,KAAK,KAAK6f,EAAgB7f,CAAO,CACrC,CACE,KAAK6f,EAAgB7f,EAAU,GAAI,CACjC,KAAK,OAASA,EAAQ,QAAU,WAChC,KAAK,OAAS6f,GAAkBF,GAChC,KAAK,QAAU3f,EACf,KAAK,MAAQA,EAAQ,KACzB,CACE,OAAOhB,EAAM,CACX,OAAO,KAAK,QAAQA,EAAM,MAAO,GAAI,EAAI,CAC7C,CACE,QAAQA,EAAM,CACZ,OAAO,KAAK,QAAQA,EAAM,OAAQ,GAAI,EAAI,CAC9C,CACE,SAASA,EAAM,CACb,OAAO,KAAK,QAAQA,EAAM,QAAS,EAAE,CACzC,CACE,aAAaA,EAAM,CACjB,OAAO,KAAK,QAAQA,EAAM,OAAQ,uBAAwB,EAAI,CAClE,CACE,QAAQA,EAAM8gB,EAAKnV,EAAQoV,EAAW,CACpC,OAAIA,GAAa,CAAC,KAAK,MAAc,MACjC5gB,EAASH,EAAK,CAAC,CAAC,IAAGA,EAAK,CAAC,EAAI,GAAG2L,CAAM,GAAG,KAAK,MAAM,IAAI3L,EAAK,CAAC,CAAC,IAC5D,KAAK,OAAO8gB,CAAG,EAAE9gB,CAAI,EAChC,CACE,OAAOghB,EAAY,CACjB,OAAO,IAAIJ,GAAO,KAAK,OAAQ,CAE3B,OAAQ,GAAG,KAAK,MAAM,IAAII,CAAU,IAEtC,GAAG,KAAK,OACd,CAAK,CACL,CACE,MAAMhgB,EAAS,CACb,OAAAA,EAAUA,GAAW,KAAK,QAC1BA,EAAQ,OAASA,EAAQ,QAAU,KAAK,OACjC,IAAI4f,GAAO,KAAK,OAAQ5f,CAAO,CAC1C,CACA,CACA,IAAIigB,EAAa,IAAIL,GAErB,MAAMM,EAAa,CACjB,aAAc,CACZ,KAAK,UAAY,CAAE,CACvB,CACE,GAAGC,EAAQzK,EAAU,CACnB,OAAAyK,EAAO,MAAM,GAAG,EAAE,QAAQ1F,GAAS,CAC5B,KAAK,UAAUA,CAAK,IAAG,KAAK,UAAUA,CAAK,EAAI,IAAI,KACxD,MAAM2F,EAAe,KAAK,UAAU3F,CAAK,EAAE,IAAI/E,CAAQ,GAAK,EAC5D,KAAK,UAAU+E,CAAK,EAAE,IAAI/E,EAAU0K,EAAe,CAAC,CAC1D,CAAK,EACM,IACX,CACE,IAAI3F,EAAO/E,EAAU,CACnB,GAAK,KAAK,UAAU+E,CAAK,EACzB,IAAI,CAAC/E,EAAU,CACb,OAAO,KAAK,UAAU+E,CAAK,EAC3B,MACN,CACI,KAAK,UAAUA,CAAK,EAAE,OAAO/E,CAAQ,EACzC,CACE,KAAK+E,KAAUzb,EAAM,CACf,KAAK,UAAUyb,CAAK,GACP,MAAM,KAAK,KAAK,UAAUA,CAAK,EAAE,SAAS,EAClD,QAAQ,CAAC,CAAC4F,EAAUC,CAAa,IAAM,CAC5C,QAASrT,EAAI,EAAGA,EAAIqT,EAAerT,IACjCoT,EAAS,GAAGrhB,CAAI,CAE1B,CAAO,EAEC,KAAK,UAAU,GAAG,GACL,MAAM,KAAK,KAAK,UAAU,GAAG,EAAE,SAAS,EAChD,QAAQ,CAAC,CAACqhB,EAAUC,CAAa,IAAM,CAC5C,QAASrT,EAAI,EAAGA,EAAIqT,EAAerT,IACjCoT,EAAS,MAAMA,EAAU,CAAC5F,EAAO,GAAGzb,CAAI,CAAC,CAEnD,CAAO,CAEP,CACA,CAEA,MAAMuhB,WAAsBL,EAAa,CACvC,YAAYjH,EAAMjZ,EAAU,CAC1B,GAAI,CAAC,aAAa,EAClB,UAAW,aACf,EAAK,CACD,MAAO,EACP,KAAK,KAAOiZ,GAAQ,CAAE,EACtB,KAAK,QAAUjZ,EACX,KAAK,QAAQ,eAAiB,SAChC,KAAK,QAAQ,aAAe,KAE1B,KAAK,QAAQ,sBAAwB,SACvC,KAAK,QAAQ,oBAAsB,GAEzC,CACE,cAAcJ,EAAI,CACZ,KAAK,QAAQ,GAAG,QAAQA,CAAE,EAAI,GAChC,KAAK,QAAQ,GAAG,KAAKA,CAAE,CAE7B,CACE,iBAAiBA,EAAI,CACnB,MAAM6E,EAAQ,KAAK,QAAQ,GAAG,QAAQ7E,CAAE,EACpC6E,EAAQ,IACV,KAAK,QAAQ,GAAG,OAAOA,EAAO,CAAC,CAErC,CACE,YAAY3E,EAAKF,EAAI+H,EAAK3H,EAAU,CAAA,EAAI,SACtC,MAAM+e,EAAe/e,EAAQ,eAAiB,OAAYA,EAAQ,aAAe,KAAK,QAAQ,aACxFwgB,EAAsBxgB,EAAQ,sBAAwB,OAAYA,EAAQ,oBAAsB,KAAK,QAAQ,oBACnH,IAAI6J,EACA/J,EAAI,QAAQ,GAAG,EAAI,GACrB+J,EAAO/J,EAAI,MAAM,GAAG,GAEpB+J,EAAO,CAAC/J,EAAKF,CAAE,EACX+H,IACE,MAAM,QAAQA,CAAG,EACnBkC,EAAK,KAAK,GAAGlC,CAAG,EACPxI,EAASwI,CAAG,GAAKoX,EAC1BlV,EAAK,KAAK,GAAGlC,EAAI,MAAMoX,CAAY,CAAC,EAEpClV,EAAK,KAAKlC,CAAG,IAInB,MAAM+E,EAASkR,GAAQ,KAAK,KAAM/T,CAAI,EAMtC,MALI,CAAC6C,GAAU,CAAC9M,GAAM,CAAC+H,GAAO7H,EAAI,QAAQ,GAAG,EAAI,KAC/CA,EAAM+J,EAAK,CAAC,EACZjK,EAAKiK,EAAK,CAAC,EACXlC,EAAMkC,EAAK,MAAM,CAAC,EAAE,KAAK,GAAG,GAE1B6C,GAAU,CAAC8T,GAAuB,CAACrhB,EAASwI,CAAG,EAAU+E,EACtD0S,IAASngB,GAAAC,EAAA,KAAK,OAAL,YAAAA,EAAYY,KAAZ,YAAAb,EAAmBW,GAAK+H,EAAKoX,CAAY,CAC7D,CACE,YAAYjf,EAAKF,EAAI+H,EAAKrG,EAAOtB,EAAU,CACzC,OAAQ,EACZ,EAAK,CACD,MAAM+e,EAAe/e,EAAQ,eAAiB,OAAYA,EAAQ,aAAe,KAAK,QAAQ,aAC9F,IAAI6J,EAAO,CAAC/J,EAAKF,CAAE,EACf+H,IAAKkC,EAAOA,EAAK,OAAOkV,EAAepX,EAAI,MAAMoX,CAAY,EAAIpX,CAAG,GACpE7H,EAAI,QAAQ,GAAG,EAAI,KACrB+J,EAAO/J,EAAI,MAAM,GAAG,EACpBwB,EAAQ1B,EACRA,EAAKiK,EAAK,CAAC,GAEb,KAAK,cAAcjK,CAAE,EACrB2d,GAAQ,KAAK,KAAM1T,EAAMvI,CAAK,EACzBtB,EAAQ,QAAQ,KAAK,KAAK,QAASF,EAAKF,EAAI+H,EAAKrG,CAAK,CAC/D,CACE,aAAaxB,EAAKF,EAAI6gB,EAAWzgB,EAAU,CACzC,OAAQ,EACZ,EAAK,CACD,UAAW5B,KAAKqiB,GACVthB,EAASshB,EAAUriB,CAAC,CAAC,GAAK,MAAM,QAAQqiB,EAAUriB,CAAC,CAAC,IAAG,KAAK,YAAY0B,EAAKF,EAAIxB,EAAGqiB,EAAUriB,CAAC,EAAG,CACpG,OAAQ,EAChB,CAAO,EAEE4B,EAAQ,QAAQ,KAAK,KAAK,QAASF,EAAKF,EAAI6gB,CAAS,CAC9D,CACE,kBAAkB3gB,EAAKF,EAAI6gB,EAAWC,EAAMxC,EAAWle,EAAU,CAC/D,OAAQ,GACR,SAAU,EACd,EAAK,CACD,IAAI6J,EAAO,CAAC/J,EAAKF,CAAE,EACfE,EAAI,QAAQ,GAAG,EAAI,KACrB+J,EAAO/J,EAAI,MAAM,GAAG,EACpB4gB,EAAOD,EACPA,EAAY7gB,EACZA,EAAKiK,EAAK,CAAC,GAEb,KAAK,cAAcjK,CAAE,EACrB,IAAI+gB,EAAO/C,GAAQ,KAAK,KAAM/T,CAAI,GAAK,CAAE,EACpC7J,EAAQ,WAAUygB,EAAY,KAAK,MAAM,KAAK,UAAUA,CAAS,CAAC,GACnEC,EACF3C,GAAW4C,EAAMF,EAAWvC,CAAS,EAErCyC,EAAO,CACL,GAAGA,EACH,GAAGF,CACJ,EAEHlD,GAAQ,KAAK,KAAM1T,EAAM8W,CAAI,EACxB3gB,EAAQ,QAAQ,KAAK,KAAK,QAASF,EAAKF,EAAI6gB,CAAS,CAC9D,CACE,qBAAqB3gB,EAAKF,EAAI,CACxB,KAAK,kBAAkBE,EAAKF,CAAE,GAChC,OAAO,KAAK,KAAKE,CAAG,EAAEF,CAAE,EAE1B,KAAK,iBAAiBA,CAAE,EACxB,KAAK,KAAK,UAAWE,EAAKF,CAAE,CAChC,CACE,kBAAkBE,EAAKF,EAAI,CACzB,OAAO,KAAK,YAAYE,EAAKF,CAAE,IAAM,MACzC,CACE,kBAAkBE,EAAKF,EAAI,CACzB,OAAKA,IAAIA,EAAK,KAAK,QAAQ,WACpB,KAAK,YAAYE,EAAKF,CAAE,CACnC,CACE,kBAAkBE,EAAK,CACrB,OAAO,KAAK,KAAKA,CAAG,CACxB,CACE,4BAA4BA,EAAK,CAC/B,MAAMmZ,EAAO,KAAK,kBAAkBnZ,CAAG,EAEvC,MAAO,CAAC,EADEmZ,GAAQ,OAAO,KAAKA,CAAI,GAAK,CAAE,GAC9B,KAAK2H,GAAK3H,EAAK2H,CAAC,GAAK,OAAO,KAAK3H,EAAK2H,CAAC,CAAC,EAAE,OAAS,CAAC,CACnE,CACE,QAAS,CACP,OAAO,KAAK,IAChB,CACA,CAEA,IAAIC,GAAgB,CAClB,WAAY,CAAE,EACd,iBAAiBC,EAAQ,CACvB,KAAK,WAAWA,EAAO,IAAI,EAAIA,CAChC,EACD,OAAOC,EAAYzf,EAAOqG,EAAK3H,EAASghB,EAAY,CAClD,OAAAD,EAAW,QAAQE,GAAa,OAC9B3f,IAAQpC,EAAA,KAAK,WAAW+hB,CAAS,IAAzB,YAAA/hB,EAA4B,QAAQoC,EAAOqG,EAAK3H,EAASghB,KAAe1f,CACtF,CAAK,EACMA,CACX,CACA,EAEA,MAAM4f,GAAmB,CAAE,EACrBC,GAAuB5E,GAAO,CAACpd,EAASod,CAAG,GAAK,OAAOA,GAAQ,WAAa,OAAOA,GAAQ,SACjG,MAAM6E,WAAmBlB,EAAa,CACpC,YAAYmB,EAAUrhB,EAAU,GAAI,CAClC,MAAO,EACP6c,GAAK,CAAC,gBAAiB,gBAAiB,iBAAkB,eAAgB,mBAAoB,aAAc,OAAO,EAAGwE,EAAU,IAAI,EACpI,KAAK,QAAUrhB,EACX,KAAK,QAAQ,eAAiB,SAChC,KAAK,QAAQ,aAAe,KAE9B,KAAK,OAASigB,EAAW,OAAO,YAAY,CAChD,CACE,eAAengB,EAAK,CACdA,IAAK,KAAK,SAAWA,EAC7B,CACE,OAAO6H,EAAKlB,EAAI,CACd,cAAe,CAAA,CACnB,EAAK,CACD,MAAM6a,EAAM,CACV,GAAG7a,CACJ,EACD,GAAIkB,GAAO,KAAM,MAAO,GACxB,MAAM4Z,EAAW,KAAK,QAAQ5Z,EAAK2Z,CAAG,EACtC,OAAOC,GAAA,YAAAA,EAAU,OAAQ,MAC7B,CACE,eAAe5Z,EAAK2Z,EAAK,CACvB,IAAIxC,EAAcwC,EAAI,cAAgB,OAAYA,EAAI,YAAc,KAAK,QAAQ,YAC7ExC,IAAgB,SAAWA,EAAc,KAC7C,MAAMC,EAAeuC,EAAI,eAAiB,OAAYA,EAAI,aAAe,KAAK,QAAQ,aACtF,IAAIlgB,EAAakgB,EAAI,IAAM,KAAK,QAAQ,WAAa,CAAE,EACvD,MAAME,EAAuB1C,GAAenX,EAAI,QAAQmX,CAAW,EAAI,GACjE2C,EAAuB,CAAC,KAAK,QAAQ,yBAA2B,CAACH,EAAI,cAAgB,CAAC,KAAK,QAAQ,wBAA0B,CAACA,EAAI,aAAe,CAACzC,GAAoBlX,EAAKmX,EAAaC,CAAY,EAC1M,GAAIyC,GAAwB,CAACC,EAAsB,CACjD,MAAMrjB,EAAIuJ,EAAI,MAAM,KAAK,aAAa,aAAa,EACnD,GAAIvJ,GAAKA,EAAE,OAAS,EAClB,MAAO,CACL,IAAAuJ,EACA,WAAYxI,EAASiC,CAAU,EAAI,CAACA,CAAU,EAAIA,CACnD,EAEH,MAAMsgB,EAAQ/Z,EAAI,MAAMmX,CAAW,GAC/BA,IAAgBC,GAAgBD,IAAgBC,GAAgB,KAAK,QAAQ,GAAG,QAAQ2C,EAAM,CAAC,CAAC,EAAI,MAAItgB,EAAasgB,EAAM,MAAO,GACtI/Z,EAAM+Z,EAAM,KAAK3C,CAAY,CACnC,CACI,MAAO,CACL,IAAApX,EACA,WAAYxI,EAASiC,CAAU,EAAI,CAACA,CAAU,EAAIA,CACnD,CACL,CACE,UAAUugB,EAAMlb,EAAGmb,EAAS,CAC1B,IAAIN,EAAM,OAAO7a,GAAM,SAAW,CAChC,GAAGA,CACT,EAAQA,EAQJ,GAPI,OAAO6a,GAAQ,UAAY,KAAK,QAAQ,mCAC1CA,EAAM,KAAK,QAAQ,iCAAiC,SAAS,GAE3D,OAAO,SAAY,WAAUA,EAAM,CACrC,GAAGA,CACJ,GACIA,IAAKA,EAAM,CAAE,GACdK,GAAQ,KAAM,MAAO,GACpB,MAAM,QAAQA,CAAI,IAAGA,EAAO,CAAC,OAAOA,CAAI,CAAC,GAC9C,MAAME,EAAgBP,EAAI,gBAAkB,OAAYA,EAAI,cAAgB,KAAK,QAAQ,cACnFvC,EAAeuC,EAAI,eAAiB,OAAYA,EAAI,aAAe,KAAK,QAAQ,aAChF,CACJ,IAAA3Z,EACA,WAAAvG,CACN,EAAQ,KAAK,eAAeugB,EAAKA,EAAK,OAAS,CAAC,EAAGL,CAAG,EAC5Czf,EAAYT,EAAWA,EAAW,OAAS,CAAC,EAClD,IAAI0d,EAAcwC,EAAI,cAAgB,OAAYA,EAAI,YAAc,KAAK,QAAQ,YAC7ExC,IAAgB,SAAWA,EAAc,KAC7C,MAAMhf,EAAMwhB,EAAI,KAAO,KAAK,SACtBQ,EAA0BR,EAAI,yBAA2B,KAAK,QAAQ,wBAC5E,IAAIxhB,GAAA,YAAAA,EAAK,iBAAkB,SACzB,OAAIgiB,EACED,EACK,CACL,IAAK,GAAGhgB,CAAS,GAAGid,CAAW,GAAGnX,CAAG,GACrC,QAASA,EACT,aAAcA,EACd,QAAS7H,EACT,OAAQ+B,EACR,WAAY,KAAK,qBAAqByf,CAAG,CAC1C,EAEI,GAAGzf,CAAS,GAAGid,CAAW,GAAGnX,CAAG,GAErCka,EACK,CACL,IAAKla,EACL,QAASA,EACT,aAAcA,EACd,QAAS7H,EACT,OAAQ+B,EACR,WAAY,KAAK,qBAAqByf,CAAG,CAC1C,EAEI3Z,EAET,MAAM4Z,EAAW,KAAK,QAAQI,EAAML,CAAG,EACvC,IAAI/E,EAAMgF,GAAA,YAAAA,EAAU,IACpB,MAAMQ,GAAaR,GAAA,YAAAA,EAAU,UAAW5Z,EAClCqa,GAAkBT,GAAA,YAAAA,EAAU,eAAgB5Z,EAC5Csa,EAAW,CAAC,kBAAmB,oBAAqB,iBAAiB,EACrEC,EAAaZ,EAAI,aAAe,OAAYA,EAAI,WAAa,KAAK,QAAQ,WAC1Ea,EAA6B,CAAC,KAAK,YAAc,KAAK,WAAW,eACjEC,EAAsBd,EAAI,QAAU,QAAa,CAACniB,EAASmiB,EAAI,KAAK,EACpEe,EAAkBjB,GAAW,gBAAgBE,CAAG,EAChDgB,EAAqBF,EAAsB,KAAK,eAAe,UAAUtiB,EAAKwhB,EAAI,MAAOA,CAAG,EAAI,GAChGiB,EAAoCjB,EAAI,SAAWc,EAAsB,KAAK,eAAe,UAAUtiB,EAAKwhB,EAAI,MAAO,CAC3H,QAAS,EACV,CAAA,EAAI,GACCkB,EAAwBJ,GAAuB,CAACd,EAAI,SAAWA,EAAI,QAAU,EAC7EmB,EAAeD,GAAyBlB,EAAI,eAAe,KAAK,QAAQ,eAAe,MAAM,GAAKA,EAAI,eAAegB,CAAkB,EAAE,GAAKhB,EAAI,eAAeiB,CAAiC,EAAE,GAAKjB,EAAI,aACnN,IAAIoB,EAAgBnG,EAChB4F,GAA8B,CAAC5F,GAAO8F,IACxCK,EAAgBD,GAElB,MAAME,EAAiBxB,GAAqBuB,CAAa,EACnDE,GAAU,OAAO,UAAU,SAAS,MAAMF,CAAa,EAC7D,GAAIP,GAA8BO,GAAiBC,GAAkBV,EAAS,QAAQW,EAAO,EAAI,GAAK,EAAEzjB,EAAS+iB,CAAU,GAAK,MAAM,QAAQQ,CAAa,GAAI,CAC7J,GAAI,CAACpB,EAAI,eAAiB,CAAC,KAAK,QAAQ,cAAe,CAChD,KAAK,QAAQ,uBAChB,KAAK,OAAO,KAAK,iEAAiE,EAEpF,MAAM/a,EAAI,KAAK,QAAQ,sBAAwB,KAAK,QAAQ,sBAAsBwb,EAAYW,EAAe,CAC3G,GAAGpB,EACH,GAAIlgB,CACd,CAAS,EAAI,QAAQuG,CAAG,KAAK,KAAK,QAAQ,2CAClC,OAAIka,GACFN,EAAS,IAAMhb,EACfgb,EAAS,WAAa,KAAK,qBAAqBD,CAAG,EAC5CC,GAEFhb,CACf,CACM,GAAIwY,EAAc,CAChB,MAAM8D,EAAiB,MAAM,QAAQH,CAAa,EAC5C7F,EAAOgG,EAAiB,CAAA,EAAK,CAAE,EAC/BC,EAAcD,EAAiBb,EAAkBD,EACvD,UAAW3jB,KAAKskB,EACd,GAAI,OAAO,UAAU,eAAe,KAAKA,EAAetkB,CAAC,EAAG,CAC1D,MAAM2kB,EAAU,GAAGD,CAAW,GAAG/D,CAAY,GAAG3gB,CAAC,GAC7CikB,GAAmB,CAAC9F,EACtBM,EAAKze,CAAC,EAAI,KAAK,UAAU2kB,EAAS,CAChC,GAAGzB,EACH,aAAcH,GAAqBsB,CAAY,EAAIA,EAAarkB,CAAC,EAAI,OAEnE,WAAY,GACZ,GAAIgD,CAEtB,CAAe,EAEDyb,EAAKze,CAAC,EAAI,KAAK,UAAU2kB,EAAS,CAChC,GAAGzB,EAED,WAAY,GACZ,GAAIlgB,CAEtB,CAAe,EAECyb,EAAKze,CAAC,IAAM2kB,IAASlG,EAAKze,CAAC,EAAIskB,EAActkB,CAAC,EAC9D,CAEQme,EAAMM,CACd,CACA,SAAesF,GAA8BhjB,EAAS+iB,CAAU,GAAK,MAAM,QAAQ3F,CAAG,EAChFA,EAAMA,EAAI,KAAK2F,CAAU,EACrB3F,IAAKA,EAAM,KAAK,kBAAkBA,EAAKoF,EAAML,EAAKM,CAAO,OACxD,CACL,IAAIoB,EAAc,GACdC,EAAU,GACV,CAAC,KAAK,cAAc1G,CAAG,GAAK8F,IAC9BW,EAAc,GACdzG,EAAMkG,GAEH,KAAK,cAAclG,CAAG,IACzB0G,EAAU,GACV1G,EAAM5U,GAGR,MAAMub,GADiC5B,EAAI,gCAAkC,KAAK,QAAQ,iCAClC2B,EAAU,OAAY1G,EACxE4G,EAAgBd,GAAmBI,IAAiBlG,GAAO,KAAK,QAAQ,cAC9E,GAAI0G,GAAWD,GAAeG,EAAe,CAE3C,GADA,KAAK,OAAO,IAAIA,EAAgB,YAAc,aAAcrjB,EAAK+B,EAAW8F,EAAKwb,EAAgBV,EAAelG,CAAG,EAC/GwC,EAAc,CAChB,MAAMqE,EAAK,KAAK,QAAQzb,EAAK,CAC3B,GAAG2Z,EACH,aAAc,EAC1B,CAAW,EACG8B,GAAMA,EAAG,KAAK,KAAK,OAAO,KAAK,iLAAiL,CAC9N,CACQ,IAAIC,EAAO,CAAE,EACb,MAAMC,GAAe,KAAK,cAAc,iBAAiB,KAAK,QAAQ,YAAahC,EAAI,KAAO,KAAK,QAAQ,EAC3G,GAAI,KAAK,QAAQ,gBAAkB,YAAcgC,IAAgBA,GAAa,CAAC,EAC7E,QAASrW,EAAI,EAAGA,EAAIqW,GAAa,OAAQrW,IACvCoW,EAAK,KAAKC,GAAarW,CAAC,CAAC,OAElB,KAAK,QAAQ,gBAAkB,MACxCoW,EAAO,KAAK,cAAc,mBAAmB/B,EAAI,KAAO,KAAK,QAAQ,EAErE+B,EAAK,KAAK/B,EAAI,KAAO,KAAK,QAAQ,EAEpC,MAAMiC,GAAO,CAACC,EAAGhhB,EAAGihB,IAAyB,QAC3C,MAAMC,EAAoBrB,GAAmBoB,IAAyBlH,EAAMkH,EAAuBP,EAC/F,KAAK,QAAQ,kBACf,KAAK,QAAQ,kBAAkBM,EAAG3hB,EAAWW,EAAGkhB,EAAmBP,EAAe7B,CAAG,GAC5EpiB,GAAA,KAAK,mBAAL,MAAAA,GAAuB,aAChC,KAAK,iBAAiB,YAAYskB,EAAG3hB,EAAWW,EAAGkhB,EAAmBP,EAAe7B,CAAG,EAE1F,KAAK,KAAK,aAAckC,EAAG3hB,EAAWW,EAAG+Z,CAAG,CAC7C,EACG,KAAK,QAAQ,cACX,KAAK,QAAQ,oBAAsB6F,EACrCiB,EAAK,QAAQzhB,GAAY,CACvB,MAAM+hB,EAAW,KAAK,eAAe,YAAY/hB,EAAU0f,CAAG,EAC1DkB,GAAyBlB,EAAI,eAAe,KAAK,QAAQ,eAAe,MAAM,GAAKqC,EAAS,QAAQ,GAAG,KAAK,QAAQ,eAAe,MAAM,EAAI,GAC/IA,EAAS,KAAK,GAAG,KAAK,QAAQ,eAAe,MAAM,EAErDA,EAAS,QAAQC,GAAU,CACzBL,GAAK,CAAC3hB,CAAQ,EAAG+F,EAAMic,EAAQtC,EAAI,eAAesC,CAAM,EAAE,GAAKnB,CAAY,CAC3F,CAAe,CACf,CAAa,EAEDc,GAAKF,EAAM1b,EAAK8a,CAAY,EAGxC,CACMlG,EAAM,KAAK,kBAAkBA,EAAKoF,EAAML,EAAKC,EAAUK,CAAO,EAC1DqB,GAAW1G,IAAQ5U,GAAO,KAAK,QAAQ,8BACzC4U,EAAM,GAAG1a,CAAS,GAAGid,CAAW,GAAGnX,CAAG,KAEnCsb,GAAWD,IAAgB,KAAK,QAAQ,yBAC3CzG,EAAM,KAAK,QAAQ,uBAAuB,KAAK,QAAQ,4BAA8B,GAAG1a,CAAS,GAAGid,CAAW,GAAGnX,CAAG,GAAKA,EAAKqb,EAAczG,EAAM,OAAW+E,CAAG,EAEzK,CACI,OAAIO,GACFN,EAAS,IAAMhF,EACfgF,EAAS,WAAa,KAAK,qBAAqBD,CAAG,EAC5CC,GAEFhF,CACX,CACE,kBAAkBA,EAAK5U,EAAK2Z,EAAKC,EAAUK,EAAS,SAClD,IAAI1iB,EAAA,KAAK,aAAL,MAAAA,EAAiB,MACnBqd,EAAM,KAAK,WAAW,MAAMA,EAAK,CAC/B,GAAG,KAAK,QAAQ,cAAc,iBAC9B,GAAG+E,CACJ,EAAEA,EAAI,KAAO,KAAK,UAAYC,EAAS,QAASA,EAAS,OAAQA,EAAS,QAAS,CAClF,SAAAA,CACR,CAAO,UACQ,CAACD,EAAI,kBAAmB,CAC7BA,EAAI,eAAe,KAAK,aAAa,KAAK,CAC5C,GAAGA,EAED,cAAe,CACb,GAAG,KAAK,QAAQ,cAChB,GAAGA,EAAI,aACnB,CAEA,CAAO,EACD,MAAMuC,EAAkB1kB,EAASod,CAAG,MAAMtd,EAAAqiB,GAAA,YAAAA,EAAK,gBAAL,YAAAriB,EAAoB,mBAAoB,OAAYqiB,EAAI,cAAc,gBAAkB,KAAK,QAAQ,cAAc,iBAC7J,IAAIwC,EACJ,GAAID,EAAiB,CACnB,MAAME,EAAKxH,EAAI,MAAM,KAAK,aAAa,aAAa,EACpDuH,EAAUC,GAAMA,EAAG,MAC3B,CACM,IAAI9K,EAAOqI,EAAI,SAAW,CAACniB,EAASmiB,EAAI,OAAO,EAAIA,EAAI,QAAUA,EAMjE,GALI,KAAK,QAAQ,cAAc,mBAAkBrI,EAAO,CACtD,GAAG,KAAK,QAAQ,cAAc,iBAC9B,GAAGA,CACJ,GACDsD,EAAM,KAAK,aAAa,YAAYA,EAAKtD,EAAMqI,EAAI,KAAO,KAAK,UAAYC,EAAS,QAASD,CAAG,EAC5FuC,EAAiB,CACnB,MAAMG,EAAKzH,EAAI,MAAM,KAAK,aAAa,aAAa,EAC9C0H,EAAUD,GAAMA,EAAG,OACrBF,EAAUG,IAAS3C,EAAI,KAAO,GAC1C,CACU,CAACA,EAAI,KAAOC,GAAYA,EAAS,MAAKD,EAAI,IAAM,KAAK,UAAYC,EAAS,SAC1ED,EAAI,OAAS,KAAO/E,EAAM,KAAK,aAAa,KAAKA,EAAK,IAAIvd,KACxD4iB,GAAA,YAAAA,EAAU,MAAO5iB,EAAK,CAAC,GAAK,CAACsiB,EAAI,SACnC,KAAK,OAAO,KAAK,6CAA6CtiB,EAAK,CAAC,CAAC,YAAY2I,EAAI,CAAC,CAAC,EAAE,EAClF,MAEF,KAAK,UAAU,GAAG3I,EAAM2I,CAAG,EACjC2Z,CAAG,GACFA,EAAI,eAAe,KAAK,aAAa,MAAO,CACtD,CACI,MAAM4C,EAAc5C,EAAI,aAAe,KAAK,QAAQ,YAC9C6C,EAAqBhlB,EAAS+kB,CAAW,EAAI,CAACA,CAAW,EAAIA,EACnE,OAAI3H,GAAO,OAAQ4H,GAAA,MAAAA,EAAoB,SAAU7C,EAAI,qBAAuB,KAC1E/E,EAAMsE,GAAc,OAAOsD,EAAoB5H,EAAK5U,EAAK,KAAK,SAAW,KAAK,QAAQ,wBAA0B,CAC9G,aAAc,CACZ,GAAG4Z,EACH,WAAY,KAAK,qBAAqBD,CAAG,CAC1C,EACD,GAAGA,CACX,EAAUA,EAAK,IAAI,GAER/E,CACX,CACE,QAAQoF,EAAML,EAAM,GAAI,CACtB,IAAI8C,EACAnB,EACAoB,EACAC,EACAC,EACJ,OAAIplB,EAASwiB,CAAI,IAAGA,EAAO,CAACA,CAAI,GAChCA,EAAK,QAAQnf,GAAK,CAChB,GAAI,KAAK,cAAc4hB,CAAK,EAAG,OAC/B,MAAMI,EAAY,KAAK,eAAehiB,EAAG8e,CAAG,EACtC3Z,EAAM6c,EAAU,IACtBvB,EAAUtb,EACV,IAAIvG,EAAaojB,EAAU,WACvB,KAAK,QAAQ,aAAYpjB,EAAaA,EAAW,OAAO,KAAK,QAAQ,UAAU,GACnF,MAAMghB,EAAsBd,EAAI,QAAU,QAAa,CAACniB,EAASmiB,EAAI,KAAK,EACpEkB,EAAwBJ,GAAuB,CAACd,EAAI,SAAWA,EAAI,QAAU,EAC7EmD,EAAuBnD,EAAI,UAAY,SAAcniB,EAASmiB,EAAI,OAAO,GAAK,OAAOA,EAAI,SAAY,WAAaA,EAAI,UAAY,GAClIoD,EAAQpD,EAAI,KAAOA,EAAI,KAAO,KAAK,cAAc,mBAAmBA,EAAI,KAAO,KAAK,SAAUA,EAAI,WAAW,EACnHlgB,EAAW,QAAQxB,GAAM,SACnB,KAAK,cAAcwkB,CAAK,IAC5BG,EAAS3kB,EACL,CAACshB,GAAiB,GAAGwD,EAAM,CAAC,CAAC,IAAI9kB,CAAE,EAAE,KAAKV,EAAA,KAAK,QAAL,MAAAA,EAAY,qBAAsB,GAACD,EAAA,KAAK,QAAL,MAAAA,EAAY,mBAAmBslB,MAC9GrD,GAAiB,GAAGwD,EAAM,CAAC,CAAC,IAAI9kB,CAAE,EAAE,EAAI,GACxC,KAAK,OAAO,KAAK,QAAQqjB,CAAO,oBAAoByB,EAAM,KAAK,IAAI,CAAC,sCAAsCH,CAAM,uBAAwB,0NAA0N,GAEpWG,EAAM,QAAQ7lB,GAAQ,OACpB,GAAI,KAAK,cAAculB,CAAK,EAAG,OAC/BE,EAAUzlB,EACV,MAAM8lB,EAAY,CAAChd,CAAG,EACtB,IAAIzI,EAAA,KAAK,aAAL,MAAAA,EAAiB,cACnB,KAAK,WAAW,cAAcylB,EAAWhd,EAAK9I,EAAMe,EAAI0hB,CAAG,MACtD,CACL,IAAIsD,EACAxC,IAAqBwC,EAAe,KAAK,eAAe,UAAU/lB,EAAMyiB,EAAI,MAAOA,CAAG,GAC1F,MAAMuD,EAAa,GAAG,KAAK,QAAQ,eAAe,OAC5CC,EAAgB,GAAG,KAAK,QAAQ,eAAe,UAAU,KAAK,QAAQ,eAAe,GAU3F,GATI1C,IACFuC,EAAU,KAAKhd,EAAMid,CAAY,EAC7BtD,EAAI,SAAWsD,EAAa,QAAQE,CAAa,IAAM,GACzDH,EAAU,KAAKhd,EAAMid,EAAa,QAAQE,EAAe,KAAK,QAAQ,eAAe,CAAC,EAEpFtC,GACFmC,EAAU,KAAKhd,EAAMkd,CAAU,GAG/BJ,EAAsB,CACxB,MAAMM,EAAa,GAAGpd,CAAG,GAAG,KAAK,QAAQ,gBAAgB,GAAG2Z,EAAI,OAAO,GACvEqD,EAAU,KAAKI,CAAU,EACrB3C,IACFuC,EAAU,KAAKI,EAAaH,CAAY,EACpCtD,EAAI,SAAWsD,EAAa,QAAQE,CAAa,IAAM,GACzDH,EAAU,KAAKI,EAAaH,EAAa,QAAQE,EAAe,KAAK,QAAQ,eAAe,CAAC,EAE3FtC,GACFmC,EAAU,KAAKI,EAAaF,CAAU,EAGxD,CACA,CACU,IAAIG,EACJ,KAAOA,EAAcL,EAAU,OACxB,KAAK,cAAcP,CAAK,IAC3BC,EAAeW,EACfZ,EAAQ,KAAK,YAAYvlB,EAAMe,EAAIolB,EAAa1D,CAAG,EAGjE,CAAS,EACT,CAAO,CACP,CAAK,EACM,CACL,IAAK8C,EACL,QAAAnB,EACA,aAAAoB,EACA,QAAAC,EACA,OAAAC,CACD,CACL,CACE,cAAchI,EAAK,CACjB,OAAOA,IAAQ,QAAa,EAAE,CAAC,KAAK,QAAQ,YAAcA,IAAQ,OAAS,EAAE,CAAC,KAAK,QAAQ,mBAAqBA,IAAQ,GAC5H,CACE,YAAY1d,EAAMe,EAAI+H,EAAK3H,EAAU,CAAA,EAAI,OACvC,OAAId,EAAA,KAAK,aAAL,MAAAA,EAAiB,YAAoB,KAAK,WAAW,YAAYL,EAAMe,EAAI+H,EAAK3H,CAAO,EACpF,KAAK,cAAc,YAAYnB,EAAMe,EAAI+H,EAAK3H,CAAO,CAChE,CACE,qBAAqBA,EAAU,GAAI,CACjC,MAAMilB,EAAc,CAAC,eAAgB,UAAW,UAAW,UAAW,MAAO,OAAQ,cAAe,KAAM,eAAgB,cAAe,gBAAiB,gBAAiB,aAAc,cAAe,eAAe,EACjNC,EAA2BllB,EAAQ,SAAW,CAACb,EAASa,EAAQ,OAAO,EAC7E,IAAIiZ,EAAOiM,EAA2BllB,EAAQ,QAAUA,EAUxD,GATIklB,GAA4B,OAAOllB,EAAQ,MAAU,MACvDiZ,EAAK,MAAQjZ,EAAQ,OAEnB,KAAK,QAAQ,cAAc,mBAC7BiZ,EAAO,CACL,GAAG,KAAK,QAAQ,cAAc,iBAC9B,GAAGA,CACJ,GAEC,CAACiM,EAA0B,CAC7BjM,EAAO,CACL,GAAGA,CACJ,EACD,UAAWtR,KAAOsd,EAChB,OAAOhM,EAAKtR,CAAG,CAEvB,CACI,OAAOsR,CACX,CACE,OAAO,gBAAgBjZ,EAAS,CAC9B,MAAM2K,EAAS,eACf,UAAWwa,KAAUnlB,EACnB,GAAI,OAAO,UAAU,eAAe,KAAKA,EAASmlB,CAAM,GAAKxa,IAAWwa,EAAO,UAAU,EAAGxa,EAAO,MAAM,GAAmB3K,EAAQmlB,CAAM,IAA5B,OAC5G,MAAO,GAGX,MAAO,EACX,CACA,CAEA,MAAMC,EAAa,CACjB,YAAYplB,EAAS,CACnB,KAAK,QAAUA,EACf,KAAK,cAAgB,KAAK,QAAQ,eAAiB,GACnD,KAAK,OAASigB,EAAW,OAAO,eAAe,CACnD,CACE,sBAAsBphB,EAAM,CAE1B,GADAA,EAAO6gB,GAAe7gB,CAAI,EACtB,CAACA,GAAQA,EAAK,QAAQ,GAAG,EAAI,EAAG,OAAO,KAC3C,MAAM2e,EAAI3e,EAAK,MAAM,GAAG,EAGxB,OAFI2e,EAAE,SAAW,IACjBA,EAAE,IAAK,EACHA,EAAEA,EAAE,OAAS,CAAC,EAAE,YAAa,IAAK,KAAY,KAC3C,KAAK,mBAAmBA,EAAE,KAAK,GAAG,CAAC,CAC9C,CACE,wBAAwB3e,EAAM,CAE5B,GADAA,EAAO6gB,GAAe7gB,CAAI,EACtB,CAACA,GAAQA,EAAK,QAAQ,GAAG,EAAI,EAAG,OAAOA,EAC3C,MAAM2e,EAAI3e,EAAK,MAAM,GAAG,EACxB,OAAO,KAAK,mBAAmB2e,EAAE,CAAC,CAAC,CACvC,CACE,mBAAmB3e,EAAM,CACvB,GAAIM,EAASN,CAAI,GAAKA,EAAK,QAAQ,GAAG,EAAI,GAAI,CAC5C,IAAIwmB,EACJ,GAAI,CACFA,EAAgB,KAAK,oBAAoBxmB,CAAI,EAAE,CAAC,CACjD,MAAW,CAAA,CAIZ,OAHIwmB,GAAiB,KAAK,QAAQ,eAChCA,EAAgBA,EAAc,YAAa,GAEzCA,IACA,KAAK,QAAQ,aACRxmB,EAAK,YAAa,EAEpBA,EACb,CACI,OAAO,KAAK,QAAQ,WAAa,KAAK,QAAQ,aAAeA,EAAK,YAAW,EAAKA,CACtF,CACE,gBAAgBA,EAAM,CACpB,OAAI,KAAK,QAAQ,OAAS,gBAAkB,KAAK,QAAQ,4BACvDA,EAAO,KAAK,wBAAwBA,CAAI,GAEnC,CAAC,KAAK,eAAiB,CAAC,KAAK,cAAc,QAAU,KAAK,cAAc,QAAQA,CAAI,EAAI,EACnG,CACE,sBAAsB6lB,EAAO,CAC3B,GAAI,CAACA,EAAO,OAAO,KACnB,IAAIN,EACJ,OAAAM,EAAM,QAAQ7lB,GAAQ,CACpB,GAAIulB,EAAO,OACX,MAAMkB,EAAa,KAAK,mBAAmBzmB,CAAI,GAC3C,CAAC,KAAK,QAAQ,eAAiB,KAAK,gBAAgBymB,CAAU,KAAGlB,EAAQkB,EACnF,CAAK,EACG,CAAClB,GAAS,KAAK,QAAQ,eACzBM,EAAM,QAAQ7lB,GAAQ,CACpB,GAAIulB,EAAO,OACX,MAAMmB,EAAY,KAAK,sBAAsB1mB,CAAI,EACjD,GAAI,KAAK,gBAAgB0mB,CAAS,EAAG,OAAOnB,EAAQmB,EACpD,MAAMC,EAAU,KAAK,wBAAwB3mB,CAAI,EACjD,GAAI,KAAK,gBAAgB2mB,CAAO,EAAG,OAAOpB,EAAQoB,EAClDpB,EAAQ,KAAK,QAAQ,cAAc,KAAKqB,GAAgB,CACtD,GAAIA,IAAiBD,EAAS,OAAOC,EACrC,GAAI,EAAAA,EAAa,QAAQ,GAAG,EAAI,GAAKD,EAAQ,QAAQ,GAAG,EAAI,KACxDC,EAAa,QAAQ,GAAG,EAAI,GAAKD,EAAQ,QAAQ,GAAG,EAAI,GAAKC,EAAa,UAAU,EAAGA,EAAa,QAAQ,GAAG,CAAC,IAAMD,GACtHC,EAAa,QAAQD,CAAO,IAAM,GAAKA,EAAQ,OAAS,GAAG,OAAOC,CAChF,CAAS,CACT,CAAO,EAEErB,IAAOA,EAAQ,KAAK,iBAAiB,KAAK,QAAQ,WAAW,EAAE,CAAC,GAC9DA,CACX,CACE,iBAAiBsB,EAAW7mB,EAAM,CAChC,GAAI,CAAC6mB,EAAW,MAAO,CAAE,EAGzB,GAFI,OAAOA,GAAc,aAAYA,EAAYA,EAAU7mB,CAAI,GAC3DM,EAASumB,CAAS,IAAGA,EAAY,CAACA,CAAS,GAC3C,MAAM,QAAQA,CAAS,EAAG,OAAOA,EACrC,GAAI,CAAC7mB,EAAM,OAAO6mB,EAAU,SAAW,CAAE,EACzC,IAAItB,EAAQsB,EAAU7mB,CAAI,EAC1B,OAAKulB,IAAOA,EAAQsB,EAAU,KAAK,sBAAsB7mB,CAAI,CAAC,GACzDulB,IAAOA,EAAQsB,EAAU,KAAK,mBAAmB7mB,CAAI,CAAC,GACtDulB,IAAOA,EAAQsB,EAAU,KAAK,wBAAwB7mB,CAAI,CAAC,GAC3DulB,IAAOA,EAAQsB,EAAU,SACvBtB,GAAS,CAAE,CACtB,CACE,mBAAmBvlB,EAAM8mB,EAAc,CACrC,MAAMC,EAAgB,KAAK,kBAAkBD,IAAiB,GAAQ,GAAKA,IAAiB,KAAK,QAAQ,aAAe,CAAA,EAAI9mB,CAAI,EAC1H6lB,EAAQ,CAAE,EACVmB,EAAU5G,GAAK,CACdA,IACD,KAAK,gBAAgBA,CAAC,EACxByF,EAAM,KAAKzF,CAAC,EAEZ,KAAK,OAAO,KAAK,uDAAuDA,CAAC,EAAE,EAE9E,EACD,OAAI9f,EAASN,CAAI,IAAMA,EAAK,QAAQ,GAAG,EAAI,IAAMA,EAAK,QAAQ,GAAG,EAAI,KAC/D,KAAK,QAAQ,OAAS,gBAAgBgnB,EAAQ,KAAK,mBAAmBhnB,CAAI,CAAC,EAC3E,KAAK,QAAQ,OAAS,gBAAkB,KAAK,QAAQ,OAAS,eAAegnB,EAAQ,KAAK,sBAAsBhnB,CAAI,CAAC,EACrH,KAAK,QAAQ,OAAS,eAAegnB,EAAQ,KAAK,wBAAwBhnB,CAAI,CAAC,GAC1EM,EAASN,CAAI,GACtBgnB,EAAQ,KAAK,mBAAmBhnB,CAAI,CAAC,EAEvC+mB,EAAc,QAAQE,GAAM,CACtBpB,EAAM,QAAQoB,CAAE,EAAI,GAAGD,EAAQ,KAAK,mBAAmBC,CAAE,CAAC,CACpE,CAAK,EACMpB,CACX,CACA,CAEA,MAAMqB,GAAgB,CACpB,KAAM,EACN,IAAK,EACL,IAAK,EACL,IAAK,EACL,KAAM,EACN,MAAO,CACT,EACMC,GAAY,CAChB,OAAQC,GAASA,IAAU,EAAI,MAAQ,QACvC,gBAAiB,KAAO,CACtB,iBAAkB,CAAC,MAAO,OAAO,CAClC,EACH,EACA,MAAMC,EAAe,CACnB,YAAYC,EAAenmB,EAAU,GAAI,CACvC,KAAK,cAAgBmmB,EACrB,KAAK,QAAUnmB,EACf,KAAK,OAASigB,EAAW,OAAO,gBAAgB,EAChD,KAAK,iBAAmB,CAAE,CAC9B,CACE,QAAQngB,EAAKK,EAAK,CAChB,KAAK,MAAML,CAAG,EAAIK,CACtB,CACE,YAAa,CACX,KAAK,iBAAmB,CAAE,CAC9B,CACE,QAAQtB,EAAMmB,EAAU,GAAI,CAC1B,MAAMomB,EAAc1G,GAAe7gB,IAAS,MAAQ,KAAOA,CAAI,EACzDmW,EAAOhV,EAAQ,QAAU,UAAY,WACrCqmB,EAAW,KAAK,UAAU,CAC9B,YAAAD,EACA,KAAApR,CACN,CAAK,EACD,GAAIqR,KAAY,KAAK,iBACnB,OAAO,KAAK,iBAAiBA,CAAQ,EAEvC,IAAIC,EACJ,GAAI,CACFA,EAAO,IAAI,KAAK,YAAYF,EAAa,CACvC,KAAApR,CACR,CAAO,CACF,MAAa,CACZ,GAAI,CAAC,KACH,YAAK,OAAO,MAAM,+CAA+C,EAC1DgR,GAET,GAAI,CAACnnB,EAAK,MAAM,KAAK,EAAG,OAAOmnB,GAC/B,MAAMO,EAAU,KAAK,cAAc,wBAAwB1nB,CAAI,EAC/DynB,EAAO,KAAK,QAAQC,EAASvmB,CAAO,CAC1C,CACI,YAAK,iBAAiBqmB,CAAQ,EAAIC,EAC3BA,CACX,CACE,YAAYznB,EAAMmB,EAAU,GAAI,CAC9B,IAAIsmB,EAAO,KAAK,QAAQznB,EAAMmB,CAAO,EACrC,OAAKsmB,IAAMA,EAAO,KAAK,QAAQ,MAAOtmB,CAAO,IACtCsmB,GAAA,YAAAA,EAAM,kBAAkB,iBAAiB,QAAS,CAC7D,CACE,oBAAoBznB,EAAM8I,EAAK3H,EAAU,CAAA,EAAI,CAC3C,OAAO,KAAK,YAAYnB,EAAMmB,CAAO,EAAE,IAAI4jB,GAAU,GAAGjc,CAAG,GAAGic,CAAM,EAAE,CAC1E,CACE,YAAY/kB,EAAMmB,EAAU,GAAI,CAC9B,IAAIsmB,EAAO,KAAK,QAAQznB,EAAMmB,CAAO,EAErC,OADKsmB,IAAMA,EAAO,KAAK,QAAQ,MAAOtmB,CAAO,GACxCsmB,EACEA,EAAK,gBAAiB,EAAC,iBAAiB,KAAK,CAACE,EAAiBC,IAAoBV,GAAcS,CAAe,EAAIT,GAAcU,CAAe,CAAC,EAAE,IAAIC,GAAkB,GAAG,KAAK,QAAQ,OAAO,GAAG1mB,EAAQ,QAAU,UAAU,KAAK,QAAQ,OAAO,GAAK,EAAE,GAAG0mB,CAAc,EAAE,EADnQ,CAAE,CAExB,CACE,UAAU7nB,EAAMonB,EAAOjmB,EAAU,CAAA,EAAI,CACnC,MAAMsmB,EAAO,KAAK,QAAQznB,EAAMmB,CAAO,EACvC,OAAIsmB,EACK,GAAG,KAAK,QAAQ,OAAO,GAAGtmB,EAAQ,QAAU,UAAU,KAAK,QAAQ,OAAO,GAAK,EAAE,GAAGsmB,EAAK,OAAOL,CAAK,CAAC,IAE/G,KAAK,OAAO,KAAK,6BAA6BpnB,CAAI,EAAE,EAC7C,KAAK,UAAU,MAAOonB,EAAOjmB,CAAO,EAC/C,CACA,CAEA,MAAM2mB,GAAuB,CAAC1N,EAAM6E,EAAanW,EAAKoX,EAAe,IAAKyB,EAAsB,KAAS,CACvG,IAAI3W,EAAOgU,GAAoB5E,EAAM6E,EAAanW,CAAG,EACrD,MAAI,CAACkC,GAAQ2W,GAAuBrhB,EAASwI,CAAG,IAC9CkC,EAAOuV,GAASnG,EAAMtR,EAAKoX,CAAY,EACnClV,IAAS,SAAWA,EAAOuV,GAAStB,EAAanW,EAAKoX,CAAY,IAEjElV,CACT,EACM+c,GAAYC,GAAOA,EAAI,QAAQ,MAAO,MAAM,EAClD,MAAMC,EAAa,CACjB,YAAY9mB,EAAU,GAAI,OACxB,KAAK,OAASigB,EAAW,OAAO,cAAc,EAC9C,KAAK,QAAUjgB,EACf,KAAK,SAASd,EAAAc,GAAA,YAAAA,EAAS,gBAAT,YAAAd,EAAwB,UAAWoC,GAASA,GAC1D,KAAK,KAAKtB,CAAO,CACrB,CACE,KAAKA,EAAU,GAAI,CACZA,EAAQ,gBAAeA,EAAQ,cAAgB,CAClD,YAAa,EACd,GACD,KAAM,CACJ,OAAQ+mB,EACR,YAAAC,EACA,oBAAAC,EACA,OAAAtc,EACA,cAAAuc,EACA,OAAAtD,EACA,cAAAuD,EACA,gBAAAC,EACA,eAAAC,EACA,eAAAC,EACA,cAAAC,EACA,qBAAAC,EACA,cAAAC,EACA,qBAAAC,EACA,wBAAAC,EACA,YAAAC,EACA,aAAAC,CACD,EAAG7nB,EAAQ,cACZ,KAAK,OAAS+mB,IAAa,OAAYA,EAAW1I,GAClD,KAAK,YAAc2I,IAAgB,OAAYA,EAAc,GAC7D,KAAK,oBAAsBC,IAAwB,OAAYA,EAAsB,GACrF,KAAK,OAAStc,EAASwT,GAAYxT,CAAM,EAAIuc,GAAiB,KAC9D,KAAK,OAAStD,EAASzF,GAAYyF,CAAM,EAAIuD,GAAiB,KAC9D,KAAK,gBAAkBC,GAAmB,IAC1C,KAAK,eAAiBC,EAAiB,GAAKC,GAAkB,IAC9D,KAAK,eAAiB,KAAK,eAAiB,GAAKD,GAAkB,GACnE,KAAK,cAAgBE,EAAgBpJ,GAAYoJ,CAAa,EAAIC,GAAwBrJ,GAAY,KAAK,EAC3G,KAAK,cAAgBsJ,EAAgBtJ,GAAYsJ,CAAa,EAAIC,GAAwBvJ,GAAY,GAAG,EACzG,KAAK,wBAA0BwJ,GAA2B,IAC1D,KAAK,YAAcC,GAAe,IAClC,KAAK,aAAeC,IAAiB,OAAYA,EAAe,GAChE,KAAK,YAAa,CACtB,CACE,OAAQ,CACF,KAAK,SAAS,KAAK,KAAK,KAAK,OAAO,CAC5C,CACE,aAAc,CACZ,MAAMC,EAAmB,CAACC,EAAgBvJ,KACpCuJ,GAAA,YAAAA,EAAgB,UAAWvJ,GAC7BuJ,EAAe,UAAY,EACpBA,GAEF,IAAI,OAAOvJ,EAAS,GAAG,EAEhC,KAAK,OAASsJ,EAAiB,KAAK,OAAQ,GAAG,KAAK,MAAM,QAAQ,KAAK,MAAM,EAAE,EAC/E,KAAK,eAAiBA,EAAiB,KAAK,eAAgB,GAAG,KAAK,MAAM,GAAG,KAAK,cAAc,QAAQ,KAAK,cAAc,GAAG,KAAK,MAAM,EAAE,EAC3I,KAAK,cAAgBA,EAAiB,KAAK,cAAe,GAAG,KAAK,aAAa,QAAQ,KAAK,aAAa,EAAE,CAC/G,CACE,YAAYhR,EAAKmC,EAAMnZ,EAAKE,EAAS,OACnC,IAAIiE,EACA3C,EACA0mB,EACJ,MAAMlK,EAAc,KAAK,SAAW,KAAK,QAAQ,eAAiB,KAAK,QAAQ,cAAc,kBAAoB,CAAE,EAC7GmK,EAAetgB,GAAO,CAC1B,GAAIA,EAAI,QAAQ,KAAK,eAAe,EAAI,EAAG,CACzC,MAAMkC,EAAO8c,GAAqB1N,EAAM6E,EAAanW,EAAK,KAAK,QAAQ,aAAc,KAAK,QAAQ,mBAAmB,EACrH,OAAO,KAAK,aAAe,KAAK,OAAOkC,EAAM,OAAW/J,EAAK,CAC3D,GAAGE,EACH,GAAGiZ,EACH,iBAAkBtR,CACnB,CAAA,EAAIkC,CACb,CACM,MAAM,EAAIlC,EAAI,MAAM,KAAK,eAAe,EAClC,EAAI,EAAE,MAAK,EAAG,KAAM,EACpBnB,EAAI,EAAE,KAAK,KAAK,eAAe,EAAE,KAAM,EAC7C,OAAO,KAAK,OAAOmgB,GAAqB1N,EAAM6E,EAAa,EAAG,KAAK,QAAQ,aAAc,KAAK,QAAQ,mBAAmB,EAAGtX,EAAG1G,EAAK,CAClI,GAAGE,EACH,GAAGiZ,EACH,iBAAkB,CAC1B,CAAO,CACF,EACD,KAAK,YAAa,EAClB,MAAMiP,GAA8BloB,GAAA,YAAAA,EAAS,8BAA+B,KAAK,QAAQ,4BACnF6jB,IAAkB3kB,EAAAc,GAAA,YAAAA,EAAS,gBAAT,YAAAd,EAAwB,mBAAoB,OAAYc,EAAQ,cAAc,gBAAkB,KAAK,QAAQ,cAAc,gBAQnJ,MAPc,CAAC,CACb,MAAO,KAAK,eACZ,UAAW6mB,GAAOD,GAAUC,CAAG,CACrC,EAAO,CACD,MAAO,KAAK,OACZ,UAAWA,GAAO,KAAK,YAAcD,GAAU,KAAK,OAAOC,CAAG,CAAC,EAAID,GAAUC,CAAG,CACtF,CAAK,EACK,QAAQsB,GAAQ,CAEpB,IADAH,EAAW,EACJ/jB,EAAQkkB,EAAK,MAAM,KAAKrR,CAAG,GAAG,CACnC,MAAMsR,EAAankB,EAAM,CAAC,EAAE,KAAM,EAElC,GADA3C,EAAQ2mB,EAAaG,CAAU,EAC3B9mB,IAAU,OACZ,GAAI,OAAO4mB,GAAgC,WAAY,CACrD,MAAMG,EAAOH,EAA4BpR,EAAK7S,EAAOjE,CAAO,EAC5DsB,EAAQnC,EAASkpB,CAAI,EAAIA,EAAO,EAC5C,SAAqBroB,GAAW,OAAO,UAAU,eAAe,KAAKA,EAASooB,CAAU,EAC5E9mB,EAAQ,WACCuiB,EAAiB,CAC1BviB,EAAQ2C,EAAM,CAAC,EACf,QACZ,MACY,KAAK,OAAO,KAAK,8BAA8BmkB,CAAU,sBAAsBtR,CAAG,EAAE,EACpFxV,EAAQ,OAED,CAACnC,EAASmC,CAAK,GAAK,CAAC,KAAK,sBACnCA,EAAQqb,GAAWrb,CAAK,GAE1B,MAAMgnB,EAAYH,EAAK,UAAU7mB,CAAK,EAStC,GARAwV,EAAMA,EAAI,QAAQ7S,EAAM,CAAC,EAAGqkB,CAAS,EACjCzE,GACFsE,EAAK,MAAM,WAAa7mB,EAAM,OAC9B6mB,EAAK,MAAM,WAAalkB,EAAM,CAAC,EAAE,QAEjCkkB,EAAK,MAAM,UAAY,EAEzBH,IACIA,GAAY,KAAK,YACnB,KAEV,CACA,CAAK,EACMlR,CACX,CACE,KAAKA,EAAKgP,EAAI9lB,EAAU,CAAA,EAAI,CAC1B,IAAIiE,EACA3C,EACAinB,EACJ,MAAMC,EAAmB,CAAC7gB,EAAK8gB,IAAqB,CAClD,MAAMC,EAAM,KAAK,wBACjB,GAAI/gB,EAAI,QAAQ+gB,CAAG,EAAI,EAAG,OAAO/gB,EACjC,MAAMsX,EAAItX,EAAI,MAAM,IAAI,OAAO,GAAG+gB,CAAG,OAAO,CAAC,EAC7C,IAAIC,EAAgB,IAAI1J,EAAE,CAAC,CAAC,GAC5BtX,EAAMsX,EAAE,CAAC,EACT0J,EAAgB,KAAK,YAAYA,EAAeJ,CAAa,EAC7D,MAAMK,EAAsBD,EAAc,MAAM,IAAI,EAC9CE,EAAsBF,EAAc,MAAM,IAAI,KAC/CC,GAAA,YAAAA,EAAqB,SAAU,GAAK,IAAM,GAAK,CAACC,GAAuBA,EAAoB,OAAS,IAAM,KAC7GF,EAAgBA,EAAc,QAAQ,KAAM,GAAG,GAEjD,GAAI,CACFJ,EAAgB,KAAK,MAAMI,CAAa,EACpCF,IAAkBF,EAAgB,CACpC,GAAGE,EACH,GAAGF,CACJ,EACF,OAAQ5kB,EAAG,CACV,YAAK,OAAO,KAAK,oDAAoDgE,CAAG,GAAIhE,CAAC,EACtE,GAAGgE,CAAG,GAAG+gB,CAAG,GAAGC,CAAa,EAC3C,CACM,OAAIJ,EAAc,cAAgBA,EAAc,aAAa,QAAQ,KAAK,MAAM,EAAI,IAAI,OAAOA,EAAc,aACtG5gB,CACR,EACD,KAAO1D,EAAQ,KAAK,cAAc,KAAK6S,CAAG,GAAG,CAC3C,IAAIgS,EAAa,CAAE,EACnBP,EAAgB,CACd,GAAGvoB,CACJ,EACDuoB,EAAgBA,EAAc,SAAW,CAACppB,EAASopB,EAAc,OAAO,EAAIA,EAAc,QAAUA,EACpGA,EAAc,mBAAqB,GACnC,OAAOA,EAAc,aACrB,IAAIQ,EAAW,GACf,GAAI9kB,EAAM,CAAC,EAAE,QAAQ,KAAK,eAAe,IAAM,IAAM,CAAC,OAAO,KAAKA,EAAM,CAAC,CAAC,EAAG,CAC3E,MAAMsC,EAAItC,EAAM,CAAC,EAAE,MAAM,KAAK,eAAe,EAAE,IAAI+kB,GAAQA,EAAK,KAAI,CAAE,EACtE/kB,EAAM,CAAC,EAAIsC,EAAE,MAAO,EACpBuiB,EAAaviB,EACbwiB,EAAW,EACnB,CAEM,GADAznB,EAAQwkB,EAAG0C,EAAiB,KAAK,KAAMvkB,EAAM,CAAC,EAAE,KAAI,EAAIskB,CAAa,EAAGA,CAAa,EACjFjnB,GAAS2C,EAAM,CAAC,IAAM6S,GAAO,CAAC3X,EAASmC,CAAK,EAAG,OAAOA,EACrDnC,EAASmC,CAAK,IAAGA,EAAQqb,GAAWrb,CAAK,GACzCA,IACH,KAAK,OAAO,KAAK,qBAAqB2C,EAAM,CAAC,CAAC,gBAAgB6S,CAAG,EAAE,EACnExV,EAAQ,IAENynB,IACFznB,EAAQwnB,EAAW,OAAO,CAAClI,EAAGpa,IAAM,KAAK,OAAOoa,EAAGpa,EAAGxG,EAAQ,IAAK,CACjE,GAAGA,EACH,iBAAkBiE,EAAM,CAAC,EAAE,KAAI,CACzC,CAAS,EAAG3C,EAAM,MAAM,GAElBwV,EAAMA,EAAI,QAAQ7S,EAAM,CAAC,EAAG3C,CAAK,EACjC,KAAK,OAAO,UAAY,CAC9B,CACI,OAAOwV,CACX,CACA,CAEA,MAAMmS,GAAiBC,GAAa,CAClC,IAAIC,EAAaD,EAAU,YAAW,EAAG,KAAM,EAC/C,MAAME,EAAgB,CAAE,EACxB,GAAIF,EAAU,QAAQ,GAAG,EAAI,GAAI,CAC/B,MAAM1L,EAAI0L,EAAU,MAAM,GAAG,EAC7BC,EAAa3L,EAAE,CAAC,EAAE,YAAW,EAAG,KAAM,EACtC,MAAM6L,EAAS7L,EAAE,CAAC,EAAE,UAAU,EAAGA,EAAE,CAAC,EAAE,OAAS,CAAC,EAC5C2L,IAAe,YAAcE,EAAO,QAAQ,GAAG,EAAI,EAChDD,EAAc,WAAUA,EAAc,SAAWC,EAAO,KAAM,GAC1DF,IAAe,gBAAkBE,EAAO,QAAQ,GAAG,EAAI,EAC3DD,EAAc,QAAOA,EAAc,MAAQC,EAAO,KAAM,GAEhDA,EAAO,MAAM,GAAG,EACxB,QAAQ/H,GAAO,CAClB,GAAIA,EAAK,CACP,KAAM,CAAC3Z,EAAK,GAAG5I,CAAI,EAAIuiB,EAAI,MAAM,GAAG,EAC9BuF,EAAM9nB,EAAK,KAAK,GAAG,EAAE,OAAO,QAAQ,WAAY,EAAE,EAClDuqB,EAAa3hB,EAAI,KAAM,EACxByhB,EAAcE,CAAU,IAAGF,EAAcE,CAAU,EAAIzC,GACxDA,IAAQ,UAASuC,EAAcE,CAAU,EAAI,IAC7CzC,IAAQ,SAAQuC,EAAcE,CAAU,EAAI,IAC3C,MAAMzC,CAAG,IAAGuC,EAAcE,CAAU,EAAI,SAASzC,EAAK,EAAE,EACvE,CACA,CAAO,CAEP,CACE,MAAO,CACL,WAAAsC,EACA,cAAAC,CACD,CACH,EACMG,GAAwBtS,GAAM,CAClC,MAAM7M,EAAQ,CAAE,EAChB,MAAO,CAACwW,EAAG4C,EAAG/c,IAAM,CAClB,IAAI+iB,EAAc/iB,EACdA,GAAKA,EAAE,kBAAoBA,EAAE,cAAgBA,EAAE,aAAaA,EAAE,gBAAgB,GAAKA,EAAEA,EAAE,gBAAgB,IACzG+iB,EAAc,CACZ,GAAGA,EACH,CAAC/iB,EAAE,gBAAgB,EAAG,MACvB,GAEH,MAAMkB,EAAM6b,EAAI,KAAK,UAAUgG,CAAW,EAC1C,IAAIC,EAAMrf,EAAMzC,CAAG,EACnB,OAAK8hB,IACHA,EAAMxS,EAAGyI,GAAe8D,CAAC,EAAG/c,CAAC,EAC7B2D,EAAMzC,CAAG,EAAI8hB,GAERA,EAAI7I,CAAC,CACb,CACH,EACM8I,GAA2BzS,GAAM,CAAC2J,EAAG4C,EAAG/c,IAAMwQ,EAAGyI,GAAe8D,CAAC,EAAG/c,CAAC,EAAEma,CAAC,EAC9E,MAAM+I,EAAU,CACd,YAAY3pB,EAAU,GAAI,CACxB,KAAK,OAASigB,EAAW,OAAO,WAAW,EAC3C,KAAK,QAAUjgB,EACf,KAAK,KAAKA,CAAO,CACrB,CACE,KAAKqhB,EAAUrhB,EAAU,CACvB,cAAe,CAAA,CACnB,EAAK,CACD,KAAK,gBAAkBA,EAAQ,cAAc,iBAAmB,IAChE,MAAM4pB,EAAK5pB,EAAQ,oBAAsBupB,GAAwBG,GACjE,KAAK,QAAU,CACb,OAAQE,EAAG,CAAC9pB,EAAKwhB,IAAQ,CACvB,MAAMuI,EAAY,IAAI,KAAK,aAAa/pB,EAAK,CAC3C,GAAGwhB,CACb,CAAS,EACD,OAAOuF,GAAOgD,EAAU,OAAOhD,CAAG,CAC1C,CAAO,EACD,SAAU+C,EAAG,CAAC9pB,EAAKwhB,IAAQ,CACzB,MAAMuI,EAAY,IAAI,KAAK,aAAa/pB,EAAK,CAC3C,GAAGwhB,EACH,MAAO,UACjB,CAAS,EACD,OAAOuF,GAAOgD,EAAU,OAAOhD,CAAG,CAC1C,CAAO,EACD,SAAU+C,EAAG,CAAC9pB,EAAKwhB,IAAQ,CACzB,MAAMuI,EAAY,IAAI,KAAK,eAAe/pB,EAAK,CAC7C,GAAGwhB,CACb,CAAS,EACD,OAAOuF,GAAOgD,EAAU,OAAOhD,CAAG,CAC1C,CAAO,EACD,aAAc+C,EAAG,CAAC9pB,EAAKwhB,IAAQ,CAC7B,MAAMuI,EAAY,IAAI,KAAK,mBAAmB/pB,EAAK,CACjD,GAAGwhB,CACb,CAAS,EACD,OAAOuF,GAAOgD,EAAU,OAAOhD,EAAKvF,EAAI,OAAS,KAAK,CAC9D,CAAO,EACD,KAAMsI,EAAG,CAAC9pB,EAAKwhB,IAAQ,CACrB,MAAMuI,EAAY,IAAI,KAAK,WAAW/pB,EAAK,CACzC,GAAGwhB,CACb,CAAS,EACD,OAAOuF,GAAOgD,EAAU,OAAOhD,CAAG,CACnC,CAAA,CACF,CACL,CACE,IAAIlQ,EAAMmP,EAAI,CACZ,KAAK,QAAQnP,EAAK,YAAW,EAAG,KAAM,CAAA,EAAImP,CAC9C,CACE,UAAUnP,EAAMmP,EAAI,CAClB,KAAK,QAAQnP,EAAK,YAAW,EAAG,MAAM,EAAI4S,GAAsBzD,CAAE,CACtE,CACE,OAAOxkB,EAAOwoB,EAAQhqB,EAAKE,EAAU,CAAA,EAAI,CACvC,MAAM+pB,EAAUD,EAAO,MAAM,KAAK,eAAe,EACjD,GAAIC,EAAQ,OAAS,GAAKA,EAAQ,CAAC,EAAE,QAAQ,GAAG,EAAI,GAAKA,EAAQ,CAAC,EAAE,QAAQ,GAAG,EAAI,GAAKA,EAAQ,KAAKvjB,GAAKA,EAAE,QAAQ,GAAG,EAAI,EAAE,EAAG,CAC9H,MAAMwjB,EAAYD,EAAQ,UAAUvjB,GAAKA,EAAE,QAAQ,GAAG,EAAI,EAAE,EAC5DujB,EAAQ,CAAC,EAAI,CAACA,EAAQ,CAAC,EAAG,GAAGA,EAAQ,OAAO,EAAGC,CAAS,CAAC,EAAE,KAAK,KAAK,eAAe,CAC1F,CAyBI,OAxBeD,EAAQ,OAAO,CAACE,EAAKzjB,IAAM,OACxC,KAAM,CACJ,WAAA2iB,EACA,cAAAC,CACR,EAAUH,GAAeziB,CAAC,EACpB,GAAI,KAAK,QAAQ2iB,CAAU,EAAG,CAC5B,IAAIe,EAAYD,EAChB,GAAI,CACF,MAAME,IAAajrB,EAAAc,GAAA,YAAAA,EAAS,eAAT,YAAAd,EAAwBc,EAAQ,oBAAqB,CAAE,EACpEwjB,EAAI2G,EAAW,QAAUA,EAAW,KAAOnqB,EAAQ,QAAUA,EAAQ,KAAOF,EAClFoqB,EAAY,KAAK,QAAQf,CAAU,EAAEc,EAAKzG,EAAG,CAC3C,GAAG4F,EACH,GAAGppB,EACH,GAAGmqB,CACf,CAAW,CACF,OAAQzrB,EAAO,CACd,KAAK,OAAO,KAAKA,CAAK,CAChC,CACQ,OAAOwrB,CACf,MACQ,KAAK,OAAO,KAAK,oCAAoCf,CAAU,EAAE,EAEnE,OAAOc,CACR,EAAE3oB,CAAK,CAEZ,CACA,CAEA,MAAM8oB,GAAgB,CAACC,EAAG1T,IAAS,CAC7B0T,EAAE,QAAQ1T,CAAI,IAAM,SACtB,OAAO0T,EAAE,QAAQ1T,CAAI,EACrB0T,EAAE,eAEN,EACA,MAAMC,WAAkBpK,EAAa,CACnC,YAAYqK,EAASC,EAAOnJ,EAAUrhB,EAAU,CAAA,EAAI,SAClD,MAAO,EACP,KAAK,QAAUuqB,EACf,KAAK,MAAQC,EACb,KAAK,SAAWnJ,EAChB,KAAK,cAAgBA,EAAS,cAC9B,KAAK,QAAUrhB,EACf,KAAK,OAASigB,EAAW,OAAO,kBAAkB,EAClD,KAAK,aAAe,CAAE,EACtB,KAAK,iBAAmBjgB,EAAQ,kBAAoB,GACpD,KAAK,aAAe,EACpB,KAAK,WAAaA,EAAQ,YAAc,EAAIA,EAAQ,WAAa,EACjE,KAAK,aAAeA,EAAQ,cAAgB,EAAIA,EAAQ,aAAe,IACvE,KAAK,MAAQ,CAAE,EACf,KAAK,MAAQ,CAAE,GACff,GAAAC,EAAA,KAAK,UAAL,YAAAA,EAAc,OAAd,MAAAD,EAAA,KAAAC,EAAqBmiB,EAAUrhB,EAAQ,QAASA,EACpD,CACE,UAAUyqB,EAAWrpB,EAAYpB,EAAS0qB,EAAU,CAClD,MAAMC,EAAS,CAAE,EACXC,EAAU,CAAE,EACZC,EAAkB,CAAE,EACpBC,EAAmB,CAAE,EAC3B,OAAAL,EAAU,QAAQ3qB,GAAO,CACvB,IAAIirB,EAAmB,GACvB3pB,EAAW,QAAQxB,GAAM,CACvB,MAAM+W,EAAO,GAAG7W,CAAG,IAAIF,CAAE,GACrB,CAACI,EAAQ,QAAU,KAAK,MAAM,kBAAkBF,EAAKF,CAAE,EACzD,KAAK,MAAM+W,CAAI,EAAI,EACV,KAAK,MAAMA,CAAI,EAAI,IAAc,KAAK,MAAMA,CAAI,IAAM,EAC3DiU,EAAQjU,CAAI,IAAM,SAAWiU,EAAQjU,CAAI,EAAI,KAEjD,KAAK,MAAMA,CAAI,EAAI,EACnBoU,EAAmB,GACfH,EAAQjU,CAAI,IAAM,SAAWiU,EAAQjU,CAAI,EAAI,IAC7CgU,EAAOhU,CAAI,IAAM,SAAWgU,EAAOhU,CAAI,EAAI,IAC3CmU,EAAiBlrB,CAAE,IAAM,SAAWkrB,EAAiBlrB,CAAE,EAAI,KAEzE,CAAO,EACImrB,IAAkBF,EAAgB/qB,CAAG,EAAI,GACpD,CAAK,GACG,OAAO,KAAK6qB,CAAM,EAAE,QAAU,OAAO,KAAKC,CAAO,EAAE,SACrD,KAAK,MAAM,KAAK,CACd,QAAAA,EACA,aAAc,OAAO,KAAKA,CAAO,EAAE,OACnC,OAAQ,CAAE,EACV,OAAQ,CAAE,EACV,SAAAF,CACR,CAAO,EAEI,CACL,OAAQ,OAAO,KAAKC,CAAM,EAC1B,QAAS,OAAO,KAAKC,CAAO,EAC5B,gBAAiB,OAAO,KAAKC,CAAe,EAC5C,iBAAkB,OAAO,KAAKC,CAAgB,CAC/C,CACL,CACE,OAAOnU,EAAMqU,EAAK/R,EAAM,CACtB,MAAM8D,EAAIpG,EAAK,MAAM,GAAG,EAClB7W,EAAMid,EAAE,CAAC,EACTnd,EAAKmd,EAAE,CAAC,EACViO,GAAK,KAAK,KAAK,gBAAiBlrB,EAAKF,EAAIorB,CAAG,EAC5C,CAACA,GAAO/R,GACV,KAAK,MAAM,kBAAkBnZ,EAAKF,EAAIqZ,EAAM,OAAW,OAAW,CAChE,SAAU,EAClB,CAAO,EAEH,KAAK,MAAMtC,CAAI,EAAIqU,EAAM,GAAK,EAC1BA,GAAO/R,IAAM,KAAK,MAAMtC,CAAI,EAAI,GACpC,MAAMsU,EAAS,CAAE,EACjB,KAAK,MAAM,QAAQZ,GAAK,CACtB3M,GAAS2M,EAAE,OAAQ,CAACvqB,CAAG,EAAGF,CAAE,EAC5BwqB,GAAcC,EAAG1T,CAAI,EACjBqU,GAAKX,EAAE,OAAO,KAAKW,CAAG,EACtBX,EAAE,eAAiB,GAAK,CAACA,EAAE,OAC7B,OAAO,KAAKA,EAAE,MAAM,EAAE,QAAQ7G,GAAK,CAC5ByH,EAAOzH,CAAC,IAAGyH,EAAOzH,CAAC,EAAI,CAAE,GAC9B,MAAM0H,EAAab,EAAE,OAAO7G,CAAC,EACzB0H,EAAW,QACbA,EAAW,QAAQpoB,GAAK,CAClBmoB,EAAOzH,CAAC,EAAE1gB,CAAC,IAAM,SAAWmoB,EAAOzH,CAAC,EAAE1gB,CAAC,EAAI,GAC7D,CAAa,CAEb,CAAS,EACDunB,EAAE,KAAO,GACLA,EAAE,OAAO,OACXA,EAAE,SAASA,EAAE,MAAM,EAEnBA,EAAE,SAAU,EAGtB,CAAK,EACD,KAAK,KAAK,SAAUY,CAAM,EAC1B,KAAK,MAAQ,KAAK,MAAM,OAAOZ,GAAK,CAACA,EAAE,IAAI,CAC/C,CACE,KAAKvqB,EAAKF,EAAIurB,EAAQC,EAAQ,EAAGC,EAAO,KAAK,aAAcX,EAAU,CACnE,GAAI,CAAC5qB,EAAI,OAAQ,OAAO4qB,EAAS,KAAM,CAAA,CAAE,EACzC,GAAI,KAAK,cAAgB,KAAK,iBAAkB,CAC9C,KAAK,aAAa,KAAK,CACrB,IAAA5qB,EACA,GAAAF,EACA,OAAAurB,EACA,MAAAC,EACA,KAAAC,EACA,SAAAX,CACR,CAAO,EACD,MACN,CACI,KAAK,eACL,MAAMY,EAAW,CAACN,EAAK/R,IAAS,CAE9B,GADA,KAAK,eACD,KAAK,aAAa,OAAS,EAAG,CAChC,MAAMsG,EAAO,KAAK,aAAa,MAAO,EACtC,KAAK,KAAKA,EAAK,IAAKA,EAAK,GAAIA,EAAK,OAAQA,EAAK,MAAOA,EAAK,KAAMA,EAAK,QAAQ,CACtF,CACM,GAAIyL,GAAO/R,GAAQmS,EAAQ,KAAK,WAAY,CAC1C,WAAW,IAAM,CACf,KAAK,KAAK,KAAK,KAAMtrB,EAAKF,EAAIurB,EAAQC,EAAQ,EAAGC,EAAO,EAAGX,CAAQ,CACpE,EAAEW,CAAI,EACP,MACR,CACMX,EAASM,EAAK/R,CAAI,CACnB,EACK6M,EAAK,KAAK,QAAQqF,CAAM,EAAE,KAAK,KAAK,OAAO,EACjD,GAAIrF,EAAG,SAAW,EAAG,CACnB,GAAI,CACF,MAAMvf,EAAIuf,EAAGhmB,EAAKF,CAAE,EAChB2G,GAAK,OAAOA,EAAE,MAAS,WACzBA,EAAE,KAAK0S,GAAQqS,EAAS,KAAMrS,CAAI,CAAC,EAAE,MAAMqS,CAAQ,EAEnDA,EAAS,KAAM/kB,CAAC,CAEnB,OAAQykB,EAAK,CACZM,EAASN,CAAG,CACpB,CACM,MACN,CACI,OAAOlF,EAAGhmB,EAAKF,EAAI0rB,CAAQ,CAC/B,CACE,eAAeb,EAAWrpB,EAAYpB,EAAU,CAAA,EAAI0qB,EAAU,CAC5D,GAAI,CAAC,KAAK,QACR,YAAK,OAAO,KAAK,gEAAgE,EAC1EA,GAAYA,EAAU,EAE3BvrB,EAASsrB,CAAS,IAAGA,EAAY,KAAK,cAAc,mBAAmBA,CAAS,GAChFtrB,EAASiC,CAAU,IAAGA,EAAa,CAACA,CAAU,GAClD,MAAMupB,EAAS,KAAK,UAAUF,EAAWrpB,EAAYpB,EAAS0qB,CAAQ,EACtE,GAAI,CAACC,EAAO,OAAO,OACjB,OAAKA,EAAO,QAAQ,QAAQD,EAAU,EAC/B,KAETC,EAAO,OAAO,QAAQhU,GAAQ,CAC5B,KAAK,QAAQA,CAAI,CACvB,CAAK,CACL,CACE,KAAK8T,EAAWrpB,EAAYspB,EAAU,CACpC,KAAK,eAAeD,EAAWrpB,EAAY,CAAA,EAAIspB,CAAQ,CAC3D,CACE,OAAOD,EAAWrpB,EAAYspB,EAAU,CACtC,KAAK,eAAeD,EAAWrpB,EAAY,CACzC,OAAQ,EACT,EAAEspB,CAAQ,CACf,CACE,QAAQ/T,EAAMhM,EAAS,GAAI,CACzB,MAAMoS,EAAIpG,EAAK,MAAM,GAAG,EAClB7W,EAAMid,EAAE,CAAC,EACTnd,EAAKmd,EAAE,CAAC,EACd,KAAK,KAAKjd,EAAKF,EAAI,OAAQ,OAAW,OAAW,CAACorB,EAAK/R,IAAS,CAC1D+R,GAAK,KAAK,OAAO,KAAK,GAAGrgB,CAAM,qBAAqB/K,CAAE,iBAAiBE,CAAG,UAAWkrB,CAAG,EACxF,CAACA,GAAO/R,GAAM,KAAK,OAAO,IAAI,GAAGtO,CAAM,oBAAoB/K,CAAE,iBAAiBE,CAAG,GAAImZ,CAAI,EAC7F,KAAK,OAAOtC,EAAMqU,EAAK/R,CAAI,CACjC,CAAK,CACL,CACE,YAAYwR,EAAW5oB,EAAW8F,EAAK4jB,EAAeC,EAAUxrB,EAAU,CAAE,EAAEyrB,EAAM,IAAM,GAAI,eAC5F,IAAIxsB,GAAAC,EAAA,KAAK,WAAL,YAAAA,EAAe,QAAf,MAAAD,EAAsB,oBAAsB,GAACG,GAAAC,EAAA,KAAK,WAAL,YAAAA,EAAe,QAAf,MAAAD,EAAsB,mBAAmByC,IAAY,CACpG,KAAK,OAAO,KAAK,qBAAqB8F,CAAG,uBAAuB9F,CAAS,uBAAwB,0NAA0N,EAC3T,MACN,CACI,GAAI,EAAqB8F,GAAQ,MAAQA,IAAQ,IACjD,KAAI+jB,EAAA,KAAK,UAAL,MAAAA,EAAc,OAAQ,CACxB,MAAMC,EAAO,CACX,GAAG3rB,EACH,SAAAwrB,CACD,EACK1F,EAAK,KAAK,QAAQ,OAAO,KAAK,KAAK,OAAO,EAChD,GAAIA,EAAG,OAAS,EACd,GAAI,CACF,IAAIvf,EACAuf,EAAG,SAAW,EAChBvf,EAAIuf,EAAG2E,EAAW5oB,EAAW8F,EAAK4jB,EAAeI,CAAI,EAErDplB,EAAIuf,EAAG2E,EAAW5oB,EAAW8F,EAAK4jB,CAAa,EAE7ChlB,GAAK,OAAOA,EAAE,MAAS,WACzBA,EAAE,KAAK0S,GAAQwS,EAAI,KAAMxS,CAAI,CAAC,EAAE,MAAMwS,CAAG,EAEzCA,EAAI,KAAMllB,CAAC,CAEd,OAAQykB,EAAK,CACZS,EAAIT,CAAG,CACjB,MAEQlF,EAAG2E,EAAW5oB,EAAW8F,EAAK4jB,EAAeE,EAAKE,CAAI,CAE9D,CACQ,CAAClB,GAAa,CAACA,EAAU,CAAC,GAC9B,KAAK,MAAM,YAAYA,EAAU,CAAC,EAAG5oB,EAAW8F,EAAK4jB,CAAa,EACtE,CACA,CAEA,MAAM7T,GAAM,KAAO,CACjB,MAAO,GACP,UAAW,GACX,GAAI,CAAC,aAAa,EAClB,UAAW,CAAC,aAAa,EACzB,YAAa,CAAC,KAAK,EACnB,WAAY,GACZ,cAAe,GACf,yBAA0B,GAC1B,KAAM,MACN,QAAS,GACT,qBAAsB,GACtB,aAAc,IACd,YAAa,IACb,gBAAiB,IACjB,iBAAkB,IAClB,wBAAyB,GACzB,YAAa,GACb,cAAe,GACf,cAAe,WACf,mBAAoB,GACpB,kBAAmB,GACnB,4BAA6B,GAC7B,YAAa,GACb,wBAAyB,GACzB,WAAY,GACZ,kBAAmB,GACnB,cAAe,GACf,WAAY,GACZ,sBAAuB,GACvB,uBAAwB,GACxB,4BAA6B,GAC7B,wBAAyB,GACzB,iCAAkC1Y,GAAQ,CACxC,IAAI4E,EAAM,CAAE,EAIZ,GAHI,OAAO5E,EAAK,CAAC,GAAM,WAAU4E,EAAM5E,EAAK,CAAC,GACzCG,EAASH,EAAK,CAAC,CAAC,IAAG4E,EAAI,aAAe5E,EAAK,CAAC,GAC5CG,EAASH,EAAK,CAAC,CAAC,IAAG4E,EAAI,aAAe5E,EAAK,CAAC,GAC5C,OAAOA,EAAK,CAAC,GAAM,UAAY,OAAOA,EAAK,CAAC,GAAM,SAAU,CAC9D,MAAMgB,EAAUhB,EAAK,CAAC,GAAKA,EAAK,CAAC,EACjC,OAAO,KAAKgB,CAAO,EAAE,QAAQ2H,GAAO,CAClC/D,EAAI+D,CAAG,EAAI3H,EAAQ2H,CAAG,CAC9B,CAAO,CACP,CACI,OAAO/D,CACR,EACD,cAAe,CACb,YAAa,GACb,OAAQtC,GAASA,EACjB,OAAQ,KACR,OAAQ,KACR,gBAAiB,IACjB,eAAgB,IAChB,cAAe,MACf,cAAe,IACf,wBAAyB,IACzB,YAAa,IACb,gBAAiB,EAClB,EACD,oBAAqB,EACvB,GACMsqB,GAAmB5rB,GAAW,SAClC,OAAIb,EAASa,EAAQ,EAAE,IAAGA,EAAQ,GAAK,CAACA,EAAQ,EAAE,GAC9Cb,EAASa,EAAQ,WAAW,IAAGA,EAAQ,YAAc,CAACA,EAAQ,WAAW,GACzEb,EAASa,EAAQ,UAAU,IAAGA,EAAQ,WAAa,CAACA,EAAQ,UAAU,KACtEf,GAAAC,EAAAc,EAAQ,gBAAR,YAAAd,EAAuB,UAAvB,YAAAD,EAAA,KAAAC,EAAiC,WAAY,IAC/Cc,EAAQ,cAAgBA,EAAQ,cAAc,OAAO,CAAC,QAAQ,CAAC,GAE7D,OAAOA,EAAQ,eAAkB,YAAWA,EAAQ,UAAYA,EAAQ,eACrEA,CACT,EAEM6rB,GAAO,IAAM,CAAE,EACfC,GAAsBC,GAAQ,CACrB,OAAO,oBAAoB,OAAO,eAAeA,CAAI,CAAC,EAC9D,QAAQ9B,GAAO,CACd,OAAO8B,EAAK9B,CAAG,GAAM,aACvB8B,EAAK9B,CAAG,EAAI8B,EAAK9B,CAAG,EAAE,KAAK8B,CAAI,EAErC,CAAG,CACH,EACA,MAAMC,WAAa9L,EAAa,CAC9B,YAAYlgB,EAAU,CAAE,EAAE0qB,EAAU,CASlC,GARA,MAAO,EACP,KAAK,QAAUkB,GAAiB5rB,CAAO,EACvC,KAAK,SAAW,CAAE,EAClB,KAAK,OAASigB,EACd,KAAK,QAAU,CACb,SAAU,CAAA,CACX,EACD6L,GAAoB,IAAI,EACpBpB,GAAY,CAAC,KAAK,eAAiB,CAAC1qB,EAAQ,QAAS,CACvD,GAAI,CAAC,KAAK,QAAQ,UAChB,YAAK,KAAKA,EAAS0qB,CAAQ,EACpB,KAET,WAAW,IAAM,CACf,KAAK,KAAK1qB,EAAS0qB,CAAQ,CAC5B,EAAE,CAAC,CACV,CACA,CACE,KAAK1qB,EAAU,CAAE,EAAE0qB,EAAU,CAC3B,KAAK,eAAiB,GAClB,OAAO1qB,GAAY,aACrB0qB,EAAW1qB,EACXA,EAAU,CAAE,GAEVA,EAAQ,WAAa,MAAQA,EAAQ,KACnCb,EAASa,EAAQ,EAAE,EACrBA,EAAQ,UAAYA,EAAQ,GACnBA,EAAQ,GAAG,QAAQ,aAAa,EAAI,IAC7CA,EAAQ,UAAYA,EAAQ,GAAG,CAAC,IAGpC,MAAMisB,EAAUvU,GAAK,EACrB,KAAK,QAAU,CACb,GAAGuU,EACH,GAAG,KAAK,QACR,GAAGL,GAAiB5rB,CAAO,CAC5B,EACD,KAAK,QAAQ,cAAgB,CAC3B,GAAGisB,EAAQ,cACX,GAAG,KAAK,QAAQ,aACjB,EACGjsB,EAAQ,eAAiB,SAC3B,KAAK,QAAQ,wBAA0BA,EAAQ,cAE7CA,EAAQ,cAAgB,SAC1B,KAAK,QAAQ,uBAAyBA,EAAQ,aAEhD,MAAMksB,EAAsBC,GACrBA,EACD,OAAOA,GAAkB,WAAmB,IAAIA,EAC7CA,EAFoB,KAI7B,GAAI,CAAC,KAAK,QAAQ,QAAS,CACrB,KAAK,QAAQ,OACflM,EAAW,KAAKiM,EAAoB,KAAK,QAAQ,MAAM,EAAG,KAAK,OAAO,EAEtEjM,EAAW,KAAK,KAAM,KAAK,OAAO,EAEpC,IAAI4J,EACA,KAAK,QAAQ,UACfA,EAAY,KAAK,QAAQ,UAEzBA,EAAYF,GAEd,MAAMyC,EAAK,IAAIhH,GAAa,KAAK,OAAO,EACxC,KAAK,MAAQ,IAAI7E,GAAc,KAAK,QAAQ,UAAW,KAAK,OAAO,EACnE,MAAMxD,EAAI,KAAK,SACfA,EAAE,OAASkD,EACXlD,EAAE,cAAgB,KAAK,MACvBA,EAAE,cAAgBqP,EAClBrP,EAAE,eAAiB,IAAImJ,GAAekG,EAAI,CACxC,QAAS,KAAK,QAAQ,gBACtB,qBAAsB,KAAK,QAAQ,oBAC3C,CAAO,EACGvC,IAAc,CAAC,KAAK,QAAQ,cAAc,QAAU,KAAK,QAAQ,cAAc,SAAWoC,EAAQ,cAAc,UAClHlP,EAAE,UAAYmP,EAAoBrC,CAAS,EAC3C9M,EAAE,UAAU,KAAKA,EAAG,KAAK,OAAO,EAChC,KAAK,QAAQ,cAAc,OAASA,EAAE,UAAU,OAAO,KAAKA,EAAE,SAAS,GAEzEA,EAAE,aAAe,IAAI+J,GAAa,KAAK,OAAO,EAC9C/J,EAAE,MAAQ,CACR,mBAAoB,KAAK,mBAAmB,KAAK,IAAI,CACtD,EACDA,EAAE,iBAAmB,IAAIuN,GAAU4B,EAAoB,KAAK,QAAQ,OAAO,EAAGnP,EAAE,cAAeA,EAAG,KAAK,OAAO,EAC9GA,EAAE,iBAAiB,GAAG,IAAK,CAACtC,KAAUzb,IAAS,CAC7C,KAAK,KAAKyb,EAAO,GAAGzb,CAAI,CAChC,CAAO,EACG,KAAK,QAAQ,mBACf+d,EAAE,iBAAmBmP,EAAoB,KAAK,QAAQ,gBAAgB,EAClEnP,EAAE,iBAAiB,MAAMA,EAAE,iBAAiB,KAAKA,EAAG,KAAK,QAAQ,UAAW,KAAK,OAAO,GAE1F,KAAK,QAAQ,aACfA,EAAE,WAAamP,EAAoB,KAAK,QAAQ,UAAU,EACtDnP,EAAE,WAAW,MAAMA,EAAE,WAAW,KAAK,IAAI,GAE/C,KAAK,WAAa,IAAIqE,GAAW,KAAK,SAAU,KAAK,OAAO,EAC5D,KAAK,WAAW,GAAG,IAAK,CAAC3G,KAAUzb,IAAS,CAC1C,KAAK,KAAKyb,EAAO,GAAGzb,CAAI,CAChC,CAAO,EACD,KAAK,QAAQ,SAAS,QAAQZ,GAAK,CAC7BA,EAAE,MAAMA,EAAE,KAAK,IAAI,CAC/B,CAAO,CACP,CAGI,GAFA,KAAK,OAAS,KAAK,QAAQ,cAAc,OACpCssB,IAAUA,EAAWmB,IACtB,KAAK,QAAQ,aAAe,CAAC,KAAK,SAAS,kBAAoB,CAAC,KAAK,QAAQ,IAAK,CACpF,MAAMnH,EAAQ,KAAK,SAAS,cAAc,iBAAiB,KAAK,QAAQ,WAAW,EAC/EA,EAAM,OAAS,GAAKA,EAAM,CAAC,IAAM,QAAO,KAAK,QAAQ,IAAMA,EAAM,CAAC,EAC5E,CACQ,CAAC,KAAK,SAAS,kBAAoB,CAAC,KAAK,QAAQ,KACnD,KAAK,OAAO,KAAK,yDAAyD,EAE3D,CAAC,cAAe,oBAAqB,oBAAqB,mBAAmB,EACrF,QAAQyG,GAAU,CACzB,KAAKA,CAAM,EAAI,IAAInsB,IAAS,KAAK,MAAMmsB,CAAM,EAAE,GAAGnsB,CAAI,CAC5D,CAAK,EACuB,CAAC,cAAe,eAAgB,oBAAqB,sBAAsB,EACnF,QAAQmsB,GAAU,CAChC,KAAKA,CAAM,EAAI,IAAInsB,KACjB,KAAK,MAAMmsB,CAAM,EAAE,GAAGnsB,CAAI,EACnB,KAEf,CAAK,EACD,MAAMqtB,EAAW/P,GAAO,EAClBgQ,EAAO,IAAM,CACjB,MAAMC,EAAS,CAACvB,EAAK9nB,IAAM,CACzB,KAAK,eAAiB,GAClB,KAAK,eAAiB,CAAC,KAAK,sBAAsB,KAAK,OAAO,KAAK,uEAAuE,EAC9I,KAAK,cAAgB,GAChB,KAAK,QAAQ,SAAS,KAAK,OAAO,IAAI,cAAe,KAAK,OAAO,EACtE,KAAK,KAAK,cAAe,KAAK,OAAO,EACrCmpB,EAAS,QAAQnpB,CAAC,EAClBwnB,EAASM,EAAK9nB,CAAC,CAChB,EACD,GAAI,KAAK,WAAa,CAAC,KAAK,cAAe,OAAOqpB,EAAO,KAAM,KAAK,EAAE,KAAK,IAAI,CAAC,EAChF,KAAK,eAAe,KAAK,QAAQ,IAAKA,CAAM,CAC7C,EACD,OAAI,KAAK,QAAQ,WAAa,CAAC,KAAK,QAAQ,UAC1CD,EAAM,EAEN,WAAWA,EAAM,CAAC,EAEbD,CACX,CACE,cAAczqB,EAAU8oB,EAAWmB,GAAM,SACvC,IAAIW,EAAe9B,EACnB,MAAMpG,EAAUnlB,EAASyC,CAAQ,EAAIA,EAAW,KAAK,SAErD,GADI,OAAOA,GAAa,aAAY4qB,EAAe5qB,GAC/C,CAAC,KAAK,QAAQ,WAAa,KAAK,QAAQ,wBAAyB,CACnE,IAAI0iB,GAAA,YAAAA,EAAS,iBAAkB,WAAa,CAAC,KAAK,QAAQ,SAAW,KAAK,QAAQ,QAAQ,SAAW,GAAI,OAAOkI,EAAc,EAC9H,MAAM7B,EAAS,CAAE,EACX8B,EAAS3sB,GAAO,CAEpB,GADI,CAACA,GACDA,IAAQ,SAAU,OACT,KAAK,SAAS,cAAc,mBAAmBA,CAAG,EAC1D,QAAQ0jB,GAAK,CACZA,IAAM,UACNmH,EAAO,QAAQnH,CAAC,EAAI,GAAGmH,EAAO,KAAKnH,CAAC,CAClD,CAAS,CACF,EACIc,EAIHmI,EAAOnI,CAAO,EAHI,KAAK,SAAS,cAAc,iBAAiB,KAAK,QAAQ,WAAW,EAC7E,QAAQd,GAAKiJ,EAAOjJ,CAAC,CAAC,GAIlCvkB,GAAAC,EAAA,KAAK,QAAQ,UAAb,YAAAA,EAAsB,UAAtB,MAAAD,EAAA,KAAAC,EAAgCskB,GAAKiJ,EAAOjJ,CAAC,GAC7C,KAAK,SAAS,iBAAiB,KAAKmH,EAAQ,KAAK,QAAQ,GAAIhnB,GAAK,CAC5D,CAACA,GAAK,CAAC,KAAK,kBAAoB,KAAK,UAAU,KAAK,oBAAoB,KAAK,QAAQ,EACzF6oB,EAAa7oB,CAAC,CACtB,CAAO,CACP,MACM6oB,EAAa,IAAI,CAEvB,CACE,gBAAgBnJ,EAAMzjB,EAAI8qB,EAAU,CAClC,MAAM2B,EAAW/P,GAAO,EACxB,OAAI,OAAO+G,GAAS,aAClBqH,EAAWrH,EACXA,EAAO,QAEL,OAAOzjB,GAAO,aAChB8qB,EAAW9qB,EACXA,EAAK,QAEFyjB,IAAMA,EAAO,KAAK,WAClBzjB,IAAIA,EAAK,KAAK,QAAQ,IACtB8qB,IAAUA,EAAWmB,IAC1B,KAAK,SAAS,iBAAiB,OAAOxI,EAAMzjB,EAAIorB,GAAO,CACrDqB,EAAS,QAAS,EAClB3B,EAASM,CAAG,CAClB,CAAK,EACMqB,CACX,CACE,IAAIvL,EAAQ,CACV,GAAI,CAACA,EAAQ,MAAM,IAAI,MAAM,+FAA+F,EAC5H,GAAI,CAACA,EAAO,KAAM,MAAM,IAAI,MAAM,0FAA0F,EAC5H,OAAIA,EAAO,OAAS,YAClB,KAAK,QAAQ,QAAUA,IAErBA,EAAO,OAAS,UAAYA,EAAO,KAAOA,EAAO,MAAQA,EAAO,SAClE,KAAK,QAAQ,OAASA,GAEpBA,EAAO,OAAS,qBAClB,KAAK,QAAQ,iBAAmBA,GAE9BA,EAAO,OAAS,eAClB,KAAK,QAAQ,WAAaA,GAExBA,EAAO,OAAS,iBAClBD,GAAc,iBAAiBC,CAAM,EAEnCA,EAAO,OAAS,cAClB,KAAK,QAAQ,UAAYA,GAEvBA,EAAO,OAAS,YAClB,KAAK,QAAQ,SAAS,KAAKA,CAAM,EAE5B,IACX,CACE,oBAAoB0C,EAAG,CACrB,GAAI,GAACA,GAAK,CAAC,KAAK,YACZ,GAAC,SAAU,KAAK,EAAE,QAAQA,CAAC,EAAI,IACnC,SAASkJ,EAAK,EAAGA,EAAK,KAAK,UAAU,OAAQA,IAAM,CACjD,MAAMC,EAAY,KAAK,UAAUD,CAAE,EACnC,GAAI,GAAC,SAAU,KAAK,EAAE,QAAQC,CAAS,EAAI,KACvC,KAAK,MAAM,4BAA4BA,CAAS,EAAG,CACrD,KAAK,iBAAmBA,EACxB,KACR,CACA,CACQ,CAAC,KAAK,kBAAoB,KAAK,UAAU,QAAQnJ,CAAC,EAAI,GAAK,KAAK,MAAM,4BAA4BA,CAAC,IACrG,KAAK,iBAAmBA,EACxB,KAAK,UAAU,QAAQA,CAAC,GAE9B,CACE,eAAe1jB,EAAK4qB,EAAU,CAC5B,KAAK,qBAAuB5qB,EAC5B,MAAMusB,EAAW/P,GAAO,EACxB,KAAK,KAAK,mBAAoBxc,CAAG,EACjC,MAAM8sB,EAAcpJ,GAAK,CACvB,KAAK,SAAWA,EAChB,KAAK,UAAY,KAAK,SAAS,cAAc,mBAAmBA,CAAC,EACjE,KAAK,iBAAmB,OACxB,KAAK,oBAAoBA,CAAC,CAC3B,EACKqJ,EAAO,CAAC7B,EAAKxH,IAAM,CACnBA,EACE,KAAK,uBAAyB1jB,IAChC8sB,EAAYpJ,CAAC,EACb,KAAK,WAAW,eAAeA,CAAC,EAChC,KAAK,qBAAuB,OAC5B,KAAK,KAAK,kBAAmBA,CAAC,EAC9B,KAAK,OAAO,IAAI,kBAAmBA,CAAC,GAGtC,KAAK,qBAAuB,OAE9B6I,EAAS,QAAQ,IAAIrtB,IAAS,KAAK,EAAE,GAAGA,CAAI,CAAC,EACzC0rB,GAAUA,EAASM,EAAK,IAAIhsB,IAAS,KAAK,EAAE,GAAGA,CAAI,CAAC,CACzD,EACK8tB,EAASzJ,GAAQ,SACjB,CAACvjB,GAAO,CAACujB,GAAQ,KAAK,SAAS,mBAAkBA,EAAO,CAAE,GAC9D,MAAM0J,EAAK5tB,EAASkkB,CAAI,EAAIA,EAAOA,GAAQA,EAAK,CAAC,EAC3CG,EAAI,KAAK,MAAM,4BAA4BuJ,CAAE,EAAIA,EAAK,KAAK,SAAS,cAAc,sBAAsB5tB,EAASkkB,CAAI,EAAI,CAACA,CAAI,EAAIA,CAAI,EACxIG,IACG,KAAK,UACRoJ,EAAYpJ,CAAC,EAEV,KAAK,WAAW,UAAU,KAAK,WAAW,eAAeA,CAAC,GAC/DvkB,GAAAC,EAAA,KAAK,SAAS,mBAAd,YAAAA,EAAgC,oBAAhC,MAAAD,EAAA,KAAAC,EAAoDskB,IAEtD,KAAK,cAAcA,EAAGwH,GAAO,CAC3B6B,EAAK7B,EAAKxH,CAAC,CACnB,CAAO,CACF,EACD,MAAI,CAAC1jB,GAAO,KAAK,SAAS,kBAAoB,CAAC,KAAK,SAAS,iBAAiB,MAC5EgtB,EAAO,KAAK,SAAS,iBAAiB,OAAM,CAAE,EACrC,CAAChtB,GAAO,KAAK,SAAS,kBAAoB,KAAK,SAAS,iBAAiB,MAC9E,KAAK,SAAS,iBAAiB,OAAO,SAAW,EACnD,KAAK,SAAS,iBAAiB,OAAM,EAAG,KAAKgtB,CAAM,EAEnD,KAAK,SAAS,iBAAiB,OAAOA,CAAM,EAG9CA,EAAOhtB,CAAG,EAELusB,CACX,CACE,UAAUvsB,EAAKF,EAAIkC,EAAW,CAC5B,MAAMkrB,EAAS,CAACrlB,EAAKgkB,KAAS5sB,IAAS,CACrC,IAAI0H,EACA,OAAOklB,GAAS,SAClBllB,EAAI,KAAK,QAAQ,iCAAiC,CAACkB,EAAKgkB,CAAI,EAAE,OAAO5sB,CAAI,CAAC,EAE1E0H,EAAI,CACF,GAAGklB,CACJ,EAEHllB,EAAE,IAAMA,EAAE,KAAOumB,EAAO,IACxBvmB,EAAE,KAAOA,EAAE,MAAQumB,EAAO,KAC1BvmB,EAAE,GAAKA,EAAE,IAAMumB,EAAO,GAClBvmB,EAAE,YAAc,KAAIA,EAAE,UAAYA,EAAE,WAAa3E,GAAakrB,EAAO,WACzE,MAAMjO,EAAe,KAAK,QAAQ,cAAgB,IAClD,IAAIkO,EACJ,OAAIxmB,EAAE,WAAa,MAAM,QAAQkB,CAAG,EAClCslB,EAAYtlB,EAAI,IAAInF,GAAK,GAAGiE,EAAE,SAAS,GAAGsY,CAAY,GAAGvc,CAAC,EAAE,EAE5DyqB,EAAYxmB,EAAE,UAAY,GAAGA,EAAE,SAAS,GAAGsY,CAAY,GAAGpX,CAAG,GAAKA,EAE7D,KAAK,EAAEslB,EAAWxmB,CAAC,CAC3B,EACD,OAAItH,EAASW,CAAG,EACdktB,EAAO,IAAMltB,EAEbktB,EAAO,KAAOltB,EAEhBktB,EAAO,GAAKptB,EACZotB,EAAO,UAAYlrB,EACZkrB,CACX,CACE,KAAKhuB,EAAM,OACT,OAAOE,EAAA,KAAK,aAAL,YAAAA,EAAiB,UAAU,GAAGF,EACzC,CACE,UAAUA,EAAM,OACd,OAAOE,EAAA,KAAK,aAAL,YAAAA,EAAiB,OAAO,GAAGF,EACtC,CACE,oBAAoBY,EAAI,CACtB,KAAK,QAAQ,UAAYA,CAC7B,CACE,mBAAmBA,EAAII,EAAU,GAAI,CACnC,GAAI,CAAC,KAAK,cACR,YAAK,OAAO,KAAK,kDAAmD,KAAK,SAAS,EAC3E,GAET,GAAI,CAAC,KAAK,WAAa,CAAC,KAAK,UAAU,OACrC,YAAK,OAAO,KAAK,6DAA8D,KAAK,SAAS,EACtF,GAET,MAAMF,EAAME,EAAQ,KAAO,KAAK,kBAAoB,KAAK,UAAU,CAAC,EAC9DktB,EAAc,KAAK,QAAU,KAAK,QAAQ,YAAc,GACxDC,EAAU,KAAK,UAAU,KAAK,UAAU,OAAS,CAAC,EACxD,GAAIrtB,EAAI,gBAAkB,SAAU,MAAO,GAC3C,MAAMI,EAAiB,CAACsjB,EAAG1gB,IAAM,CAC/B,MAAMsqB,EAAY,KAAK,SAAS,iBAAiB,MAAM,GAAG5J,CAAC,IAAI1gB,CAAC,EAAE,EAClE,OAAOsqB,IAAc,IAAMA,IAAc,GAAKA,IAAc,CAC7D,EACD,GAAIptB,EAAQ,SAAU,CACpB,MAAMqtB,EAAYrtB,EAAQ,SAAS,KAAME,CAAc,EACvD,GAAImtB,IAAc,OAAW,OAAOA,CAC1C,CAGI,MAFI,QAAK,kBAAkBvtB,EAAKF,CAAE,GAC9B,CAAC,KAAK,SAAS,iBAAiB,SAAW,KAAK,QAAQ,WAAa,CAAC,KAAK,QAAQ,yBACnFM,EAAeJ,EAAKF,CAAE,IAAM,CAACstB,GAAehtB,EAAeitB,EAASvtB,CAAE,GAE9E,CACE,eAAeA,EAAI8qB,EAAU,CAC3B,MAAM2B,EAAW/P,GAAO,EACxB,OAAK,KAAK,QAAQ,IAIdnd,EAASS,CAAE,IAAGA,EAAK,CAACA,CAAE,GAC1BA,EAAG,QAAQ,GAAK,CACV,KAAK,QAAQ,GAAG,QAAQ,CAAC,EAAI,GAAG,KAAK,QAAQ,GAAG,KAAK,CAAC,CAChE,CAAK,EACD,KAAK,cAAcorB,GAAO,CACxBqB,EAAS,QAAS,EACd3B,GAAUA,EAASM,CAAG,CAChC,CAAK,EACMqB,IAXD3B,GAAUA,EAAU,EACjB,QAAQ,QAAS,EAW9B,CACE,cAAcrH,EAAMqH,EAAU,CAC5B,MAAM2B,EAAW/P,GAAO,EACpBnd,EAASkkB,CAAI,IAAGA,EAAO,CAACA,CAAI,GAChC,MAAMiK,EAAY,KAAK,QAAQ,SAAW,CAAE,EACtCC,EAAUlK,EAAK,OAAOvjB,GAAOwtB,EAAU,QAAQxtB,CAAG,EAAI,GAAK,KAAK,SAAS,cAAc,gBAAgBA,CAAG,CAAC,EACjH,OAAKytB,EAAQ,QAIb,KAAK,QAAQ,QAAUD,EAAU,OAAOC,CAAO,EAC/C,KAAK,cAAcvC,GAAO,CACxBqB,EAAS,QAAS,EACd3B,GAAUA,EAASM,CAAG,CAChC,CAAK,EACMqB,IARD3B,GAAUA,EAAU,EACjB,QAAQ,QAAS,EAQ9B,CACE,IAAI5qB,EAAK,SAEP,GADKA,IAAKA,EAAM,KAAK,qBAAqBZ,EAAA,KAAK,YAAL,YAAAA,EAAgB,QAAS,EAAI,KAAK,UAAU,CAAC,EAAI,KAAK,WAC5F,CAACY,EAAK,MAAO,MACjB,MAAM0tB,EAAU,CAAC,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,MAAO,MAAO,MAAO,MAAO,KAAM,MAAO,KAAK,EACjbrH,IAAgBlnB,EAAA,KAAK,WAAL,YAAAA,EAAe,gBAAiB,IAAImmB,GAAa1N,IAAK,EAC5E,OAAO8V,EAAQ,QAAQrH,EAAc,wBAAwBrmB,CAAG,CAAC,EAAI,IAAMA,EAAI,YAAa,EAAC,QAAQ,OAAO,EAAI,EAAI,MAAQ,KAChI,CACE,OAAO,eAAeE,EAAU,CAAE,EAAE0qB,EAAU,CAC5C,OAAO,IAAIsB,GAAKhsB,EAAS0qB,CAAQ,CACrC,CACE,cAAc1qB,EAAU,GAAI0qB,EAAWmB,GAAM,CAC3C,MAAM4B,EAAoBztB,EAAQ,kBAC9BytB,GAAmB,OAAOztB,EAAQ,kBACtC,MAAM0tB,EAAgB,CACpB,GAAG,KAAK,QACR,GAAG1tB,EAED,QAAS,EAEZ,EACK2tB,EAAQ,IAAI3B,GAAK0B,CAAa,EAcpC,IAbI1tB,EAAQ,QAAU,QAAaA,EAAQ,SAAW,UACpD2tB,EAAM,OAASA,EAAM,OAAO,MAAM3tB,CAAO,GAErB,CAAC,QAAS,WAAY,UAAU,EACxC,QAAQ5B,GAAK,CACzBuvB,EAAMvvB,CAAC,EAAI,KAAKA,CAAC,CACvB,CAAK,EACDuvB,EAAM,SAAW,CACf,GAAG,KAAK,QACT,EACDA,EAAM,SAAS,MAAQ,CACrB,mBAAoBA,EAAM,mBAAmB,KAAKA,CAAK,CACxD,EACGF,EAAmB,CACrB,MAAMG,EAAa,OAAO,KAAK,KAAK,MAAM,IAAI,EAAE,OAAO,CAACrS,EAAMiI,KAC5DjI,EAAKiI,CAAC,EAAI,CACR,GAAG,KAAK,MAAM,KAAKA,CAAC,CACrB,EACDjI,EAAKiI,CAAC,EAAI,OAAO,KAAKjI,EAAKiI,CAAC,CAAC,EAAE,OAAO,CAAC/b,EAAK3E,KAC1C2E,EAAI3E,CAAC,EAAI,CACP,GAAGyY,EAAKiI,CAAC,EAAE1gB,CAAC,CACb,EACM2E,GACN8T,EAAKiI,CAAC,CAAC,EACHjI,GACN,EAAE,EACLoS,EAAM,MAAQ,IAAIpN,GAAcqN,EAAYF,CAAa,EACzDC,EAAM,SAAS,cAAgBA,EAAM,KAC3C,CACI,OAAAA,EAAM,WAAa,IAAIvM,GAAWuM,EAAM,SAAUD,CAAa,EAC/DC,EAAM,WAAW,GAAG,IAAK,CAAClT,KAAUzb,IAAS,CAC3C2uB,EAAM,KAAKlT,EAAO,GAAGzb,CAAI,CAC/B,CAAK,EACD2uB,EAAM,KAAKD,EAAehD,CAAQ,EAClCiD,EAAM,WAAW,QAAUD,EAC3BC,EAAM,WAAW,iBAAiB,SAAS,MAAQ,CACjD,mBAAoBA,EAAM,mBAAmB,KAAKA,CAAK,CACxD,EACMA,CACX,CACE,QAAS,CACP,MAAO,CACL,QAAS,KAAK,QACd,MAAO,KAAK,MACZ,SAAU,KAAK,SACf,UAAW,KAAK,UAChB,iBAAkB,KAAK,gBACxB,CACL,CACA,CACA,MAAM7sB,EAAWkrB,GAAK,eAAgB,EACtClrB,EAAS,eAAiBkrB,GAAK,eAERlrB,EAAS,eACpBA,EAAS,IACRA,EAAS,KACAA,EAAS,cACPA,EAAS,gBACrBA,EAAS,IACEA,EAAS,eACdA,EAAS,UACjBA,EAAS,EACJA,EAAS,OACIA,EAAS,oBACVA,EAAS,mBACbA,EAAS,eACVA,EAAS,cCvmE/B,KAAM,CACJ,MAAAqV,GACA,QAAA0X,EACF,EAAI,CAAE,EACN,SAASC,GAAS3tB,EAAK,CACrB,OAAA0tB,GAAQ,KAAK1X,GAAM,KAAK,UAAW,CAAC,EAAG8H,GAAU,CAC/C,GAAIA,EACF,UAAWrZ,KAAQqZ,EACb9d,EAAIyE,CAAI,IAAM,SAAWzE,EAAIyE,CAAI,EAAIqZ,EAAOrZ,CAAI,EAG5D,CAAG,EACMzE,CACT,CACA,SAAS4tB,GAAO7W,EAAO,CACrB,OAAI,OAAOA,GAAU,SAAiB,GAGlB,CAAC,kBAAmB,uBAAwB,uBAAwB,2BAA4B,kBAAmB,gBAAiB,mBAAoB,aAAc,cAAe,oBAAqB,wBAAyB,oBAAqB,YAAY,EACrQ,KAAKsH,GAAWA,EAAQ,KAAKtH,CAAK,CAAC,CACxD,CAGA,MAAM8W,GAAqB,wCACrBC,GAAkB,SAAUtX,EAAMkQ,EAAK,CAI3C,MAAMvF,EAHQ,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAChF,KAAM,GACP,EAEKhgB,EAAQ,mBAAmBulB,CAAG,EACpC,IAAI/P,EAAM,GAAGH,CAAI,IAAIrV,CAAK,GAC1B,GAAIggB,EAAI,OAAS,EAAG,CAClB,MAAM4M,EAAS5M,EAAI,OAAS,EAC5B,GAAI,OAAO,MAAM4M,CAAM,EAAG,MAAM,IAAI,MAAM,2BAA2B,EACrEpX,GAAO,aAAa,KAAK,MAAMoX,CAAM,CAAC,EAC1C,CACE,GAAI5M,EAAI,OAAQ,CACd,GAAI,CAAC0M,GAAmB,KAAK1M,EAAI,MAAM,EACrC,MAAM,IAAI,UAAU,0BAA0B,EAEhDxK,GAAO,YAAYwK,EAAI,MAAM,EACjC,CACE,GAAIA,EAAI,KAAM,CACZ,GAAI,CAAC0M,GAAmB,KAAK1M,EAAI,IAAI,EACnC,MAAM,IAAI,UAAU,wBAAwB,EAE9CxK,GAAO,UAAUwK,EAAI,IAAI,EAC7B,CACE,GAAIA,EAAI,QAAS,CACf,GAAI,OAAOA,EAAI,QAAQ,aAAgB,WACrC,MAAM,IAAI,UAAU,2BAA2B,EAEjDxK,GAAO,aAAawK,EAAI,QAAQ,YAAa,CAAA,EACjD,CAGE,GAFIA,EAAI,WAAUxK,GAAO,cACrBwK,EAAI,SAAQxK,GAAO,YACnBwK,EAAI,SAEN,OADiB,OAAOA,EAAI,UAAa,SAAWA,EAAI,SAAS,cAAgBA,EAAI,SACrE,CACd,IAAK,GACHxK,GAAO,oBACP,MACF,IAAK,MACHA,GAAO,iBACP,MACF,IAAK,SACHA,GAAO,oBACP,MACF,IAAK,OACHA,GAAO,kBACP,MACF,QACE,MAAM,IAAI,UAAU,4BAA4B,CACxD,CAEE,OAAIwK,EAAI,cAAaxK,GAAO,iBACrBA,CACT,EACMqX,GAAS,CACb,OAAOxX,EAAMrV,EAAO8sB,EAASC,EAAQ,CACnC,IAAIC,EAAgB,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CACtF,KAAM,IACN,SAAU,QACX,EACGF,IACFE,EAAc,QAAU,IAAI,KAC5BA,EAAc,QAAQ,QAAQA,EAAc,QAAQ,UAAYF,EAAU,GAAK,GAAI,GAEjFC,IAAQC,EAAc,OAASD,GACnC,SAAS,OAASJ,GAAgBtX,EAAM,mBAAmBrV,CAAK,EAAGgtB,CAAa,CACjF,EACD,KAAK3X,EAAM,CACT,MAAM4X,EAAS,GAAG5X,CAAI,IAChB6X,EAAK,SAAS,OAAO,MAAM,GAAG,EACpC,QAASvhB,EAAI,EAAGA,EAAIuhB,EAAG,OAAQvhB,IAAK,CAClC,IAAIgS,EAAIuP,EAAGvhB,CAAC,EACZ,KAAOgS,EAAE,OAAO,CAAC,IAAM,KAAKA,EAAIA,EAAE,UAAU,EAAGA,EAAE,MAAM,EACvD,GAAIA,EAAE,QAAQsP,CAAM,IAAM,EAAG,OAAOtP,EAAE,UAAUsP,EAAO,OAAQtP,EAAE,MAAM,CAC7E,CACI,OAAO,IACR,EACD,OAAOtI,EAAM,CACX,KAAK,OAAOA,EAAM,GAAI,EAAE,CAC5B,CACA,EACA,IAAI8X,GAAW,CACb,KAAM,SAEN,OAAOC,EAAM,CACX,GAAI,CACF,aAAAC,CACN,EAAQD,EACJ,GAAIC,GAAgB,OAAO,SAAa,IACtC,OAAOR,GAAO,KAAKQ,CAAY,GAAK,MAGvC,EAED,kBAAkB7uB,EAAK8uB,EAAO,CAC5B,GAAI,CACF,aAAAD,EACA,cAAAE,EACA,aAAAC,EACA,cAAAR,CACN,EAAQM,EACAD,GAAgB,OAAO,SAAa,KACtCR,GAAO,OAAOQ,EAAc7uB,EAAK+uB,EAAeC,EAAcR,CAAa,CAEjF,CACA,EAEIS,GAAc,CAChB,KAAM,cAEN,OAAOL,EAAM,OACX,GAAI,CACF,kBAAAM,CACN,EAAQN,EACAtK,EACJ,GAAI,OAAO,OAAW,IAAa,CACjC,GAAI,CACF,OAAA6K,CACD,EAAG,OAAO,SACP,CAAC,OAAO,SAAS,UAAU/vB,EAAA,OAAO,SAAS,OAAhB,YAAAA,EAAsB,QAAQ,MAAO,KAClE+vB,EAAS,OAAO,SAAS,KAAK,UAAU,OAAO,SAAS,KAAK,QAAQ,GAAG,CAAC,GAG3E,MAAMC,EADQD,EAAO,UAAU,CAAC,EACX,MAAM,GAAG,EAC9B,QAAShiB,EAAI,EAAGA,EAAIiiB,EAAO,OAAQjiB,IAAK,CACtC,MAAMkiB,EAAMD,EAAOjiB,CAAC,EAAE,QAAQ,GAAG,EAC7BkiB,EAAM,GACID,EAAOjiB,CAAC,EAAE,UAAU,EAAGkiB,CAAG,IAC1BH,IACV5K,EAAQ8K,EAAOjiB,CAAC,EAAE,UAAUkiB,EAAM,CAAC,EAG/C,CACA,CACI,OAAO/K,CACX,CACA,EAEA,IAAIgL,GAAyB,KAC7B,MAAMC,GAAwB,IAAM,CAClC,GAAID,KAA2B,KAAM,OAAOA,GAC5C,GAAI,CAEF,GADAA,GAAyB,OAAO,OAAW,KAAe,OAAO,eAAiB,KAC9E,CAACA,GACH,MAAO,GAET,MAAME,EAAU,wBAChB,OAAO,aAAa,QAAQA,EAAS,KAAK,EAC1C,OAAO,aAAa,WAAWA,CAAO,CACvC,MAAW,CACVF,GAAyB,EAC7B,CACE,OAAOA,EACT,EACA,IAAIG,GAAe,CACjB,KAAM,eAEN,OAAOb,EAAM,CACX,GAAI,CACF,mBAAAc,CACN,EAAQd,EACJ,GAAIc,GAAsBH,KACxB,OAAO,OAAO,aAAa,QAAQG,CAAkB,GAAK,MAG7D,EAED,kBAAkB1vB,EAAK8uB,EAAO,CAC5B,GAAI,CACF,mBAAAY,CACN,EAAQZ,EACAY,GAAsBH,MACxB,OAAO,aAAa,QAAQG,EAAoB1vB,CAAG,CAEzD,CACA,EAEA,IAAI2vB,GAA2B,KAC/B,MAAMC,GAA0B,IAAM,CACpC,GAAID,KAA6B,KAAM,OAAOA,GAC9C,GAAI,CAEF,GADAA,GAA2B,OAAO,OAAW,KAAe,OAAO,iBAAmB,KAClF,CAACA,GACH,MAAO,GAET,MAAMH,EAAU,wBAChB,OAAO,eAAe,QAAQA,EAAS,KAAK,EAC5C,OAAO,eAAe,WAAWA,CAAO,CACzC,MAAW,CACVG,GAA2B,EAC/B,CACE,OAAOA,EACT,EACA,IAAIE,GAAiB,CACnB,KAAM,iBACN,OAAOjB,EAAM,CACX,GAAI,CACF,qBAAAkB,CACN,EAAQlB,EACJ,GAAIkB,GAAwBF,KAC1B,OAAO,OAAO,eAAe,QAAQE,CAAoB,GAAK,MAGjE,EACD,kBAAkB9vB,EAAK8uB,EAAO,CAC5B,GAAI,CACF,qBAAAgB,CACN,EAAQhB,EACAgB,GAAwBF,MAC1B,OAAO,eAAe,QAAQE,EAAsB9vB,CAAG,CAE7D,CACA,EAEI+vB,GAAc,CAChB,KAAM,YACN,OAAO7vB,EAAS,CACd,MAAMokB,EAAQ,CAAE,EAChB,GAAI,OAAO,UAAc,IAAa,CACpC,KAAM,CACJ,UAAAqG,EACA,aAAAqF,EACA,SAAAluB,CACR,EAAU,UACJ,GAAI6oB,EAEF,QAAS,EAAI,EAAG,EAAIA,EAAU,OAAQ,IACpCrG,EAAM,KAAKqG,EAAU,CAAC,CAAC,EAGvBqF,GACF1L,EAAM,KAAK0L,CAAY,EAErBluB,GACFwiB,EAAM,KAAKxiB,CAAQ,CAE3B,CACI,OAAOwiB,EAAM,OAAS,EAAIA,EAAQ,MACtC,CACA,EAEI2L,GAAU,CACZ,KAAM,UAEN,OAAOrB,EAAM,CACX,GAAI,CACF,QAAAqB,CACN,EAAQrB,EACAtK,EACJ,MAAM4L,EAAkBD,IAAY,OAAO,SAAa,IAAc,SAAS,gBAAkB,MACjG,OAAIC,GAAmB,OAAOA,EAAgB,cAAiB,aAC7D5L,EAAQ4L,EAAgB,aAAa,MAAM,GAEtC5L,CACX,CACA,EAEIva,GAAO,CACT,KAAM,OAEN,OAAO6kB,EAAM,OACX,GAAI,CACF,oBAAAuB,CACN,EAAQvB,EACJ,GAAI,OAAO,OAAW,IAAa,OACnC,MAAM9sB,EAAW,OAAO,SAAS,SAAS,MAAM,iBAAiB,EACjE,OAAK,MAAM,QAAQA,CAAQ,GAEpB1C,EAAA0C,EADO,OAAOquB,GAAwB,SAAWA,EAAsB,CACzD,IAAd,YAAA/wB,EAAiB,QAAQ,IAAK,IAFP,MAGlC,CACA,EAEIgxB,GAAY,CACd,KAAM,YACN,OAAOxB,EAAM,SACX,GAAI,CACF,yBAAAyB,CACN,EAAQzB,EAEJ,MAAM0B,EAAmC,OAAOD,GAA6B,SAAWA,EAA2B,EAAI,EAIjHvuB,EAAW,OAAO,OAAW,OAAe3C,GAAAC,EAAA,OAAO,WAAP,YAAAA,EAAiB,WAAjB,YAAAD,EAA2B,MAAM,2DAGnF,GAAK2C,EAEL,OAAOA,EAASwuB,CAAgC,CACpD,CACA,EAGA,IAAIC,GAAa,GACjB,GAAI,CAEF,SAAS,OACTA,GAAa,EAEf,MAAY,CAAA,CACZ,MAAMC,GAAQ,CAAC,cAAe,SAAU,eAAgB,iBAAkB,YAAa,SAAS,EAC3FD,IAAYC,GAAM,OAAO,EAAG,CAAC,EAClC,MAAM1vB,GAAc,KAAO,CACzB,MAAA0vB,GACA,kBAAmB,MACnB,aAAc,UACd,mBAAoB,aACpB,qBAAsB,aAEtB,OAAQ,CAAC,cAAc,EACvB,gBAAiB,CAAC,QAAQ,EAI1B,wBAAyB9M,GAAKA,CAChC,GACA,MAAM+M,EAAQ,CACZ,YAAYlP,EAAU,CACpB,IAAIrhB,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EACpF,KAAK,KAAO,mBACZ,KAAK,UAAY,CAAE,EACnB,KAAK,KAAKqhB,EAAUrhB,CAAO,CAC/B,CACE,MAAO,CACL,IAAIqhB,EAAW,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CACjF,cAAe,CAAA,CAChB,EACGrhB,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EAChF2C,EAAc,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EACxF,KAAK,SAAW0e,EAChB,KAAK,QAAUyM,GAAS9tB,EAAS,KAAK,SAAW,CAAA,EAAIY,IAAa,EAC9D,OAAO,KAAK,QAAQ,yBAA4B,UAAY,KAAK,QAAQ,wBAAwB,QAAQ,OAAO,EAAI,KACtH,KAAK,QAAQ,wBAA0B4iB,GAAKA,EAAE,QAAQ,IAAK,GAAG,GAI5D,KAAK,QAAQ,qBAAoB,KAAK,QAAQ,oBAAsB,KAAK,QAAQ,oBACrF,KAAK,YAAc7gB,EACnB,KAAK,YAAY8rB,EAAQ,EACzB,KAAK,YAAYM,EAAW,EAC5B,KAAK,YAAYQ,EAAY,EAC7B,KAAK,YAAYI,EAAc,EAC/B,KAAK,YAAYE,EAAW,EAC5B,KAAK,YAAYE,EAAO,EACxB,KAAK,YAAYlmB,EAAI,EACrB,KAAK,YAAYqmB,EAAS,CAC9B,CACE,YAAYM,EAAU,CACpB,YAAK,UAAUA,EAAS,IAAI,EAAIA,EACzB,IACX,CACE,QAAS,CACP,IAAIC,EAAiB,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,KAAK,QAAQ,MAClGC,EAAW,CAAE,EASjB,OARAD,EAAe,QAAQE,GAAgB,CACrC,GAAI,KAAK,UAAUA,CAAY,EAAG,CAChC,IAAIC,EAAS,KAAK,UAAUD,CAAY,EAAE,OAAO,KAAK,OAAO,EACzDC,GAAU,OAAOA,GAAW,WAAUA,EAAS,CAACA,CAAM,GACtDA,IAAQF,EAAWA,EAAS,OAAOE,CAAM,EACrD,CACA,CAAK,EACDF,EAAWA,EAAS,OAAOG,GAAwBA,GAAM,MAAQ,CAAC9C,GAAO8C,CAAC,CAAC,EAAE,IAAIA,GAAK,KAAK,QAAQ,wBAAwBA,CAAC,CAAC,EACzH,KAAK,UAAY,KAAK,SAAS,eAAiB,KAAK,SAAS,cAAc,sBAA8BH,EACvGA,EAAS,OAAS,EAAIA,EAAS,CAAC,EAAI,IAC/C,CACE,kBAAkB5wB,EAAK,CACrB,IAAIgxB,EAAS,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,KAAK,QAAQ,OACzFA,IACD,KAAK,QAAQ,iBAAmB,KAAK,QAAQ,gBAAgB,QAAQhxB,CAAG,EAAI,IAChFgxB,EAAO,QAAQC,GAAa,CACtB,KAAK,UAAUA,CAAS,GAAG,KAAK,UAAUA,CAAS,EAAE,kBAAkBjxB,EAAK,KAAK,OAAO,CAClG,CAAK,EACL,CACA,CACAywB,GAAQ,KAAO,00KClYT9P,GAAY,CAChB,GAAI,CACF,OAAQuQ,GACR,MAAOC,GACP,OAAQC,EACV,EACA,GAAI,CACF,OAAQC,GACR,MAAOC,GACP,OAAQC,EAAA,CAEZ,EAEAzyB,EACG,IAAI0yB,EAAgB,EACpB,IAAItwB,EAAgB,EACpB,KAAK,CACJ,UAAAyf,GACA,YAAa,KACb,MAAO,GAGP,UAAW,SACX,GAAI,CAAC,SAAU,QAAS,QAAQ,EAEhC,cAAe,CACb,YAAa,EACf,EAEA,UAAW,CACT,MAAO,CAAC,eAAgB,YAAa,SAAS,EAC9C,OAAQ,CAAC,cAAc,CAAA,CAE3B,CAAC,ECvCH,SAAS8Q,IAAM,CAEX,OAAChY,EAAA,KAAAiY,GAAoB,CAAA,OAAQjzB,GAC3B,SAAA,CAAAuW,EAAA,IAACsH,GAAU,EAAA,EACV,EAAA,EACH,CAEJ,CCRAqV,GAAA,WAAW,SAAS,eAAe,MAAM,CAAE,EAAE,OAC1C3c,EAAA,IAAA4c,EAAA,WAAA,CACC,SAAC5c,EAAAA,IAAAyc,GAAA,CAAA,CAAI,CACP,CAAA,CACF", "x_google_ignoreList": [0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 29, 30, 31, 38, 49, 50]}