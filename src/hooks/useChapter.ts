import { useQuery } from '@tanstack/react-query';
import { apiClient } from '../services/api';

// Query keys
export const chapterKeys = {
  all: ['chapters'] as const,
  details: () => [...chapterKeys.all, 'detail'] as const,
  detail: (id: string) => [...chapterKeys.details(), id] as const,
};

// Get single chapter
export const useChapterQuery = (chapterId: string) => {
  return useQuery({
    queryKey: chapterKeys.detail(chapterId),
    queryFn: () => apiClient.getChapterById(chapterId),
    enabled: !!chapterId,
    staleTime: 10 * 60 * 1000, // 10 minutes - chapters don't change often
  });
};
