import{r as ha,g as ma,a as pa}from"./vendor-BtP0CW_r.js";var y=ha();const Lo=ma(y);var Ke={},rr;function ya(){if(rr)return Ke;rr=1,Object.defineProperty(Ke,"__esModule",{value:!0}),Ke.parse=l,Ke.serialize=d;const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,n=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,o=(()=>{const h=function(){};return h.prototype=Object.create(null),h})();function l(h,b){const v=new o,x=h.length;if(x<2)return v;const C=(b==null?void 0:b.decode)||f;let w=0;do{const O=h.indexOf("=",w);if(O===-1)break;const L=h.indexOf(";",w),N=L===-1?x:L;if(O>N){w=h.lastIndexOf(";",O-1)+1;continue}const U=u(h,w,O),p=i(h,O,U),j=h.slice(U,p);if(v[j]===void 0){let V=u(h,O+1,N),_=i(h,N,V);const G=C(h.slice(V,_));v[j]=G}w=N+1}while(w<x);return v}function u(h,b,v){do{const x=h.charCodeAt(b);if(x!==32&&x!==9)return b}while(++b<v);return v}function i(h,b,v){for(;b>v;){const x=h.charCodeAt(--b);if(x!==32&&x!==9)return b+1}return v}function d(h,b,v){const x=(v==null?void 0:v.encode)||encodeURIComponent;if(!e.test(h))throw new TypeError(`argument name is invalid: ${h}`);const C=x(b);if(!t.test(C))throw new TypeError(`argument val is invalid: ${b}`);let w=h+"="+C;if(!v)return w;if(v.maxAge!==void 0){if(!Number.isInteger(v.maxAge))throw new TypeError(`option maxAge is invalid: ${v.maxAge}`);w+="; Max-Age="+v.maxAge}if(v.domain){if(!r.test(v.domain))throw new TypeError(`option domain is invalid: ${v.domain}`);w+="; Domain="+v.domain}if(v.path){if(!n.test(v.path))throw new TypeError(`option path is invalid: ${v.path}`);w+="; Path="+v.path}if(v.expires){if(!g(v.expires)||!Number.isFinite(v.expires.valueOf()))throw new TypeError(`option expires is invalid: ${v.expires}`);w+="; Expires="+v.expires.toUTCString()}if(v.httpOnly&&(w+="; HttpOnly"),v.secure&&(w+="; Secure"),v.partitioned&&(w+="; Partitioned"),v.priority)switch(typeof v.priority=="string"?v.priority.toLowerCase():void 0){case"low":w+="; Priority=Low";break;case"medium":w+="; Priority=Medium";break;case"high":w+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${v.priority}`)}if(v.sameSite)switch(typeof v.sameSite=="string"?v.sameSite.toLowerCase():v.sameSite){case!0:case"strict":w+="; SameSite=Strict";break;case"lax":w+="; SameSite=Lax";break;case"none":w+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${v.sameSite}`)}return w}function f(h){if(h.indexOf("%")===-1)return h;try{return decodeURIComponent(h)}catch{return h}}function g(h){return a.call(h)==="[object Date]"}return Ke}ya();/**
 * react-router v7.6.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var Cr=e=>{throw TypeError(e)},va=(e,t,r)=>t.has(e)||Cr("Cannot "+r),Ct=(e,t,r)=>(va(e,t,"read from private field"),r?r.call(e):t.get(e)),ga=(e,t,r)=>t.has(e)?Cr("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),ar="popstate";function wa(e={}){function t(n,a){let{pathname:o,search:l,hash:u}=n.location;return Qe("",{pathname:o,search:l,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:xe(a)}return Ea(t,r,null,e)}function A(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function J(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ba(){return Math.random().toString(36).substring(2,10)}function nr(e,t){return{usr:e.state,key:e.key,idx:t}}function Qe(e,t,r=null,n){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Se(t):t,state:r,key:t&&t.key||n||ba()}}function xe({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Se(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substring(n),e=e.substring(0,n)),e&&(t.pathname=e)}return t}function Ea(e,t,r,n={}){let{window:a=document.defaultView,v5Compat:o=!1}=n,l=a.history,u="POP",i=null,d=f();d==null&&(d=0,l.replaceState({...l.state,idx:d},""));function f(){return(l.state||{idx:null}).idx}function g(){u="POP";let C=f(),w=C==null?null:C-d;d=C,i&&i({action:u,location:x.location,delta:w})}function h(C,w){u="PUSH";let O=Qe(x.location,C,w);d=f()+1;let L=nr(O,d),N=x.createHref(O);try{l.pushState(L,"",N)}catch(U){if(U instanceof DOMException&&U.name==="DataCloneError")throw U;a.location.assign(N)}o&&i&&i({action:u,location:x.location,delta:1})}function b(C,w){u="REPLACE";let O=Qe(x.location,C,w);d=f();let L=nr(O,d),N=x.createHref(O);l.replaceState(L,"",N),o&&i&&i({action:u,location:x.location,delta:0})}function v(C){return Lr(C)}let x={get action(){return u},get location(){return e(a,l)},listen(C){if(i)throw new Error("A history only accepts one active listener");return a.addEventListener(ar,g),i=C,()=>{a.removeEventListener(ar,g),i=null}},createHref(C){return t(a,C)},createURL:v,encodeLocation(C){let w=v(C);return{pathname:w.pathname,search:w.search,hash:w.hash}},push:h,replace:b,go(C){return l.go(C)}};return x}function Lr(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),A(r,"No window.location.(origin|href) available to create URL");let n=typeof e=="string"?e:xe(e);return n=n.replace(/ $/,"%20"),!t&&n.startsWith("//")&&(n=r+n),new URL(n,r)}var Ge,or=class{constructor(e){if(ga(this,Ge,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(Ct(this,Ge).has(e))return Ct(this,Ge).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){Ct(this,Ge).set(e,t)}};Ge=new WeakMap;var Ra=new Set(["lazy","caseSensitive","path","id","index","children"]);function xa(e){return Ra.has(e)}var Sa=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function Ca(e){return Sa.has(e)}function La(e){return e.index===!0}function mt(e,t,r=[],n={}){return e.map((a,o)=>{let l=[...r,String(o)],u=typeof a.id=="string"?a.id:l.join("-");if(A(a.index!==!0||!a.children,"Cannot specify children on an index route"),A(!n[u],`Found a route id collision on id "${u}".  Route id's must be globally unique within Data Router usages`),La(a)){let i={...a,...t(a),id:u};return n[u]=i,i}else{let i={...a,...t(a),id:u,children:void 0};return n[u]=i,a.children&&(i.children=mt(a.children,t,l,n)),i}})}function Re(e,t,r="/"){return ct(e,t,r,!1)}function ct(e,t,r,n){let a=typeof t=="string"?Se(t):t,o=de(a.pathname||"/",r);if(o==null)return null;let l=Pr(e);Da(l);let u=null;for(let i=0;u==null&&i<l.length;++i){let d=za(o);u=Aa(l[i],d,n)}return u}function Pa(e,t){let{route:r,pathname:n,params:a}=e;return{id:r.id,pathname:n,params:a,data:t[r.id],handle:r.handle}}function Pr(e,t=[],r=[],n=""){let a=(o,l,u)=>{let i={relativePath:u===void 0?o.path||"":u,caseSensitive:o.caseSensitive===!0,childrenIndex:l,route:o};i.relativePath.startsWith("/")&&(A(i.relativePath.startsWith(n),`Absolute route path "${i.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(n.length));let d=me([n,i.relativePath]),f=r.concat(i);o.children&&o.children.length>0&&(A(o.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${d}".`),Pr(o.children,t,f,d)),!(o.path==null&&!o.index)&&t.push({path:d,score:Na(d,o.index),routesMeta:f})};return e.forEach((o,l)=>{var u;if(o.path===""||!((u=o.path)!=null&&u.includes("?")))a(o,l);else for(let i of Dr(o.path))a(o,l,i)}),t}function Dr(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),o=r.replace(/\?$/,"");if(n.length===0)return a?[o,""]:[o];let l=Dr(n.join("/")),u=[];return u.push(...l.map(i=>i===""?o:[o,i].join("/"))),a&&u.push(...l),u.map(i=>e.startsWith("/")&&i===""?"/":i)}function Da(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Ua(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}var Ma=/^:[\w-]+$/,Ta=3,Fa=2,$a=1,Oa=10,ka=-2,ir=e=>e==="*";function Na(e,t){let r=e.split("/"),n=r.length;return r.some(ir)&&(n+=ka),t&&(n+=Fa),r.filter(a=>!ir(a)).reduce((a,o)=>a+(Ma.test(o)?Ta:o===""?$a:Oa),n)}function Ua(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function Aa(e,t,r=!1){let{routesMeta:n}=e,a={},o="/",l=[];for(let u=0;u<n.length;++u){let i=n[u],d=u===n.length-1,f=o==="/"?t:t.slice(o.length)||"/",g=pt({path:i.relativePath,caseSensitive:i.caseSensitive,end:d},f),h=i.route;if(!g&&d&&r&&!n[n.length-1].route.index&&(g=pt({path:i.relativePath,caseSensitive:i.caseSensitive,end:!1},f)),!g)return null;Object.assign(a,g.params),l.push({params:a,pathname:me([o,g.pathname]),pathnameBase:Ha(me([o,g.pathnameBase])),route:h}),g.pathnameBase!=="/"&&(o=me([o,g.pathnameBase]))}return l}function pt(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=_a(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:n.reduce((d,{paramName:f,isOptional:g},h)=>{if(f==="*"){let v=u[h]||"";l=o.slice(0,o.length-v.length).replace(/(.)\/+$/,"$1")}const b=u[h];return g&&!b?d[f]=void 0:d[f]=(b||"").replace(/%2F/g,"/"),d},{}),pathname:o,pathnameBase:l,pattern:e}}function _a(e,t=!1,r=!0){J(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,u,i)=>(n.push({paramName:u,isOptional:i!=null}),i?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function za(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return J(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function de(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function Ia(e,t="/"){let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?Se(e):e;return{pathname:r?r.startsWith("/")?r:ja(r,t):t,search:Ba(n),hash:Wa(a)}}function ja(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function Lt(e,t,r,n){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(n)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Mr(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function kt(e){let t=Mr(e);return t.map((r,n)=>n===t.length-1?r.pathname:r.pathnameBase)}function Nt(e,t,r,n=!1){let a;typeof e=="string"?a=Se(e):(a={...e},A(!a.pathname||!a.pathname.includes("?"),Lt("?","pathname","search",a)),A(!a.pathname||!a.pathname.includes("#"),Lt("#","pathname","hash",a)),A(!a.search||!a.search.includes("#"),Lt("#","search","hash",a)));let o=e===""||a.pathname==="",l=o?"/":a.pathname,u;if(l==null)u=r;else{let g=t.length-1;if(!n&&l.startsWith("..")){let h=l.split("/");for(;h[0]==="..";)h.shift(),g-=1;a.pathname=h.join("/")}u=g>=0?t[g]:"/"}let i=Ia(a,u),d=l&&l!=="/"&&l.endsWith("/"),f=(o||l===".")&&r.endsWith("/");return!i.pathname.endsWith("/")&&(d||f)&&(i.pathname+="/"),i}var me=e=>e.join("/").replace(/\/\/+/g,"/"),Ha=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ba=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Wa=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,yt=class{constructor(e,t,r,n=!1){this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function Ze(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Tr=["POST","PUT","PATCH","DELETE"],Va=new Set(Tr),Ka=["GET",...Tr],Ya=new Set(Ka),Ja=new Set([301,302,303,307,308]),qa=new Set([307,308]),Pt={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Ga={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Ye={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},Ut=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Xa=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),Fr="remix-router-transitions",$r=Symbol("ResetLoaderData");function Qa(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";A(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let n=e.hydrationRouteProperties||[],a=e.mapRouteProperties||Xa,o={},l=mt(e.routes,a,void 0,o),u,i=e.basename||"/",d=e.dataStrategy||an,f={unstable_middleware:!1,...e.future},g=null,h=new Set,b=null,v=null,x=null,C=e.hydrationData!=null,w=Re(l,e.history.location,i),O=!1,L=null,N;if(w==null&&!e.patchRoutesOnNavigation){let s=ue(404,{pathname:e.history.location.pathname}),{matches:c,route:m}=gr(l);N=!0,w=c,L={[m.id]:s}}else if(w&&!e.hydrationData&&ot(w,l,e.history.location.pathname).active&&(w=null),w)if(w.some(s=>s.route.lazy))N=!1;else if(!w.some(s=>s.route.loader))N=!0;else{let s=e.hydrationData?e.hydrationData.loaderData:null,c=e.hydrationData?e.hydrationData.errors:null;if(c){let m=w.findIndex(E=>c[E.route.id]!==void 0);N=w.slice(0,m+1).every(E=>!Ft(E.route,s,c))}else N=w.every(m=>!Ft(m.route,s,c))}else{N=!1,w=[];let s=ot(null,l,e.history.location.pathname);s.active&&s.matches&&(O=!0,w=s.matches)}let U,p={historyAction:e.history.action,location:e.history.location,matches:w,initialized:N,navigation:Pt,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||L,fetchers:new Map,blockers:new Map},j="POP",V=!1,_,G=!1,ce=new Map,Ce=null,Le=!1,H=!1,K=new Set,B=new Map,Q=0,ae=-1,Z=new Map,re=new Set,fe=new Map,he=new Map,ee=new Set,Pe=new Map,at,De=null;function Gr(){if(g=e.history.listen(({action:s,location:c,delta:m})=>{if(at){at(),at=void 0;return}J(Pe.size===0||m!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let E=Qt({currentLocation:p.location,nextLocation:c,historyAction:s});if(E&&m!=null){let R=new Promise(P=>{at=P});e.history.go(m*-1),nt(E,{state:"blocked",location:c,proceed(){nt(E,{state:"proceeding",proceed:void 0,reset:void 0,location:c}),R.then(()=>e.history.go(m))},reset(){let P=new Map(p.blockers);P.set(E,Ye),te({blockers:P})}});return}return Me(s,c)}),r){pn(t,ce);let s=()=>yn(t,ce);t.addEventListener("pagehide",s),Ce=()=>t.removeEventListener("pagehide",s)}return p.initialized||Me("POP",p.location,{initialHydration:!0}),U}function Xr(){g&&g(),Ce&&Ce(),h.clear(),_&&_.abort(),p.fetchers.forEach((s,c)=>Et(c)),p.blockers.forEach((s,c)=>Xt(c))}function Qr(s){return h.add(s),()=>h.delete(s)}function te(s,c={}){p={...p,...s};let m=[],E=[];p.fetchers.forEach((R,P)=>{R.state==="idle"&&(ee.has(P)?m.push(P):E.push(P))}),ee.forEach(R=>{!p.fetchers.has(R)&&!B.has(R)&&m.push(R)}),[...h].forEach(R=>R(p,{deletedFetchers:m,viewTransitionOpts:c.viewTransitionOpts,flushSync:c.flushSync===!0})),m.forEach(R=>Et(R)),E.forEach(R=>p.fetchers.delete(R))}function Ae(s,c,{flushSync:m}={}){var T,$;let E=p.actionData!=null&&p.navigation.formMethod!=null&&ie(p.navigation.formMethod)&&p.navigation.state==="loading"&&((T=s.state)==null?void 0:T._isRedirect)!==!0,R;c.actionData?Object.keys(c.actionData).length>0?R=c.actionData:R=null:E?R=p.actionData:R=null;let P=c.loaderData?yr(p.loaderData,c.loaderData,c.matches||[],c.errors):p.loaderData,F=p.blockers;F.size>0&&(F=new Map(F),F.forEach((M,k)=>F.set(k,Ye)));let S=V===!0||p.navigation.formMethod!=null&&ie(p.navigation.formMethod)&&(($=s.state)==null?void 0:$._isRedirect)!==!0;u&&(l=u,u=void 0),Le||j==="POP"||(j==="PUSH"?e.history.push(s,s.state):j==="REPLACE"&&e.history.replace(s,s.state));let D;if(j==="POP"){let M=ce.get(p.location.pathname);M&&M.has(s.pathname)?D={currentLocation:p.location,nextLocation:s}:ce.has(s.pathname)&&(D={currentLocation:s,nextLocation:p.location})}else if(G){let M=ce.get(p.location.pathname);M?M.add(s.pathname):(M=new Set([s.pathname]),ce.set(p.location.pathname,M)),D={currentLocation:p.location,nextLocation:s}}te({...c,actionData:R,loaderData:P,historyAction:j,location:s,initialized:!0,navigation:Pt,revalidation:"idle",restoreScrollPosition:er(s,c.matches||p.matches),preventScrollReset:S,blockers:F},{viewTransitionOpts:D,flushSync:m===!0}),j="POP",V=!1,G=!1,Le=!1,H=!1,De==null||De.resolve(),De=null}async function Wt(s,c){if(typeof s=="number"){e.history.go(s);return}let m=Tt(p.location,p.matches,i,s,c==null?void 0:c.fromRouteId,c==null?void 0:c.relative),{path:E,submission:R,error:P}=lr(!1,m,c),F=p.location,S=Qe(p.location,E,c&&c.state);S={...S,...e.history.encodeLocation(S)};let D=c&&c.replace!=null?c.replace:void 0,T="PUSH";D===!0?T="REPLACE":D===!1||R!=null&&ie(R.formMethod)&&R.formAction===p.location.pathname+p.location.search&&(T="REPLACE");let $=c&&"preventScrollReset"in c?c.preventScrollReset===!0:void 0,M=(c&&c.flushSync)===!0,k=Qt({currentLocation:F,nextLocation:S,historyAction:T});if(k){nt(k,{state:"blocked",location:S,proceed(){nt(k,{state:"proceeding",proceed:void 0,reset:void 0,location:S}),Wt(s,c)},reset(){let W=new Map(p.blockers);W.set(k,Ye),te({blockers:W})}});return}await Me(T,S,{submission:R,pendingError:P,preventScrollReset:$,replace:c&&c.replace,enableViewTransition:c&&c.viewTransition,flushSync:M})}function Zr(){De||(De=vn()),bt(),te({revalidation:"loading"});let s=De.promise;return p.navigation.state==="submitting"?s:p.navigation.state==="idle"?(Me(p.historyAction,p.location,{startUninterruptedRevalidation:!0}),s):(Me(j||p.historyAction,p.navigation.location,{overrideNavigation:p.navigation,enableViewTransition:G===!0}),s)}async function Me(s,c,m){_&&_.abort(),_=null,j=s,Le=(m&&m.startUninterruptedRevalidation)===!0,ua(p.location,p.matches),V=(m&&m.preventScrollReset)===!0,G=(m&&m.enableViewTransition)===!0;let E=u||l,R=m&&m.overrideNavigation,P=m!=null&&m.initialHydration&&p.matches&&p.matches.length>0&&!O?p.matches:Re(E,c,i),F=(m&&m.flushSync)===!0;if(P&&p.initialized&&!H&&dn(p.location,c)&&!(m&&m.submission&&ie(m.submission.formMethod))){Ae(c,{matches:P},{flushSync:F});return}let S=ot(P,E,c.pathname);if(S.active&&S.matches&&(P=S.matches),!P){let{error:X,notFoundMatches:ne,route:z}=Rt(c.pathname);Ae(c,{matches:ne,loaderData:{},errors:{[z.id]:X}},{flushSync:F});return}_=new AbortController;let D=ze(e.history,c,_.signal,m&&m.submission),T=new or(e.unstable_getContext?await e.unstable_getContext():void 0),$;if(m&&m.pendingError)$=[Oe(P).route.id,{type:"error",error:m.pendingError}];else if(m&&m.submission&&ie(m.submission.formMethod)){let X=await ea(D,c,m.submission,P,T,S.active,m&&m.initialHydration===!0,{replace:m.replace,flushSync:F});if(X.shortCircuited)return;if(X.pendingActionResult){let[ne,z]=X.pendingActionResult;if(oe(z)&&Ze(z.error)&&z.error.status===404){_=null,Ae(c,{matches:X.matches,loaderData:{},errors:{[ne]:z.error}});return}}P=X.matches||P,$=X.pendingActionResult,R=Dt(c,m.submission),F=!1,S.active=!1,D=ze(e.history,D.url,D.signal)}let{shortCircuited:M,matches:k,loaderData:W,errors:q}=await ta(D,c,P,T,S.active,R,m&&m.submission,m&&m.fetcherSubmission,m&&m.replace,m&&m.initialHydration===!0,F,$);M||(_=null,Ae(c,{matches:k||P,...vr($),loaderData:W,errors:q}))}async function ea(s,c,m,E,R,P,F,S={}){bt();let D=hn(c,m);if(te({navigation:D},{flushSync:S.flushSync===!0}),P){let M=await it(E,c.pathname,s.signal);if(M.type==="aborted")return{shortCircuited:!0};if(M.type==="error"){let k=Oe(M.partialMatches).route.id;return{matches:M.partialMatches,pendingActionResult:[k,{type:"error",error:M.error}]}}else if(M.matches)E=M.matches;else{let{notFoundMatches:k,error:W,route:q}=Rt(c.pathname);return{matches:k,pendingActionResult:[q.id,{type:"error",error:W}]}}}let T,$=Xe(E,c);if(!$.route.action&&!$.route.lazy)T={type:"error",error:ue(405,{method:s.method,pathname:c.pathname,routeId:$.route.id})};else{let M=Ie(a,o,s,E,$,F?[]:n,R),k=await je(s,M,R,null);if(T=k[$.route.id],!T){for(let W of E)if(k[W.route.id]){T=k[W.route.id];break}}if(s.signal.aborted)return{shortCircuited:!0}}if(ke(T)){let M;return S&&S.replace!=null?M=S.replace:M=hr(T.response.headers.get("Location"),new URL(s.url),i)===p.location.pathname+p.location.search,await Te(s,T,!0,{submission:m,replace:M}),{shortCircuited:!0}}if(oe(T)){let M=Oe(E,$.route.id);return(S&&S.replace)!==!0&&(j="PUSH"),{matches:E,pendingActionResult:[M.route.id,T,$.route.id]}}return{matches:E,pendingActionResult:[$.route.id,T]}}async function ta(s,c,m,E,R,P,F,S,D,T,$,M){let k=P||Dt(c,F),W=F||S||br(k),q=!Le&&!T;if(R){if(q){let se=Vt(M);te({navigation:k,...se!==void 0?{actionData:se}:{}},{flushSync:$})}let I=await it(m,c.pathname,s.signal);if(I.type==="aborted")return{shortCircuited:!0};if(I.type==="error"){let se=Oe(I.partialMatches).route.id;return{matches:I.partialMatches,loaderData:{},errors:{[se]:I.error}}}else if(I.matches)m=I.matches;else{let{error:se,notFoundMatches:be,route:st}=Rt(c.pathname);return{matches:be,loaderData:{},errors:{[st.id]:se}}}}let X=u||l,{dsMatches:ne,revalidatingFetchers:z}=sr(s,E,a,o,e.history,p,m,W,c,T?[]:n,T===!0,H,K,ee,fe,re,X,i,e.patchRoutesOnNavigation!=null,M);if(ae=++Q,!e.dataStrategy&&!ne.some(I=>I.shouldLoad)&&z.length===0){let I=qt();return Ae(c,{matches:m,loaderData:{},errors:M&&oe(M[1])?{[M[0]]:M[1].error}:null,...vr(M),...I?{fetchers:new Map(p.fetchers)}:{}},{flushSync:$}),{shortCircuited:!0}}if(q){let I={};if(!R){I.navigation=k;let se=Vt(M);se!==void 0&&(I.actionData=se)}z.length>0&&(I.fetchers=ra(z)),te(I,{flushSync:$})}z.forEach(I=>{we(I.key),I.controller&&B.set(I.key,I.controller)});let He=()=>z.forEach(I=>we(I.key));_&&_.signal.addEventListener("abort",He);let{loaderResults:Fe,fetcherResults:Be}=await Kt(ne,z,s,E);if(s.signal.aborted)return{shortCircuited:!0};_&&_.signal.removeEventListener("abort",He),z.forEach(I=>B.delete(I.key));let le=ut(Fe);if(le)return await Te(s,le.result,!0,{replace:D}),{shortCircuited:!0};if(le=ut(Be),le)return re.add(le.key),await Te(s,le.result,!0,{replace:D}),{shortCircuited:!0};let{loaderData:We,errors:Ve}=pr(p,m,Fe,M,z,Be);T&&p.errors&&(Ve={...p.errors,...Ve});let xt=qt(),$e=Gt(ae),lt=xt||$e||z.length>0;return{matches:m,loaderData:We,errors:Ve,...lt?{fetchers:new Map(p.fetchers)}:{}}}function Vt(s){if(s&&!oe(s[1]))return{[s[0]]:s[1].data};if(p.actionData)return Object.keys(p.actionData).length===0?null:p.actionData}function ra(s){return s.forEach(c=>{let m=p.fetchers.get(c.key),E=Je(void 0,m?m.data:void 0);p.fetchers.set(c.key,E)}),new Map(p.fetchers)}async function aa(s,c,m,E){we(s);let R=(E&&E.flushSync)===!0,P=u||l,F=Tt(p.location,p.matches,i,m,c,E==null?void 0:E.relative),S=Re(P,F,i),D=ot(S,P,F);if(D.active&&D.matches&&(S=D.matches),!S){ve(s,c,ue(404,{pathname:F}),{flushSync:R});return}let{path:T,submission:$,error:M}=lr(!0,F,E);if(M){ve(s,c,M,{flushSync:R});return}let k=Xe(S,T),W=new or(e.unstable_getContext?await e.unstable_getContext():void 0),q=(E&&E.preventScrollReset)===!0;if($&&ie($.formMethod)){await na(s,c,T,k,S,W,D.active,R,q,$);return}fe.set(s,{routeId:c,path:T}),await oa(s,c,T,k,S,W,D.active,R,q,$)}async function na(s,c,m,E,R,P,F,S,D,T){bt(),fe.delete(s);function $(Y){if(!Y.route.action&&!Y.route.lazy){let _e=ue(405,{method:T.formMethod,pathname:m,routeId:c});return ve(s,c,_e,{flushSync:S}),!0}return!1}if(!F&&$(E))return;let M=p.fetchers.get(s);ge(s,mn(T,M),{flushSync:S});let k=new AbortController,W=ze(e.history,m,k.signal,T);if(F){let Y=await it(R,m,W.signal,s);if(Y.type==="aborted")return;if(Y.type==="error"){ve(s,c,Y.error,{flushSync:S});return}else if(Y.matches){if(R=Y.matches,E=Xe(R,m),$(E))return}else{ve(s,c,ue(404,{pathname:m}),{flushSync:S});return}}B.set(s,k);let q=Q,X=Ie(a,o,W,R,E,n,P),z=(await je(W,X,P,s))[E.route.id];if(W.signal.aborted){B.get(s)===k&&B.delete(s);return}if(ee.has(s)){if(ke(z)||oe(z)){ge(s,Ee(void 0));return}}else{if(ke(z))if(B.delete(s),ae>q){ge(s,Ee(void 0));return}else return re.add(s),ge(s,Je(T)),Te(W,z,!1,{fetcherSubmission:T,preventScrollReset:D});if(oe(z)){ve(s,c,z.error);return}}let He=p.navigation.location||p.location,Fe=ze(e.history,He,k.signal),Be=u||l,le=p.navigation.state!=="idle"?Re(Be,p.navigation.location,i):p.matches;A(le,"Didn't find any matches after fetcher action");let We=++Q;Z.set(s,We);let Ve=Je(T,z.data);p.fetchers.set(s,Ve);let{dsMatches:xt,revalidatingFetchers:$e}=sr(Fe,P,a,o,e.history,p,le,T,He,n,!1,H,K,ee,fe,re,Be,i,e.patchRoutesOnNavigation!=null,[E.route.id,z]);$e.filter(Y=>Y.key!==s).forEach(Y=>{let _e=Y.key,tr=p.fetchers.get(_e),fa=Je(void 0,tr?tr.data:void 0);p.fetchers.set(_e,fa),we(_e),Y.controller&&B.set(_e,Y.controller)}),te({fetchers:new Map(p.fetchers)});let lt=()=>$e.forEach(Y=>we(Y.key));k.signal.addEventListener("abort",lt);let{loaderResults:I,fetcherResults:se}=await Kt(xt,$e,Fe,P);if(k.signal.aborted)return;if(k.signal.removeEventListener("abort",lt),Z.delete(s),B.delete(s),$e.forEach(Y=>B.delete(Y.key)),p.fetchers.has(s)){let Y=Ee(z.data);p.fetchers.set(s,Y)}let be=ut(I);if(be)return Te(Fe,be.result,!1,{preventScrollReset:D});if(be=ut(se),be)return re.add(be.key),Te(Fe,be.result,!1,{preventScrollReset:D});let{loaderData:st,errors:St}=pr(p,le,I,void 0,$e,se);Gt(We),p.navigation.state==="loading"&&We>ae?(A(j,"Expected pending action"),_&&_.abort(),Ae(p.navigation.location,{matches:le,loaderData:st,errors:St,fetchers:new Map(p.fetchers)})):(te({errors:St,loaderData:yr(p.loaderData,st,le,St),fetchers:new Map(p.fetchers)}),H=!1)}async function oa(s,c,m,E,R,P,F,S,D,T){let $=p.fetchers.get(s);ge(s,Je(T,$?$.data:void 0),{flushSync:S});let M=new AbortController,k=ze(e.history,m,M.signal);if(F){let z=await it(R,m,k.signal,s);if(z.type==="aborted")return;if(z.type==="error"){ve(s,c,z.error,{flushSync:S});return}else if(z.matches)R=z.matches,E=Xe(R,m);else{ve(s,c,ue(404,{pathname:m}),{flushSync:S});return}}B.set(s,M);let W=Q,q=Ie(a,o,k,R,E,n,P),ne=(await je(k,q,P,s))[E.route.id];if(B.get(s)===M&&B.delete(s),!k.signal.aborted){if(ee.has(s)){ge(s,Ee(void 0));return}if(ke(ne))if(ae>W){ge(s,Ee(void 0));return}else{re.add(s),await Te(k,ne,!1,{preventScrollReset:D});return}if(oe(ne)){ve(s,c,ne.error);return}ge(s,Ee(ne.data))}}async function Te(s,c,m,{submission:E,fetcherSubmission:R,preventScrollReset:P,replace:F}={}){c.response.headers.has("X-Remix-Revalidate")&&(H=!0);let S=c.response.headers.get("Location");A(S,"Expected a Location header on the redirect Response"),S=hr(S,new URL(s.url),i);let D=Qe(p.location,S,{_isRedirect:!0});if(r){let q=!1;if(c.response.headers.has("X-Remix-Reload-Document"))q=!0;else if(Ut.test(S)){const X=Lr(S,!0);q=X.origin!==t.location.origin||de(X.pathname,i)==null}if(q){F?t.location.replace(S):t.location.assign(S);return}}_=null;let T=F===!0||c.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:$,formAction:M,formEncType:k}=p.navigation;!E&&!R&&$&&M&&k&&(E=br(p.navigation));let W=E||R;if(qa.has(c.response.status)&&W&&ie(W.formMethod))await Me(T,D,{submission:{...W,formAction:S},preventScrollReset:P||V,enableViewTransition:m?G:void 0});else{let q=Dt(D,E);await Me(T,D,{overrideNavigation:q,fetcherSubmission:R,preventScrollReset:P||V,enableViewTransition:m?G:void 0})}}async function je(s,c,m,E){let R,P={};try{R=await nn(d,s,c,E,m,!1)}catch(F){return c.filter(S=>S.shouldLoad).forEach(S=>{P[S.route.id]={type:"error",error:F}}),P}if(s.signal.aborted)return P;for(let[F,S]of Object.entries(R))if(cn(S)){let D=S.result;P[F]={type:"redirect",response:sn(D,s,F,c,i)}}else P[F]=await ln(S);return P}async function Kt(s,c,m,E){let R=je(m,s,E,null),P=Promise.all(c.map(async D=>{if(D.matches&&D.match&&D.request&&D.controller){let $=(await je(D.request,D.matches,E,D.key))[D.match.route.id];return{[D.key]:$}}else return Promise.resolve({[D.key]:{type:"error",error:ue(404,{pathname:D.path})}})})),F=await R,S=(await P).reduce((D,T)=>Object.assign(D,T),{});return{loaderResults:F,fetcherResults:S}}function bt(){H=!0,fe.forEach((s,c)=>{B.has(c)&&K.add(c),we(c)})}function ge(s,c,m={}){p.fetchers.set(s,c),te({fetchers:new Map(p.fetchers)},{flushSync:(m&&m.flushSync)===!0})}function ve(s,c,m,E={}){let R=Oe(p.matches,c);Et(s),te({errors:{[R.route.id]:m},fetchers:new Map(p.fetchers)},{flushSync:(E&&E.flushSync)===!0})}function Yt(s){return he.set(s,(he.get(s)||0)+1),ee.has(s)&&ee.delete(s),p.fetchers.get(s)||Ga}function Et(s){let c=p.fetchers.get(s);B.has(s)&&!(c&&c.state==="loading"&&Z.has(s))&&we(s),fe.delete(s),Z.delete(s),re.delete(s),ee.delete(s),K.delete(s),p.fetchers.delete(s)}function ia(s){let c=(he.get(s)||0)-1;c<=0?(he.delete(s),ee.add(s)):he.set(s,c),te({fetchers:new Map(p.fetchers)})}function we(s){let c=B.get(s);c&&(c.abort(),B.delete(s))}function Jt(s){for(let c of s){let m=Yt(c),E=Ee(m.data);p.fetchers.set(c,E)}}function qt(){let s=[],c=!1;for(let m of re){let E=p.fetchers.get(m);A(E,`Expected fetcher: ${m}`),E.state==="loading"&&(re.delete(m),s.push(m),c=!0)}return Jt(s),c}function Gt(s){let c=[];for(let[m,E]of Z)if(E<s){let R=p.fetchers.get(m);A(R,`Expected fetcher: ${m}`),R.state==="loading"&&(we(m),Z.delete(m),c.push(m))}return Jt(c),c.length>0}function la(s,c){let m=p.blockers.get(s)||Ye;return Pe.get(s)!==c&&Pe.set(s,c),m}function Xt(s){p.blockers.delete(s),Pe.delete(s)}function nt(s,c){let m=p.blockers.get(s)||Ye;A(m.state==="unblocked"&&c.state==="blocked"||m.state==="blocked"&&c.state==="blocked"||m.state==="blocked"&&c.state==="proceeding"||m.state==="blocked"&&c.state==="unblocked"||m.state==="proceeding"&&c.state==="unblocked",`Invalid blocker state transition: ${m.state} -> ${c.state}`);let E=new Map(p.blockers);E.set(s,c),te({blockers:E})}function Qt({currentLocation:s,nextLocation:c,historyAction:m}){if(Pe.size===0)return;Pe.size>1&&J(!1,"A router only supports one blocker at a time");let E=Array.from(Pe.entries()),[R,P]=E[E.length-1],F=p.blockers.get(R);if(!(F&&F.state==="proceeding")&&P({currentLocation:s,nextLocation:c,historyAction:m}))return R}function Rt(s){let c=ue(404,{pathname:s}),m=u||l,{matches:E,route:R}=gr(m);return{notFoundMatches:E,route:R,error:c}}function sa(s,c,m){if(b=s,x=c,v=m||null,!C&&p.navigation===Pt){C=!0;let E=er(p.location,p.matches);E!=null&&te({restoreScrollPosition:E})}return()=>{b=null,x=null,v=null}}function Zt(s,c){return v&&v(s,c.map(E=>Pa(E,p.loaderData)))||s.key}function ua(s,c){if(b&&x){let m=Zt(s,c);b[m]=x()}}function er(s,c){if(b){let m=Zt(s,c),E=b[m];if(typeof E=="number")return E}return null}function ot(s,c,m){if(e.patchRoutesOnNavigation)if(s){if(Object.keys(s[0].params).length>0)return{active:!0,matches:ct(c,m,i,!0)}}else return{active:!0,matches:ct(c,m,i,!0)||[]};return{active:!1,matches:null}}async function it(s,c,m,E){if(!e.patchRoutesOnNavigation)return{type:"success",matches:s};let R=s;for(;;){let P=u==null,F=u||l,S=o;try{await e.patchRoutesOnNavigation({signal:m,path:c,matches:R,fetcherKey:E,patch:($,M)=>{m.aborted||ur($,M,F,S,a)}})}catch($){return{type:"error",error:$,partialMatches:R}}finally{P&&!m.aborted&&(l=[...l])}if(m.aborted)return{type:"aborted"};let D=Re(F,c,i);if(D)return{type:"success",matches:D};let T=ct(F,c,i,!0);if(!T||R.length===T.length&&R.every(($,M)=>$.route.id===T[M].route.id))return{type:"success",matches:null};R=T}}function da(s){o={},u=mt(s,a,void 0,o)}function ca(s,c){let m=u==null;ur(s,c,u||l,o,a),m&&(l=[...l],te({}))}return U={get basename(){return i},get future(){return f},get state(){return p},get routes(){return l},get window(){return t},initialize:Gr,subscribe:Qr,enableScrollRestoration:sa,navigate:Wt,fetch:aa,revalidate:Zr,createHref:s=>e.history.createHref(s),encodeLocation:s=>e.history.encodeLocation(s),getFetcher:Yt,deleteFetcher:ia,dispose:Xr,getBlocker:la,deleteBlocker:Xt,patchRoutes:ca,_internalFetchControllers:B,_internalSetRoutes:da},U}function Za(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function Tt(e,t,r,n,a,o){let l,u;if(a){l=[];for(let d of t)if(l.push(d),d.route.id===a){u=d;break}}else l=t,u=t[t.length-1];let i=Nt(n||".",kt(l),de(e.pathname,r)||e.pathname,o==="path");if(n==null&&(i.search=e.search,i.hash=e.hash),(n==null||n===""||n===".")&&u){let d=At(i.search);if(u.route.index&&!d)i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index";else if(!u.route.index&&d){let f=new URLSearchParams(i.search),g=f.getAll("index");f.delete("index"),g.filter(b=>b).forEach(b=>f.append("index",b));let h=f.toString();i.search=h?`?${h}`:""}}return r!=="/"&&(i.pathname=i.pathname==="/"?r:me([r,i.pathname])),xe(i)}function lr(e,t,r){if(!r||!Za(r))return{path:t};if(r.formMethod&&!fn(r.formMethod))return{path:t,error:ue(405,{method:r.formMethod})};let n=()=>({path:t,error:ue(400,{type:"invalid-body"})}),o=(r.formMethod||"get").toUpperCase(),l=_r(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!ie(o))return n();let g=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((h,[b,v])=>`${h}${b}=${v}
`,""):String(r.body);return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:void 0,text:g}}}else if(r.formEncType==="application/json"){if(!ie(o))return n();try{let g=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:g,text:void 0}}}catch{return n()}}}A(typeof FormData=="function","FormData is not available in this environment");let u,i;if(r.formData)u=Ot(r.formData),i=r.formData;else if(r.body instanceof FormData)u=Ot(r.body),i=r.body;else if(r.body instanceof URLSearchParams)u=r.body,i=mr(u);else if(r.body==null)u=new URLSearchParams,i=new FormData;else try{u=new URLSearchParams(r.body),i=mr(u)}catch{return n()}let d={formMethod:o,formAction:l,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:i,json:void 0,text:void 0};if(ie(d.formMethod))return{path:t,submission:d};let f=Se(t);return e&&f.search&&At(f.search)&&u.append("index",""),f.search=`?${u}`,{path:xe(f),submission:d}}function sr(e,t,r,n,a,o,l,u,i,d,f,g,h,b,v,x,C,w,O,L){var Le;let N=L?oe(L[1])?L[1].error:L[1].data:void 0,U=a.createURL(o.location),p=a.createURL(i),j;if(f&&o.errors){let H=Object.keys(o.errors)[0];j=l.findIndex(K=>K.route.id===H)}else if(L&&oe(L[1])){let H=L[0];j=l.findIndex(K=>K.route.id===H)-1}let V=L?L[1].statusCode:void 0,_=V&&V>=400,G={currentUrl:U,currentParams:((Le=o.matches[0])==null?void 0:Le.params)||{},nextUrl:p,nextParams:l[0].params,...u,actionResult:N,actionStatus:V},ce=l.map((H,K)=>{let{route:B}=H,Q=null;if(j!=null&&K>j?Q=!1:B.lazy?Q=!0:B.loader==null?Q=!1:f?Q=Ft(B,o.loaderData,o.errors):en(o.loaderData,o.matches[K],H)&&(Q=!0),Q!==null)return $t(r,n,e,H,d,t,Q);let ae=_?!1:g||U.pathname+U.search===p.pathname+p.search||U.search!==p.search||tn(o.matches[K],H),Z={...G,defaultShouldRevalidate:ae},re=vt(H,Z);return $t(r,n,e,H,d,t,re,Z)}),Ce=[];return v.forEach((H,K)=>{if(f||!l.some(ee=>ee.route.id===H.routeId)||b.has(K))return;let B=o.fetchers.get(K),Q=B&&B.state!=="idle"&&B.data===void 0,ae=Re(C,H.path,w);if(!ae){if(O&&Q)return;Ce.push({key:K,routeId:H.routeId,path:H.path,matches:null,match:null,request:null,controller:null});return}if(x.has(K))return;let Z=Xe(ae,H.path),re=new AbortController,fe=ze(a,H.path,re.signal),he=null;if(h.has(K))h.delete(K),he=Ie(r,n,fe,ae,Z,d,t);else if(Q)g&&(he=Ie(r,n,fe,ae,Z,d,t));else{let ee={...G,defaultShouldRevalidate:_?!1:g};vt(Z,ee)&&(he=Ie(r,n,fe,ae,Z,d,t,ee))}he&&Ce.push({key:K,routeId:H.routeId,path:H.path,matches:he,match:Z,request:fe,controller:re})}),{dsMatches:ce,revalidatingFetchers:Ce}}function Ft(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&e.id in t,a=r!=null&&r[e.id]!==void 0;return!n&&a?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!a}function en(e,t,r){let n=!t||r.route.id!==t.route.id,a=!e.hasOwnProperty(r.route.id);return n||a}function tn(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function vt(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function ur(e,t,r,n,a){let o;if(e){let i=n[e];A(i,`No route found to patch children into: routeId = ${e}`),i.children||(i.children=[]),o=i.children}else o=r;let l=t.filter(i=>!o.some(d=>Or(i,d))),u=mt(l,a,[e||"_","patch",String((o==null?void 0:o.length)||"0")],n);o.push(...u)}function Or(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>{var a;return(a=t.children)==null?void 0:a.some(o=>Or(r,o))}):!1}var dr=new WeakMap,kr=({key:e,route:t,manifest:r,mapRouteProperties:n})=>{let a=r[t.id];if(A(a,"No route found in manifest"),!a.lazy||typeof a.lazy!="object")return;let o=a.lazy[e];if(!o)return;let l=dr.get(a);l||(l={},dr.set(a,l));let u=l[e];if(u)return u;let i=(async()=>{let d=xa(e),g=a[e]!==void 0&&e!=="hasErrorBoundary";if(d)J(!d,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),l[e]=Promise.resolve();else if(g)J(!1,`Route "${a.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let h=await o();h!=null&&(Object.assign(a,{[e]:h}),Object.assign(a,n(a)))}typeof a.lazy=="object"&&(a.lazy[e]=void 0,Object.values(a.lazy).every(h=>h===void 0)&&(a.lazy=void 0))})();return l[e]=i,i},cr=new WeakMap;function rn(e,t,r,n,a){let o=r[e.id];if(A(o,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let f=cr.get(o);if(f)return{lazyRoutePromise:f,lazyHandlerPromise:f};let g=(async()=>{A(typeof e.lazy=="function","No lazy route function found");let h=await e.lazy(),b={};for(let v in h){let x=h[v];if(x===void 0)continue;let C=Ca(v),O=o[v]!==void 0&&v!=="hasErrorBoundary";C?J(!C,"Route property "+v+" is not a supported property to be returned from a lazy route function. This property will be ignored."):O?J(!O,`Route "${o.id}" has a static property "${v}" defined but its lazy function is also returning a value for this property. The lazy route property "${v}" will be ignored.`):b[v]=x}Object.assign(o,b),Object.assign(o,{...n(o),lazy:void 0})})();return cr.set(o,g),g.catch(()=>{}),{lazyRoutePromise:g,lazyHandlerPromise:g}}let l=Object.keys(e.lazy),u=[],i;for(let f of l){if(a&&a.includes(f))continue;let g=kr({key:f,route:e,manifest:r,mapRouteProperties:n});g&&(u.push(g),f===t&&(i=g))}let d=u.length>0?Promise.all(u).then(()=>{}):void 0;return d==null||d.catch(()=>{}),i==null||i.catch(()=>{}),{lazyRoutePromise:d,lazyHandlerPromise:i}}async function fr(e){let t=e.matches.filter(a=>a.shouldLoad),r={};return(await Promise.all(t.map(a=>a.resolve()))).forEach((a,o)=>{r[t[o].route.id]=a}),r}async function an(e){return e.matches.some(t=>t.route.unstable_middleware)?Nr(e,!1,()=>fr(e),(t,r)=>({[r]:{type:"error",result:t}})):fr(e)}async function Nr(e,t,r,n){let{matches:a,request:o,params:l,context:u}=e,i={handlerResult:void 0};try{let d=a.flatMap(g=>g.route.unstable_middleware?g.route.unstable_middleware.map(h=>[g.route.id,h]):[]),f=await Ur({request:o,params:l,context:u},d,t,i,r);return t?f:i.handlerResult}catch(d){if(!i.middlewareError)throw d;let f=await n(i.middlewareError.error,i.middlewareError.routeId);return i.handlerResult?Object.assign(i.handlerResult,f):f}}async function Ur(e,t,r,n,a,o=0){let{request:l}=e;if(l.signal.aborted)throw l.signal.reason?l.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${l.method} ${l.url}`);let u=t[o];if(!u)return n.handlerResult=await a(),n.handlerResult;let[i,d]=u,f=!1,g,h=async()=>{if(f)throw new Error("You may only call `next()` once per middleware");f=!0,await Ur(e,t,r,n,a,o+1)};try{let b=await d({request:e.request,params:e.params,context:e.context},h);return f?b===void 0?g:b:h()}catch(b){throw n.middlewareError?n.middlewareError.error!==b&&(n.middlewareError={routeId:i,error:b}):n.middlewareError={routeId:i,error:b},b}}function Ar(e,t,r,n,a){let o=kr({key:"unstable_middleware",route:n.route,manifest:t,mapRouteProperties:e}),l=rn(n.route,ie(r.method)?"action":"loader",t,e,a);return{middleware:o,route:l.lazyRoutePromise,handler:l.lazyHandlerPromise}}function $t(e,t,r,n,a,o,l,u=null){let i=!1,d=Ar(e,t,r,n,a);return{...n,_lazyPromises:d,shouldLoad:l,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler(f){return i=!0,u?typeof f=="boolean"?vt(n,{...u,defaultShouldRevalidate:f}):vt(n,u):l},resolve(f){return i||l||f&&r.method==="GET"&&(n.route.lazy||n.route.loader)?on({request:r,match:n,lazyHandlerPromise:d==null?void 0:d.handler,lazyRoutePromise:d==null?void 0:d.route,handlerOverride:f,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}}function Ie(e,t,r,n,a,o,l,u=null){return n.map(i=>i.route.id!==a.route.id?{...i,shouldLoad:!1,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler:()=>!1,_lazyPromises:Ar(e,t,r,i,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:$t(e,t,r,i,o,l,!0,u))}async function nn(e,t,r,n,a,o){r.some(d=>{var f;return(f=d._lazyPromises)==null?void 0:f.middleware})&&await Promise.all(r.map(d=>{var f;return(f=d._lazyPromises)==null?void 0:f.middleware}));let l={request:t,params:r[0].params,context:a,matches:r},i=await e({...l,fetcherKey:n,unstable_runClientMiddleware:d=>{let f=l;return Nr(f,!1,()=>d({...f,fetcherKey:n,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(g,h)=>({[h]:{type:"error",result:g}}))}});try{await Promise.all(r.flatMap(d=>{var f,g;return[(f=d._lazyPromises)==null?void 0:f.handler,(g=d._lazyPromises)==null?void 0:g.route]}))}catch{}return i}async function on({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:n,handlerOverride:a,scopedContext:o}){let l,u,i=ie(e.method),d=i?"action":"loader",f=g=>{let h,b=new Promise((C,w)=>h=w);u=()=>h(),e.signal.addEventListener("abort",u);let v=C=>typeof g!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${d}" [routeId: ${t.route.id}]`)):g({request:e,params:t.params,context:o},...C!==void 0?[C]:[]),x=(async()=>{try{return{type:"data",result:await(a?a(w=>v(w)):v())}}catch(C){return{type:"error",result:C}}})();return Promise.race([x,b])};try{let g=i?t.route.action:t.route.loader;if(r||n)if(g){let h,[b]=await Promise.all([f(g).catch(v=>{h=v}),r,n]);if(h!==void 0)throw h;l=b}else{await r;let h=i?t.route.action:t.route.loader;if(h)[l]=await Promise.all([f(h),n]);else if(d==="action"){let b=new URL(e.url),v=b.pathname+b.search;throw ue(405,{method:e.method,pathname:v,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(g)l=await f(g);else{let h=new URL(e.url),b=h.pathname+h.search;throw ue(404,{pathname:b})}}catch(g){return{type:"error",result:g}}finally{u&&e.signal.removeEventListener("abort",u)}return l}async function ln(e){var n,a,o,l,u,i;let{result:t,type:r}=e;if(zr(t)){let d;try{let f=t.headers.get("Content-Type");f&&/\bapplication\/json\b/.test(f)?t.body==null?d=null:d=await t.json():d=await t.text()}catch(f){return{type:"error",error:f}}return r==="error"?{type:"error",error:new yt(t.status,t.statusText,d),statusCode:t.status,headers:t.headers}:{type:"data",data:d,statusCode:t.status,headers:t.headers}}return r==="error"?wr(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:(n=t.init)==null?void 0:n.status,headers:(a=t.init)!=null&&a.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new yt(((o=t.init)==null?void 0:o.status)||500,void 0,t.data),statusCode:Ze(t)?t.status:void 0,headers:(l=t.init)!=null&&l.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:Ze(t)?t.status:void 0}:wr(t)?{type:"data",data:t.data,statusCode:(u=t.init)==null?void 0:u.status,headers:(i=t.init)!=null&&i.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function sn(e,t,r,n,a){let o=e.headers.get("Location");if(A(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!Ut.test(o)){let l=n.slice(0,n.findIndex(u=>u.route.id===r)+1);o=Tt(new URL(t.url),l,a,o),e.headers.set("Location",o)}return e}function hr(e,t,r){if(Ut.test(e)){let n=e,a=n.startsWith("//")?new URL(t.protocol+n):new URL(n),o=de(a.pathname,r)!=null;if(a.origin===t.origin&&o)return a.pathname+a.search+a.hash}return e}function ze(e,t,r,n){let a=e.createURL(_r(t)).toString(),o={signal:r};if(n&&ie(n.formMethod)){let{formMethod:l,formEncType:u}=n;o.method=l.toUpperCase(),u==="application/json"?(o.headers=new Headers({"Content-Type":u}),o.body=JSON.stringify(n.json)):u==="text/plain"?o.body=n.text:u==="application/x-www-form-urlencoded"&&n.formData?o.body=Ot(n.formData):o.body=n.formData}return new Request(a,o)}function Ot(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function mr(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function un(e,t,r,n=!1,a=!1){let o={},l=null,u,i=!1,d={},f=r&&oe(r[1])?r[1].error:void 0;return e.forEach(g=>{if(!(g.route.id in t))return;let h=g.route.id,b=t[h];if(A(!ke(b),"Cannot handle redirect results in processLoaderData"),oe(b)){let v=b.error;if(f!==void 0&&(v=f,f=void 0),l=l||{},a)l[h]=v;else{let x=Oe(e,h);l[x.route.id]==null&&(l[x.route.id]=v)}n||(o[h]=$r),i||(i=!0,u=Ze(b.error)?b.error.status:500),b.headers&&(d[h]=b.headers)}else o[h]=b.data,b.statusCode&&b.statusCode!==200&&!i&&(u=b.statusCode),b.headers&&(d[h]=b.headers)}),f!==void 0&&r&&(l={[r[0]]:f},r[2]&&(o[r[2]]=void 0)),{loaderData:o,errors:l,statusCode:u||200,loaderHeaders:d}}function pr(e,t,r,n,a,o){let{loaderData:l,errors:u}=un(t,r,n);return a.filter(i=>!i.matches||i.matches.some(d=>d.shouldLoad)).forEach(i=>{let{key:d,match:f,controller:g}=i,h=o[d];if(A(h,"Did not find corresponding fetcher result"),!(g&&g.signal.aborted))if(oe(h)){let b=Oe(e.matches,f==null?void 0:f.route.id);u&&u[b.route.id]||(u={...u,[b.route.id]:h.error}),e.fetchers.delete(d)}else if(ke(h))A(!1,"Unhandled fetcher revalidation redirect");else{let b=Ee(h.data);e.fetchers.set(d,b)}}),{loaderData:l,errors:u}}function yr(e,t,r,n){let a=Object.entries(t).filter(([,o])=>o!==$r).reduce((o,[l,u])=>(o[l]=u,o),{});for(let o of r){let l=o.route.id;if(!t.hasOwnProperty(l)&&e.hasOwnProperty(l)&&o.route.loader&&(a[l]=e[l]),n&&n.hasOwnProperty(l))break}return a}function vr(e){return e?oe(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function Oe(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function gr(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function ue(e,{pathname:t,routeId:r,method:n,type:a,message:o}={}){let l="Unknown Server Error",u="Unknown @remix-run/router error";return e===400?(l="Bad Request",n&&t&&r?u=`You made a ${n} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:a==="invalid-body"&&(u="Unable to encode submission body")):e===403?(l="Forbidden",u=`Route "${r}" does not match URL "${t}"`):e===404?(l="Not Found",u=`No route matches URL "${t}"`):e===405&&(l="Method Not Allowed",n&&t&&r?u=`You made a ${n.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:n&&(u=`Invalid request method "${n.toUpperCase()}"`)),new yt(e||500,l,new Error(u),!0)}function ut(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,a]=t[r];if(ke(a))return{key:n,result:a}}}function _r(e){let t=typeof e=="string"?Se(e):e;return xe({...t,hash:""})}function dn(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function cn(e){return zr(e.result)&&Ja.has(e.result.status)}function oe(e){return e.type==="error"}function ke(e){return(e&&e.type)==="redirect"}function wr(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function zr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function fn(e){return Ya.has(e.toUpperCase())}function ie(e){return Va.has(e.toUpperCase())}function At(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function Xe(e,t){let r=typeof t=="string"?Se(t).search:t.search;if(e[e.length-1].route.index&&At(r||""))return e[e.length-1];let n=Mr(e);return n[n.length-1]}function br(e){let{formMethod:t,formAction:r,formEncType:n,text:a,formData:o,json:l}=e;if(!(!t||!r||!n)){if(a!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:a};if(o!=null)return{formMethod:t,formAction:r,formEncType:n,formData:o,json:void 0,text:void 0};if(l!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:l,text:void 0}}}function Dt(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function hn(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function Je(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function mn(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Ee(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function pn(e,t){try{let r=e.sessionStorage.getItem(Fr);if(r){let n=JSON.parse(r);for(let[a,o]of Object.entries(n||{}))o&&Array.isArray(o)&&t.set(a,new Set(o||[]))}}catch{}}function yn(e,t){if(t.size>0){let r={};for(let[n,a]of t)r[n]=[...a];try{e.sessionStorage.setItem(Fr,JSON.stringify(r))}catch(n){J(!1,`Failed to save applied view transitions in sessionStorage (${n}).`)}}}function vn(){let e,t,r=new Promise((n,a)=>{e=async o=>{n(o);try{await r}catch{}},t=async o=>{a(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Ne=y.createContext(null);Ne.displayName="DataRouter";var et=y.createContext(null);et.displayName="DataRouterState";var _t=y.createContext({isTransitioning:!1});_t.displayName="ViewTransition";var Ir=y.createContext(new Map);Ir.displayName="Fetchers";var gn=y.createContext(null);gn.displayName="Await";var pe=y.createContext(null);pe.displayName="Navigation";var gt=y.createContext(null);gt.displayName="Location";var ye=y.createContext({outlet:null,matches:[],isDataRoute:!1});ye.displayName="Route";var zt=y.createContext(null);zt.displayName="RouteError";function wn(e,{relative:t}={}){A(tt(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:n}=y.useContext(pe),{hash:a,pathname:o,search:l}=rt(e,{relative:t}),u=o;return r!=="/"&&(u=o==="/"?r:me([r,o])),n.createHref({pathname:u,search:l,hash:a})}function tt(){return y.useContext(gt)!=null}function Ue(){return A(tt(),"useLocation() may be used only in the context of a <Router> component."),y.useContext(gt).location}var jr="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Hr(e){y.useContext(pe).static||y.useLayoutEffect(e)}function bn(){let{isDataRoute:e}=y.useContext(ye);return e?Nn():En()}function En(){A(tt(),"useNavigate() may be used only in the context of a <Router> component.");let e=y.useContext(Ne),{basename:t,navigator:r}=y.useContext(pe),{matches:n}=y.useContext(ye),{pathname:a}=Ue(),o=JSON.stringify(kt(n)),l=y.useRef(!1);return Hr(()=>{l.current=!0}),y.useCallback((i,d={})=>{if(J(l.current,jr),!l.current)return;if(typeof i=="number"){r.go(i);return}let f=Nt(i,JSON.parse(o),a,d.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:me([t,f.pathname])),(d.replace?r.replace:r.push)(f,d.state,d)},[t,r,o,a,e])}var Rn=y.createContext(null);function xn(e){let t=y.useContext(ye).outlet;return t&&y.createElement(Rn.Provider,{value:e},t)}function rt(e,{relative:t}={}){let{matches:r}=y.useContext(ye),{pathname:n}=Ue(),a=JSON.stringify(kt(r));return y.useMemo(()=>Nt(e,JSON.parse(a),n,t==="path"),[e,a,n,t])}function Sn(e,t,r,n){A(tt(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a}=y.useContext(pe),{matches:o}=y.useContext(ye),l=o[o.length-1],u=l?l.params:{},i=l?l.pathname:"/",d=l?l.pathnameBase:"/",f=l&&l.route;{let w=f&&f.path||"";Br(i,!f||w.endsWith("*")||w.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${i}" (under <Route path="${w}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${w}"> to <Route path="${w==="/"?"*":`${w}/*`}">.`)}let g=Ue(),h;h=g;let b=h.pathname||"/",v=b;if(d!=="/"){let w=d.replace(/^\//,"").split("/");v="/"+b.replace(/^\//,"").split("/").slice(w.length).join("/")}let x=Re(e,{pathname:v});return J(f||x!=null,`No routes matched location "${h.pathname}${h.search}${h.hash}" `),J(x==null||x[x.length-1].route.element!==void 0||x[x.length-1].route.Component!==void 0||x[x.length-1].route.lazy!==void 0,`Matched leaf route at location "${h.pathname}${h.search}${h.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),Mn(x&&x.map(w=>Object.assign({},w,{params:Object.assign({},u,w.params),pathname:me([d,a.encodeLocation?a.encodeLocation(w.pathname).pathname:w.pathname]),pathnameBase:w.pathnameBase==="/"?d:me([d,a.encodeLocation?a.encodeLocation(w.pathnameBase).pathname:w.pathnameBase])})),o,r,n)}function Cn(){let e=kn(),t=Ze(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:n},o={padding:"2px 4px",backgroundColor:n},l=null;return console.error("Error handled by React Router default ErrorBoundary:",e),l=y.createElement(y.Fragment,null,y.createElement("p",null,"💿 Hey developer 👋"),y.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",y.createElement("code",{style:o},"ErrorBoundary")," or"," ",y.createElement("code",{style:o},"errorElement")," prop on your route.")),y.createElement(y.Fragment,null,y.createElement("h2",null,"Unexpected Application Error!"),y.createElement("h3",{style:{fontStyle:"italic"}},t),r?y.createElement("pre",{style:a},r):null,l)}var Ln=y.createElement(Cn,null),Pn=class extends y.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?y.createElement(ye.Provider,{value:this.props.routeContext},y.createElement(zt.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Dn({routeContext:e,match:t,children:r}){let n=y.useContext(Ne);return n&&n.static&&n.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),y.createElement(ye.Provider,{value:e},r)}function Mn(e,t=[],r=null,n=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let a=e,o=r==null?void 0:r.errors;if(o!=null){let i=a.findIndex(d=>d.route.id&&(o==null?void 0:o[d.route.id])!==void 0);A(i>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),a=a.slice(0,Math.min(a.length,i+1))}let l=!1,u=-1;if(r)for(let i=0;i<a.length;i++){let d=a[i];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=i),d.route.id){let{loaderData:f,errors:g}=r,h=d.route.loader&&!f.hasOwnProperty(d.route.id)&&(!g||g[d.route.id]===void 0);if(d.route.lazy||h){l=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((i,d,f)=>{let g,h=!1,b=null,v=null;r&&(g=o&&d.route.id?o[d.route.id]:void 0,b=d.route.errorElement||Ln,l&&(u<0&&f===0?(Br("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),h=!0,v=null):u===f&&(h=!0,v=d.route.hydrateFallbackElement||null)));let x=t.concat(a.slice(0,f+1)),C=()=>{let w;return g?w=b:h?w=v:d.route.Component?w=y.createElement(d.route.Component,null):d.route.element?w=d.route.element:w=i,y.createElement(Dn,{match:d,routeContext:{outlet:i,matches:x,isDataRoute:r!=null},children:w})};return r&&(d.route.ErrorBoundary||d.route.errorElement||f===0)?y.createElement(Pn,{location:r.location,revalidation:r.revalidation,component:b,error:g,children:C(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):C()},null)}function It(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Tn(e){let t=y.useContext(Ne);return A(t,It(e)),t}function Fn(e){let t=y.useContext(et);return A(t,It(e)),t}function $n(e){let t=y.useContext(ye);return A(t,It(e)),t}function jt(e){let t=$n(e),r=t.matches[t.matches.length-1];return A(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function On(){return jt("useRouteId")}function kn(){var n;let e=y.useContext(zt),t=Fn("useRouteError"),r=jt("useRouteError");return e!==void 0?e:(n=t.errors)==null?void 0:n[r]}function Nn(){let{router:e}=Tn("useNavigate"),t=jt("useNavigate"),r=y.useRef(!1);return Hr(()=>{r.current=!0}),y.useCallback(async(a,o={})=>{J(r.current,jr),r.current&&(typeof a=="number"?e.navigate(a):await e.navigate(a,{fromRouteId:t,...o}))},[e,t])}var Er={};function Br(e,t,r){!t&&!Er[e]&&(Er[e]=!0,J(!1,r))}var Rr={};function xr(e,t){!e&&!Rr[t]&&(Rr[t]=!0,console.warn(t))}function Un(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&J(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:y.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&J(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:y.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&J(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:y.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var An=["HydrateFallback","hydrateFallbackElement"],_n=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function zn({router:e,flushSync:t}){let[r,n]=y.useState(e.state),[a,o]=y.useState(),[l,u]=y.useState({isTransitioning:!1}),[i,d]=y.useState(),[f,g]=y.useState(),[h,b]=y.useState(),v=y.useRef(new Map),x=y.useCallback((L,{deletedFetchers:N,flushSync:U,viewTransitionOpts:p})=>{L.fetchers.forEach((V,_)=>{V.data!==void 0&&v.current.set(_,V.data)}),N.forEach(V=>v.current.delete(V)),xr(U===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let j=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(xr(p==null||j,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!p||!j){t&&U?t(()=>n(L)):y.startTransition(()=>n(L));return}if(t&&U){t(()=>{f&&(i&&i.resolve(),f.skipTransition()),u({isTransitioning:!0,flushSync:!0,currentLocation:p.currentLocation,nextLocation:p.nextLocation})});let V=e.window.document.startViewTransition(()=>{t(()=>n(L))});V.finished.finally(()=>{t(()=>{d(void 0),g(void 0),o(void 0),u({isTransitioning:!1})})}),t(()=>g(V));return}f?(i&&i.resolve(),f.skipTransition(),b({state:L,currentLocation:p.currentLocation,nextLocation:p.nextLocation})):(o(L),u({isTransitioning:!0,flushSync:!1,currentLocation:p.currentLocation,nextLocation:p.nextLocation}))},[e.window,t,f,i]);y.useLayoutEffect(()=>e.subscribe(x),[e,x]),y.useEffect(()=>{l.isTransitioning&&!l.flushSync&&d(new _n)},[l]),y.useEffect(()=>{if(i&&a&&e.window){let L=a,N=i.promise,U=e.window.document.startViewTransition(async()=>{y.startTransition(()=>n(L)),await N});U.finished.finally(()=>{d(void 0),g(void 0),o(void 0),u({isTransitioning:!1})}),g(U)}},[a,i,e.window]),y.useEffect(()=>{i&&a&&r.location.key===a.location.key&&i.resolve()},[i,f,r.location,a]),y.useEffect(()=>{!l.isTransitioning&&h&&(o(h.state),u({isTransitioning:!0,flushSync:!1,currentLocation:h.currentLocation,nextLocation:h.nextLocation}),b(void 0))},[l.isTransitioning,h]);let C=y.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:L=>e.navigate(L),push:(L,N,U)=>e.navigate(L,{state:N,preventScrollReset:U==null?void 0:U.preventScrollReset}),replace:(L,N,U)=>e.navigate(L,{replace:!0,state:N,preventScrollReset:U==null?void 0:U.preventScrollReset})}),[e]),w=e.basename||"/",O=y.useMemo(()=>({router:e,navigator:C,static:!1,basename:w}),[e,C,w]);return y.createElement(y.Fragment,null,y.createElement(Ne.Provider,{value:O},y.createElement(et.Provider,{value:r},y.createElement(Ir.Provider,{value:v.current},y.createElement(_t.Provider,{value:l},y.createElement(Hn,{basename:w,location:r.location,navigationType:r.historyAction,navigator:C},y.createElement(In,{routes:e.routes,future:e.future,state:r})))))),null)}var In=y.memo(jn);function jn({routes:e,future:t,state:r}){return Sn(e,void 0,r,t)}function Po(e){return xn(e.context)}function Hn({basename:e="/",children:t=null,location:r,navigationType:n="POP",navigator:a,static:o=!1}){A(!tt(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),u=y.useMemo(()=>({basename:l,navigator:a,static:o,future:{}}),[l,a,o]);typeof r=="string"&&(r=Se(r));let{pathname:i="/",search:d="",hash:f="",state:g=null,key:h="default"}=r,b=y.useMemo(()=>{let v=de(i,l);return v==null?null:{location:{pathname:v,search:d,hash:f,state:g,key:h},navigationType:n}},[l,i,d,f,g,h,n]);return J(b!=null,`<Router basename="${l}"> is not able to match the URL "${i}${d}${f}" because it does not start with the basename, so the <Router> won't render anything.`),b==null?null:y.createElement(pe.Provider,{value:u},y.createElement(gt.Provider,{children:t,value:b}))}var ft="get",ht="application/x-www-form-urlencoded";function wt(e){return e!=null&&typeof e.tagName=="string"}function Bn(e){return wt(e)&&e.tagName.toLowerCase()==="button"}function Wn(e){return wt(e)&&e.tagName.toLowerCase()==="form"}function Vn(e){return wt(e)&&e.tagName.toLowerCase()==="input"}function Kn(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Yn(e,t){return e.button===0&&(!t||t==="_self")&&!Kn(e)}var dt=null;function Jn(){if(dt===null)try{new FormData(document.createElement("form"),0),dt=!1}catch{dt=!0}return dt}var qn=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Mt(e){return e!=null&&!qn.has(e)?(J(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${ht}"`),null):e}function Gn(e,t){let r,n,a,o,l;if(Wn(e)){let u=e.getAttribute("action");n=u?de(u,t):null,r=e.getAttribute("method")||ft,a=Mt(e.getAttribute("enctype"))||ht,o=new FormData(e)}else if(Bn(e)||Vn(e)&&(e.type==="submit"||e.type==="image")){let u=e.form;if(u==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let i=e.getAttribute("formaction")||u.getAttribute("action");if(n=i?de(i,t):null,r=e.getAttribute("formmethod")||u.getAttribute("method")||ft,a=Mt(e.getAttribute("formenctype"))||Mt(u.getAttribute("enctype"))||ht,o=new FormData(u,e),!Jn()){let{name:d,type:f,value:g}=e;if(f==="image"){let h=d?`${d}.`:"";o.append(`${h}x`,"0"),o.append(`${h}y`,"0")}else d&&o.append(d,g)}}else{if(wt(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=ft,n=null,a=ht,l=e}return o&&a==="text/plain"&&(l=o,o=void 0),{action:n,method:r.toLowerCase(),encType:a,formData:o,body:l}}function Ht(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function Xn(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Qn(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Zn(e,t,r){let n=await Promise.all(e.map(async a=>{let o=t.routes[a.route.id];if(o){let l=await Xn(o,r);return l.links?l.links():[]}return[]}));return ao(n.flat(1).filter(Qn).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function Sr(e,t,r,n,a,o){let l=(i,d)=>r[d]?i.route.id!==r[d].route.id:!0,u=(i,d)=>{var f;return r[d].pathname!==i.pathname||((f=r[d].route.path)==null?void 0:f.endsWith("*"))&&r[d].params["*"]!==i.params["*"]};return o==="assets"?t.filter((i,d)=>l(i,d)||u(i,d)):o==="data"?t.filter((i,d)=>{var g;let f=n.routes[i.route.id];if(!f||!f.hasLoader)return!1;if(l(i,d)||u(i,d))return!0;if(i.route.shouldRevalidate){let h=i.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:((g=r[0])==null?void 0:g.params)||{},nextUrl:new URL(e,window.origin),nextParams:i.params,defaultShouldRevalidate:!0});if(typeof h=="boolean")return h}return!0}):[]}function eo(e,t,{includeHydrateFallback:r}={}){return to(e.map(n=>{let a=t.routes[n.route.id];if(!a)return[];let o=[a.module];return a.clientActionModule&&(o=o.concat(a.clientActionModule)),a.clientLoaderModule&&(o=o.concat(a.clientLoaderModule)),r&&a.hydrateFallbackModule&&(o=o.concat(a.hydrateFallbackModule)),a.imports&&(o=o.concat(a.imports)),o}).flat(1))}function to(e){return[...new Set(e)]}function ro(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}function ao(e,t){let r=new Set;return new Set(t),e.reduce((n,a)=>{let o=JSON.stringify(ro(a));return r.has(o)||(r.add(o),n.push({key:o,link:a})),n},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var no=new Set([100,101,204,205]);function oo(e,t){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":t&&de(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function Wr(){let e=y.useContext(Ne);return Ht(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function io(){let e=y.useContext(et);return Ht(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Bt=y.createContext(void 0);Bt.displayName="FrameworkContext";function Vr(){let e=y.useContext(Bt);return Ht(e,"You must render this element inside a <HydratedRouter> element"),e}function lo(e,t){let r=y.useContext(Bt),[n,a]=y.useState(!1),[o,l]=y.useState(!1),{onFocus:u,onBlur:i,onMouseEnter:d,onMouseLeave:f,onTouchStart:g}=t,h=y.useRef(null);y.useEffect(()=>{if(e==="render"&&l(!0),e==="viewport"){let x=w=>{w.forEach(O=>{l(O.isIntersecting)})},C=new IntersectionObserver(x,{threshold:.5});return h.current&&C.observe(h.current),()=>{C.disconnect()}}},[e]),y.useEffect(()=>{if(n){let x=setTimeout(()=>{l(!0)},100);return()=>{clearTimeout(x)}}},[n]);let b=()=>{a(!0)},v=()=>{a(!1),l(!1)};return r?e!=="intent"?[o,h,{}]:[o,h,{onFocus:qe(u,b),onBlur:qe(i,v),onMouseEnter:qe(d,b),onMouseLeave:qe(f,v),onTouchStart:qe(g,b)}]:[!1,h,{}]}function qe(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function so({page:e,...t}){let{router:r}=Wr(),n=y.useMemo(()=>Re(r.routes,e,r.basename),[r.routes,e,r.basename]);return n?y.createElement(co,{page:e,matches:n,...t}):null}function uo(e){let{manifest:t,routeModules:r}=Vr(),[n,a]=y.useState([]);return y.useEffect(()=>{let o=!1;return Zn(e,t,r).then(l=>{o||a(l)}),()=>{o=!0}},[e,t,r]),n}function co({page:e,matches:t,...r}){let n=Ue(),{manifest:a,routeModules:o}=Vr(),{basename:l}=Wr(),{loaderData:u,matches:i}=io(),d=y.useMemo(()=>Sr(e,t,i,a,n,"data"),[e,t,i,a,n]),f=y.useMemo(()=>Sr(e,t,i,a,n,"assets"),[e,t,i,a,n]),g=y.useMemo(()=>{if(e===n.pathname+n.search+n.hash)return[];let v=new Set,x=!1;if(t.forEach(w=>{var L;let O=a.routes[w.route.id];!O||!O.hasLoader||(!d.some(N=>N.route.id===w.route.id)&&w.route.id in u&&((L=o[w.route.id])!=null&&L.shouldRevalidate)||O.hasClientLoader?x=!0:v.add(w.route.id))}),v.size===0)return[];let C=oo(e,l);return x&&v.size>0&&C.searchParams.set("_routes",t.filter(w=>v.has(w.route.id)).map(w=>w.route.id).join(",")),[C.pathname+C.search]},[l,u,n,a,d,t,e,o]),h=y.useMemo(()=>eo(f,a),[f,a]),b=uo(f);return y.createElement(y.Fragment,null,g.map(v=>y.createElement("link",{key:v,rel:"prefetch",as:"fetch",href:v,...r})),h.map(v=>y.createElement("link",{key:v,rel:"modulepreload",href:v,...r})),b.map(({key:v,link:x})=>y.createElement("link",{key:v,...x})))}function fo(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var Kr=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Kr&&(window.__reactRouterVersion="7.6.2")}catch{}function Do(e,t){return Qa({basename:t==null?void 0:t.basename,unstable_getContext:t==null?void 0:t.unstable_getContext,future:t==null?void 0:t.future,history:wa({window:t==null?void 0:t.window}),hydrationData:ho(),routes:e,mapRouteProperties:Un,hydrationRouteProperties:An,dataStrategy:t==null?void 0:t.dataStrategy,patchRoutesOnNavigation:t==null?void 0:t.patchRoutesOnNavigation,window:t==null?void 0:t.window}).initialize()}function ho(){let e=window==null?void 0:window.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:mo(e.errors)}),e}function mo(e){if(!e)return null;let t=Object.entries(e),r={};for(let[n,a]of t)if(a&&a.__type==="RouteErrorResponse")r[n]=new yt(a.status,a.statusText,a.data,a.internal===!0);else if(a&&a.__type==="Error"){if(a.__subType){let o=window[a.__subType];if(typeof o=="function")try{let l=new o(a.message);l.stack="",r[n]=l}catch{}}if(r[n]==null){let o=new Error(a.message);o.stack="",r[n]=o}}else r[n]=a;return r}var Yr=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Jr=y.forwardRef(function({onClick:t,discover:r="render",prefetch:n="none",relative:a,reloadDocument:o,replace:l,state:u,target:i,to:d,preventScrollReset:f,viewTransition:g,...h},b){let{basename:v}=y.useContext(pe),x=typeof d=="string"&&Yr.test(d),C,w=!1;if(typeof d=="string"&&x&&(C=d,Kr))try{let _=new URL(window.location.href),G=d.startsWith("//")?new URL(_.protocol+d):new URL(d),ce=de(G.pathname,v);G.origin===_.origin&&ce!=null?d=ce+G.search+G.hash:w=!0}catch{J(!1,`<Link to="${d}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let O=wn(d,{relative:a}),[L,N,U]=lo(n,h),p=go(d,{replace:l,state:u,target:i,preventScrollReset:f,relative:a,viewTransition:g});function j(_){t&&t(_),_.defaultPrevented||p(_)}let V=y.createElement("a",{...h,...U,href:C||O,onClick:w||o?t:j,ref:fo(b,N),target:i,"data-discover":!x&&r==="render"?"true":void 0});return L&&!x?y.createElement(y.Fragment,null,V,y.createElement(so,{page:O})):V});Jr.displayName="Link";var po=y.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:n="",end:a=!1,style:o,to:l,viewTransition:u,children:i,...d},f){let g=rt(l,{relative:d.relative}),h=Ue(),b=y.useContext(et),{navigator:v,basename:x}=y.useContext(pe),C=b!=null&&xo(g)&&u===!0,w=v.encodeLocation?v.encodeLocation(g).pathname:g.pathname,O=h.pathname,L=b&&b.navigation&&b.navigation.location?b.navigation.location.pathname:null;r||(O=O.toLowerCase(),L=L?L.toLowerCase():null,w=w.toLowerCase()),L&&x&&(L=de(L,x)||L);const N=w!=="/"&&w.endsWith("/")?w.length-1:w.length;let U=O===w||!a&&O.startsWith(w)&&O.charAt(N)==="/",p=L!=null&&(L===w||!a&&L.startsWith(w)&&L.charAt(w.length)==="/"),j={isActive:U,isPending:p,isTransitioning:C},V=U?t:void 0,_;typeof n=="function"?_=n(j):_=[n,U?"active":null,p?"pending":null,C?"transitioning":null].filter(Boolean).join(" ");let G=typeof o=="function"?o(j):o;return y.createElement(Jr,{...d,"aria-current":V,className:_,ref:f,style:G,to:l,viewTransition:u},typeof i=="function"?i(j):i)});po.displayName="NavLink";var yo=y.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:n,replace:a,state:o,method:l=ft,action:u,onSubmit:i,relative:d,preventScrollReset:f,viewTransition:g,...h},b)=>{let v=Eo(),x=Ro(u,{relative:d}),C=l.toLowerCase()==="get"?"get":"post",w=typeof u=="string"&&Yr.test(u),O=L=>{if(i&&i(L),L.defaultPrevented)return;L.preventDefault();let N=L.nativeEvent.submitter,U=(N==null?void 0:N.getAttribute("formmethod"))||l;v(N||L.currentTarget,{fetcherKey:t,method:U,navigate:r,replace:a,state:o,relative:d,preventScrollReset:f,viewTransition:g})};return y.createElement("form",{ref:b,method:C,action:x,onSubmit:n?i:O,...h,"data-discover":!w&&e==="render"?"true":void 0})});yo.displayName="Form";function vo(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function qr(e){let t=y.useContext(Ne);return A(t,vo(e)),t}function go(e,{target:t,replace:r,state:n,preventScrollReset:a,relative:o,viewTransition:l}={}){let u=bn(),i=Ue(),d=rt(e,{relative:o});return y.useCallback(f=>{if(Yn(f,t)){f.preventDefault();let g=r!==void 0?r:xe(i)===xe(d);u(e,{replace:g,state:n,preventScrollReset:a,relative:o,viewTransition:l})}},[i,u,d,r,n,t,e,a,o,l])}var wo=0,bo=()=>`__${String(++wo)}__`;function Eo(){let{router:e}=qr("useSubmit"),{basename:t}=y.useContext(pe),r=On();return y.useCallback(async(n,a={})=>{let{action:o,method:l,encType:u,formData:i,body:d}=Gn(n,t);if(a.navigate===!1){let f=a.fetcherKey||bo();await e.fetch(f,r,a.action||o,{preventScrollReset:a.preventScrollReset,formData:i,body:d,formMethod:a.method||l,formEncType:a.encType||u,flushSync:a.flushSync})}else await e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:i,body:d,formMethod:a.method||l,formEncType:a.encType||u,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,r])}function Ro(e,{relative:t}={}){let{basename:r}=y.useContext(pe),n=y.useContext(ye);A(n,"useFormAction must be used inside a RouteContext");let[a]=n.matches.slice(-1),o={...rt(e||".",{relative:t})},l=Ue();if(e==null){o.search=l.search;let u=new URLSearchParams(o.search),i=u.getAll("index");if(i.some(f=>f==="")){u.delete("index"),i.filter(g=>g).forEach(g=>u.append("index",g));let f=u.toString();o.search=f?`?${f}`:""}}return(!e||e===".")&&a.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(o.pathname=o.pathname==="/"?r:me([r,o.pathname])),xe(o)}function xo(e,t={}){let r=y.useContext(_t);A(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=qr("useViewTransitionState"),a=rt(e,{relative:t.relative});if(!r.isTransitioning)return!1;let o=de(r.currentLocation.pathname,n)||r.currentLocation.pathname,l=de(r.nextLocation.pathname,n)||r.nextLocation.pathname;return pt(a.pathname,l)!=null||pt(a.pathname,o)!=null}[...no];var So=pa();/**
 * react-router v7.6.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Mo(e){return y.createElement(zn,{flushSync:So.flushSync,...e})}export{Jr as L,Po as O,Lo as R,Mo as a,Do as c,y as r,bn as u};
//# sourceMappingURL=router-BHs97Tqp.js.map
