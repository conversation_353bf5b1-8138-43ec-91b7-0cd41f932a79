var Qt=e=>{throw TypeError(e)};var wt=(e,t,s)=>t.has(e)||Qt("Cannot "+s);var i=(e,t,s)=>(wt(e,t,"read from private field"),s?s.call(e):t.get(e)),d=(e,t,s)=>t.has(e)?Qt("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),o=(e,t,s,r)=>(wt(e,t,"write to private field"),r?r.call(e,s):t.set(e,s),s),S=(e,t,s)=>(wt(e,t,"access private method"),s);var pt=(e,t,s,r)=>({set _(n){o(e,t,n,s)},get _(){return i(e,t,r)}});import{r as Bt}from"./router-BU9vYC2A.js";import{r as Zt}from"./vendor-BtP0CW_r.js";var Pt={exports:{}},ot={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qt;function te(){if(qt)return ot;qt=1;var e=Zt(),t=Symbol.for("react.element"),s=Symbol.for("react.fragment"),r=Object.prototype.hasOwnProperty,n=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function f(a,c,g){var p,m={},h=null,v=null;g!==void 0&&(h=""+g),c.key!==void 0&&(h=""+c.key),c.ref!==void 0&&(v=c.ref);for(p in c)r.call(c,p)&&!u.hasOwnProperty(p)&&(m[p]=c[p]);if(a&&a.defaultProps)for(p in c=a.defaultProps,c)m[p]===void 0&&(m[p]=c[p]);return{$$typeof:t,type:a,key:h,ref:v,props:m,_owner:n.current}}return ot.Fragment=s,ot.jsx=f,ot.jsxs=f,ot}var Dt;function ee(){return Dt||(Dt=1,Pt.exports=te()),Pt.exports}var se=ee(),bt=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},gt=typeof window>"u"||"Deno"in globalThis;function M(){}function re(e,t){return typeof e=="function"?e(t):e}function ie(e){return typeof e=="number"&&e>=0&&e!==1/0}function ne(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Ft(e,t){return typeof e=="function"?e(t):e}function ae(e,t){return typeof e=="function"?e(t):e}function Mt(e,t){const{type:s="all",exact:r,fetchStatus:n,predicate:u,queryKey:f,stale:a}=e;if(f){if(r){if(t.queryHash!==Ct(f,t.options))return!1}else if(!ct(t.queryKey,f))return!1}if(s!=="all"){const c=t.isActive();if(s==="active"&&!c||s==="inactive"&&c)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||n&&n!==t.state.fetchStatus||u&&!u(t))}function At(e,t){const{exact:s,status:r,predicate:n,mutationKey:u}=e;if(u){if(!t.options.mutationKey)return!1;if(s){if(ht(t.options.mutationKey)!==ht(u))return!1}else if(!ct(t.options.mutationKey,u))return!1}return!(r&&t.state.status!==r||n&&!n(t))}function Ct(e,t){return((t==null?void 0:t.queryKeyHashFn)||ht)(e)}function ht(e){return JSON.stringify(e,(t,s)=>St(s)?Object.keys(s).sort().reduce((r,n)=>(r[n]=s[n],r),{}):s)}function ct(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(s=>ct(e[s],t[s])):!1}function Jt(e,t){if(e===t)return e;const s=xt(e)&&xt(t);if(s||St(e)&&St(t)){const r=s?e:Object.keys(e),n=r.length,u=s?t:Object.keys(t),f=u.length,a=s?[]:{},c=new Set(r);let g=0;for(let p=0;p<f;p++){const m=s?p:u[p];(!s&&c.has(m)||s)&&e[m]===void 0&&t[m]===void 0?(a[m]=void 0,g++):(a[m]=Jt(e[m],t[m]),a[m]===e[m]&&e[m]!==void 0&&g++)}return n===f&&g===n?e:a}return t}function xt(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function St(e){if(!jt(e))return!1;const t=e.constructor;if(t===void 0)return!0;const s=t.prototype;return!(!jt(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function jt(e){return Object.prototype.toString.call(e)==="[object Object]"}function ue(e){return new Promise(t=>{setTimeout(t,e)})}function oe(e,t,s){return typeof s.structuralSharing=="function"?s.structuralSharing(e,t):s.structuralSharing!==!1?Jt(e,t):t}function he(e,t,s=0){const r=[...e,t];return s&&r.length>s?r.slice(1):r}function ce(e,t,s=0){const r=[t,...e];return s&&r.length>s?r.slice(0,-1):r}var Rt=Symbol();function $t(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===Rt?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var z,H,Z,It,le=(It=class extends bt{constructor(){super();d(this,z);d(this,H);d(this,Z);o(this,Z,t=>{if(!gt&&window.addEventListener){const s=()=>t();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){i(this,H)||this.setEventListener(i(this,Z))}onUnsubscribe(){var t;this.hasListeners()||((t=i(this,H))==null||t.call(this),o(this,H,void 0))}setEventListener(t){var s;o(this,Z,t),(s=i(this,H))==null||s.call(this),o(this,H,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){i(this,z)!==t&&(o(this,z,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(s=>{s(t)})}isFocused(){var t;return typeof i(this,z)=="boolean"?i(this,z):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},z=new WeakMap,H=new WeakMap,Z=new WeakMap,It),zt=new le,tt,G,et,_t,de=(_t=class extends bt{constructor(){super();d(this,tt,!0);d(this,G);d(this,et);o(this,et,t=>{if(!gt&&window.addEventListener){const s=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",r)}}})}onSubscribe(){i(this,G)||this.setEventListener(i(this,et))}onUnsubscribe(){var t;this.hasListeners()||((t=i(this,G))==null||t.call(this),o(this,G,void 0))}setEventListener(t){var s;o(this,et,t),(s=i(this,G))==null||s.call(this),o(this,G,t(this.setOnline.bind(this)))}setOnline(t){i(this,tt)!==t&&(o(this,tt,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return i(this,tt)}},tt=new WeakMap,G=new WeakMap,et=new WeakMap,_t),vt=new de;function fe(){let e,t;const s=new Promise((n,u)=>{e=n,t=u});s.status="pending",s.catch(()=>{});function r(n){Object.assign(s,n),delete s.resolve,delete s.reject}return s.resolve=n=>{r({status:"fulfilled",value:n}),e(n)},s.reject=n=>{r({status:"rejected",reason:n}),t(n)},s}function ye(e){return Math.min(1e3*2**e,3e4)}function Vt(e){return(e??"online")==="online"?vt.isOnline():!0}var Wt=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Ot(e){return e instanceof Wt}function Yt(e){let t=!1,s=0,r=!1,n;const u=fe(),f=l=>{var y;r||(h(new Wt(l)),(y=e.abort)==null||y.call(e))},a=()=>{t=!0},c=()=>{t=!1},g=()=>zt.isFocused()&&(e.networkMode==="always"||vt.isOnline())&&e.canRun(),p=()=>Vt(e.networkMode)&&e.canRun(),m=l=>{var y;r||(r=!0,(y=e.onSuccess)==null||y.call(e,l),n==null||n(),u.resolve(l))},h=l=>{var y;r||(r=!0,(y=e.onError)==null||y.call(e,l),n==null||n(),u.reject(l))},v=()=>new Promise(l=>{var y;n=F=>{(r||g())&&l(F)},(y=e.onPause)==null||y.call(e)}).then(()=>{var l;n=void 0,r||(l=e.onContinue)==null||l.call(e)}),w=()=>{if(r)return;let l;const y=s===0?e.initialPromise:void 0;try{l=y??e.fn()}catch(F){l=Promise.reject(F)}Promise.resolve(l).then(m).catch(F=>{var k;if(r)return;const D=e.retry??(gt?0:3),P=e.retryDelay??ye,Q=typeof P=="function"?P(s,F):P,j=D===!0||typeof D=="number"&&s<D||typeof D=="function"&&D(s,F);if(t||!j){h(F);return}s++,(k=e.onFail)==null||k.call(e,s,F),ue(Q).then(()=>g()?void 0:v()).then(()=>{t?h(F):w()})})};return{promise:u,cancel:f,continue:()=>(n==null||n(),u),cancelRetry:a,continueRetry:c,canStart:p,start:()=>(p()?w():v().then(w),u)}}var pe=e=>setTimeout(e,0);function me(){let e=[],t=0,s=a=>{a()},r=a=>{a()},n=pe;const u=a=>{t?e.push(a):n(()=>{s(a)})},f=()=>{const a=e;e=[],a.length&&n(()=>{r(()=>{a.forEach(c=>{s(c)})})})};return{batch:a=>{let c;t++;try{c=a()}finally{t--,t||f()}return c},batchCalls:a=>(...c)=>{u(()=>{a(...c)})},schedule:u,setNotifyFunction:a=>{s=a},setBatchNotifyFunction:a=>{r=a},setScheduler:a=>{n=a}}}var E=me(),V,Kt,Xt=(Kt=class{constructor(){d(this,V)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),ie(this.gcTime)&&o(this,V,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(gt?1/0:5*60*1e3))}clearGcTimeout(){i(this,V)&&(clearTimeout(i(this,V)),o(this,V,void 0))}},V=new WeakMap,Kt),st,rt,q,W,C,lt,Y,A,_,kt,ve=(kt=class extends Xt{constructor(t){super();d(this,A);d(this,st);d(this,rt);d(this,q);d(this,W);d(this,C);d(this,lt);d(this,Y);o(this,Y,!1),o(this,lt,t.defaultOptions),this.setOptions(t.options),this.observers=[],o(this,W,t.client),o(this,q,i(this,W).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,o(this,st,ge(this.options)),this.state=t.state??i(this,st),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=i(this,C))==null?void 0:t.promise}setOptions(t){this.options={...i(this,lt),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&i(this,q).remove(this)}setData(t,s){const r=oe(this.state.data,t,this.options);return S(this,A,_).call(this,{data:r,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),r}setState(t,s){S(this,A,_).call(this,{type:"setState",state:t,setStateOptions:s})}cancel(t){var r,n;const s=(r=i(this,C))==null?void 0:r.promise;return(n=i(this,C))==null||n.cancel(t),s?s.then(M).catch(M):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(i(this,st))}isActive(){return this.observers.some(t=>ae(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Rt||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>Ft(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!ne(this.state.dataUpdatedAt,t)}onFocus(){var s;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(s=i(this,C))==null||s.continue()}onOnline(){var s;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(s=i(this,C))==null||s.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),i(this,q).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(s=>s!==t),this.observers.length||(i(this,C)&&(i(this,Y)?i(this,C).cancel({revert:!0}):i(this,C).cancelRetry()),this.scheduleGc()),i(this,q).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||S(this,A,_).call(this,{type:"invalidate"})}fetch(t,s){var g,p,m;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(i(this,C))return i(this,C).continueRetry(),i(this,C).promise}if(t&&this.setOptions(t),!this.options.queryFn){const h=this.observers.find(v=>v.options.queryFn);h&&this.setOptions(h.options)}const r=new AbortController,n=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(o(this,Y,!0),r.signal)})},u=()=>{const h=$t(this.options,s),w=(()=>{const l={client:i(this,W),queryKey:this.queryKey,meta:this.meta};return n(l),l})();return o(this,Y,!1),this.options.persister?this.options.persister(h,w,this):h(w)},a=(()=>{const h={fetchOptions:s,options:this.options,queryKey:this.queryKey,client:i(this,W),state:this.state,fetchFn:u};return n(h),h})();(g=this.options.behavior)==null||g.onFetch(a,this),o(this,rt,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((p=a.fetchOptions)==null?void 0:p.meta))&&S(this,A,_).call(this,{type:"fetch",meta:(m=a.fetchOptions)==null?void 0:m.meta});const c=h=>{var v,w,l,y;Ot(h)&&h.silent||S(this,A,_).call(this,{type:"error",error:h}),Ot(h)||((w=(v=i(this,q).config).onError)==null||w.call(v,h,this),(y=(l=i(this,q).config).onSettled)==null||y.call(l,this.state.data,h,this)),this.scheduleGc()};return o(this,C,Yt({initialPromise:s==null?void 0:s.initialPromise,fn:a.fetchFn,abort:r.abort.bind(r),onSuccess:h=>{var v,w,l,y;if(h===void 0){c(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(h)}catch(F){c(F);return}(w=(v=i(this,q).config).onSuccess)==null||w.call(v,h,this),(y=(l=i(this,q).config).onSettled)==null||y.call(l,h,this.state.error,this),this.scheduleGc()},onError:c,onFail:(h,v)=>{S(this,A,_).call(this,{type:"failed",failureCount:h,error:v})},onPause:()=>{S(this,A,_).call(this,{type:"pause"})},onContinue:()=>{S(this,A,_).call(this,{type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0})),i(this,C).start()}},st=new WeakMap,rt=new WeakMap,q=new WeakMap,W=new WeakMap,C=new WeakMap,lt=new WeakMap,Y=new WeakMap,A=new WeakSet,_=function(t){const s=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...be(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=t.error;return Ot(n)&&n.revert&&i(this,rt)?{...i(this,rt),fetchStatus:"idle"}:{...r,error:n,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=s(this.state),E.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),i(this,q).notify({query:this,type:"updated",action:t})})},kt);function be(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Vt(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function ge(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,s=t!==void 0,r=s?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var T,Lt,we=(Lt=class extends bt{constructor(t={}){super();d(this,T);this.config=t,o(this,T,new Map)}build(t,s,r){const n=s.queryKey,u=s.queryHash??Ct(n,s);let f=this.get(u);return f||(f=new ve({client:t,queryKey:n,queryHash:u,options:t.defaultQueryOptions(s),state:r,defaultOptions:t.getQueryDefaults(n)}),this.add(f)),f}add(t){i(this,T).has(t.queryHash)||(i(this,T).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const s=i(this,T).get(t.queryHash);s&&(t.destroy(),s===t&&i(this,T).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){E.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return i(this,T).get(t)}getAll(){return[...i(this,T).values()]}find(t){const s={exact:!0,...t};return this.getAll().find(r=>Mt(s,r))}findAll(t={}){const s=this.getAll();return Object.keys(t).length>0?s.filter(r=>Mt(t,r)):s}notify(t){E.batch(()=>{this.listeners.forEach(s=>{s(t)})})}onFocus(){E.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){E.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},T=new WeakMap,Lt),U,R,X,I,L,Ht,Pe=(Ht=class extends Xt{constructor(t){super();d(this,I);d(this,U);d(this,R);d(this,X);this.mutationId=t.mutationId,o(this,R,t.mutationCache),o(this,U,[]),this.state=t.state||Oe(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){i(this,U).includes(t)||(i(this,U).push(t),this.clearGcTimeout(),i(this,R).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){o(this,U,i(this,U).filter(s=>s!==t)),this.scheduleGc(),i(this,R).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){i(this,U).length||(this.state.status==="pending"?this.scheduleGc():i(this,R).remove(this))}continue(){var t;return((t=i(this,X))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var u,f,a,c,g,p,m,h,v,w,l,y,F,D,P,Q,j,k,ft,yt;const s=()=>{S(this,I,L).call(this,{type:"continue"})};o(this,X,Yt({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(O,$)=>{S(this,I,L).call(this,{type:"failed",failureCount:O,error:$})},onPause:()=>{S(this,I,L).call(this,{type:"pause"})},onContinue:s,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>i(this,R).canRun(this)}));const r=this.state.status==="pending",n=!i(this,X).canStart();try{if(r)s();else{S(this,I,L).call(this,{type:"pending",variables:t,isPaused:n}),await((f=(u=i(this,R).config).onMutate)==null?void 0:f.call(u,t,this));const $=await((c=(a=this.options).onMutate)==null?void 0:c.call(a,t));$!==this.state.context&&S(this,I,L).call(this,{type:"pending",context:$,variables:t,isPaused:n})}const O=await i(this,X).start();return await((p=(g=i(this,R).config).onSuccess)==null?void 0:p.call(g,O,t,this.state.context,this)),await((h=(m=this.options).onSuccess)==null?void 0:h.call(m,O,t,this.state.context)),await((w=(v=i(this,R).config).onSettled)==null?void 0:w.call(v,O,null,this.state.variables,this.state.context,this)),await((y=(l=this.options).onSettled)==null?void 0:y.call(l,O,null,t,this.state.context)),S(this,I,L).call(this,{type:"success",data:O}),O}catch(O){try{throw await((D=(F=i(this,R).config).onError)==null?void 0:D.call(F,O,t,this.state.context,this)),await((Q=(P=this.options).onError)==null?void 0:Q.call(P,O,t,this.state.context)),await((k=(j=i(this,R).config).onSettled)==null?void 0:k.call(j,void 0,O,this.state.variables,this.state.context,this)),await((yt=(ft=this.options).onSettled)==null?void 0:yt.call(ft,void 0,O,t,this.state.context)),O}finally{S(this,I,L).call(this,{type:"error",error:O})}}finally{i(this,R).runNext(this)}}},U=new WeakMap,R=new WeakMap,X=new WeakMap,I=new WeakSet,L=function(t){const s=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=s(this.state),E.batch(()=>{i(this,U).forEach(r=>{r.onMutationUpdate(t)}),i(this,R).notify({mutation:this,type:"updated",action:t})})},Ht);function Oe(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var K,x,dt,Gt,Fe=(Gt=class extends bt{constructor(t={}){super();d(this,K);d(this,x);d(this,dt);this.config=t,o(this,K,new Set),o(this,x,new Map),o(this,dt,0)}build(t,s,r){const n=new Pe({mutationCache:this,mutationId:++pt(this,dt)._,options:t.defaultMutationOptions(s),state:r});return this.add(n),n}add(t){i(this,K).add(t);const s=mt(t);if(typeof s=="string"){const r=i(this,x).get(s);r?r.push(t):i(this,x).set(s,[t])}this.notify({type:"added",mutation:t})}remove(t){if(i(this,K).delete(t)){const s=mt(t);if(typeof s=="string"){const r=i(this,x).get(s);if(r)if(r.length>1){const n=r.indexOf(t);n!==-1&&r.splice(n,1)}else r[0]===t&&i(this,x).delete(s)}}this.notify({type:"removed",mutation:t})}canRun(t){const s=mt(t);if(typeof s=="string"){const r=i(this,x).get(s),n=r==null?void 0:r.find(u=>u.state.status==="pending");return!n||n===t}else return!0}runNext(t){var r;const s=mt(t);if(typeof s=="string"){const n=(r=i(this,x).get(s))==null?void 0:r.find(u=>u!==t&&u.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}else return Promise.resolve()}clear(){E.batch(()=>{i(this,K).forEach(t=>{this.notify({type:"removed",mutation:t})}),i(this,K).clear(),i(this,x).clear()})}getAll(){return Array.from(i(this,K))}find(t){const s={exact:!0,...t};return this.getAll().find(r=>At(s,r))}findAll(t={}){return this.getAll().filter(s=>At(t,s))}notify(t){E.batch(()=>{this.listeners.forEach(s=>{s(t)})})}resumePausedMutations(){const t=this.getAll().filter(s=>s.state.isPaused);return E.batch(()=>Promise.all(t.map(s=>s.continue().catch(M))))}},K=new WeakMap,x=new WeakMap,dt=new WeakMap,Gt);function mt(e){var t;return(t=e.options.scope)==null?void 0:t.id}function Tt(e){return{onFetch:(t,s)=>{var p,m,h,v,w;const r=t.options,n=(h=(m=(p=t.fetchOptions)==null?void 0:p.meta)==null?void 0:m.fetchMore)==null?void 0:h.direction,u=((v=t.state.data)==null?void 0:v.pages)||[],f=((w=t.state.data)==null?void 0:w.pageParams)||[];let a={pages:[],pageParams:[]},c=0;const g=async()=>{let l=!1;const y=P=>{Object.defineProperty(P,"signal",{enumerable:!0,get:()=>(t.signal.aborted?l=!0:t.signal.addEventListener("abort",()=>{l=!0}),t.signal)})},F=$t(t.options,t.fetchOptions),D=async(P,Q,j)=>{if(l)return Promise.reject();if(Q==null&&P.pages.length)return Promise.resolve(P);const ft=(()=>{const Et={client:t.client,queryKey:t.queryKey,pageParam:Q,direction:j?"backward":"forward",meta:t.options.meta};return y(Et),Et})(),yt=await F(ft),{maxPages:O}=t.options,$=j?ce:he;return{pages:$(P.pages,yt,O),pageParams:$(P.pageParams,Q,O)}};if(n&&u.length){const P=n==="backward",Q=P?Se:Ut,j={pages:u,pageParams:f},k=Q(r,j);a=await D(j,k,P)}else{const P=e??u.length;do{const Q=c===0?f[0]??r.initialPageParam:Ut(r,a);if(c>0&&Q==null)break;a=await D(a,Q),c++}while(c<P)}return a};t.options.persister?t.fetchFn=()=>{var l,y;return(y=(l=t.options).persister)==null?void 0:y.call(l,g,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s)}:t.fetchFn=g}}}function Ut(e,{pages:t,pageParams:s}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,s[r],s):void 0}function Se(e,{pages:t,pageParams:s}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,s[0],s):void 0}var b,N,B,it,nt,J,at,ut,Nt,qe=(Nt=class{constructor(e={}){d(this,b);d(this,N);d(this,B);d(this,it);d(this,nt);d(this,J);d(this,at);d(this,ut);o(this,b,e.queryCache||new we),o(this,N,e.mutationCache||new Fe),o(this,B,e.defaultOptions||{}),o(this,it,new Map),o(this,nt,new Map),o(this,J,0)}mount(){pt(this,J)._++,i(this,J)===1&&(o(this,at,zt.subscribe(async e=>{e&&(await this.resumePausedMutations(),i(this,b).onFocus())})),o(this,ut,vt.subscribe(async e=>{e&&(await this.resumePausedMutations(),i(this,b).onOnline())})))}unmount(){var e,t;pt(this,J)._--,i(this,J)===0&&((e=i(this,at))==null||e.call(this),o(this,at,void 0),(t=i(this,ut))==null||t.call(this),o(this,ut,void 0))}isFetching(e){return i(this,b).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return i(this,N).findAll({...e,status:"pending"}).length}getQueryData(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=i(this,b).get(t.queryHash))==null?void 0:s.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),s=i(this,b).build(this,t),r=s.state.data;return r===void 0?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime(Ft(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return i(this,b).findAll(e).map(({queryKey:t,state:s})=>{const r=s.data;return[t,r]})}setQueryData(e,t,s){const r=this.defaultQueryOptions({queryKey:e}),n=i(this,b).get(r.queryHash),u=n==null?void 0:n.state.data,f=re(t,u);if(f!==void 0)return i(this,b).build(this,r).setData(f,{...s,manual:!0})}setQueriesData(e,t,s){return E.batch(()=>i(this,b).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,s)]))}getQueryState(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=i(this,b).get(t.queryHash))==null?void 0:s.state}removeQueries(e){const t=i(this,b);E.batch(()=>{t.findAll(e).forEach(s=>{t.remove(s)})})}resetQueries(e,t){const s=i(this,b);return E.batch(()=>(s.findAll(e).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const s={revert:!0,...t},r=E.batch(()=>i(this,b).findAll(e).map(n=>n.cancel(s)));return Promise.all(r).then(M).catch(M)}invalidateQueries(e,t={}){return E.batch(()=>(i(this,b).findAll(e).forEach(s=>{s.invalidate()}),(e==null?void 0:e.refetchType)==="none"?Promise.resolve():this.refetchQueries({...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"},t)))}refetchQueries(e,t={}){const s={...t,cancelRefetch:t.cancelRefetch??!0},r=E.batch(()=>i(this,b).findAll(e).filter(n=>!n.isDisabled()&&!n.isStatic()).map(n=>{let u=n.fetch(void 0,s);return s.throwOnError||(u=u.catch(M)),n.state.fetchStatus==="paused"?Promise.resolve():u}));return Promise.all(r).then(M)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const s=i(this,b).build(this,t);return s.isStaleByTime(Ft(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(M).catch(M)}fetchInfiniteQuery(e){return e.behavior=Tt(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(M).catch(M)}ensureInfiniteQueryData(e){return e.behavior=Tt(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return vt.isOnline()?i(this,N).resumePausedMutations():Promise.resolve()}getQueryCache(){return i(this,b)}getMutationCache(){return i(this,N)}getDefaultOptions(){return i(this,B)}setDefaultOptions(e){o(this,B,e)}setQueryDefaults(e,t){i(this,it).set(ht(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...i(this,it).values()],s={};return t.forEach(r=>{ct(e,r.queryKey)&&Object.assign(s,r.defaultOptions)}),s}setMutationDefaults(e,t){i(this,nt).set(ht(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...i(this,nt).values()],s={};return t.forEach(r=>{ct(e,r.mutationKey)&&Object.assign(s,r.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;const t={...i(this,B).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Ct(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===Rt&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...i(this,B).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){i(this,b).clear(),i(this,N).clear()}},b=new WeakMap,N=new WeakMap,B=new WeakMap,it=new WeakMap,nt=new WeakMap,J=new WeakMap,at=new WeakMap,ut=new WeakMap,Nt),Ce=Bt.createContext(void 0),De=({client:e,children:t})=>(Bt.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),se.jsx(Ce.Provider,{value:e,children:t}));export{qe as Q,De as a,se as j};
//# sourceMappingURL=query-CjHflsNX.js.map
