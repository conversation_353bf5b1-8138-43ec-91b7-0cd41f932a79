import React from "react";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import { Layout } from "../components/layout/Layout";
import { HomePage } from "../pages/HomePage";
import { LoginPage } from "../pages/LoginPage";

// Import pages directly for now (can be lazy loaded later)
import {
  BrowsePage,
  MangaDetailPage,
  ReaderPage,
  RegisterPage,
  BookmarksPage,
  HistoryPage,
  ProfilePage,
} from "../pages";

const router = createBrowserRouter([
  {
    path: "/",
    element: <Layout />,
    children: [
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: "login",
        element: <LoginPage />,
      },
      {
        path: "register",
        element: <RegisterPage />,
      },
      {
        path: "browse",
        element: <BrowsePage />,
      },
      {
        path: "manga/:id",
        element: <MangaDetailPage />,
      },
      {
        path: "read/:chapterId",
        element: <ReaderPage />,
      },
      {
        path: "bookmarks",
        element: <BookmarksPage />,
      },
      {
        path: "history",
        element: <HistoryPage />,
      },
      {
        path: "profile",
        element: <ProfilePage />,
      },
    ],
  },
]);

export const AppRouter: React.FC = () => {
  return <RouterProvider router={router} />;
};
