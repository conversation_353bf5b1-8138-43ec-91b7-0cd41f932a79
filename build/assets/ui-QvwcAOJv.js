import{r as l}from"./router-BHs97Tqp.js";import{j as f}from"./query-LIbp8HbF.js";function d(e,n){if(typeof e=="function")return e(n);e!=null&&(e.current=n)}function S(...e){return n=>{let o=!1;const r=e.map(t=>{const i=d(t,n);return!o&&typeof i=="function"&&(o=!0),i});if(o)return()=>{for(let t=0;t<r.length;t++){const i=r[t];typeof i=="function"?i():d(e[t],null)}}}}function m(e){const n=g(e),o=l.forwardRef((r,t)=>{const{children:i,...a}=r,s=l.Children.toArray(i),c=s.find(h);if(c){const u=c.props.children,y=s.map(p=>p===c?l.Children.count(u)>1?l.Children.only(null):l.isValidElement(u)?u.props.children:null:p);return f.jsx(n,{...a,ref:t,children:l.isValidElement(u)?l.cloneElement(u,void 0,y):null})}return f.jsx(n,{...a,ref:t,children:i})});return o.displayName=`${e}.Slot`,o}var b=m("Slot");function g(e){const n=l.forwardRef((o,r)=>{const{children:t,...i}=o;if(l.isValidElement(t)){const a=C(t),s=v(i,t.props);return t.type!==l.Fragment&&(s.ref=r?S(r,a):a),l.cloneElement(t,s)}return l.Children.count(t)>1?l.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}var E=Symbol("radix.slottable");function h(e){return l.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===E}function v(e,n){const o={...n};for(const r in n){const t=e[r],i=n[r];/^on[A-Z]/.test(r)?t&&i?o[r]=(...s)=>{const c=i(...s);return t(...s),c}:t&&(o[r]=t):r==="style"?o[r]={...t,...i}:r==="className"&&(o[r]=[t,i].filter(Boolean).join(" "))}return{...e,...o}}function C(e){var r,t;let n=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning,o?e.props.ref:e.props.ref||e.ref)}var x=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],j=x.reduce((e,n)=>{const o=m(`Primitive.${n}`),r=l.forwardRef((t,i)=>{const{asChild:a,...s}=t,c=a?o:n;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(c,{...s,ref:i})});return r.displayName=`Primitive.${n}`,{...e,[n]:r}},{});export{j as P,b as S};
//# sourceMappingURL=ui-QvwcAOJv.js.map
