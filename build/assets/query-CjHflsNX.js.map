{"version": 3, "file": "query-CjHflsNX.js", "sources": ["../../node_modules/react/cjs/react-jsx-runtime.production.min.js", "../../node_modules/react/jsx-runtime.js", "../../node_modules/@tanstack/query-core/build/modern/subscribable.js", "../../node_modules/@tanstack/query-core/build/modern/utils.js", "../../node_modules/@tanstack/query-core/build/modern/focusManager.js", "../../node_modules/@tanstack/query-core/build/modern/onlineManager.js", "../../node_modules/@tanstack/query-core/build/modern/thenable.js", "../../node_modules/@tanstack/query-core/build/modern/retryer.js", "../../node_modules/@tanstack/query-core/build/modern/notifyManager.js", "../../node_modules/@tanstack/query-core/build/modern/removable.js", "../../node_modules/@tanstack/query-core/build/modern/query.js", "../../node_modules/@tanstack/query-core/build/modern/queryCache.js", "../../node_modules/@tanstack/query-core/build/modern/mutation.js", "../../node_modules/@tanstack/query-core/build/modern/mutationCache.js", "../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js", "../../node_modules/@tanstack/query-core/build/modern/queryClient.js", "../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var f=require(\"react\"),k=Symbol.for(\"react.element\"),l=Symbol.for(\"react.fragment\"),m=Object.prototype.hasOwnProperty,n=f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,p={key:!0,ref:!0,__self:!0,__source:!0};\nfunction q(c,a,g){var b,d={},e=null,h=null;void 0!==g&&(e=\"\"+g);void 0!==a.key&&(e=\"\"+a.key);void 0!==a.ref&&(h=a.ref);for(b in a)m.call(a,b)&&!p.hasOwnProperty(b)&&(d[b]=a[b]);if(c&&c.defaultProps)for(b in a=c.defaultProps,a)void 0===d[b]&&(d[b]=a[b]);return{$$typeof:k,type:c,key:e,ref:h,props:d,_owner:n.current}}exports.Fragment=l;exports.jsx=q;exports.jsxs=q;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {\n  }\n  onUnsubscribe() {\n  }\n};\nexport {\n  Subscribable\n};\n//# sourceMappingURL=subscribable.js.map", "// src/utils.ts\nvar isServer = typeof window === \"undefined\" || \"Deno\" in globalThis;\nfunction noop() {\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const { exact, status, predicate, mutationKey } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(\n    queryKey,\n    (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n      result[key] = val[key];\n      return result;\n    }, {}) : val\n  );\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return Object.keys(b).every((key) => partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aItems = array ? a : Object.keys(a);\n    const aSize = aItems.length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    const aItemsSet = new Set(aItems);\n    let equalItems = 0;\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      if ((!array && aItemsSet.has(key) || array) && a[key] === void 0 && b[key] === void 0) {\n        copy[key] = void 0;\n        equalItems++;\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key]);\n        if (copy[key] === a[key] && a[key] !== void 0) {\n          equalItems++;\n        }\n      }\n    }\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n  return b;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    if (process.env.NODE_ENV !== \"production\") {\n      try {\n        return replaceEqualDeep(prevData, data);\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`\n        );\n        throw error;\n      }\n    }\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`\n      );\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n  if (typeof throwOnError === \"function\") {\n    return throwOnError(...params);\n  }\n  return !!throwOnError;\n}\nexport {\n  addToEnd,\n  addToStart,\n  ensureQueryFn,\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  isPlainArray,\n  isPlainObject,\n  isServer,\n  isValidTimeout,\n  keepPreviousData,\n  matchMutation,\n  matchQuery,\n  noop,\n  partialMatchKey,\n  replaceData,\n  replaceEqualDeep,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  shouldThrowError,\n  skipToken,\n  sleep,\n  timeUntilStale\n};\n//# sourceMappingURL=utils.js.map", "// src/focusManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar FocusManager = class extends Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onFocus) => {\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach((listener) => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\nexport {\n  FocusManager,\n  focusManager\n};\n//# sourceMappingURL=focusManager.js.map", "// src/onlineManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar OnlineManager = class extends Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onOnline) => {\n      if (!isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach((listener) => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\nexport {\n  OnlineManager,\n  onlineManager\n};\n//# sourceMappingURL=onlineManager.js.map", "// src/thenable.ts\nimport { noop } from \"./utils.js\";\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {\n  });\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = (value) => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = (reason) => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\nfunction tryResolveSync(promise) {\n  let data;\n  promise.then((result) => {\n    data = result;\n    return result;\n  }, noop)?.catch(noop);\n  if (data !== void 0) {\n    return { data };\n  }\n  return void 0;\n}\nexport {\n  pendingThenable,\n  tryResolveSync\n};\n//# sourceMappingURL=thenable.js.map", "// src/retryer.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport { isServer, sleep } from \"./utils.js\";\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n  constructor(options) {\n    super(\"CancelledError\");\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  const thenable = pendingThenable();\n  const cancel = (cancelOptions) => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort?.();\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => focusManager.isFocused() && (config.networkMode === \"always\" || onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess?.(value);\n      continueFn?.();\n      thenable.resolve(value);\n    }\n  };\n  const reject = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError?.(value);\n      continueFn?.();\n      thenable.reject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch((error) => {\n      if (isResolved) {\n        return;\n      }\n      const retry = config.retry ?? (isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      sleep(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return thenable;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return thenable;\n    }\n  };\n}\nexport {\n  CancelledError,\n  canFetch,\n  createRetryer,\n  isCancelledError\n};\n//# sourceMappingURL=retryer.js.map", "// src/notifyManager.ts\nvar defaultScheduler = (cb) => setTimeout(cb, 0);\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = (callback) => {\n    callback();\n  };\n  let batchNotifyFn = (callback) => {\n    callback();\n  };\n  let scheduleFn = defaultScheduler;\n  const schedule = (callback) => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  return {\n    batch: (callback) => {\n      let result;\n      transactions++;\n      try {\n        result = callback();\n      } finally {\n        transactions--;\n        if (!transactions) {\n          flush();\n        }\n      }\n      return result;\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: (callback) => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args);\n        });\n      };\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn) => {\n      notifyFn = fn;\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn) => {\n      batchNotifyFn = fn;\n    },\n    setScheduler: (fn) => {\n      scheduleFn = fn;\n    }\n  };\n}\nvar notifyManager = createNotifyManager();\nexport {\n  createNotifyManager,\n  defaultScheduler,\n  notifyManager\n};\n//# sourceMappingURL=notifyManager.js.map", "// src/removable.ts\nimport { isServer, isValidTimeout } from \"./utils.js\";\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if (isValidTimeout(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (isServer ? Infinity : 5 * 60 * 1e3)\n    );\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\nexport {\n  Removable\n};\n//# sourceMappingURL=removable.js.map", "// src/query.ts\nimport {\n  ensureQueryFn,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  skipToken,\n  timeUntilStale\n} from \"./utils.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { canFetch, createRetryer, isCancelledError } from \"./retryer.js\";\nimport { Removable } from \"./removable.js\";\nvar Query = class extends Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #client;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#client = config.client;\n    this.#cache = this.#client.getQueryCache();\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = { ...this.#defaultOptions, ...options };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({ type: \"setState\", state, setStateOptions });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({ silent: true });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(\n      (observer) => resolveEnabled(observer.options.enabled, this) !== false\n    );\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStatic() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => resolveStaleTime(observer.options.staleTime, this) === \"static\"\n      );\n    }\n    return false;\n  }\n  isStale() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale\n      );\n    }\n    return this.state.data === void 0 || this.state.isInvalidated;\n  }\n  isStaleByTime(staleTime = 0) {\n    if (this.state.data === void 0) {\n      return true;\n    }\n    if (staleTime === \"static\") {\n      return false;\n    }\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    return !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({ type: \"observerAdded\", query: this, observer });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({ type: \"observerRemoved\", query: this, observer });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: \"invalidate\" });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({ silent: true });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`\n        );\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = (object) => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions);\n      const createQueryFnContext = () => {\n        const queryFnContext2 = {\n          client: this.#client,\n          queryKey: this.queryKey,\n          meta: this.meta\n        };\n        addSignalProperty(queryFnContext2);\n        return queryFnContext2;\n      };\n      const queryFnContext = createQueryFnContext();\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this\n        );\n      }\n      return queryFn(queryFnContext);\n    };\n    const createFetchContext = () => {\n      const context2 = {\n        fetchOptions,\n        options: this.options,\n        queryKey: this.queryKey,\n        client: this.#client,\n        state: this.state,\n        fetchFn\n      };\n      addSignalProperty(context2);\n      return context2;\n    };\n    const context = createFetchContext();\n    this.options.behavior?.onFetch(context, this);\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({ type: \"fetch\", meta: context.fetchOptions?.meta });\n    }\n    const onError = (error) => {\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!isCancelledError(error)) {\n        this.#cache.config.onError?.(\n          error,\n          this\n        );\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this\n        );\n      }\n      this.scheduleGc();\n    };\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === void 0) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`\n            );\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        try {\n          this.setData(data);\n        } catch (error) {\n          onError(error);\n          return;\n        }\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this\n        );\n        this.scheduleGc();\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            }\n          };\n        case \"error\":\n          const error = action.error;\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: \"idle\" };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({ query: this, type: \"updated\", action });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? \"fetching\" : \"paused\",\n    ...data === void 0 && {\n      error: null,\n      status: \"pending\"\n    }\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\nexport {\n  Query,\n  fetchState\n};\n//# sourceMappingURL=query.js.map", "// src/queryCache.ts\nimport { hashQueryKeyByOptions, matchQuery } from \"./utils.js\";\nimport { Query } from \"./query.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar QueryCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */ new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? hashQueryKeyByOptions(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({ type: \"removed\", query });\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (query) => matchQuery(defaultedFilters, query)\n    );\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter((query) => matchQuery(filters, query)) : queries;\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline();\n      });\n    });\n  }\n};\nexport {\n  QueryCache\n};\n//# sourceMappingURL=queryCache.js.map", "// src/mutation.ts\nimport { notify<PERSON>anager } from \"./notifyManager.js\";\nimport { Removable } from \"./removable.js\";\nimport { createRetryer } from \"./retryer.js\";\nvar Mutation = class extends Removable {\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter((x) => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    const onContinue = () => {\n      this.#dispatch({ type: \"continue\" });\n    };\n    this.#retryer = createRetryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (restored) {\n        onContinue();\n      } else {\n        this.#dispatch({ type: \"pending\", variables, isPaused });\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this\n        );\n        const context = await this.options.onMutate?.(variables);\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSuccess?.(data, variables, this.state.context);\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSettled?.(data, null, variables, this.state.context);\n      this.#dispatch({ type: \"success\", data });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(\n          error,\n          variables,\n          this.state.context,\n          this\n        );\n        await this.options.onError?.(\n          error,\n          variables,\n          this.state.context\n        );\n        await this.#mutationCache.config.onSettled?.(\n          void 0,\n          error,\n          this.state.variables,\n          this.state.context,\n          this\n        );\n        await this.options.onSettled?.(\n          void 0,\n          error,\n          variables,\n          this.state.context\n        );\n        throw error;\n      } finally {\n        this.#dispatch({ type: \"error\", error });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\nexport {\n  Mutation,\n  getDefaultState\n};\n//# sourceMappingURL=mutation.js.map", "// src/mutationCache.ts\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Mutation } from \"./mutation.js\";\nimport { matchMutation, noop } from \"./utils.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar MutationCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */ new Set();\n    this.#scopes = /* @__PURE__ */ new Map();\n    this.#mutationId = 0;\n  }\n  #mutations;\n  #scopes;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    this.#mutations.add(mutation);\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const scopedMutations = this.#scopes.get(scope);\n      if (scopedMutations) {\n        scopedMutations.push(mutation);\n      } else {\n        this.#scopes.set(scope, [mutation]);\n      }\n    }\n    this.notify({ type: \"added\", mutation });\n  }\n  remove(mutation) {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation);\n      if (typeof scope === \"string\") {\n        const scopedMutations = this.#scopes.get(scope);\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation);\n            if (index !== -1) {\n              scopedMutations.splice(index, 1);\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope);\n          }\n        }\n      }\n    }\n    this.notify({ type: \"removed\", mutation });\n  }\n  canRun(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const mutationsWithSameScope = this.#scopes.get(scope);\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === \"pending\"\n      );\n      return !firstPendingMutation || firstPendingMutation === mutation;\n    } else {\n      return true;\n    }\n  }\n  runNext(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const foundMutation = this.#scopes.get(scope)?.find((m) => m !== mutation && m.state.isPaused);\n      return foundMutation?.continue() ?? Promise.resolve();\n    } else {\n      return Promise.resolve();\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: \"removed\", mutation });\n      });\n      this.#mutations.clear();\n      this.#scopes.clear();\n    });\n  }\n  getAll() {\n    return Array.from(this.#mutations);\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (mutation) => matchMutation(defaultedFilters, mutation)\n    );\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter((mutation) => matchMutation(filters, mutation));\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused);\n    return notifyManager.batch(\n      () => Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(noop))\n      )\n    );\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id;\n}\nexport {\n  MutationCache\n};\n//# sourceMappingURL=mutationCache.js.map", "// src/infiniteQueryBehavior.ts\nimport { addToEnd, addToStart, ensureQueryFn } from \"./utils.js\";\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options;\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n      const oldPages = context.state.data?.pages || [];\n      const oldPageParams = context.state.data?.pageParams || [];\n      let result = { pages: [], pageParams: [] };\n      let currentPage = 0;\n      const fetchFn = async () => {\n        let cancelled = false;\n        const addSignalProperty = (object) => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const createQueryFnContext = () => {\n            const queryFnContext2 = {\n              client: context.client,\n              queryKey: context.queryKey,\n              pageParam: param,\n              direction: previous ? \"backward\" : \"forward\",\n              meta: context.options.meta\n            };\n            addSignalProperty(queryFnContext2);\n            return queryFnContext2;\n          };\n          const queryFnContext = createQueryFnContext();\n          const page = await queryFn(queryFnContext);\n          const { maxPages } = context.options;\n          const addTo = previous ? addToStart : addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          const remainingPages = pages ?? oldPages.length;\n          do {\n            const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n            if (currentPage > 0 && param == null) {\n              break;\n            }\n            result = await fetchPage(result, param);\n            currentPage++;\n          } while (currentPage < remainingPages);\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn,\n            {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal\n            },\n            query\n          );\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n  const lastIndex = pages.length - 1;\n  return pages.length > 0 ? options.getNextPageParam(\n    pages[lastIndex],\n    pages,\n    pageParams[lastIndex],\n    pageParams\n  ) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n  return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n  if (!data) return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam) return false;\n  return getPreviousPageParam(options, data) != null;\n}\nexport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior\n};\n//# sourceMappingURL=infiniteQueryBehavior.js.map", "// src/queryClient.ts\nimport {\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  noop,\n  partialMatchKey,\n  resolveStaleTime,\n  skipToken\n} from \"./utils.js\";\nimport { QueryCache } from \"./queryCache.js\";\nimport { MutationCache } from \"./mutationCache.js\";\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { infiniteQueryBehavior } from \"./infiniteQueryBehavior.js\";\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new QueryCache();\n    this.#mutationCache = config.mutationCache || new MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */ new Map();\n    this.#mutationDefaults = /* @__PURE__ */ new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1) return;\n    this.#unsubscribeFocus = focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0) return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: \"fetching\" }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({ ...filters, status: \"pending\" }).length;\n  }\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    const query = this.#queryCache.build(this, defaultedOptions);\n    const cachedData = query.state.data;\n    if (cachedData === void 0) {\n      return this.fetchQuery(options);\n    }\n    if (options.revalidateIfStale && query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))) {\n      void this.prefetchQuery(defaultedOptions);\n    }\n    return Promise.resolve(cachedData);\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({ queryKey });\n    const query = this.#queryCache.get(\n      defaultedOptions.queryHash\n    );\n    const prevData = query?.state.data;\n    const data = functionalUpdate(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, { ...options, manual: true });\n  }\n  setQueriesData(filters, updater, options) {\n    return notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map(({ queryKey }) => [\n        queryKey,\n        this.setQueryData(queryKey, updater, options)\n      ])\n    );\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(\n      options.queryHash\n    )?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset();\n      });\n      return this.refetchQueries(\n        {\n          type: \"active\",\n          ...filters\n        },\n        options\n      );\n    });\n  }\n  cancelQueries(filters, cancelOptions = {}) {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map((query) => query.cancel(defaultedCancelOptions))\n    );\n    return Promise.all(promises).then(noop).catch(noop);\n  }\n  invalidateQueries(filters, options = {}) {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate();\n      });\n      if (filters?.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      return this.refetchQueries(\n        {\n          ...filters,\n          type: filters?.refetchType ?? filters?.type ?? \"active\"\n        },\n        options\n      );\n    });\n  }\n  refetchQueries(filters, options = {}) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true\n    };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).filter((query) => !query.isDisabled() && !query.isStatic()).map((query) => {\n        let promise = query.fetch(void 0, fetchOptions);\n        if (!fetchOptions.throwOnError) {\n          promise = promise.catch(noop);\n        }\n        return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n      })\n    );\n    return Promise.all(promises).then(noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(\n      resolveStaleTime(defaultedOptions.staleTime, query)\n    ) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(noop).catch(noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop);\n  }\n  ensureInfiniteQueryData(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.ensureQueryData(options);\n  }\n  resumePausedMutations() {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      );\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\nexport {\n  QueryClient\n};\n//# sourceMappingURL=queryClient.js.map", "\"use client\";\n\n// src/QueryClientProvider.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar QueryClientContext = React.createContext(\n  void 0\n);\nvar useQueryClient = (queryClient) => {\n  const client = React.useContext(QueryClientContext);\n  if (queryClient) {\n    return queryClient;\n  }\n  if (!client) {\n    throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n  }\n  return client;\n};\nvar QueryClientProvider = ({\n  client,\n  children\n}) => {\n  React.useEffect(() => {\n    client.mount();\n    return () => {\n      client.unmount();\n    };\n  }, [client]);\n  return /* @__PURE__ */ jsx(QueryClientContext.Provider, { value: client, children });\n};\nexport {\n  QueryClientContext,\n  QueryClientProvider,\n  useQueryClient\n};\n//# sourceMappingURL=QueryClientProvider.js.map"], "names": ["f", "require$$0", "k", "l", "m", "p", "q", "c", "a", "b", "d", "e", "h", "reactJsxRuntime_production_min", "jsxRuntimeModule", "Subscribable", "listener", "isServer", "noop", "functionalUpdate", "updater", "input", "isValidTimeout", "value", "timeUntilStale", "updatedAt", "staleTime", "resolveStaleTime", "query", "resolveEnabled", "enabled", "matchQuery", "filters", "type", "exact", "fetchStatus", "predicate", "query<PERSON><PERSON>", "stale", "hashQueryKeyByOptions", "partialMatchKey", "isActive", "matchMutation", "mutation", "status", "<PERSON><PERSON><PERSON>", "hash<PERSON><PERSON>", "options", "_", "val", "isPlainObject", "result", "key", "replaceEqualDeep", "array", "is<PERSON><PERSON>A<PERSON>y", "aItems", "aSize", "bItems", "bSize", "copy", "aItemsSet", "equalItems", "i", "o", "hasObjectPrototype", "ctor", "prot", "sleep", "timeout", "resolve", "replaceData", "prevData", "data", "addToEnd", "items", "item", "max", "newItems", "addToStart", "skipToken", "ensureQueryFn", "fetchOptions", "FocusManager", "_a", "__privateAdd", "_focused", "_cleanup", "_setup", "__privateSet", "onFocus", "__privateGet", "setup", "focused", "isFocused", "focusManager", "OnlineManager", "_online", "onOnline", "onlineListener", "offlineListener", "online", "onlineManager", "pendingThenable", "reject", "thenable", "_resolve", "_reject", "finalize", "reason", "defaultRetryDelay", "failureCount", "canFetch", "networkMode", "CancelledError", "isCancelledError", "createRetryer", "config", "isRetryCancelled", "isResolved", "continueFn", "cancel", "cancelOptions", "cancelRetry", "continueRetry", "canContinue", "canStart", "pause", "continueResolve", "run", "promiseOrValue", "initialPromise", "error", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "defaultScheduler", "cb", "createNotifyManager", "queue", "transactions", "notifyFn", "callback", "batchNotifyFn", "scheduleFn", "schedule", "flush", "originalQueue", "args", "fn", "notify<PERSON><PERSON>ger", "Removable", "_gcTimeout", "newGcTime", "Query", "_Query_instances", "_initialState", "_revertState", "_cache", "_client", "_retryer", "_defaultOptions", "_abortSignalConsumed", "getDefaultState", "newData", "__privateMethod", "dispatch_fn", "state", "setStateOptions", "promise", "_b", "observer", "x", "abortController", "addSignalProperty", "object", "fetchFn", "queryFn", "queryFnContext", "queryFnContext2", "context", "context2", "_c", "onError", "_d", "action", "reducer", "fetchState", "hasData", "initialDataUpdatedAt", "Query<PERSON>ache", "_queries", "client", "queryHash", "queryInMap", "defaultedFilters", "queries", "event", "Mutation", "_Mutation_instances", "_observers", "_mutationCache", "variables", "onContinue", "restored", "isPaused", "_f", "_e", "_h", "_g", "_j", "_i", "_l", "_k", "_n", "_m", "_p", "_o", "_r", "_q", "_t", "_s", "MutationCache", "_mutations", "_scopes", "_mutationId", "__privateWrapper", "scope", "scopeFor", "scopedMutations", "index", "mutationsWithSameScope", "firstPendingMutation", "foundMutation", "pausedMutations", "infiniteQueryBehavior", "pages", "direction", "oldPages", "oldPageParams", "currentPage", "cancelled", "fetchPage", "param", "previous", "page", "maxPages", "addTo", "pageParamFn", "getPreviousPageParam", "getNextPageParam", "oldData", "remainingPages", "pageParams", "lastIndex", "QueryClient", "_queryCache", "_queryDefaults", "_mutationDefaults", "_mountCount", "_unsubscribeFocus", "_unsubscribeOnline", "defaultedOptions", "cachedData", "queryCache", "defaultedCancelOptions", "promises", "defaults", "query<PERSON><PERSON><PERSON>", "QueryClientContext", "React.createContext", "QueryClientProvider", "children", "React.useEffect", "jsx"], "mappings": ";;;;;;;;6CASa,IAAIA,EAAEC,GAAgB,EAACC,EAAE,OAAO,IAAI,eAAe,EAAEC,EAAE,OAAO,IAAI,gBAAgB,EAAEC,EAAE,OAAO,UAAU,eAAe,EAAEJ,EAAE,mDAAmD,kBAAkBK,EAAE,CAAC,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,SAAS,EAAE,EAClP,SAASC,EAAEC,EAAEC,EAAE,EAAE,CAAC,IAAIC,EAAEC,EAAE,CAAE,EAACC,EAAE,KAAKC,EAAE,KAAc,IAAT,SAAaD,EAAE,GAAG,GAAYH,EAAE,MAAX,SAAiBG,EAAE,GAAGH,EAAE,KAAcA,EAAE,MAAX,SAAiBI,EAAEJ,EAAE,KAAK,IAAIC,KAAKD,EAAEJ,EAAE,KAAKI,EAAEC,CAAC,GAAG,CAACJ,EAAE,eAAeI,CAAC,IAAIC,EAAED,CAAC,EAAED,EAAEC,CAAC,GAAG,GAAGF,GAAGA,EAAE,aAAa,IAAIE,KAAKD,EAAED,EAAE,aAAaC,EAAWE,EAAED,CAAC,IAAZ,SAAgBC,EAAED,CAAC,EAAED,EAAEC,CAAC,GAAG,MAAM,CAAC,SAASP,EAAE,KAAKK,EAAE,IAAII,EAAE,IAAIC,EAAE,MAAMF,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,OAAAG,YAAiBV,EAAEU,GAAW,IAACP,EAAEO,GAAA,KAAaP,2CCPjWQ,GAAA,QAAUb,GAAmD,0BCFlEc,GAAe,KAAM,CACvB,aAAc,CACZ,KAAK,UAA4B,IAAI,IACrC,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,CAC7C,CACE,UAAUC,EAAU,CAClB,YAAK,UAAU,IAAIA,CAAQ,EAC3B,KAAK,YAAa,EACX,IAAM,CACX,KAAK,UAAU,OAAOA,CAAQ,EAC9B,KAAK,cAAe,CACrB,CACL,CACE,cAAe,CACb,OAAO,KAAK,UAAU,KAAO,CACjC,CACE,aAAc,CAChB,CACE,eAAgB,CAClB,CACA,ECpBIC,GAAW,OAAO,OAAW,KAAe,SAAU,WAC1D,SAASC,GAAO,CAChB,CACA,SAASC,GAAiBC,EAASC,EAAO,CACxC,OAAO,OAAOD,GAAY,WAAaA,EAAQC,CAAK,EAAID,CAC1D,CACA,SAASE,GAAeC,EAAO,CAC7B,OAAO,OAAOA,GAAU,UAAYA,GAAS,GAAKA,IAAU,GAC9D,CACA,SAASC,GAAeC,EAAWC,EAAW,CACrC,OAAA,KAAK,IAAID,GAAaC,GAAa,GAAK,KAAK,MAAO,CAAC,CAC9D,CACA,SAASC,GAAiBD,EAAWE,EAAO,CAC1C,OAAO,OAAOF,GAAc,WAAaA,EAAUE,CAAK,EAAIF,CAC9D,CACA,SAASG,GAAeC,EAASF,EAAO,CACtC,OAAO,OAAOE,GAAY,WAAaA,EAAQF,CAAK,EAAIE,CAC1D,CACA,SAASC,GAAWC,EAASJ,EAAO,CAC5B,KAAA,CACJ,KAAAK,EAAO,MACP,MAAAC,EACA,YAAAC,EACA,UAAAC,EACA,SAAAC,EACA,MAAAC,CAAA,EACEN,EACJ,GAAIK,GACF,GAAIH,GACF,GAAIN,EAAM,YAAcW,GAAsBF,EAAUT,EAAM,OAAO,EAC5D,MAAA,WAEA,CAACY,GAAgBZ,EAAM,SAAUS,CAAQ,EAC3C,MAAA,GAGX,GAAIJ,IAAS,MAAO,CACZ,MAAAQ,EAAWb,EAAM,SAAS,EAI5B,GAHAK,IAAS,UAAY,CAACQ,GAGtBR,IAAS,YAAcQ,EAClB,MAAA,EACT,CAQF,MANI,SAAOH,GAAU,WAAaV,EAAM,QAAA,IAAcU,GAGlDH,GAAeA,IAAgBP,EAAM,MAAM,aAG3CQ,GAAa,CAACA,EAAUR,CAAK,EAInC,CACA,SAASc,GAAcV,EAASW,EAAU,CACxC,KAAM,CAAE,MAAAT,EAAO,OAAAU,EAAQ,UAAAR,EAAW,YAAAS,CAAgB,EAAAb,EAClD,GAAIa,EAAa,CACX,GAAA,CAACF,EAAS,QAAQ,YACb,MAAA,GAET,GAAIT,GACF,GAAIY,GAAQH,EAAS,QAAQ,WAAW,IAAMG,GAAQD,CAAW,EACxD,MAAA,WAEA,CAACL,GAAgBG,EAAS,QAAQ,YAAaE,CAAW,EAC5D,MAAA,EACT,CAKF,MAHI,EAAAD,GAAUD,EAAS,MAAM,SAAWC,GAGpCR,GAAa,CAACA,EAAUO,CAAQ,EAItC,CACA,SAASJ,GAAsBF,EAAUU,EAAS,CAEhD,QADeA,GAAA,YAAAA,EAAS,iBAAkBD,IAC5BT,CAAQ,CACxB,CACA,SAASS,GAAQT,EAAU,CACzB,OAAO,KAAK,UACVA,EACA,CAACW,EAAGC,IAAQC,GAAcD,CAAG,EAAI,OAAO,KAAKA,CAAG,EAAE,KAAK,EAAE,OAAO,CAACE,EAAQC,KAChED,EAAAC,CAAG,EAAIH,EAAIG,CAAG,EACdD,GACN,CAAE,CAAA,EAAIF,CACX,CACF,CACA,SAAST,GAAgBhC,EAAGC,EAAG,CAC7B,OAAID,IAAMC,EACD,GAEL,OAAOD,GAAM,OAAOC,EACf,GAELD,GAAKC,GAAK,OAAOD,GAAM,UAAY,OAAOC,GAAM,SAC3C,OAAO,KAAKA,CAAC,EAAE,MAAO2C,GAAQZ,GAAgBhC,EAAE4C,CAAG,EAAG3C,EAAE2C,CAAG,CAAC,CAAC,EAE/D,EACT,CACA,SAASC,GAAiB7C,EAAGC,EAAG,CAC9B,GAAID,IAAMC,EACD,OAAAD,EAET,MAAM8C,EAAQC,GAAa/C,CAAC,GAAK+C,GAAa9C,CAAC,EAC/C,GAAI6C,GAASJ,GAAc1C,CAAC,GAAK0C,GAAczC,CAAC,EAAG,CACjD,MAAM+C,EAASF,EAAQ9C,EAAI,OAAO,KAAKA,CAAC,EAClCiD,EAAQD,EAAO,OACfE,EAASJ,EAAQ7C,EAAI,OAAO,KAAKA,CAAC,EAClCkD,EAAQD,EAAO,OACfE,EAAON,EAAQ,CAAA,EAAK,CAAC,EACrBO,EAAY,IAAI,IAAIL,CAAM,EAChC,IAAIM,EAAa,EACjB,QAASC,EAAI,EAAGA,EAAIJ,EAAOI,IAAK,CAC9B,MAAMX,EAAME,EAAQS,EAAIL,EAAOK,CAAC,GAC3B,CAACT,GAASO,EAAU,IAAIT,CAAG,GAAKE,IAAU9C,EAAE4C,CAAG,IAAM,QAAU3C,EAAE2C,CAAG,IAAM,QAC7EQ,EAAKR,CAAG,EAAI,OACZU,MAEKF,EAAAR,CAAG,EAAIC,GAAiB7C,EAAE4C,CAAG,EAAG3C,EAAE2C,CAAG,CAAC,EACvCQ,EAAKR,CAAG,IAAM5C,EAAE4C,CAAG,GAAK5C,EAAE4C,CAAG,IAAM,QACrCU,IAEJ,CAEF,OAAOL,IAAUE,GAASG,IAAeL,EAAQjD,EAAIoD,CAAA,CAEhD,OAAAnD,CACT,CAYA,SAAS8C,GAAahC,EAAO,CACpB,OAAA,MAAM,QAAQA,CAAK,GAAKA,EAAM,SAAW,OAAO,KAAKA,CAAK,EAAE,MACrE,CACA,SAAS2B,GAAcc,EAAG,CACpB,GAAA,CAACC,GAAmBD,CAAC,EAChB,MAAA,GAET,MAAME,EAAOF,EAAE,YACf,GAAIE,IAAS,OACJ,MAAA,GAET,MAAMC,EAAOD,EAAK,UAOlB,MANI,GAACD,GAAmBE,CAAI,GAGxB,CAACA,EAAK,eAAe,eAAe,GAGpC,OAAO,eAAeH,CAAC,IAAM,OAAO,UAI1C,CACA,SAASC,GAAmBD,EAAG,CAC7B,OAAO,OAAO,UAAU,SAAS,KAAKA,CAAC,IAAM,iBAC/C,CACA,SAASI,GAAMC,EAAS,CACf,OAAA,IAAI,QAASC,GAAY,CAC9B,WAAWA,EAASD,CAAO,CAAA,CAC5B,CACH,CACA,SAASE,GAAYC,EAAUC,EAAM1B,EAAS,CACxC,OAAA,OAAOA,EAAQ,mBAAsB,WAChCA,EAAQ,kBAAkByB,EAAUC,CAAI,EACtC1B,EAAQ,oBAAsB,GAWhCM,GAAiBmB,EAAUC,CAAI,EAEjCA,CACT,CAIA,SAASC,GAASC,EAAOC,EAAMC,EAAM,EAAG,CACtC,MAAMC,EAAW,CAAC,GAAGH,EAAOC,CAAI,EAChC,OAAOC,GAAOC,EAAS,OAASD,EAAMC,EAAS,MAAM,CAAC,EAAIA,CAC5D,CACA,SAASC,GAAWJ,EAAOC,EAAMC,EAAM,EAAG,CACxC,MAAMC,EAAW,CAACF,EAAM,GAAGD,CAAK,EACzB,OAAAE,GAAOC,EAAS,OAASD,EAAMC,EAAS,MAAM,EAAG,EAAE,EAAIA,CAChE,CACA,IAAIE,GAAY,OAAO,EACvB,SAASC,GAAclC,EAASmC,EAAc,CAQ5C,MAAI,CAACnC,EAAQ,UAAWmC,GAAA,MAAAA,EAAc,gBAC7B,IAAMA,EAAa,eAExB,CAACnC,EAAQ,SAAWA,EAAQ,UAAYiC,GACnC,IAAM,QAAQ,OAAO,IAAI,MAAM,qBAAqBjC,EAAQ,SAAS,GAAG,CAAC,EAE3EA,EAAQ,OACjB,cCzNIoC,IAAeC,GAAA,cAAcrE,EAAa,CAI5C,aAAc,CACZ,MAAO,EAJTsE,EAAA,KAAAC,GACAD,EAAA,KAAAE,GACAF,EAAA,KAAAG,GAGEC,EAAA,KAAKD,EAAUE,GAAY,CACzB,GAAI,CAACzE,IAAY,OAAO,iBAAkB,CACxC,MAAMD,EAAW,IAAM0E,EAAS,EAChC,cAAO,iBAAiB,mBAAoB1E,EAAU,EAAK,EACpD,IAAM,CACX,OAAO,oBAAoB,mBAAoBA,CAAQ,CACxD,CACT,CAEK,EACL,CACE,aAAc,CACP2E,EAAA,KAAKJ,IACR,KAAK,iBAAiBI,EAAA,KAAKH,EAAM,CAEvC,CACE,eAAgB,OACT,KAAK,kBACRJ,EAAAO,EAAA,KAAKJ,KAAL,MAAAH,EAAA,WACAK,EAAA,KAAKF,EAAW,QAEtB,CACE,iBAAiBK,EAAO,OACtBH,EAAA,KAAKD,EAASI,IACdR,EAAAO,EAAA,KAAKJ,KAAL,MAAAH,EAAA,WACAK,EAAA,KAAKF,EAAWK,EAAOC,GAAY,CAC7B,OAAOA,GAAY,UACrB,KAAK,WAAWA,CAAO,EAEvB,KAAK,QAAS,CAEtB,CAAK,EACL,CACE,WAAWA,EAAS,CACFF,EAAA,KAAKL,KAAaO,IAEhCJ,EAAA,KAAKH,EAAWO,GAChB,KAAK,QAAS,EAEpB,CACE,SAAU,CACR,MAAMC,EAAY,KAAK,UAAW,EAClC,KAAK,UAAU,QAAS9E,GAAa,CACnCA,EAAS8E,CAAS,CACxB,CAAK,CACL,CACE,WAAY,OACV,OAAI,OAAOH,EAAA,KAAKL,IAAa,UACpBK,EAAA,KAAKL,KAEPF,EAAA,WAAW,WAAX,YAAAA,EAAqB,mBAAoB,QACpD,CACA,EAzDEE,EAAA,YACAC,EAAA,YACAC,EAAA,YAHiBJ,IA2DfW,GAAe,IAAIZ,cC3DnBa,IAAgBZ,GAAA,cAAcrE,EAAa,CAI7C,aAAc,CACZ,MAAO,EAJTsE,EAAA,KAAAY,GAAU,IACVZ,EAAA,KAAAE,GACAF,EAAA,KAAAG,IAGEC,EAAA,KAAKD,GAAUU,GAAa,CAC1B,GAAI,CAACjF,IAAY,OAAO,iBAAkB,CACxC,MAAMkF,EAAiB,IAAMD,EAAS,EAAI,EACpCE,EAAkB,IAAMF,EAAS,EAAK,EAC5C,cAAO,iBAAiB,SAAUC,EAAgB,EAAK,EACvD,OAAO,iBAAiB,UAAWC,EAAiB,EAAK,EAClD,IAAM,CACX,OAAO,oBAAoB,SAAUD,CAAc,EACnD,OAAO,oBAAoB,UAAWC,CAAe,CACtD,CACT,CAEK,EACL,CACE,aAAc,CACPT,EAAA,KAAKJ,IACR,KAAK,iBAAiBI,EAAA,KAAKH,GAAM,CAEvC,CACE,eAAgB,OACT,KAAK,kBACRJ,EAAAO,EAAA,KAAKJ,KAAL,MAAAH,EAAA,WACAK,EAAA,KAAKF,EAAW,QAEtB,CACE,iBAAiBK,EAAO,OACtBH,EAAA,KAAKD,GAASI,IACdR,EAAAO,EAAA,KAAKJ,KAAL,MAAAH,EAAA,WACAK,EAAA,KAAKF,EAAWK,EAAM,KAAK,UAAU,KAAK,IAAI,CAAC,EACnD,CACE,UAAUS,EAAQ,CACAV,EAAA,KAAKM,MAAYI,IAE/BZ,EAAA,KAAKQ,GAAUI,GACf,KAAK,UAAU,QAASrF,GAAa,CACnCA,EAASqF,CAAM,CACvB,CAAO,EAEP,CACE,UAAW,CACT,OAAOV,EAAA,KAAKM,GAChB,CACA,EA/CEA,GAAA,YACAV,EAAA,YACAC,GAAA,YAHkBJ,IAiDhBkB,GAAgB,IAAIN,GClDxB,SAASO,IAAkB,CACzB,IAAIjC,EACAkC,EACJ,MAAMC,EAAW,IAAI,QAAQ,CAACC,EAAUC,IAAY,CAClDrC,EAAUoC,EACVF,EAASG,CACb,CAAG,EACDF,EAAS,OAAS,UAClBA,EAAS,MAAM,IAAM,CACvB,CAAG,EACD,SAASG,EAASnC,EAAM,CACtB,OAAO,OAAOgC,EAAUhC,CAAI,EAC5B,OAAOgC,EAAS,QAChB,OAAOA,EAAS,MACpB,CACE,OAAAA,EAAS,QAAWlF,GAAU,CAC5BqF,EAAS,CACP,OAAQ,YACR,MAAArF,CACN,CAAK,EACD+C,EAAQ/C,CAAK,CACd,EACDkF,EAAS,OAAUI,GAAW,CAC5BD,EAAS,CACP,OAAQ,WACR,OAAAC,CACN,CAAK,EACDL,EAAOK,CAAM,CACd,EACMJ,CACT,CC3BA,SAASK,GAAkBC,EAAc,CACvC,OAAO,KAAK,IAAI,IAAM,GAAKA,EAAc,GAAG,CAC9C,CACA,SAASC,GAASC,EAAa,CAC7B,OAAQA,GAAe,YAAc,SAAWX,GAAc,SAAQ,EAAK,EAC7E,CACA,IAAIY,GAAiB,cAAc,KAAM,CACvC,YAAYnE,EAAS,CACnB,MAAM,gBAAgB,EACtB,KAAK,OAASA,GAAA,YAAAA,EAAS,OACvB,KAAK,OAASA,GAAA,YAAAA,EAAS,MAC3B,CACA,EACA,SAASoE,GAAiB5F,EAAO,CAC/B,OAAOA,aAAiB2F,EAC1B,CACA,SAASE,GAAcC,EAAQ,CAC7B,IAAIC,EAAmB,GACnBP,EAAe,EACfQ,EAAa,GACbC,EACJ,MAAMf,EAAWF,GAAiB,EAC5BkB,EAAUC,GAAkB,OAC3BH,IACHf,EAAO,IAAIU,GAAeQ,CAAa,CAAC,GACxCtC,EAAAiC,EAAO,QAAP,MAAAjC,EAAA,KAAAiC,GAEH,EACKM,EAAc,IAAM,CACxBL,EAAmB,EACpB,EACKM,EAAgB,IAAM,CAC1BN,EAAmB,EACpB,EACKO,EAAc,IAAM9B,GAAa,UAAS,IAAOsB,EAAO,cAAgB,UAAYf,GAAc,SAAQ,IAAOe,EAAO,OAAQ,EAChIS,EAAW,IAAMd,GAASK,EAAO,WAAW,GAAKA,EAAO,OAAQ,EAChE/C,EAAW/C,GAAU,OACpBgG,IACHA,EAAa,IACbnC,EAAAiC,EAAO,YAAP,MAAAjC,EAAA,KAAAiC,EAAmB9F,GACnBiG,GAAA,MAAAA,IACAf,EAAS,QAAQlF,CAAK,EAEzB,EACKiF,EAAUjF,GAAU,OACnBgG,IACHA,EAAa,IACbnC,EAAAiC,EAAO,UAAP,MAAAjC,EAAA,KAAAiC,EAAiB9F,GACjBiG,GAAA,MAAAA,IACAf,EAAS,OAAOlF,CAAK,EAExB,EACKwG,EAAQ,IACL,IAAI,QAASC,GAAoB,OACtCR,EAAcjG,GAAU,EAClBgG,GAAcM,MAChBG,EAAgBzG,CAAK,CAExB,GACD6D,EAAAiC,EAAO,UAAP,MAAAjC,EAAA,KAAAiC,EACN,CAAK,EAAE,KAAK,IAAM,OACZG,EAAa,OACRD,IACHnC,EAAAiC,EAAO,aAAP,MAAAjC,EAAA,KAAAiC,EAER,CAAK,EAEGY,EAAM,IAAM,CAChB,GAAIV,EACF,OAEF,IAAIW,EACJ,MAAMC,EAAiBpB,IAAiB,EAAIM,EAAO,eAAiB,OACpE,GAAI,CACFa,EAAiBC,GAAkBd,EAAO,GAAI,CAC/C,OAAQe,EAAO,CACdF,EAAiB,QAAQ,OAAOE,CAAK,CAC3C,CACI,QAAQ,QAAQF,CAAc,EAAE,KAAK5D,CAAO,EAAE,MAAO8D,GAAU,OAC7D,GAAIb,EACF,OAEF,MAAMc,EAAQhB,EAAO,QAAUpG,GAAW,EAAI,GACxCqH,EAAajB,EAAO,YAAcP,GAClCyB,EAAQ,OAAOD,GAAe,WAAaA,EAAWvB,EAAcqB,CAAK,EAAIE,EAC7EE,EAAcH,IAAU,IAAQ,OAAOA,GAAU,UAAYtB,EAAesB,GAAS,OAAOA,GAAU,YAAcA,EAAMtB,EAAcqB,CAAK,EACnJ,GAAId,GAAoB,CAACkB,EAAa,CACpChC,EAAO4B,CAAK,EACZ,MACR,CACMrB,KACA3B,EAAAiC,EAAO,SAAP,MAAAjC,EAAA,KAAAiC,EAAgBN,EAAcqB,GAC9BhE,GAAMmE,CAAK,EAAE,KAAK,IACTV,EAAW,EAAK,OAASE,EAAO,CACxC,EAAE,KAAK,IAAM,CACRT,EACFd,EAAO4B,CAAK,EAEZH,EAAK,CAEf,CAAO,CACP,CAAK,CACF,EACD,MAAO,CACL,QAASxB,EACT,OAAAgB,EACA,SAAU,KACRD,GAAA,MAAAA,IACOf,GAET,YAAAkB,EACA,cAAAC,EACA,SAAAE,EACA,MAAO,KACDA,EAAQ,EACVG,EAAK,EAELF,EAAO,EAAC,KAAKE,CAAG,EAEXxB,EAEV,CACH,CC9HA,IAAIgC,GAAoBC,GAAO,WAAWA,EAAI,CAAC,EAC/C,SAASC,IAAsB,CAC7B,IAAIC,EAAQ,CAAE,EACVC,EAAe,EACfC,EAAYC,GAAa,CAC3BA,EAAU,CACX,EACGC,EAAiBD,GAAa,CAChCA,EAAU,CACX,EACGE,EAAaR,GACjB,MAAMS,EAAYH,GAAa,CACzBF,EACFD,EAAM,KAAKG,CAAQ,EAEnBE,EAAW,IAAM,CACfH,EAASC,CAAQ,CACzB,CAAO,CAEJ,EACKI,EAAQ,IAAM,CAClB,MAAMC,EAAgBR,EACtBA,EAAQ,CAAE,EACNQ,EAAc,QAChBH,EAAW,IAAM,CACfD,EAAc,IAAM,CAClBI,EAAc,QAASL,GAAa,CAClCD,EAASC,CAAQ,CAC7B,CAAW,CACX,CAAS,CACT,CAAO,CAEJ,EACD,MAAO,CACL,MAAQA,GAAa,CACnB,IAAI5F,EACJ0F,IACA,GAAI,CACF1F,EAAS4F,EAAU,CAC3B,QAAgB,CACRF,IACKA,GACHM,EAAO,CAEjB,CACM,OAAOhG,CACR,EAID,WAAa4F,GACJ,IAAIM,IAAS,CAClBH,EAAS,IAAM,CACbH,EAAS,GAAGM,CAAI,CAC1B,CAAS,CACF,EAEH,SAAAH,EAKA,kBAAoBI,GAAO,CACzBR,EAAWQ,CACZ,EAKD,uBAAyBA,GAAO,CAC9BN,EAAgBM,CACjB,EACD,aAAeA,GAAO,CACpBL,EAAaK,CACnB,CACG,CACH,CACA,IAAIC,EAAgBZ,GAAqB,OC5ErCa,IAAYpE,GAAA,KAAM,CAAN,cACdC,EAAA,KAAAoE,GACA,SAAU,CACR,KAAK,eAAgB,CACzB,CACE,YAAa,CACX,KAAK,eAAgB,EACjBnI,GAAe,KAAK,MAAM,GAC5BmE,EAAA,KAAKgE,EAAa,WAAW,IAAM,CACjC,KAAK,eAAgB,CAC7B,EAAS,KAAK,MAAM,EAEpB,CACE,aAAaC,EAAW,CACtB,KAAK,OAAS,KAAK,IACjB,KAAK,QAAU,EACfA,IAAczI,GAAW,IAAW,EAAI,GAAK,IAC9C,CACL,CACE,gBAAiB,CACX0E,EAAA,KAAK8D,KACP,aAAa9D,EAAA,KAAK8D,EAAU,EAC5BhE,EAAA,KAAKgE,EAAa,QAExB,CACA,EAxBEA,EAAA,YADcrE,4BCWZuE,IAAQvE,GAAA,cAAcoE,EAAU,CAQlC,YAAYnC,EAAQ,CACZ,MAAA,EATEhC,EAAA,KAAAuE,GACVvE,EAAA,KAAAwE,IACAxE,EAAA,KAAAyE,IACAzE,EAAA,KAAA0E,GACA1E,EAAA,KAAA2E,GACA3E,EAAA,KAAA4E,GACA5E,EAAA,KAAA6E,IACA7E,EAAA,KAAA8E,GAGE1E,EAAA,KAAK0E,EAAuB,IAC5B1E,EAAA,KAAKyE,GAAkB7C,EAAO,gBACzB,KAAA,WAAWA,EAAO,OAAO,EAC9B,KAAK,UAAY,CAAC,EAClB5B,EAAA,KAAKuE,EAAU3C,EAAO,QACjB5B,EAAA,KAAAsE,EAASpE,EAAA,KAAKqE,GAAQ,cAAc,GACzC,KAAK,SAAW3C,EAAO,SACvB,KAAK,UAAYA,EAAO,UACnB5B,EAAA,KAAAoE,GAAgBO,GAAgB,KAAK,OAAO,GAC5C,KAAA,MAAQ/C,EAAO,OAAS1B,EAAA,KAAKkE,IAClC,KAAK,WAAW,CAAA,CAElB,IAAI,MAAO,CACT,OAAO,KAAK,QAAQ,IAAA,CAEtB,IAAI,SAAU,OACZ,OAAOzE,EAAAO,EAAA,KAAKsE,KAAL,YAAA7E,EAAe,OAAA,CAExB,WAAWrC,EAAS,CAClB,KAAK,QAAU,CAAE,GAAG4C,EAAA,KAAKuE,IAAiB,GAAGnH,CAAQ,EAChD,KAAA,aAAa,KAAK,QAAQ,MAAM,CAAA,CAEvC,gBAAiB,CACX,CAAC,KAAK,UAAU,QAAU,KAAK,MAAM,cAAgB,QAClD4C,EAAA,KAAAoE,GAAO,OAAO,IAAI,CACzB,CAEF,QAAQM,EAAStH,EAAS,CACxB,MAAM0B,EAAOF,GAAY,KAAK,MAAM,KAAM8F,EAAS,KAAK,OAAO,EAC/D,OAAAC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CACb,KAAA9F,EACA,KAAM,UACN,cAAe1B,GAAA,YAAAA,EAAS,UACxB,OAAQA,GAAA,YAAAA,EAAS,MAAA,GAEZ0B,CAAA,CAET,SAAS+F,EAAOC,EAAiB,CAC/BH,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,WAAY,MAAAC,EAAO,gBAAAC,GAAiB,CAE7D,OAAO1H,EAAS,SACR,MAAA2H,GAAUtF,EAAAO,EAAA,KAAKsE,KAAL,YAAA7E,EAAe,QAC1B,OAAAuF,EAAAhF,EAAA,KAAAsE,KAAA,MAAAU,EAAU,OAAO5H,GACf2H,EAAUA,EAAQ,KAAKxJ,CAAI,EAAE,MAAMA,CAAI,EAAI,QAAQ,QAAQ,CAAA,CAEpE,SAAU,CACR,MAAM,QAAQ,EACd,KAAK,OAAO,CAAE,OAAQ,EAAA,CAAM,CAAA,CAE9B,OAAQ,CACN,KAAK,QAAQ,EACR,KAAA,SAASyE,EAAA,KAAKkE,GAAa,CAAA,CAElC,UAAW,CACT,OAAO,KAAK,UAAU,KACnBe,GAAa/I,GAAe+I,EAAS,QAAQ,QAAS,IAAI,IAAM,EACnE,CAAA,CAEF,YAAa,CACP,OAAA,KAAK,kBAAkB,EAAI,EACtB,CAAC,KAAK,SAAS,EAEjB,KAAK,QAAQ,UAAY5F,IAAa,KAAK,MAAM,gBAAkB,KAAK,MAAM,mBAAqB,CAAA,CAE5G,UAAW,CACL,OAAA,KAAK,kBAAkB,EAAI,EACtB,KAAK,UAAU,KACnB4F,GAAajJ,GAAiBiJ,EAAS,QAAQ,UAAW,IAAI,IAAM,QACvE,EAEK,EAAA,CAET,SAAU,CACJ,OAAA,KAAK,kBAAkB,EAAI,EACtB,KAAK,UAAU,KACnBA,GAAaA,EAAS,mBAAmB,OAC5C,EAEK,KAAK,MAAM,OAAS,QAAU,KAAK,MAAM,aAAA,CAElD,cAAclJ,EAAY,EAAG,CACvB,OAAA,KAAK,MAAM,OAAS,OACf,GAELA,IAAc,SACT,GAEL,KAAK,MAAM,cACN,GAEF,CAACF,GAAe,KAAK,MAAM,cAAeE,CAAS,CAAA,CAE5D,SAAU,OACF,MAAAkJ,EAAW,KAAK,UAAU,KAAMC,GAAMA,EAAE,0BAA0B,EACxED,GAAA,MAAAA,EAAU,QAAQ,CAAE,cAAe,EAAA,IACnCxF,EAAAO,EAAA,KAAKsE,KAAL,MAAA7E,EAAe,UAAS,CAE1B,UAAW,OACH,MAAAwF,EAAW,KAAK,UAAU,KAAMC,GAAMA,EAAE,wBAAwB,EACtED,GAAA,MAAAA,EAAU,QAAQ,CAAE,cAAe,EAAA,IACnCxF,EAAAO,EAAA,KAAKsE,KAAL,MAAA7E,EAAe,UAAS,CAE1B,YAAYwF,EAAU,CACf,KAAK,UAAU,SAASA,CAAQ,IAC9B,KAAA,UAAU,KAAKA,CAAQ,EAC5B,KAAK,eAAe,EACfjF,EAAA,KAAAoE,GAAO,OAAO,CAAE,KAAM,gBAAiB,MAAO,KAAM,SAAAa,EAAU,EACrE,CAEF,eAAeA,EAAU,CACnB,KAAK,UAAU,SAASA,CAAQ,IAClC,KAAK,UAAY,KAAK,UAAU,OAAQC,GAAMA,IAAMD,CAAQ,EACvD,KAAK,UAAU,SACdjF,EAAA,KAAKsE,KACHtE,EAAA,KAAKwE,GACPxE,EAAA,KAAKsE,GAAS,OAAO,CAAE,OAAQ,GAAM,EAErCtE,EAAA,KAAKsE,GAAS,YAAY,GAG9B,KAAK,WAAW,GAEbtE,EAAA,KAAAoE,GAAO,OAAO,CAAE,KAAM,kBAAmB,MAAO,KAAM,SAAAa,EAAU,EACvE,CAEF,mBAAoB,CAClB,OAAO,KAAK,UAAU,MAAA,CAExB,YAAa,CACN,KAAK,MAAM,eACdN,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,YAAA,EACzB,CAEF,MAAMxH,EAASmC,EAAc,WACvB,GAAA,KAAK,MAAM,cAAgB,QAC7B,GAAI,KAAK,MAAM,OAAS,SAAUA,GAAA,MAAAA,EAAc,eAC9C,KAAK,OAAO,CAAE,OAAQ,EAAA,CAAM,UACnBS,EAAA,KAAKsE,GACd,OAAAtE,EAAA,KAAKsE,GAAS,cAAc,EACrBtE,EAAA,KAAKsE,GAAS,QAMrB,GAHAlH,GACF,KAAK,WAAWA,CAAO,EAErB,CAAC,KAAK,QAAQ,QAAS,CACnB,MAAA6H,EAAW,KAAK,UAAU,KAAMC,GAAMA,EAAE,QAAQ,OAAO,EACzDD,GACG,KAAA,WAAWA,EAAS,OAAO,CAClC,CASI,MAAAE,EAAkB,IAAI,gBACtBC,EAAqBC,GAAW,CAC7B,OAAA,eAAeA,EAAQ,SAAU,CACtC,WAAY,GACZ,IAAK,KACHvF,EAAA,KAAK0E,EAAuB,IACrBW,EAAgB,OACzB,CACD,CACH,EACMG,EAAU,IAAM,CACpB,MAAMC,EAAUjG,GAAc,KAAK,QAASC,CAAY,EAUlDiG,GATuB,IAAM,CACjC,MAAMC,EAAkB,CACtB,OAAQzF,EAAA,KAAKqE,GACb,SAAU,KAAK,SACf,KAAM,KAAK,IACb,EACA,OAAAe,EAAkBK,CAAe,EAC1BA,CACT,GAC4C,EAExC,OADJ3F,EAAA,KAAK0E,EAAuB,IACxB,KAAK,QAAQ,UACR,KAAK,QAAQ,UAClBe,EACAC,EACA,IACF,EAEKD,EAAQC,CAAc,CAC/B,EAaME,GAZqB,IAAM,CAC/B,MAAMC,EAAW,CACf,aAAApG,EACA,QAAS,KAAK,QACd,SAAU,KAAK,SACf,OAAQS,EAAA,KAAKqE,GACb,MAAO,KAAK,MACZ,QAAAiB,CACF,EACA,OAAAF,EAAkBO,CAAQ,EACnBA,CACT,GACmC,GACnClG,EAAA,KAAK,QAAQ,WAAb,MAAAA,EAAuB,QAAQiG,EAAS,MACxC5F,EAAA,KAAKqE,GAAe,KAAK,QACrB,KAAK,MAAM,cAAgB,QAAU,KAAK,MAAM,cAAca,EAAAU,EAAQ,eAAR,YAAAV,EAAsB,QACjFL,EAAA,KAAAV,EAAAW,GAAA,UAAU,CAAE,KAAM,QAAS,MAAMgB,EAAAF,EAAQ,eAAR,YAAAE,EAAsB,OAExD,MAAAC,EAAWpD,GAAU,aACnBjB,GAAiBiB,CAAK,GAAKA,EAAM,QACrCkC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CACb,KAAM,QACN,MAAAnC,CAAA,GAGCjB,GAAiBiB,CAAK,KACzBuC,GAAAvF,EAAAO,EAAA,KAAKoE,GAAO,QAAO,UAAnB,MAAAY,EAAA,KAAAvF,EACEgD,EACA,OAEFqD,GAAAF,EAAA5F,EAAA,KAAKoE,GAAO,QAAO,YAAnB,MAAA0B,EAAA,KAAAF,EACE,KAAK,MAAM,KACXnD,EACA,OAGJ,KAAK,WAAW,CAClB,EACA,OAAA3C,EAAA,KAAKwE,EAAW7C,GAAc,CAC5B,eAAgBlC,GAAA,YAAAA,EAAc,eAC9B,GAAImG,EAAQ,QACZ,MAAOP,EAAgB,MAAM,KAAKA,CAAe,EACjD,UAAYrG,GAAS,aACnB,GAAIA,IAAS,OAAQ,CAMnB+G,EAAQ,IAAI,MAAM,GAAG,KAAK,SAAS,oBAAoB,CAAC,EACxD,MAAA,CAEE,GAAA,CACF,KAAK,QAAQ/G,CAAI,QACV2D,EAAO,CACdoD,EAAQpD,CAAK,EACb,MAAA,EAEFuC,GAAAvF,EAAAO,EAAA,KAAKoE,GAAO,QAAO,YAAnB,MAAAY,EAAA,KAAAvF,EAA+BX,EAAM,OACrCgH,GAAAF,EAAA5F,EAAA,KAAKoE,GAAO,QAAO,YAAnB,MAAA0B,EAAA,KAAAF,EACE9G,EACA,KAAK,MAAM,MACX,MAEF,KAAK,WAAW,CAClB,EACA,QAAA+G,EACA,OAAQ,CAACzE,EAAcqB,IAAU,CAC/BkC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,SAAU,aAAAxD,EAAc,MAAAqB,GACjD,EACA,QAAS,IAAM,CACbkC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,OAAA,EACzB,EACA,WAAY,IAAM,CAChBD,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,UAAA,EACzB,EACA,MAAOc,EAAQ,QAAQ,MACvB,WAAYA,EAAQ,QAAQ,WAC5B,YAAaA,EAAQ,QAAQ,YAC7B,OAAQ,IAAM,EAAA,CACf,GACM1F,EAAA,KAAKsE,GAAS,MAAM,CAAA,CA6E/B,EArWEJ,GAAA,YACAC,GAAA,YACAC,EAAA,YACAC,EAAA,YACAC,EAAA,YACAC,GAAA,YACAC,EAAA,YAPUP,EAAA,YA2RVW,WAAUmB,EAAQ,CACV,MAAAC,EAAWnB,GAAU,CACzB,OAAQkB,EAAO,KAAM,CACnB,IAAK,SACI,MAAA,CACL,GAAGlB,EACH,kBAAmBkB,EAAO,aAC1B,mBAAoBA,EAAO,KAC7B,EACF,IAAK,QACI,MAAA,CACL,GAAGlB,EACH,YAAa,QACf,EACF,IAAK,WACI,MAAA,CACL,GAAGA,EACH,YAAa,UACf,EACF,IAAK,QACI,MAAA,CACL,GAAGA,EACH,GAAGoB,GAAWpB,EAAM,KAAM,KAAK,OAAO,EACtC,UAAWkB,EAAO,MAAQ,IAC5B,EACF,IAAK,UACI,MAAA,CACL,GAAGlB,EACH,KAAMkB,EAAO,KACb,gBAAiBlB,EAAM,gBAAkB,EACzC,cAAekB,EAAO,eAAiB,KAAK,IAAI,EAChD,MAAO,KACP,cAAe,GACf,OAAQ,UACR,GAAG,CAACA,EAAO,QAAU,CACnB,YAAa,OACb,kBAAmB,EACnB,mBAAoB,IAAA,CAExB,EACF,IAAK,QACH,MAAMtD,EAAQsD,EAAO,MACrB,OAAIvE,GAAiBiB,CAAK,GAAKA,EAAM,QAAUzC,EAAA,KAAKmE,IAC3C,CAAE,GAAGnE,EAAA,KAAKmE,IAAc,YAAa,MAAO,EAE9C,CACL,GAAGU,EACH,MAAApC,EACA,iBAAkBoC,EAAM,iBAAmB,EAC3C,eAAgB,KAAK,IAAI,EACzB,kBAAmBA,EAAM,kBAAoB,EAC7C,mBAAoBpC,EACpB,YAAa,OACb,OAAQ,OACV,EACF,IAAK,aACI,MAAA,CACL,GAAGoC,EACH,cAAe,EACjB,EACF,IAAK,WACI,MAAA,CACL,GAAGA,EACH,GAAGkB,EAAO,KACZ,CAAA,CAEN,EACK,KAAA,MAAQC,EAAQ,KAAK,KAAK,EAC/BpC,EAAc,MAAM,IAAM,CACnB,KAAA,UAAU,QAASqB,GAAa,CACnCA,EAAS,cAAc,CAAA,CACxB,EACIjF,EAAA,KAAAoE,GAAO,OAAO,CAAE,MAAO,KAAM,KAAM,UAAW,OAAA2B,EAAQ,CAAA,CAC5D,CAAA,EApWOtG,IAuWZ,SAASwG,GAAWnH,EAAM1B,EAAS,CAC1B,MAAA,CACL,kBAAmB,EACnB,mBAAoB,KACpB,YAAaiE,GAASjE,EAAQ,WAAW,EAAI,WAAa,SAC1D,GAAG0B,IAAS,QAAU,CACpB,MAAO,KACP,OAAQ,SAAA,CAEZ,CACF,CACA,SAAS2F,GAAgBrH,EAAS,CAC1B,MAAA0B,EAAO,OAAO1B,EAAQ,aAAgB,WAAaA,EAAQ,cAAgBA,EAAQ,YACnF8I,EAAUpH,IAAS,OACnBqH,EAAuBD,EAAU,OAAO9I,EAAQ,sBAAyB,WAAaA,EAAQ,qBAAA,EAAyBA,EAAQ,qBAAuB,EACrJ,MAAA,CACL,KAAA0B,EACA,gBAAiB,EACjB,cAAeoH,EAAUC,GAAwB,KAAK,IAAQ,EAAA,EAC9D,MAAO,KACP,iBAAkB,EAClB,eAAgB,EAChB,kBAAmB,EACnB,mBAAoB,KACpB,UAAW,KACX,cAAe,GACf,OAAQD,EAAU,UAAY,UAC9B,YAAa,MACf,CACF,UC5YIE,IAAa3G,GAAA,cAAcrE,EAAa,CAC1C,YAAYsG,EAAS,GAAI,CACvB,MAAO,EAIThC,EAAA,KAAA2G,GAHE,KAAK,OAAS3E,EACd5B,EAAA,KAAKuG,EAA2B,IAAI,IACxC,CAEE,MAAMC,EAAQlJ,EAASyH,EAAO,CAC5B,MAAMnI,EAAWU,EAAQ,SACnBmJ,EAAYnJ,EAAQ,WAAaR,GAAsBF,EAAUU,CAAO,EAC9E,IAAInB,EAAQ,KAAK,IAAIsK,CAAS,EAC9B,OAAKtK,IACHA,EAAQ,IAAI+H,GAAM,CAChB,OAAAsC,EACA,SAAA5J,EACA,UAAA6J,EACA,QAASD,EAAO,oBAAoBlJ,CAAO,EAC3C,MAAAyH,EACA,eAAgByB,EAAO,iBAAiB5J,CAAQ,CACxD,CAAO,EACD,KAAK,IAAIT,CAAK,GAETA,CACX,CACE,IAAIA,EAAO,CACJ+D,EAAA,KAAKqG,GAAS,IAAIpK,EAAM,SAAS,IACpC+D,EAAA,KAAKqG,GAAS,IAAIpK,EAAM,UAAWA,CAAK,EACxC,KAAK,OAAO,CACV,KAAM,QACN,MAAAA,CACR,CAAO,EAEP,CACE,OAAOA,EAAO,CACZ,MAAMuK,EAAaxG,EAAA,KAAKqG,GAAS,IAAIpK,EAAM,SAAS,EAChDuK,IACFvK,EAAM,QAAS,EACXuK,IAAevK,GACjB+D,EAAA,KAAKqG,GAAS,OAAOpK,EAAM,SAAS,EAEtC,KAAK,OAAO,CAAE,KAAM,UAAW,MAAAA,CAAK,CAAE,EAE5C,CACE,OAAQ,CACN2H,EAAc,MAAM,IAAM,CACxB,KAAK,OAAM,EAAG,QAAS3H,GAAU,CAC/B,KAAK,OAAOA,CAAK,CACzB,CAAO,CACP,CAAK,CACL,CACE,IAAIsK,EAAW,CACb,OAAOvG,EAAA,KAAKqG,GAAS,IAAIE,CAAS,CACtC,CACE,QAAS,CACP,MAAO,CAAC,GAAGvG,EAAA,KAAKqG,GAAS,OAAM,CAAE,CACrC,CACE,KAAKhK,EAAS,CACZ,MAAMoK,EAAmB,CAAE,MAAO,GAAM,GAAGpK,CAAS,EACpD,OAAO,KAAK,OAAM,EAAG,KAClBJ,GAAUG,GAAWqK,EAAkBxK,CAAK,CAC9C,CACL,CACE,QAAQI,EAAU,GAAI,CACpB,MAAMqK,EAAU,KAAK,OAAQ,EAC7B,OAAO,OAAO,KAAKrK,CAAO,EAAE,OAAS,EAAIqK,EAAQ,OAAQzK,GAAUG,GAAWC,EAASJ,CAAK,CAAC,EAAIyK,CACrG,CACE,OAAOC,EAAO,CACZ/C,EAAc,MAAM,IAAM,CACxB,KAAK,UAAU,QAASvI,GAAa,CACnCA,EAASsL,CAAK,CACtB,CAAO,CACP,CAAK,CACL,CACE,SAAU,CACR/C,EAAc,MAAM,IAAM,CACxB,KAAK,OAAM,EAAG,QAAS3H,GAAU,CAC/BA,EAAM,QAAS,CACvB,CAAO,CACP,CAAK,CACL,CACE,UAAW,CACT2H,EAAc,MAAM,IAAM,CACxB,KAAK,OAAM,EAAG,QAAS3H,GAAU,CAC/BA,EAAM,SAAU,CACxB,CAAO,CACP,CAAK,CACL,CACA,EAjFEoK,EAAA,YANe5G,iBCDbmH,IAAWnH,GAAA,cAAcoE,EAAU,CAIrC,YAAYnC,EAAQ,CAClB,MAAO,EALIhC,EAAA,KAAAmH,GACbnH,EAAA,KAAAoH,GACApH,EAAA,KAAAqH,GACArH,EAAA,KAAA4E,GAGE,KAAK,WAAa5C,EAAO,WACzB5B,EAAA,KAAKiH,EAAiBrF,EAAO,eAC7B5B,EAAA,KAAKgH,EAAa,CAAE,GACpB,KAAK,MAAQpF,EAAO,OAAS+C,GAAiB,EAC9C,KAAK,WAAW/C,EAAO,OAAO,EAC9B,KAAK,WAAY,CACrB,CACE,WAAWtE,EAAS,CAClB,KAAK,QAAUA,EACf,KAAK,aAAa,KAAK,QAAQ,MAAM,CACzC,CACE,IAAI,MAAO,CACT,OAAO,KAAK,QAAQ,IACxB,CACE,YAAY6H,EAAU,CACfjF,EAAA,KAAK8G,GAAW,SAAS7B,CAAQ,IACpCjF,EAAA,KAAK8G,GAAW,KAAK7B,CAAQ,EAC7B,KAAK,eAAgB,EACrBjF,EAAA,KAAK+G,GAAe,OAAO,CACzB,KAAM,gBACN,SAAU,KACV,SAAA9B,CACR,CAAO,EAEP,CACE,eAAeA,EAAU,CACvBnF,EAAA,KAAKgH,EAAa9G,EAAA,KAAK8G,GAAW,OAAQ5B,GAAMA,IAAMD,CAAQ,GAC9D,KAAK,WAAY,EACjBjF,EAAA,KAAK+G,GAAe,OAAO,CACzB,KAAM,kBACN,SAAU,KACV,SAAA9B,CACN,CAAK,CACL,CACE,gBAAiB,CACVjF,EAAA,KAAK8G,GAAW,SACf,KAAK,MAAM,SAAW,UACxB,KAAK,WAAY,EAEjB9G,EAAA,KAAK+G,GAAe,OAAO,IAAI,EAGvC,CACE,UAAW,OACT,QAAOtH,EAAAO,EAAA,KAAKsE,KAAL,YAAA7E,EAAe,aACtB,KAAK,QAAQ,KAAK,MAAM,SAAS,CACrC,CACE,MAAM,QAAQuH,EAAW,+CACvB,MAAMC,EAAa,IAAM,CACvBtC,EAAA,KAAKkC,EAAAjC,GAAL,UAAe,CAAE,KAAM,UAAU,EAClC,EACD9E,EAAA,KAAKwE,EAAW7C,GAAc,CAC5B,GAAI,IACG,KAAK,QAAQ,WAGX,KAAK,QAAQ,WAAWuF,CAAS,EAF/B,QAAQ,OAAO,IAAI,MAAM,qBAAqB,CAAC,EAI1D,OAAQ,CAAC5F,EAAcqB,IAAU,CAC/BkC,EAAA,KAAKkC,EAAAjC,GAAL,UAAe,CAAE,KAAM,SAAU,aAAAxD,EAAc,MAAAqB,GAChD,EACD,QAAS,IAAM,CACbkC,EAAA,KAAKkC,EAAAjC,GAAL,UAAe,CAAE,KAAM,OAAO,EAC/B,EACD,WAAAqC,EACA,MAAO,KAAK,QAAQ,OAAS,EAC7B,WAAY,KAAK,QAAQ,WACzB,YAAa,KAAK,QAAQ,YAC1B,OAAQ,IAAMjH,EAAA,KAAK+G,GAAe,OAAO,IAAI,CACnD,CAAK,GACD,MAAMG,EAAW,KAAK,MAAM,SAAW,UACjCC,EAAW,CAACnH,EAAA,KAAKsE,GAAS,SAAU,EAC1C,GAAI,CACF,GAAI4C,EACFD,EAAY,MACP,CACLtC,EAAA,KAAKkC,EAAAjC,GAAL,UAAe,CAAE,KAAM,UAAW,UAAAoC,EAAW,SAAAG,IAC7C,OAAMnC,GAAAvF,EAAAO,EAAA,KAAK+G,GAAe,QAAO,WAA3B,YAAA/B,EAAA,KAAAvF,EACJuH,EACA,OAEF,MAAMtB,EAAU,OAAMI,GAAAF,EAAA,KAAK,SAAQ,WAAb,YAAAE,EAAA,KAAAF,EAAwBoB,IAC1CtB,IAAY,KAAK,MAAM,SACzBf,EAAA,KAAKkC,EAAAjC,GAAL,UAAe,CACb,KAAM,UACN,QAAAc,EACA,UAAAsB,EACA,SAAAG,CACZ,EAEA,CACM,MAAMrI,EAAO,MAAMkB,EAAA,KAAKsE,GAAS,MAAO,EACxC,cAAM8C,GAAAC,EAAArH,EAAA,KAAK+G,GAAe,QAAO,YAA3B,YAAAK,EAAA,KAAAC,EACJvI,EACAkI,EACA,KAAK,MAAM,QACX,OAEF,OAAMM,GAAAC,EAAA,KAAK,SAAQ,YAAb,YAAAD,EAAA,KAAAC,EAAyBzI,EAAMkI,EAAW,KAAK,MAAM,UAC3D,OAAMQ,GAAAC,EAAAzH,EAAA,KAAK+G,GAAe,QAAO,YAA3B,YAAAS,EAAA,KAAAC,EACJ3I,EACA,KACA,KAAK,MAAM,UACX,KAAK,MAAM,QACX,OAEF,OAAM4I,GAAAC,EAAA,KAAK,SAAQ,YAAb,YAAAD,EAAA,KAAAC,EAAyB7I,EAAM,KAAMkI,EAAW,KAAK,MAAM,UACjErC,EAAA,KAAKkC,EAAAjC,GAAL,UAAe,CAAE,KAAM,UAAW,KAAA9F,CAAI,GAC/BA,CACR,OAAQ2D,EAAO,CACd,GAAI,CACF,aAAMmF,GAAAC,EAAA7H,EAAA,KAAK+G,GAAe,QAAO,UAA3B,YAAAa,EAAA,KAAAC,EACJpF,EACAuE,EACA,KAAK,MAAM,QACX,OAEF,OAAMc,GAAAC,EAAA,KAAK,SAAQ,UAAb,YAAAD,EAAA,KAAAC,EACJtF,EACAuE,EACA,KAAK,MAAM,UAEb,OAAMgB,GAAAC,EAAAjI,EAAA,KAAK+G,GAAe,QAAO,YAA3B,YAAAiB,EAAA,KAAAC,EACJ,OACAxF,EACA,KAAK,MAAM,UACX,KAAK,MAAM,QACX,OAEF,OAAMyF,IAAAC,GAAA,KAAK,SAAQ,YAAb,YAAAD,GAAA,KAAAC,GACJ,OACA1F,EACAuE,EACA,KAAK,MAAM,UAEPvE,CACd,QAAgB,CACRkC,EAAA,KAAKkC,EAAAjC,GAAL,UAAe,CAAE,KAAM,QAAS,MAAAnC,CAAK,EAC7C,CACA,QAAc,CACRzC,EAAA,KAAK+G,GAAe,QAAQ,IAAI,CACtC,CACA,CAmEA,EAtNED,EAAA,YACAC,EAAA,YACAzC,EAAA,YAHauC,EAAA,YAqJbjC,EAAS,SAACmB,EAAQ,CAChB,MAAMC,EAAWnB,GAAU,CACzB,OAAQkB,EAAO,KAAI,CACjB,IAAK,SACH,MAAO,CACL,GAAGlB,EACH,aAAckB,EAAO,aACrB,cAAeA,EAAO,KACvB,EACH,IAAK,QACH,MAAO,CACL,GAAGlB,EACH,SAAU,EACX,EACH,IAAK,WACH,MAAO,CACL,GAAGA,EACH,SAAU,EACX,EACH,IAAK,UACH,MAAO,CACL,GAAGA,EACH,QAASkB,EAAO,QAChB,KAAM,OACN,aAAc,EACd,cAAe,KACf,MAAO,KACP,SAAUA,EAAO,SACjB,OAAQ,UACR,UAAWA,EAAO,UAClB,YAAa,KAAK,IAAG,CACtB,EACH,IAAK,UACH,MAAO,CACL,GAAGlB,EACH,KAAMkB,EAAO,KACb,aAAc,EACd,cAAe,KACf,MAAO,KACP,OAAQ,UACR,SAAU,EACX,EACH,IAAK,QACH,MAAO,CACL,GAAGlB,EACH,KAAM,OACN,MAAOkB,EAAO,MACd,aAAclB,EAAM,aAAe,EACnC,cAAekB,EAAO,MACtB,SAAU,GACV,OAAQ,OACT,CACX,CACK,EACD,KAAK,MAAQC,EAAQ,KAAK,KAAK,EAC/BpC,EAAc,MAAM,IAAM,CACxB5D,EAAA,KAAK8G,GAAW,QAAS7B,GAAa,CACpCA,EAAS,iBAAiBc,CAAM,CACxC,CAAO,EACD/F,EAAA,KAAK+G,GAAe,OAAO,CACzB,SAAU,KACV,KAAM,UACN,OAAAhB,CACR,CAAO,CACP,CAAK,CACL,EAtNetG,IAwNf,SAASgF,IAAkB,CACzB,MAAO,CACL,QAAS,OACT,KAAM,OACN,MAAO,KACP,aAAc,EACd,cAAe,KACf,SAAU,GACV,OAAQ,OACR,UAAW,OACX,YAAa,CACd,CACH,eCnOI2D,IAAgB3I,GAAA,cAAcrE,EAAa,CAC7C,YAAYsG,EAAS,GAAI,CACvB,MAAO,EAMThC,EAAA,KAAA2I,GACA3I,EAAA,KAAA4I,GACA5I,EAAA,KAAA6I,IAPE,KAAK,OAAS7G,EACd5B,EAAA,KAAKuI,EAA6B,IAAI,KACtCvI,EAAA,KAAKwI,EAA0B,IAAI,KACnCxI,EAAA,KAAKyI,GAAc,EACvB,CAIE,MAAMjC,EAAQlJ,EAASyH,EAAO,CAC5B,MAAM7H,EAAW,IAAI4J,GAAS,CAC5B,cAAe,KACf,WAAmB,EAAL4B,GAAA,KAAKD,IAAL,EACd,QAASjC,EAAO,uBAAuBlJ,CAAO,EAC9C,MAAAyH,CACN,CAAK,EACD,YAAK,IAAI7H,CAAQ,EACVA,CACX,CACE,IAAIA,EAAU,CACZgD,EAAA,KAAKqI,GAAW,IAAIrL,CAAQ,EAC5B,MAAMyL,EAAQC,GAAS1L,CAAQ,EAC/B,GAAI,OAAOyL,GAAU,SAAU,CAC7B,MAAME,EAAkB3I,EAAA,KAAKsI,GAAQ,IAAIG,CAAK,EAC1CE,EACFA,EAAgB,KAAK3L,CAAQ,EAE7BgD,EAAA,KAAKsI,GAAQ,IAAIG,EAAO,CAACzL,CAAQ,CAAC,CAE1C,CACI,KAAK,OAAO,CAAE,KAAM,QAAS,SAAAA,CAAQ,CAAE,CAC3C,CACE,OAAOA,EAAU,CACf,GAAIgD,EAAA,KAAKqI,GAAW,OAAOrL,CAAQ,EAAG,CACpC,MAAMyL,EAAQC,GAAS1L,CAAQ,EAC/B,GAAI,OAAOyL,GAAU,SAAU,CAC7B,MAAME,EAAkB3I,EAAA,KAAKsI,GAAQ,IAAIG,CAAK,EAC9C,GAAIE,EACF,GAAIA,EAAgB,OAAS,EAAG,CAC9B,MAAMC,EAAQD,EAAgB,QAAQ3L,CAAQ,EAC1C4L,IAAU,IACZD,EAAgB,OAAOC,EAAO,CAAC,CAElC,MAAUD,EAAgB,CAAC,IAAM3L,GAChCgD,EAAA,KAAKsI,GAAQ,OAAOG,CAAK,CAGrC,CACA,CACI,KAAK,OAAO,CAAE,KAAM,UAAW,SAAAzL,CAAQ,CAAE,CAC7C,CACE,OAAOA,EAAU,CACf,MAAMyL,EAAQC,GAAS1L,CAAQ,EAC/B,GAAI,OAAOyL,GAAU,SAAU,CAC7B,MAAMI,EAAyB7I,EAAA,KAAKsI,GAAQ,IAAIG,CAAK,EAC/CK,EAAuBD,GAAA,YAAAA,EAAwB,KAClDpO,GAAMA,EAAE,MAAM,SAAW,WAE5B,MAAO,CAACqO,GAAwBA,IAAyB9L,CAC/D,KACM,OAAO,EAEb,CACE,QAAQA,EAAU,OAChB,MAAMyL,EAAQC,GAAS1L,CAAQ,EAC/B,GAAI,OAAOyL,GAAU,SAAU,CAC7B,MAAMM,GAAgBtJ,EAAAO,EAAA,KAAKsI,GAAQ,IAAIG,CAAK,IAAtB,YAAAhJ,EAAyB,KAAMhF,GAAMA,IAAMuC,GAAYvC,EAAE,MAAM,UACrF,OAAOsO,GAAA,YAAAA,EAAe,aAAc,QAAQ,QAAS,CAC3D,KACM,QAAO,QAAQ,QAAS,CAE9B,CACE,OAAQ,CACNnF,EAAc,MAAM,IAAM,CACxB5D,EAAA,KAAKqI,GAAW,QAASrL,GAAa,CACpC,KAAK,OAAO,CAAE,KAAM,UAAW,SAAAA,CAAQ,CAAE,CACjD,CAAO,EACDgD,EAAA,KAAKqI,GAAW,MAAO,EACvBrI,EAAA,KAAKsI,GAAQ,MAAO,CAC1B,CAAK,CACL,CACE,QAAS,CACP,OAAO,MAAM,KAAKtI,EAAA,KAAKqI,EAAU,CACrC,CACE,KAAKhM,EAAS,CACZ,MAAMoK,EAAmB,CAAE,MAAO,GAAM,GAAGpK,CAAS,EACpD,OAAO,KAAK,OAAM,EAAG,KAClBW,GAAaD,GAAc0J,EAAkBzJ,CAAQ,CACvD,CACL,CACE,QAAQX,EAAU,GAAI,CACpB,OAAO,KAAK,OAAM,EAAG,OAAQW,GAAaD,GAAcV,EAASW,CAAQ,CAAC,CAC9E,CACE,OAAO2J,EAAO,CACZ/C,EAAc,MAAM,IAAM,CACxB,KAAK,UAAU,QAASvI,GAAa,CACnCA,EAASsL,CAAK,CACtB,CAAO,CACP,CAAK,CACL,CACE,uBAAwB,CACtB,MAAMqC,EAAkB,KAAK,SAAS,OAAQ9D,GAAMA,EAAE,MAAM,QAAQ,EACpE,OAAOtB,EAAc,MACnB,IAAM,QAAQ,IACZoF,EAAgB,IAAKhM,GAAaA,EAAS,SAAU,EAAC,MAAMzB,CAAI,CAAC,CACzE,CACK,CACL,CACA,EAtGE8M,EAAA,YACAC,EAAA,YACAC,GAAA,YAVkB9I,IA+GpB,SAASiJ,GAAS1L,EAAU,OAC1B,OAAOyC,EAAAzC,EAAS,QAAQ,QAAjB,YAAAyC,EAAwB,EACjC,CCpHA,SAASwJ,GAAsBC,EAAO,CACpC,MAAO,CACL,QAAS,CAACxD,EAASzJ,IAAU,eAC3B,MAAMmB,EAAUsI,EAAQ,QAClByD,GAAYvD,GAAAZ,GAAAvF,EAAAiG,EAAQ,eAAR,YAAAjG,EAAsB,OAAtB,YAAAuF,EAA4B,YAA5B,YAAAY,EAAuC,UACnDwD,IAAWtD,EAAAJ,EAAQ,MAAM,OAAd,YAAAI,EAAoB,QAAS,CAAE,EAC1CuD,IAAgBhC,EAAA3B,EAAQ,MAAM,OAAd,YAAA2B,EAAoB,aAAc,CAAE,EAC1D,IAAI7J,EAAS,CAAE,MAAO,CAAA,EAAI,WAAY,CAAA,CAAI,EACtC8L,EAAc,EAClB,MAAMhE,EAAU,SAAY,CAC1B,IAAIiE,EAAY,GAChB,MAAMnE,EAAqBC,GAAW,CACpC,OAAO,eAAeA,EAAQ,SAAU,CACtC,WAAY,GACZ,IAAK,KACCK,EAAQ,OAAO,QACjB6D,EAAY,GAEZ7D,EAAQ,OAAO,iBAAiB,QAAS,IAAM,CAC7C6D,EAAY,EAC9B,CAAiB,EAEI7D,EAAQ,OAE7B,CAAW,CACF,EACKH,EAAUjG,GAAcoG,EAAQ,QAASA,EAAQ,YAAY,EAC7D8D,EAAY,MAAO1K,EAAM2K,EAAOC,IAAa,CACjD,GAAIH,EACF,OAAO,QAAQ,OAAQ,EAEzB,GAAIE,GAAS,MAAQ3K,EAAK,MAAM,OAC9B,OAAO,QAAQ,QAAQA,CAAI,EAa7B,MAAM0G,IAXuB,IAAM,CACjC,MAAMC,GAAkB,CACtB,OAAQC,EAAQ,OAChB,SAAUA,EAAQ,SAClB,UAAW+D,EACX,UAAWC,EAAW,WAAa,UACnC,KAAMhE,EAAQ,QAAQ,IACvB,EACD,OAAAN,EAAkBK,EAAe,EAC1BA,EACR,GAC4C,EACvCkE,GAAO,MAAMpE,EAAQC,EAAc,EACnC,CAAE,SAAAoE,GAAalE,EAAQ,QACvBmE,EAAQH,EAAWtK,GAAaL,GACtC,MAAO,CACL,MAAO8K,EAAM/K,EAAK,MAAO6K,GAAMC,CAAQ,EACvC,WAAYC,EAAM/K,EAAK,WAAY2K,EAAOG,CAAQ,CACnD,CACF,EACD,GAAIT,GAAaC,EAAS,OAAQ,CAChC,MAAMM,EAAWP,IAAc,WACzBW,EAAcJ,EAAWK,GAAuBC,GAChDC,EAAU,CACd,MAAOb,EACP,WAAYC,CACb,EACKI,EAAQK,EAAY1M,EAAS6M,CAAO,EAC1CzM,EAAS,MAAMgM,EAAUS,EAASR,EAAOC,CAAQ,CAC3D,KAAe,CACL,MAAMQ,EAAiBhB,GAASE,EAAS,OACzC,EAAG,CACD,MAAMK,EAAQH,IAAgB,EAAID,EAAc,CAAC,GAAKjM,EAAQ,iBAAmB4M,GAAiB5M,EAASI,CAAM,EACjH,GAAI8L,EAAc,GAAKG,GAAS,KAC9B,MAEFjM,EAAS,MAAMgM,EAAUhM,EAAQiM,CAAK,EACtCH,GACD,OAAQA,EAAcY,EACjC,CACQ,OAAO1M,CACR,EACGkI,EAAQ,QAAQ,UAClBA,EAAQ,QAAU,IAAM,SACtB,OAAOV,GAAAvF,EAAAiG,EAAQ,SAAQ,YAAhB,YAAAV,EAAA,KAAAvF,EACL6F,EACA,CACE,OAAQI,EAAQ,OAChB,SAAUA,EAAQ,SAClB,KAAMA,EAAQ,QAAQ,KACtB,OAAQA,EAAQ,MACjB,EACDzJ,EAEH,EAEDyJ,EAAQ,QAAUJ,CAE1B,CACG,CACH,CACA,SAAS0E,GAAiB5M,EAAS,CAAE,MAAA8L,EAAO,WAAAiB,CAAU,EAAI,CACxD,MAAMC,EAAYlB,EAAM,OAAS,EACjC,OAAOA,EAAM,OAAS,EAAI9L,EAAQ,iBAChC8L,EAAMkB,CAAS,EACflB,EACAiB,EAAWC,CAAS,EACpBD,CACJ,EAAM,MACN,CACA,SAASJ,GAAqB3M,EAAS,CAAE,MAAA8L,EAAO,WAAAiB,CAAU,EAAI,OAC5D,OAAOjB,EAAM,OAAS,GAAIzJ,EAAArC,EAAQ,uBAAR,YAAAqC,EAAA,KAAArC,EAA+B8L,EAAM,CAAC,EAAGA,EAAOiB,EAAW,CAAC,EAAGA,GAAc,MACzG,4BC5FIE,IAAc5K,GAAA,KAAM,CAStB,YAAYiC,EAAS,GAAI,CARzBhC,EAAA,KAAA4K,GACA5K,EAAA,KAAAqH,GACArH,EAAA,KAAA6E,GACA7E,EAAA,KAAA6K,IACA7K,EAAA,KAAA8K,IACA9K,EAAA,KAAA+K,GACA/K,EAAA,KAAAgL,IACAhL,EAAA,KAAAiL,IAEE7K,EAAA,KAAKwK,EAAc5I,EAAO,YAAc,IAAI0E,IAC5CtG,EAAA,KAAKiH,EAAiBrF,EAAO,eAAiB,IAAI0G,IAClDtI,EAAA,KAAKyE,EAAkB7C,EAAO,gBAAkB,CAAE,GAClD5B,EAAA,KAAKyK,GAAiC,IAAI,KAC1CzK,EAAA,KAAK0K,GAAoC,IAAI,KAC7C1K,EAAA,KAAK2K,EAAc,EACvB,CACE,OAAQ,CACNjC,GAAA,KAAKiC,GAAL,IACIzK,EAAA,KAAKyK,KAAgB,IACzB3K,EAAA,KAAK4K,GAAoBtK,GAAa,UAAU,MAAOF,GAAY,CAC7DA,IACF,MAAM,KAAK,sBAAuB,EAClCF,EAAA,KAAKsK,GAAY,QAAS,EAElC,CAAK,GACDxK,EAAA,KAAK6K,GAAqBhK,GAAc,UAAU,MAAOD,GAAW,CAC9DA,IACF,MAAM,KAAK,sBAAuB,EAClCV,EAAA,KAAKsK,GAAY,SAAU,EAEnC,CAAK,GACL,CACE,SAAU,SACR9B,GAAA,KAAKiC,GAAL,IACIzK,EAAA,KAAKyK,KAAgB,KACzBhL,EAAAO,EAAA,KAAK0K,MAAL,MAAAjL,EAAA,WACAK,EAAA,KAAK4K,GAAoB,SACzB1F,EAAAhF,EAAA,KAAK2K,MAAL,MAAA3F,EAAA,WACAlF,EAAA,KAAK6K,GAAqB,QAC9B,CACE,WAAWtO,EAAS,CAClB,OAAO2D,EAAA,KAAKsK,GAAY,QAAQ,CAAE,GAAGjO,EAAS,YAAa,UAAY,CAAA,EAAE,MAC7E,CACE,WAAWA,EAAS,CAClB,OAAO2D,EAAA,KAAK+G,GAAe,QAAQ,CAAE,GAAG1K,EAAS,OAAQ,SAAW,CAAA,EAAE,MAC1E,CAQE,aAAaK,EAAU,OACrB,MAAMU,EAAU,KAAK,oBAAoB,CAAE,SAAAV,CAAQ,CAAE,EACrD,OAAO+C,EAAAO,EAAA,KAAKsK,GAAY,IAAIlN,EAAQ,SAAS,IAAtC,YAAAqC,EAAyC,MAAM,IAC1D,CACE,gBAAgBrC,EAAS,CACvB,MAAMwN,EAAmB,KAAK,oBAAoBxN,CAAO,EACnDnB,EAAQ+D,EAAA,KAAKsK,GAAY,MAAM,KAAMM,CAAgB,EACrDC,EAAa5O,EAAM,MAAM,KAC/B,OAAI4O,IAAe,OACV,KAAK,WAAWzN,CAAO,GAE5BA,EAAQ,mBAAqBnB,EAAM,cAAcD,GAAiB4O,EAAiB,UAAW3O,CAAK,CAAC,GACjG,KAAK,cAAc2O,CAAgB,EAEnC,QAAQ,QAAQC,CAAU,EACrC,CACE,eAAexO,EAAS,CACtB,OAAO2D,EAAA,KAAKsK,GAAY,QAAQjO,CAAO,EAAE,IAAI,CAAC,CAAE,SAAAK,EAAU,MAAAmI,KAAY,CACpE,MAAM/F,EAAO+F,EAAM,KACnB,MAAO,CAACnI,EAAUoC,CAAI,CAC5B,CAAK,CACL,CACE,aAAapC,EAAUjB,EAAS2B,EAAS,CACvC,MAAMwN,EAAmB,KAAK,oBAAoB,CAAE,SAAAlO,CAAQ,CAAE,EACxDT,EAAQ+D,EAAA,KAAKsK,GAAY,IAC7BM,EAAiB,SAClB,EACK/L,EAAW5C,GAAA,YAAAA,EAAO,MAAM,KACxB6C,EAAOtD,GAAiBC,EAASoD,CAAQ,EAC/C,GAAIC,IAAS,OAGb,OAAOkB,EAAA,KAAKsK,GAAY,MAAM,KAAMM,CAAgB,EAAE,QAAQ9L,EAAM,CAAE,GAAG1B,EAAS,OAAQ,EAAI,CAAE,CACpG,CACE,eAAef,EAASZ,EAAS2B,EAAS,CACxC,OAAOwG,EAAc,MACnB,IAAM5D,EAAA,KAAKsK,GAAY,QAAQjO,CAAO,EAAE,IAAI,CAAC,CAAE,SAAAK,KAAe,CAC5DA,EACA,KAAK,aAAaA,EAAUjB,EAAS2B,CAAO,CAC7C,CAAA,CACF,CACL,CACE,cAAcV,EAAU,OACtB,MAAMU,EAAU,KAAK,oBAAoB,CAAE,SAAAV,CAAQ,CAAE,EACrD,OAAO+C,EAAAO,EAAA,KAAKsK,GAAY,IACtBlN,EAAQ,SACd,IAFW,YAAAqC,EAEJ,KACP,CACE,cAAcpD,EAAS,CACrB,MAAMyO,EAAa9K,EAAA,KAAKsK,GACxB1G,EAAc,MAAM,IAAM,CACxBkH,EAAW,QAAQzO,CAAO,EAAE,QAASJ,GAAU,CAC7C6O,EAAW,OAAO7O,CAAK,CAC/B,CAAO,CACP,CAAK,CACL,CACE,aAAaI,EAASe,EAAS,CAC7B,MAAM0N,EAAa9K,EAAA,KAAKsK,GACxB,OAAO1G,EAAc,MAAM,KACzBkH,EAAW,QAAQzO,CAAO,EAAE,QAASJ,GAAU,CAC7CA,EAAM,MAAO,CACrB,CAAO,EACM,KAAK,eACV,CACE,KAAM,SACN,GAAGI,CACJ,EACDe,CACD,EACF,CACL,CACE,cAAcf,EAAS0F,EAAgB,GAAI,CACzC,MAAMgJ,EAAyB,CAAE,OAAQ,GAAM,GAAGhJ,CAAe,EAC3DiJ,EAAWpH,EAAc,MAC7B,IAAM5D,EAAA,KAAKsK,GAAY,QAAQjO,CAAO,EAAE,IAAKJ,GAAUA,EAAM,OAAO8O,CAAsB,CAAC,CAC5F,EACD,OAAO,QAAQ,IAAIC,CAAQ,EAAE,KAAKzP,CAAI,EAAE,MAAMA,CAAI,CACtD,CACE,kBAAkBc,EAASe,EAAU,GAAI,CACvC,OAAOwG,EAAc,MAAM,KACzB5D,EAAA,KAAKsK,GAAY,QAAQjO,CAAO,EAAE,QAASJ,GAAU,CACnDA,EAAM,WAAY,CAC1B,CAAO,GACGI,GAAA,YAAAA,EAAS,eAAgB,OACpB,QAAQ,QAAS,EAEnB,KAAK,eACV,CACE,GAAGA,EACH,MAAMA,GAAA,YAAAA,EAAS,eAAeA,GAAA,YAAAA,EAAS,OAAQ,QAChD,EACDe,CACD,EACF,CACL,CACE,eAAef,EAASe,EAAU,GAAI,CACpC,MAAMmC,EAAe,CACnB,GAAGnC,EACH,cAAeA,EAAQ,eAAiB,EACzC,EACK4N,EAAWpH,EAAc,MAC7B,IAAM5D,EAAA,KAAKsK,GAAY,QAAQjO,CAAO,EAAE,OAAQJ,GAAU,CAACA,EAAM,WAAU,GAAM,CAACA,EAAM,SAAU,CAAA,EAAE,IAAKA,GAAU,CACjH,IAAI8I,EAAU9I,EAAM,MAAM,OAAQsD,CAAY,EAC9C,OAAKA,EAAa,eAChBwF,EAAUA,EAAQ,MAAMxJ,CAAI,GAEvBU,EAAM,MAAM,cAAgB,SAAW,QAAQ,QAAO,EAAK8I,CACnE,CAAA,CACF,EACD,OAAO,QAAQ,IAAIiG,CAAQ,EAAE,KAAKzP,CAAI,CAC1C,CACE,WAAW6B,EAAS,CAClB,MAAMwN,EAAmB,KAAK,oBAAoBxN,CAAO,EACrDwN,EAAiB,QAAU,SAC7BA,EAAiB,MAAQ,IAE3B,MAAM3O,EAAQ+D,EAAA,KAAKsK,GAAY,MAAM,KAAMM,CAAgB,EAC3D,OAAO3O,EAAM,cACXD,GAAiB4O,EAAiB,UAAW3O,CAAK,CACxD,EAAQA,EAAM,MAAM2O,CAAgB,EAAI,QAAQ,QAAQ3O,EAAM,MAAM,IAAI,CACxE,CACE,cAAcmB,EAAS,CACrB,OAAO,KAAK,WAAWA,CAAO,EAAE,KAAK7B,CAAI,EAAE,MAAMA,CAAI,CACzD,CACE,mBAAmB6B,EAAS,CAC1B,OAAAA,EAAQ,SAAW6L,GAAsB7L,EAAQ,KAAK,EAC/C,KAAK,WAAWA,CAAO,CAClC,CACE,sBAAsBA,EAAS,CAC7B,OAAO,KAAK,mBAAmBA,CAAO,EAAE,KAAK7B,CAAI,EAAE,MAAMA,CAAI,CACjE,CACE,wBAAwB6B,EAAS,CAC/B,OAAAA,EAAQ,SAAW6L,GAAsB7L,EAAQ,KAAK,EAC/C,KAAK,gBAAgBA,CAAO,CACvC,CACE,uBAAwB,CACtB,OAAIuD,GAAc,WACTX,EAAA,KAAK+G,GAAe,sBAAuB,EAE7C,QAAQ,QAAS,CAC5B,CACE,eAAgB,CACd,OAAO/G,EAAA,KAAKsK,EAChB,CACE,kBAAmB,CACjB,OAAOtK,EAAA,KAAK+G,EAChB,CACE,mBAAoB,CAClB,OAAO/G,EAAA,KAAKuE,EAChB,CACE,kBAAkBnH,EAAS,CACzB0C,EAAA,KAAKyE,EAAkBnH,EAC3B,CACE,iBAAiBV,EAAUU,EAAS,CAClC4C,EAAA,KAAKuK,IAAe,IAAIpN,GAAQT,CAAQ,EAAG,CACzC,SAAAA,EACA,eAAgBU,CACtB,CAAK,CACL,CACE,iBAAiBV,EAAU,CACzB,MAAMuO,EAAW,CAAC,GAAGjL,EAAA,KAAKuK,IAAe,OAAM,CAAE,EAC3C/M,EAAS,CAAE,EACjB,OAAAyN,EAAS,QAASC,GAAiB,CAC7BrO,GAAgBH,EAAUwO,EAAa,QAAQ,GACjD,OAAO,OAAO1N,EAAQ0N,EAAa,cAAc,CAEzD,CAAK,EACM1N,CACX,CACE,oBAAoBN,EAAaE,EAAS,CACxC4C,EAAA,KAAKwK,IAAkB,IAAIrN,GAAQD,CAAW,EAAG,CAC/C,YAAAA,EACA,eAAgBE,CACtB,CAAK,CACL,CACE,oBAAoBF,EAAa,CAC/B,MAAM+N,EAAW,CAAC,GAAGjL,EAAA,KAAKwK,IAAkB,OAAM,CAAE,EAC9ChN,EAAS,CAAE,EACjB,OAAAyN,EAAS,QAASC,GAAiB,CAC7BrO,GAAgBK,EAAagO,EAAa,WAAW,GACvD,OAAO,OAAO1N,EAAQ0N,EAAa,cAAc,CAEzD,CAAK,EACM1N,CACX,CACE,oBAAoBJ,EAAS,CAC3B,GAAIA,EAAQ,WACV,OAAOA,EAET,MAAMwN,EAAmB,CACvB,GAAG5K,EAAA,KAAKuE,GAAgB,QACxB,GAAG,KAAK,iBAAiBnH,EAAQ,QAAQ,EACzC,GAAGA,EACH,WAAY,EACb,EACD,OAAKwN,EAAiB,YACpBA,EAAiB,UAAYhO,GAC3BgO,EAAiB,SACjBA,CACD,GAECA,EAAiB,qBAAuB,SAC1CA,EAAiB,mBAAqBA,EAAiB,cAAgB,UAErEA,EAAiB,eAAiB,SACpCA,EAAiB,aAAe,CAAC,CAACA,EAAiB,UAEjD,CAACA,EAAiB,aAAeA,EAAiB,YACpDA,EAAiB,YAAc,gBAE7BA,EAAiB,UAAYvL,KAC/BuL,EAAiB,QAAU,IAEtBA,CACX,CACE,uBAAuBxN,EAAS,CAC9B,OAAIA,GAAA,MAAAA,EAAS,WACJA,EAEF,CACL,GAAG4C,EAAA,KAAKuE,GAAgB,UACxB,IAAGnH,GAAA,YAAAA,EAAS,cAAe,KAAK,oBAAoBA,EAAQ,WAAW,EACvE,GAAGA,EACH,WAAY,EACb,CACL,CACE,OAAQ,CACN4C,EAAA,KAAKsK,GAAY,MAAO,EACxBtK,EAAA,KAAK+G,GAAe,MAAO,CAC/B,CACA,EA3REuD,EAAA,YACAvD,EAAA,YACAxC,EAAA,YACAgG,GAAA,YACAC,GAAA,YACAC,EAAA,YACAC,GAAA,YACAC,GAAA,YARgBlL,ICXd0L,GAAqBC,GAAmB,cAC1C,MACF,EAWIC,GAAsB,CAAC,CACzB,OAAA/E,EACA,SAAAgF,CACF,KACEC,GAAAA,UAAgB,KACdjF,EAAO,MAAO,EACP,IAAM,CACXA,EAAO,QAAS,CACjB,GACA,CAACA,CAAM,CAAC,EACYkF,GAAAA,IAAIL,GAAmB,SAAU,CAAE,MAAO7E,EAAQ,SAAAgF,EAAU", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]}